import java.math.BigDecimal;
import java.util.regex.PatternSyntaxException;

public class test_decimal {
    public static String numFilter(String value) throws PatternSyntaxException {
        if (value != null && !value.isEmpty()) {
            String regEx = "[^\\d.]*";
            value = value.replaceAll(regEx, "");
        } else {
            value = "0";
        }
        return value;
    }
    
    public static String format(String number, int scale) {
        number = numFilter(number);
        BigDecimal decimalResult = new BigDecimal(number);
        return decimalResult.setScale(scale, BigDecimal.ROUND_UP).toPlainString();
    }
    
    public static String formatNumberDisplay(double value) {
        if (value == (int) value) {
            // 整数时显示整数格式
            return String.valueOf((int) value);
        } else {
            // 小数时保留1位小数
            return format(String.valueOf(value), 1);
        }
    }
    
    public static void main(String[] args) {
        System.out.println("Testing formatNumberDisplay:");
        System.out.println("formatNumberDisplay(0.5) = '" + formatNumberDisplay(0.5) + "'");
        System.out.println("formatNumberDisplay(1.0) = '" + formatNumberDisplay(1.0) + "'");
        System.out.println("formatNumberDisplay(1.5) = '" + formatNumberDisplay(1.5) + "'");
        System.out.println("formatNumberDisplay(2.3) = '" + formatNumberDisplay(2.3) + "'");
        
        System.out.println("\nTesting format directly:");
        System.out.println("format('0.5', 1) = '" + format("0.5", 1) + "'");
        System.out.println("format('1.5', 1) = '" + format("1.5", 1) + "'");
        
        System.out.println("\nTesting numFilter:");
        System.out.println("numFilter('0.5') = '" + numFilter("0.5") + "'");
        System.out.println("numFilter('1.5') = '" + numFilter("1.5") + "'");
        System.out.println("numFilter('') = '" + numFilter("") + "'");
        System.out.println("numFilter(null) = '" + numFilter(null) + "'");
    }
}
