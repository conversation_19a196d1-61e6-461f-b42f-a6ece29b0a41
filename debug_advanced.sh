#!/bin/bash

# Android高级调试脚本
# 功能：编译、安装、运行、调试、日志过滤等

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 项目配置
APP_PACKAGE="com.doctor.yy"
MAIN_ACTIVITY="com.doctor.br.activity.LauncherActivity"
APK_PATH="app/build/outputs/apk/product/debug"

# 默认参数
CLEAN_BUILD=false
INSTALL_ONLY=false
LOG_LEVEL="V"
LOG_FILTER=""
SHOW_HELP=false

# 打印函数
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_debug() { echo -e "${PURPLE}[DEBUG]${NC} $1"; }

# 显示帮助信息
show_help() {
    echo "========================================"
    echo "    Android高级调试脚本"
    echo "========================================"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --clean          清理构建（强制重新编译）"
    echo "  -i, --install-only   仅安装APK，不重新编译"
    echo "  -l, --log-level      日志级别 (V|D|I|W|E) 默认: V"
    echo "  -f, --filter         日志过滤关键词"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                   # 完整流程：编译->安装->运行->显示日志"
    echo "  $0 -c                # 清理构建后执行完整流程"
    echo "  $0 -i                # 仅安装现有APK并运行"
    echo "  $0 -l E              # 仅显示错误级别日志"
    echo "  $0 -f \"MainActivity\" # 过滤包含MainActivity的日志"
    echo ""
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--clean)
                CLEAN_BUILD=true
                shift
                ;;
            -i|--install-only)
                INSTALL_ONLY=true
                shift
                ;;
            -l|--log-level)
                LOG_LEVEL="$2"
                shift 2
                ;;
            -f|--filter)
                LOG_FILTER="$2"
                shift 2
                ;;
            -h|--help)
                SHOW_HELP=true
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查设备连接
check_device() {
    print_info "检查设备连接..."
    
    devices=$(adb devices | grep -v "List of devices attached" | grep "device$" | wc -l)
    
    if [ $devices -eq 0 ]; then
        print_error "没有检测到连接的设备！"
        echo "请确保："
        echo "  1. 设备已连接并开启USB调试"
        echo "  2. 已授权此计算机进行调试"
        echo "  3. 运行 'adb devices' 检查连接状态"
        exit 1
    elif [ $devices -gt 1 ]; then
        print_warning "检测到多个设备："
        adb devices
        device_name=$(adb devices | grep "device$" | head -1 | awk '{print $1}')
        print_info "将使用设备: $device_name"
    else
        device_name=$(adb devices | grep "device$" | awk '{print $1}')
        print_success "设备已连接: $device_name"
    fi
    
    # 显示设备信息
    device_model=$(adb shell getprop ro.product.model 2>/dev/null || echo "未知")
    android_version=$(adb shell getprop ro.build.version.release 2>/dev/null || echo "未知")
    print_debug "设备型号: $device_model, Android版本: $android_version"
}

# 清理项目
clean_project() {
    if [ "$CLEAN_BUILD" = true ]; then
        print_info "执行清理构建..."
        ./gradlew clean
        print_success "项目清理完成"
    fi
}

# 编译项目
build_project() {
    if [ "$INSTALL_ONLY" = false ]; then
        print_info "开始编译项目..."
        
        # 显示编译进度
        ./gradlew assembleDebug --info | while read line; do
            if [[ $line == *"BUILD SUCCESSFUL"* ]]; then
                print_success "项目编译成功"
            elif [[ $line == *"BUILD FAILED"* ]]; then
                print_error "项目编译失败"
            elif [[ $line == *"> Task"* ]]; then
                echo -e "${CYAN}$line${NC}"
            fi
        done
        
        if [ ${PIPESTATUS[0]} -ne 0 ]; then
            print_error "编译失败，请检查错误信息"
            exit 1
        fi
    else
        print_info "跳过编译，使用现有APK"
    fi
}

# 查找APK文件
find_apk() {
    print_info "查找APK文件..." >&2
    
    apk_file=$(find $APK_PATH -name "*.apk" -type f | head -1)
    
    if [ -z "$apk_file" ]; then
        print_error "未找到APK文件，请先编译项目" >&2
        exit 1
    fi
    
    # 显示APK信息
    apk_size=$(ls -lh "$apk_file" | awk '{print $5}')
    print_success "找到APK文件: $(basename $apk_file) (大小: $apk_size)" >&2
    echo "$apk_file"
}

# 安装APK
install_apk() {
    local apk_file=$1
    print_info "安装APK到设备..."
    
    # 检查应用是否已安装
    if adb shell pm list packages | grep -q "$APP_PACKAGE"; then
        print_debug "应用已安装，将进行覆盖安装"
    fi
    
    adb install -r "$apk_file"
    
    if [ $? -eq 0 ]; then
        print_success "APK安装成功"
        
        # 显示应用信息
        app_version=$(adb shell dumpsys package $APP_PACKAGE | grep "versionName" | head -1 | awk -F'=' '{print $2}')
        if [ -n "$app_version" ]; then
            print_debug "应用版本: $app_version"
        fi
    else
        print_error "APK安装失败"
        exit 1
    fi
}

# 启动应用
start_app() {
    print_info "启动应用..."
    
    # 先停止应用
    adb shell am force-stop $APP_PACKAGE
    sleep 1
    
    # 清除日志缓存
    adb logcat -c
    
    # 启动应用并测量启动时间
    start_time=$(date +%s%3N)
    adb shell am start -W -n $APP_PACKAGE/$MAIN_ACTIVITY > /tmp/launch_result.txt
    
    if [ $? -eq 0 ]; then
        # 解析启动时间
        if [ -f /tmp/launch_result.txt ]; then
            total_time=$(grep "TotalTime" /tmp/launch_result.txt | awk '{print $2}')
            if [ -n "$total_time" ]; then
                print_success "应用启动成功 (启动时间: ${total_time}ms)"
            else
                print_success "应用启动成功"
            fi
            rm -f /tmp/launch_result.txt
        else
            print_success "应用启动成功"
        fi
    else
        print_error "应用启动失败"
        exit 1
    fi
}

# 显示实时日志
show_logs() {
    print_info "开始显示实时日志..."
    print_info "日志级别: $LOG_LEVEL"
    if [ -n "$LOG_FILTER" ]; then
        print_info "过滤关键词: $LOG_FILTER"
    fi
    print_warning "按 Ctrl+C 停止日志显示"
    echo ""
    
    # 等待应用启动
    sleep 2
    
    # 获取应用PID
    app_pid=$(adb shell "ps | grep $APP_PACKAGE" | awk '{print $2}' | head -1)
    
    if [ -n "$app_pid" ]; then
        print_debug "应用进程ID: $app_pid"
        
        # 构建logcat命令
        logcat_cmd="adb logcat --pid=$app_pid -v time *:$LOG_LEVEL"
        
        if [ -n "$LOG_FILTER" ]; then
            eval $logcat_cmd | grep -i "$LOG_FILTER" --color=always
        else
            eval $logcat_cmd
        fi
    else
        print_warning "未找到应用进程，显示所有相关日志"
        
        if [ -n "$LOG_FILTER" ]; then
            adb logcat -v time *:$LOG_LEVEL | grep -i "$APP_PACKAGE\|$LOG_FILTER\|AndroidRuntime\|System.err" --color=always
        else
            adb logcat -v time *:$LOG_LEVEL | grep -i "$APP_PACKAGE\|AndroidRuntime\|System.err" --color=always
        fi
    fi
}

# 主函数
main() {
    # 解析参数
    parse_args "$@"
    
    if [ "$SHOW_HELP" = true ]; then
        show_help
        exit 0
    fi
    
    echo "========================================"
    echo "    Android高级调试脚本"
    echo "========================================"
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "gradlew" ]; then
        print_error "请在Android项目根目录下运行此脚本"
        exit 1
    fi
    
    # 显示当前配置
    print_debug "应用包名: $APP_PACKAGE"
    print_debug "主Activity: $MAIN_ACTIVITY"
    print_debug "清理构建: $CLEAN_BUILD"
    print_debug "仅安装: $INSTALL_ONLY"
    echo ""
    
    # 执行调试流程
    check_device
    echo ""
    
    clean_project
    if [ "$CLEAN_BUILD" = true ]; then
        echo ""
    fi
    
    build_project
    echo ""
    
    apk_file=$(find_apk)
    echo ""
    
    install_apk "$apk_file"
    echo ""
    
    start_app
    echo ""
    
    show_logs
}

# 信号处理
cleanup() {
    print_info "正在清理..."
    # 可以在这里添加清理逻辑
    print_info "脚本已停止"
    exit 0
}

trap cleanup INT TERM

# 运行主函数
main "$@" 