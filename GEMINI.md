
# 项目总结: 必然中医 (brzy_android)

这是一个名为 "必然中医" 的 Android 原生应用程序. 该项目旨在为用户提供中医相关的服务.

## 项目结构

项目采用多模块架构,主要包含以下几个模块:

*   `app`: 这是主应用程序模块,包含了大部分的业务逻辑,UI 界面和核心功能.
    *   `src/main/java/com/doctor/`: 应用程序的主要 Java/Kotlin 源代码.
    *   `src/main/res/`: 应用程序的资源文件,包括布局,图片,字符串等.
    *   `build.gradle`: 该模块的 Gradle 构建脚本,定义了依赖关系和构建配置.
    *   `libs/`: 包含了一些本地的 .jar 库,例如 `netty-all-4.1.8.Final.jar` 和 `zxing-3.2.1.jar`.
*   `baselibrary`: 一个基础库模块,可能包含了一些通用的工具类,基类或可重用的组件,供其他模块使用.
*   `SwipeLibrary`: 一个专门用于处理滑动操作的库,可能用于实现类似滑动删除或滑动菜单等功能.

## 主要技术和依赖库

根据 `app/build.gradle` 文件,该项目使用了以下主要技术和第三方库:

*   **语言:** Java 和 Kotlin
*   **构建工具:** Gradle
*   **数据库:** GreenDAO (一个用于 Android 的对象关系映射 ORM 框架)
*   **UI:**
    *   ButterKnife (视图注入框架)
    *   AndroidX AppCompat, ConstraintLayout
    *   PhotoPicker, ImageShowPicker (图片选择和浏览器)
    *   Android-PickerView (三级联动选择器)
    *   BGA-adapter (RecyclerView 的适配器)
    *   recyclerview-swipe (RecyclerView 的滑动功能)
*   **推送通知:** JPush (极光推送)
*   **社交分享:** MobTech ShareSDK (集成了微信, QQ 和短信分享)
*   **视频播放:** ExoPlayer 和 VideoCache
*   **性能监控:** BlockCanary 和 LeakCanary
*   **Bug 跟踪:** Tencent Bugly

## 构建配置

项目配置了不同的构建变体 (Build Variants):

*   **Build Types:**
    *   `debug`: 用于开发和调试.
    *   `release`: 用于发布生产版本.
*   **Product Flavors:**
    *   `product`: 通用渠道版本.
    *   `huawei`: 专门为华为应用市场打包的版本.

此外,项目还配置了代码混淆 (ProGuard),签名配置和多渠道打包.

## 其他

*   项目使用了 `.gitignore` 来忽略不需要版本控制的文件和目录.
*   `.github/workflows/build-huawei-release.yml` 文件表明项目使用了 GitHub Actions 来自动化构建华为渠道的发布版本.
*   项目中包含了多个 `keystore` 文件,用于应用程序的签名.
