<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="#353c43"
    android:gravity="bottom" >

    <RelativeLayout
        android:id="@+id/xlistview_header_content"
        android:layout_width="fill_parent"
        android:layout_height="60dp" >

        <LinearLayout
            android:id="@+id/xlistview_header_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical" >

            <TextView
                android:textColor="#fff"
                android:id="@+id/xlistview_header_hint_textview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/xlistview_header_hint_normal" />


        </LinearLayout>

        <ImageView
            android:id="@+id/xlistview_header_arrow"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_alignLeft="@id/xlistview_header_text"
            android:layout_centerVertical="true"
            android:layout_marginLeft="-50dp"
            android:src="@drawable/xlistview_arrow" />
        <ImageView
            android:id="@+id/xlistview_header_progressbar"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="-55dp"
            android:visibility="gone"
            android:layout_alignLeft="@id/xlistview_header_text"
            android:layout_marginRight="10dip"
            android:src="@drawable/bga_refresh_loding"/>
    </RelativeLayout>

</LinearLayout>