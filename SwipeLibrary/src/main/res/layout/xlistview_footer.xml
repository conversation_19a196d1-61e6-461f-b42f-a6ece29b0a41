<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/xlistview_footer_content"
        android:layout_width="fill_parent"
        android:layout_height="30dp"
        >

        <LinearLayout
            android:id="@+id/xlistview_footer_progressbar"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_normal_refresh_footer_chrysanthemum"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_marginRight="10dip"
                android:src="@drawable/bga_refresh_loding"/>
            <TextView
                android:layout_marginLeft="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正在加载..."/>
        </LinearLayout>

    </RelativeLayout>

</LinearLayout>