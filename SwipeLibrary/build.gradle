apply plugin: 'com.android.library'
android {
    compileSdkVersion 25
    buildToolsVersion '25.0.0'

    defaultConfig {
        minSdkVersion 15
        targetSdkVersion 25
        versionCode 2
        versionName "1.1"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    compile fileTree(include: ['*.jar'], dir: 'libs')
    compile 'com.android.support:support-v4:25.3.0'
}

////添加
//publish {
//    userOrg = 'huang'//bintray.com用户名
//    groupId = 'com.hankkin'//jcenter上的路径
//    artifactId = 'swiperefresh'//项目名称
//    publishVersion = '1.1'//版本号
//    desc = '更新左滑事件需选中bug'//描述，不重要
//    website = 'https://github.com/Hankkin/SwipeRefreshDemo'//网站，不重要
//}