<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态：白色文字，蓝色背景 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/br_color_theme" />
            <corners android:radius="12dp" />
            <stroke android:width="1dp" android:color="@color/br_color_theme" />
        </shape>
    </item>
    
    <!-- 默认状态：黑色文字，白色背景，灰色边框 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/br_color_white" />
            <corners android:radius="12dp" />
            <stroke android:width="1dp" android:color="@color/br_color_gray_8e98a4" />
        </shape>
    </item>
</selector>
