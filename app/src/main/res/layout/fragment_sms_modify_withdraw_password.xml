<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/current_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 手机号显示 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginTop="10dp"
            android:gravity="bottom"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginBottom="8dp"
                android:src="@drawable/safe_setting_phone" />

            <TextView
                android:id="@+id/phone_tv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:gravity="bottom"
                android:paddingBottom="8dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:singleLine="true"
                android:textColor="@color/br_color_theme_text"
                android:textSize="@dimen/textsize_16" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@color/br_color_split_line" />

        <!-- 短信验证码 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:gravity="bottom"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginBottom="8dp"
                android:src="@drawable/safe_setting_sms" />

            <EditText
                android:id="@+id/sms_code_et"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@null"
                android:gravity="bottom"
                android:hint="请输入短信验证码"
                android:inputType="number"
                android:maxLength="6"
                android:paddingBottom="8dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:singleLine="true"
                android:textColor="@color/br_color_theme_text"
                android:textColorHint="@color/br_color_et_hint"
                android:textSize="@dimen/textsize_16" />

            <TextView
                android:id="@+id/get_code_btn"
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/btn_circle_theme"
                android:gravity="center"
                android:paddingLeft="7dp"
                android:paddingRight="7dp"
                android:text="获取验证码"
                android:textColor="@color/br_color_white"
                android:textSize="@dimen/br_tips_text_size" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@color/br_color_split_line" />

        <!-- 新密码 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:gravity="bottom"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginBottom="8dp"
                android:src="@drawable/safe_setting_password" />

            <EditText
                android:id="@+id/new_password_et"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:gravity="bottom"
                android:hint="请输入6位数字密码"
                android:inputType="numberPassword"
                android:maxLength="6"
                android:paddingBottom="8dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:singleLine="true"
                android:textColor="@color/br_color_theme_text"
                android:textColorHint="@color/br_color_et_hint"
                android:textSize="@dimen/textsize_16" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@color/br_color_split_line" />

        <!-- 确认密码 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:gravity="bottom"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginBottom="8dp"
                android:src="@drawable/safe_setting_password" />

            <EditText
                android:id="@+id/confirm_password_et"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:gravity="bottom"
                android:hint="请再次输入新密码"
                android:inputType="numberPassword"
                android:maxLength="6"
                android:paddingBottom="8dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:singleLine="true"
                android:textColor="@color/br_color_theme_text"
                android:textColorHint="@color/br_color_et_hint"
                android:textSize="@dimen/textsize_16" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@color/br_color_split_line" />

        <!-- 提交按钮 -->
        <com.doctor.br.view.NoDoubleClickBtn
            style="?android:attr/borderlessButtonStyle"
            android:id="@+id/submit_btn"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:layout_marginTop="50dp"
            android:background="@drawable/btn_circle_theme"
            android:gravity="center"
            android:text="确认修改"
            android:textColor="@color/br_color_white"
            android:textSize="@dimen/textsize_18" />
    </LinearLayout>

</RelativeLayout>