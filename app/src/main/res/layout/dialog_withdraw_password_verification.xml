<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_bg"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingBottom="25dp"
    android:paddingLeft="30dp"
    android:paddingRight="30dp"
    android:paddingTop="20dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="请输入提现密码"
        android:textColor="@color/br_color_theme"
        android:textSize="@dimen/textsize_17"
        android:visibility="visible" />

    <!-- 密码输入区域 -->
    <LinearLayout
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_marginTop="22dp"
        android:background="@drawable/corner_f6f6f6"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="20dp"
        android:paddingRight="20dp">

        <ImageView
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginRight="10dp"
            android:src="@drawable/safe_setting_password" />

        <EditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="请输入6位数字密码"
            android:inputType="numberPassword"
            android:maxLength="6"
            android:singleLine="true"
            android:textColor="@color/br_color_theme_text"
            android:textColorHint="@color/br_color_et_hint"
            android:textSize="@dimen/textsize_16" />
    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        style="?android:buttonBarButtonStyle"
        android:layout_width="250dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="34dp">

        <Button
            android:id="@+id/btn_cancel"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_gray"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/br_color_theme_text"
            android:textSize="@dimen/textsize_16" />

        <Button
            android:id="@+id/btn_confirm"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginLeft="14dp"
            android:layout_marginStart="14dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_blue"
            android:gravity="center"
            android:text="确认"
            android:textColor="@color/br_color_white"
            android:textSize="@dimen/textsize_16" />
    </LinearLayout>

</LinearLayout>