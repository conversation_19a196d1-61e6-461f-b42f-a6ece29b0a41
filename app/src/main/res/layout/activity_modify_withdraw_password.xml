<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/br_color_white"
    android:orientation="vertical">


    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        app:tabGravity="center"
        app:tabIndicatorColor="@color/br_color_theme"
        app:tabIndicatorHeight="4dp"
        app:tabMode="fixed"
        app:tabSelectedTextColor="@color/br_color_theme"
        app:tabTextAppearance="@style/TabLayoutTextStyle"
        app:tabTextColor="@color/br_color_theme_text" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/br_color_split_line" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/br_color_white" />
</LinearLayout>