package com.doctor.br.activity.medical;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;

import android.text.Editable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.doctor.br.activity.chatmain.ChatMainActivity;
import com.doctor.br.activity.chatmain.ChooseCommonPrescriptionActivity;
import com.doctor.br.adapter.medical.MedicineHLvAdapter;
import com.doctor.br.bean.DataCacheType;
import com.doctor.br.bean.MedicationDetailBean;
import com.doctor.br.bean.PopItem;
import com.doctor.br.bean.event.FormAndOrderMsgBean;
import com.doctor.br.bean.medical.BoilWayBean;
import com.doctor.br.bean.medical.CheckMedicineToxiticyBean;
import com.doctor.br.bean.medical.DosageFormBean;
import com.doctor.br.bean.medical.DrugsBatchQueryBean;
import com.doctor.br.bean.medical.HerbInfo;
import com.doctor.br.bean.medical.MedicineDetailMsgBean;
import com.doctor.br.bean.medical.MedicineOrTempleBean;
import com.doctor.br.bean.medical.OrderMsgBean;
import com.doctor.br.bean.medical.SpecialUnitBean;
import com.doctor.br.bean.medical.TemplateDrugBean;
import com.doctor.br.db.entity.DataCache;
import com.doctor.br.db.utils.DataCacheDaoUtil;
import com.doctor.br.dialog.ToxiticyDialog;
import com.doctor.br.fragment.chatmain.CommonPrescriptionFragment;
import com.doctor.br.fragment.chatmain.MedicationFragment;
import com.doctor.br.fragment.chatmain.PrescriptionHistoryFragment;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.NettyResultCode;
import com.doctor.br.netty.client.NettyUtils;
import com.doctor.br.netty.impl.DataReceiverImpl;
import com.doctor.br.netty.model.ResultType;
import com.doctor.br.utils.BroadcastAction;
import com.doctor.br.utils.ClipMedicationHelper;
import com.doctor.br.utils.DateUtils;
import com.doctor.br.utils.DecimalUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.MedicineUnit;
import com.doctor.br.view.BRGradientView;
import com.doctor.br.view.BottomPopWindow;
import com.doctor.br.view.CustomShowPopup;
import com.doctor.br.view.DosageFormsPop;
import com.doctor.br.view.FlowLayout;
import com.doctor.br.view.HorizontalListView;
import com.doctor.br.view.NumberKeyboardView;
import com.doctor.br.view.TableLayout;
import com.doctor.br.view.TemplateListPopWindow;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.listener.OnButtonClickListener;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.AlertDialog;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.CustomDialogPlus;
import org.newapp.ones.base.widgets.InputDialog;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Author:sunxiaxia
 * createdDate:2017/10/13
 * description:添加药材界面
 */




public class AddMedicineActivity extends NoActionBarActivity implements NumberKeyboardView.OnKeyDownListener, NumberKeyboardView.OnViewTouchListener {

    //编辑模式枚举
    public enum EditMode {
        NORMAL,
        FORWARD,
        BACKWARD,
        REPLACE
    }

    @BindView(R2.id.back_img)
    ImageView mBackImg;
    @BindView(R2.id.medical_type_tv)
    TextView mDosageForm;
    @BindView(R2.id.taking_medicine_way_tv)
    TextView mSupplierName;
    @BindView(R2.id.change_taking_way)
    ImageView mChangeTakingWay;
    @BindView(R2.id.save_tv)
    TextView mSaveTv;
    @BindView(R2.id.title_rl)
    RelativeLayout mTitleRl;
    @BindView(R2.id.top_ll)
    LinearLayout mTopLl;
    @BindView(R2.id.colse_hint)
    ImageView mColseHint;
    @BindView(R2.id.hint_rl)
    RelativeLayout mHintRl;

    @BindView(R2.id.hint_tv)
    TextView mHintTv;

    @BindView(R2.id.medicine_container_ll)
    LinearLayout mMedicineContainerLl;
    @BindView(R2.id.down_arrow)
    ImageView mDownArrow;
    @BindView(R2.id.up_slide_medicines)
    FlowLayout mUpSlideMedicines;
    @BindView(R2.id.up_slide_medicines_sl)
    ScrollView mUpSlideMedicinesSl;
    @BindView(R2.id.up_arrow)
    ImageView ivUpArrow;
    @BindView(R2.id.horizontal_lv)
    HorizontalListView horizontalLv;
    @BindView(R2.id.input_et)
    EditText mInputEt;
    @BindView(R2.id.common_template_tv)
    TextView mCommonTemplateTv;
    @BindView(R2.id.bottom_rl)
    LinearLayout mBottomRl;
    @BindView(R2.id.num_keyboard)
    NumberKeyboardView numKeyboard;
    @BindView(R2.id.medicines_table)
    TableLayout mMedicinesTable;
    @BindView(R2.id.medicine_scrollview)
    ScrollView medicineScrollview;
    @BindView(R2.id.special_unit_tv)
    TextView mSpecialUnitTv;
    @BindView(R2.id.dose_container)
    LinearLayout mDoseContainer;
    @BindView(R2.id.dose_hint_ll)
    LinearLayout mDoseHintLl;
    @BindView(R2.id.up_slide_medicines_rl)
    RelativeLayout rlUpSlideMedicines;
    @BindView(R2.id.layout_root)
    RelativeLayout mLayoutRoot;
    @BindView(R2.id.rl_horizontal_layout)
    RelativeLayout rlHorizontalLayout;
    @BindView(R2.id.scroll_inner_view)
    LinearLayout scrollInnerView;
    @BindView(R2.id.tv_clear)
    TextView tvClear;
    @BindView(R2.id.tv_paste)
    TextView tvPaste;
    @BindView(R2.id.tv_single_drug_price)
    TextView tvSingleDrugPrice;
    @BindView(R.id.tv_showSetting)
    TextView tvShowSetting;
    @BindView(R.id.tv_adjustByMultiple)
    TextView tvAdjustByMultiple;
    @BindView(R2.id.rl_paste_clear)
    RelativeLayout rlPasteClear;
    @BindView(R2.id.ll_paste_clear_price_layout)
    LinearLayout llPasteClearPriceLayout;
    @BindView(R2.id.btn_know)
    Button btnKnow;
    @BindView(R2.id.guide_layout)
    FrameLayout guideLayout;
    @BindView(R2.id.tv_medicine_num)
    TextView tvMedicineNum;
    @BindView(R2.id.ll_change_dosage)
    LinearLayout llChangeDosage;
    @BindView(R.id.iv_top_mark)
    ImageView ivTopMark;
    @BindView(R.id.smart_input_tv)
    TextView smartInputTv;


    //bool变量，是否通过代码修改剂量
    private boolean isIterateChangeDose = false;



    /**
     * 添加药材
     */
    private static final String ADDMEDICINETYPE_ADD = "addMedicine";
    /**
     * 修改剂型
     */
    private static final String ADDMEDICINETYPE_CHANGE = "changeDrugForm";
    /**
     * 修改剂型
     */
    private static final String ADDMEDICINETYPE_TEMPLATE = "template";
    /**
     * 粘贴药方
     */
    private static final String ADDMEDICINETYPE_PASTE = "paste";
    /**
     * 临时保存的药方
     */
    private static final String ADDMEDICINETYPE_CACHE = "cache";
    private static final String isClick = "ISCLICK";
    private static final String isLongClick = "ISLONGCLICK";
    private static final int COUNT_MEDICINE_PRICE = 1000;
    private static Object lock = new Object();


    private String drugProviderId;
    private String drugType;
    private String dosageForm;
    private String supplierName;
    private String costDesc;
    private String patientId;
    private BottomPopWindow mBottomPopWindow;
    private List<PopItem> takingWayPopItems;
    private List<PopItem> boilPopItems;
    private boolean isFirst = true;
    private List<MedicineDetailMsgBean> mMedicineList;
    private String mCustomBoilWay;
    private InputDialog mDialog;
    private MedicineHLvAdapter mMedicineHLvAdapter;
    private List<MedicineOrTempleBean> mMedicineOrTemples;
    private ConfirmDialog mConfirmDialog;
    private ToxiticyDialog toxiticyDialog;
    private DosageFormsPop mDosageFormsPop;
    private List<DosageFormBean.FormList> mDosageFormList;
    private List<String> mMedicineIdList;
    private int leftPosition;
    private String rightPosition = "";

    //当前的标准
    private String standard = "";
    //当前的标准描述
    private String standardDesc = "";

    /**
     * 是否改变了剂型
     */
    private boolean isChangedDrugForm = false;
    /**
     * 是否粘贴药方
     */
    private boolean isPaste = false;
    /**
     * 1添加药材，2是修改剂型,3调模板,4粘贴药方
     */
    private String addMedicineType = "";
    private HashMap<String, String> doseMap;
    private HashMap<String, String> useMethodMap;
    private TemplateListPopWindow templateListPop;
    private String templateName;
    private String isPregnant;
    private DosageFormBean.FormList.FormDetailMsg mFormDetailMsg;
    private DosageFormBean.FormList formListBean;
    /**
     * 记录剂量是否改变，true，改变了，提示“部分药材已自动调整剂量”
     */
    private boolean doseIsChanged;
    private OrderMsgBean orderMsgBean;
    private String drugForm;
    /**
     * 保存智能开方药材列表信息
     */
    private ArrayList<HerbInfo> smartInputList;
    /**
     * 打开页面时的drugForm类型
     */
    private String tag_drugForm;
    private FormAndOrderMsgBean orderEventBean;
    private RequestCallBack getMedicineToxiticyMsgCallBack;
    private RequestCallBack getMedicinePriceMsgCallBack;
    private RequestCallBack addBoilWayCallBack;
    private RequestCallBack getBoilWayCallBack;
    private RequestCallBack getDosageFormListCallBack;
    //批量查询药材信息
    private RequestCallBack getMedicineListCallBack;
    /**
     * 复制的用药集合
     */
    private List<MedicationDetailBean.DetailsBean> clipMedicatioinList;
    /**
     * 库存中存在此药材
     */
    private boolean isExit = true;
    private CountPriceThread countPriceThread;
    /**
     * 是否缓存中药材，用于添加药材相关操作
     */
    private boolean isCache;
    /**
     * 标记是否是处理缓存中的药材，用于点击返回按钮的逻辑处理
     */
    private boolean isHandleCacheMedicine;
    private String userId;
    private String takerId;
    private String takerName;
    private boolean isShowMedicalServiceFee;
    private int from = FormAndOrderMsgBean.FROM_NORMAL_MEDICINE;

    // 添加字体缩放比例成员变量
    private float mCurrentFontSizeScale = 1.0f;

    private Button floatingButton;
    private int screenWidth, screenHeight, topBarHeight, bottomBarHeight;

    private CustomShowPopup customShowPopup;

    //当前编辑模式，默认为正常
    private EditMode mEditMode = EditMode.NORMAL;

    //当前编辑模式对应的索引
    private int mEditIndex = -1;

    //当前编辑的gradient
    private BRGradientView mEditGradientView;

    //如果颗粒提示弹窗弹出，则保存当天的时间
    private String showNoticeTimeString;

    private TextView[] multipleButtons;
    private EditText etMultiple;
    /**
     * 是否需要获取厂商列表，用来更新缓存中信息
     */
    private boolean isNeedGetSupplierList = false;
    /**
     * 临时保存需要添加的Medicines
     */
    private List<MedicineDetailMsgBean> tempMedicines = new ArrayList<>();

    private ActivityResultLauncher<Intent> launcher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == Activity.RESULT_OK) {
                    LogUtils.i(AddMedicineActivity.class, "result.getData()====" + result.getData());
                    Intent data = result.getData();

                    if (data == null) {
                        return;
                    }

                    if (!"BRIntelligent".equals(data.getStringExtra("key"))) {
                        //从自定义煎法界面返回的话，则刷新自定义煎法数据
                        if (mBottomPopWindow != null && mBottomPopWindow.isShowing()){
                            refreshBoilWayData();
                        }
                        return;
                    }

                    ArrayList<HerbInfo> herbList = data.getParcelableArrayListExtra("herbList");
                    if (herbList == null || herbList.isEmpty()) {
                        return;
                    }

                    //循环获取 herbInfo
//                    for (HerbInfo herbInfo : herbList) {
//                        LogUtils.i(AddMedicineActivity.class, "herbInfo.drugName=" + herbInfo.getDrugName());
//                    }

                    smartInputList = new ArrayList<>(herbList);

                    //获取到列表中的药名为字符串
                    JSONArray jsonArray = new JSONArray();
                    for (HerbInfo herb : herbList) {
                        jsonArray.put(herb.getDrugName());
                    }
                    String drugNames = jsonArray.toString();

                    LogUtils.i(AddMedicineActivity.class, "drugNames=" + drugNames);

                    Map<String, String> map = new HashMap<>();
                    map.put("drugType", drugType);  
                    map.put("drugForm", drugForm);
                    map.put("drugProviderId", drugProviderId);
                    map.put("dcId","");
                    map.put("nameList", drugNames);

                    LogUtils.i(AddMedicineActivity.class, "000451 map=" + map);
                    getMedicineListCallBack = addHttpPostRequest(HttpUrlManager.GET_DRUGS_BY_BATCH_QUERY, map, DrugsBatchQueryBean.class, this);

                }
            }
    );

    Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                //计算完药价返回结果处理
                case COUNT_MEDICINE_PRICE:
                    if (tvSingleDrugPrice != null && msg.obj != null) {
                        String price = (String) msg.obj;
                        tvSingleDrugPrice.setText(TextUtils.isEmpty(price) ? "￥0.00" : "￥" + price);
                    }
                    break;
                default:
                    break;
            }
        }
    };


    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_add_medicine);

        //获取字体比例保存在本地的值
        mCurrentFontSizeScale = SharedPreferenceForeverUtils.getFloat(mContext, PublicParams.ADDMEDICINE_FONT_SIZE_SCALE, 1.0f);

        // 获取屏幕宽度和高度
        DisplayMetrics displayMetrics = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        screenWidth = displayMetrics.widthPixels;
        screenHeight = displayMetrics.heightPixels;

        // 初始化 CustomShowPopup 并设置回调
        customShowPopup = new CustomShowPopup(this, new CustomShowPopup.OnLayoutOptionSelectedListener() {
            @Override
            public void onLayoutOptionSelected(boolean isSingleColumn) {
                if (isSingleColumn) {
                    mMedicinesTable.setColumnCount(1, true);
                } else {
                    mMedicinesTable.setColumnCount(2, true);
                }
            }

            @Override
            public void onFontSizeScaleOptionSelected(float fontSizeScale) {
                LogUtils.i(AddMedicineActivity.class, "font size scale == " + fontSizeScale);
                updateMedicineItemsFontSize(fontSizeScale);

                //如果选择的是超大字体 并且 是双排，则调整为单排显示
                boolean isSingleColumn = SharedPreferenceForeverUtils.getBoolean(mContext, PublicParams.IS_SINGLE_COLUMN, false);
                if (fontSizeScale == 1.4F && !isSingleColumn){
                    mMedicinesTable.setColumnCount(1, true);
                    //弹窗选择修改为单排
                    customShowPopup.setLayoutOption(true);
                }
            }
        });

        //判断显示医技服务费，目前只有福建省不显示,不显示医技服务费的药费需换算成老板药价
        isShowMedicalServiceFee = SharedPreferenceUtils.getBoolean(mContext, PublicParams.SHOW_MEDICAL_SERVICE_FEE, true);
        receiveDataFromMedicationFragment();
        showAddMedicineGuideUi();
        initViewData();
        from = getIntent().getIntExtra(PublicParams.FROM, FormAndOrderMsgBean.FROM_NORMAL_MEDICINE);
        numKeyboard.setOnKeyDownListener(this);
        numKeyboard.setOnViewTouchListener(this);
        initClipMedication();//获取复制的药方
        try {
            if (getIntent().getSerializableExtra("editMedicines") != null) {
                List<MedicineDetailMsgBean> medicines = (List<MedicineDetailMsgBean>) getIntent().getSerializableExtra("editMedicines");
                isCache = getIntent().getBooleanExtra(PublicParams.IS_CACHE_MEDICINE, false);
                isHandleCacheMedicine = isCache;
                if (isCache) {//是缓存中的药材,重新请求药材详情数据

                    isNeedGetSupplierList = true;

                    tempMedicines = medicines;
                    //请求获取厂商列表 HttpUrlManager.DOSAGE_FORM_LIST
                    getDosageFormList();
//                    addCacheMedicines(medicines);
                } else {
                    addMedicineType = ADDMEDICINETYPE_TEMPLATE;
                    for (MedicineDetailMsgBean medicine : medicines) {
                        if (!TextUtils.isEmpty(medicine.getUseMethod())) {
                            useMethodMap.put(medicine.getDrugId(), medicine.getUseMethod());
                        }
                    }
                    addMedicineView(medicines, mMedicinesTable, addMedicineType);
                }
            }
            showInputKeyBoard();
        } catch (Exception e) {
            e.printStackTrace();
        }

        //初始化行高
        float originalHeight = mMedicinesTable.getItemHeight();
        int newRowHeight = (int)(originalHeight * mCurrentFontSizeScale);

        mMedicinesTable.setRowHeight(newRowHeight);
    }

    /**
     * 更新所有药材项的字体大小和布局高度
     * @param fontSizeScale 字体大小的缩放比例
     */
    private void updateMedicineItemsFontSize(float fontSizeScale) {

        mCurrentFontSizeScale = fontSizeScale;

        //持久化保存
        SharedPreferenceForeverUtils.putFloat(mContext, PublicParams.ADDMEDICINE_FONT_SIZE_SCALE, mCurrentFontSizeScale);

         // 计算新的行高
         float originalHeight = mMedicinesTable.getItemHeight();
         int newRowHeight = (int)(originalHeight * fontSizeScale);
         
         // 设置 TableLayout 的行高
        mMedicinesTable.setRowHeight(newRowHeight);

         
        // 遍历 TableLayout 中的所有子视图
        for (int i = 0; i < mMedicinesTable.getChildCount(); i++) {
            View itemView = mMedicinesTable.getChildAt(i);
            if (itemView != null) {
                // 获取药材项中的所有TextView
                TextView nameTv = itemView.findViewById(R.id.name_tv);
                TextView unitTv = itemView.findViewById(R.id.unit_tv);
                TextView understockTv = itemView.findViewById(R.id.understock_tv);
                TextView boilMedicineTv = itemView.findViewById(R.id.boil_medicine_tv);
                TextView priceTv = itemView.findViewById(R.id.price_tv);
                EditText doseEt = itemView.findViewById(R.id.dose_et);

                // 设置新的字体大小
                if (nameTv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_name_text_size);
                    nameTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * fontSizeScale);
                }
                if (unitTv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_unit_text_size);
                    unitTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * fontSizeScale);
                }
                if (understockTv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_understock_text_size);
                    understockTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * fontSizeScale);
                }
                if (boilMedicineTv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_boil_text_size);
                    boilMedicineTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * fontSizeScale);
                }
                if (priceTv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_price_text_size);
                    priceTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * fontSizeScale);
                }
                if (doseEt != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_dose_text_size);
                    doseEt.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * fontSizeScale);
                }

                // // 调整整体布局高度
                // ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
                // if (layoutParams != null) {
                //     // 获取原始item高度
                //     float originalHeight = getResources().getDimension(R.dimen.br_margin_45);
                //     // 根据字体缩放比例调整高度
                //     layoutParams.height = (int)(originalHeight * fontSizeScale);
                //     itemView.setLayoutParams(layoutParams);
                // }
            }
        }

        // 刷新布局
        mMedicinesTable.requestLayout();
    }


    /**
     * 显示添加药材时的引导页
     */
    private void showAddMedicineGuideUi() {
        if (!SharedPreferenceForeverUtils.getBoolean(this, PublicParams.IS_FIRST_SHOW_ADDMEDICNE_GUIDE)) {
            guideLayout.setVisibility(View.VISIBLE);
        } else {
            guideLayout.setVisibility(View.GONE);
            showInputKeyBoardDelayMore();
            showHintView();//显示特殊煎药法引导提示
        }
    }



    /**
     * 获取复制的药方
     */
    private void initClipMedication() {
        clipMedicatioinList = new ClipMedicationHelper().getMedicatioinList();
        if (clipMedicatioinList == null) {//如果集合为空，说明没有复制的内容
            tvPaste.setTextColor(getResources().getColor(R.color.br_color_et_hint));
        } else {//有复制的内容
            tvPaste.setTextColor(getResources().getColor(R.color.br_color_theme));
        }
    }

    /**
     * 接收从上个页面传过来的数据
     */
    private void receiveDataFromMedicationFragment() {
        mFormDetailMsg = (DosageFormBean.FormList.FormDetailMsg) getIntent().getSerializableExtra(PublicParams.DOSAGE_SUPPLIER_BEAN);
        formListBean = (DosageFormBean.FormList) getIntent().getSerializableExtra(PublicParams.DOSAGEFORMBEAN);
        //用药者id
        takerId = getIntent().getStringExtra(PublicParams.TAKERID);
        takerName = getIntent().getStringExtra(PublicParams.TAKER_NAME);
        leftPosition = getIntent().getIntExtra("leftPosition", 0);//剂型弹框左侧数据选中标记
        rightPosition = getIntent().getStringExtra("rightPosition");//剂型弹框右侧数据选中标记

        // 添加空值检查，防止空指针异常
        if (mFormDetailMsg != null) {
            drugType = mFormDetailMsg.getProductSubType();//药材类型 Y/K 颗粒或饮片 productSubType
            drugProviderId = mFormDetailMsg.getId();//厂商id
            dosageForm = mFormDetailMsg.getProductSubTypeName();//某厂商提供的剂型
        } else {
            LogUtils.e(AddMedicineActivity.class, "mFormDetailMsg 为空，无法获取厂商信息");
            drugType = "";
            drugProviderId = "";
            dosageForm = "";
        }

        if (formListBean != null) {
            drugForm = formListBean.getDrugFormName();//剂型(外层剂型，蜜丸、水丸......)
        } else {
            LogUtils.e(AddMedicineActivity.class, "formListBean 为空，无法获取剂型信息");
            // 尝试从 mFormDetailMsg 获取剂型信息作为备用
            drugForm = dosageForm;
        }
        tag_drugForm = drugForm;

        // 添加对 mFormDetailMsg 的空值检查，防止空指针异常
        if (mFormDetailMsg != null) {
            supplierName = mFormDetailMsg.getName();//厂商名称
            costDesc = mFormDetailMsg.getCostDesc();//制作描述
            standard = mFormDetailMsg.getStandard();//药材标准
            standardDesc = mFormDetailMsg.getStandardDesc();//标准描述
        } else {
            LogUtils.e(AddMedicineActivity.class, "mFormDetailMsg 为空，使用默认厂商信息");
            supplierName = "未知厂商";
            costDesc = "";
            standard = "";
            standardDesc = "";
        }

        if (TextUtils.isEmpty(getIntent().getStringExtra(PublicParams.IS_PREGNANT))) {
            isPregnant = "0";
        } else {
            isPregnant = getIntent().getStringExtra(PublicParams.IS_PREGNANT);//是否怀孕
        }

        patientId = SharedPreferenceUtils.getString(mContext, PublicParams.PATIENT_USRE_ID);

        //sharePreferce存储剂型、厂商等信息
        String selfSupport = "";
        if (mFormDetailMsg != null) {
            selfSupport = mFormDetailMsg.getSelfSupport();
        }
        saveDrugType(this, drugType, drugProviderId, drugForm, selfSupport);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    /**
     * 初始化数据
     */
    private void initViewData() {
        EventBusUtils.register(this);
        orderMsgBean = new OrderMsgBean();

        // 添加空值检查，防止空指针异常
        if (formListBean != null) {
            setDosageForm(formListBean.getDrugFormName());
        } else {
            // 如果 formListBean 为空，尝试从 mFormDetailMsg 获取剂型信息
            if (mFormDetailMsg != null && !TextUtils.isEmpty(mFormDetailMsg.getProductSubTypeName())) {
                setDosageForm(mFormDetailMsg.getProductSubTypeName());
            } else {
                // 如果都为空，设置默认值并记录错误
                LogUtils.e(AddMedicineActivity.class, "formListBean 和 mFormDetailMsg 都为空，无法获取剂型信息");
                setDosageForm("未知剂型");
            }
        }

        mSupplierName.setText("(" + supplierName + ")");
        mBottomPopWindow = new BottomPopWindow(this);
        takingWayPopItems = new ArrayList<>();
        boilPopItems = new ArrayList<>();
        mMedicineList = new ArrayList<>();
        mMedicineOrTemples = new ArrayList<>();
        mMedicineIdList = new ArrayList<>();//存放已添加的药材的id，用于去重操作
        doseMap = new HashMap<>();//存放剂量， key是药材id，value是剂量
        useMethodMap = new HashMap<>();//存放特殊煎法，key 是药材id，value 是特殊煎法
        mDosageFormList = new ArrayList<>();
        userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        getBoilWay();
        //getDosageFormList();
        mMedicineHLvAdapter = new MedicineHLvAdapter(this, mMedicineOrTemples, R.layout.medicine_hlv_item_layout, MedicineHLvAdapter.TYPE_ADD_MEDICINE);
        horizontalLv.setAdapter(mMedicineHLvAdapter);
        horizontalLv.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int i, long id) {
                if (i >= 0 && i < mMedicineOrTemples.size()) {
                    String type = mMedicineOrTemples.get(i).getType();
                    String ids = mMedicineOrTemples.get(i).getId();
                    requestMedicineOrTemplateMsg(ids, type, isClick);
                }

            }
        });
        horizontalLv.setOnItemLongClickListener(new AdapterView.OnItemLongClickListener() {
            @Override
            public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
                getTemplateList(mMedicineOrTemples.get(position));
                return true;
            }
        });

        smartInputTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                //重置编辑模式
                resetToNormalMode();

                LogUtils.i(AddMedicineActivity.class, "smartInputTv clicked ==");
                Intent intent = new Intent(mContext, BRIntelligentEntryActivity.class);
//                startActivity(intent);
                // 添加必要参数
                intent.putExtra("drugType", drugType);
                intent.putExtra("drugForm", drugForm);
                intent.putExtra("drugProviderId", drugProviderId);
                launcher.launch(intent);
            }
        });


        tvMedicineNum.setText(createSpannable("/共", "0", "味"));
//监控输入框输入内容 并进行检索药材
        addInputEtTextChangedListener();
    }

    /**
     * 设置标题drugform
     */
    private void setDosageForm(String drugForm) {
        mDosageForm.setText(drugForm);

        if (PublicParams.DOSAGEFORM_GRANULE.equals(drugForm) ||
        PublicParams.DOSAGEFORM_SLICES.equals(drugForm) ||
        PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm) ||
        PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(drugForm) ||
        PublicParams.DOSAGEFORM_WATERED_PILL.equals(drugForm) ||
        PublicParams.DOSAGEFORM_HONEYED_PILL.equals(drugForm) ||
        PublicParams.DOSAGEFORM_POWDER.equals(drugForm) ||
        PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(drugForm)) {
            ivTopMark.setVisibility(View.VISIBLE);
        }else {
            ivTopMark.setVisibility(View.GONE);
        }
    }

    /**
     * 恢复到正常编辑模式
     */
    private void resetToNormalMode() {
        // 重置编辑模式
        mEditMode = EditMode.NORMAL;
        mEditIndex = -1;

        // 清除UI状态
        if (mEditGradientView != null) {
            mEditGradientView.setVisibility(View.GONE);
            mEditGradientView = null;
        }

        // 关闭弹窗(如果存在)
        if (mBottomPopWindow != null && mBottomPopWindow.isShowing()) {
            mBottomPopWindow.dimissPop();
        }
    }

    /**
     * 监控输入框输入内容 并进行检索药材
     */
    private void addInputEtTextChangedListener() {
        mInputEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String input = s.toString().trim();

                // 检查输入是否为空
                if (!TextUtils.isEmpty(input)) {
                    // 如果输入只有一个字符并且是英文字符，则清空数据，不进行检索
                    if (input.length() == 1 && input.matches("[a-zA-Z]")) {
                        clearData();  // 清空数据的方法
                        return;
                    }

                    // 如果输入的第一个字符是中文，或者输入长度超过1个英文字符，则进行检索
                    if (input.length() > 1 || input.substring(0, 1).matches("[\\u4e00-\\u9fa5]")) {
                        // addMedicineType = ADDMEDICINETYPE_ADD;
                        NettyUtils.searchMedicines(input, drugType, drugForm, drugProviderId);
                    }
                } else {
                    // 输入为空时清空数据
                    clearData();
                }
            }

            // 清空数据的方法
            private void clearData() {
                rlHorizontalLayout.setVisibility(View.GONE);
                mMedicineOrTemples.clear();
                mMedicineHLvAdapter.updateData(mMedicineOrTemples);
                mUpSlideMedicines.removeAllViews();
            }
        });
    }

    /**
     * @param id   药材或者药方的id
     * @param type 药材或药方 1.药材，2.常用方，3.经典方
     */
    private void requestMedicineOrTemplateMsg(String id, String type, String taskId) {
        if (!TextUtils.isEmpty(type) && ("2".equals(type) || "3".equals(type))) {//2.常用方 3、经典方
            NettyUtils.getTemplateDetailMsg(id, type, taskId, drugType, drugProviderId, drugForm);
        } else if (!TextUtils.isEmpty(type) && "1".equals(type)) {//药材
            NettyUtils.getMedicineDetailMsg(drugType, id, drugProviderId, drugForm);
        }
    }


    /**
     * 获取药材煎法列表数据
     */
    private void getBoilWay() {
        getBoilWayCallBack = addHttpPostRequest(HttpUrlManager.BOIL_WAY, null, BoilWayBean.class, this);
    }

    /**
     * 获取选则剂型列表
     */
    private void getDosageFormList() {
        //获取请求的参数-- id
        StringBuilder idBuilder = new StringBuilder();
        for (int i = 0; mMedicineList != null && mMedicineList.size() > 0 && i < mMedicineList.size(); i++) {
            idBuilder.append(mMedicineList.get(i).getDrugId() + ",");
        }
        String idStr = idBuilder.toString();
        Map<String, String> params = new HashMap<>();
        if (!idStr.isEmpty()) {
            params.put("drugIds", idStr);
        }
        params.put("apiVer", "2");
        getDosageFormListCallBack = addHttpPostRequest(HttpUrlManager.DOSAGE_FORM_LIST, params, DosageFormBean.class, this);
    }

    /**
     * 判断选取的厂商id是否在最新的厂商列表中，如不不存在，则从列表中选取第一个。存在则保持。
     */
    private void checkProviderId() {
        if (mDosageFormList != null && !mDosageFormList.isEmpty()) {
            boolean isProviderIdExist = false;

            // 只遍历当前剂型下的供应商列表
            for (DosageFormBean.FormList formList : mDosageFormList) {
                if (formList.getDrugFormName().equals(drugForm)) {
                    if (formList.getList() != null) {
                        for (DosageFormBean.FormList.FormDetailMsg provider : formList.getList()) {
                            if (provider.getId().equals(drugProviderId)) {
                                isProviderIdExist = true;
                                break;
                            }
                        }
                    }

                    // 如果当前供应商ID不存在,则使用此剂型的第一个供应商
                    if (!isProviderIdExist && formList.getList() != null
                            && !formList.getList().isEmpty()) {
                        drugProviderId = formList.getList().get(0).getId();

                        // 获取第一个剂型的第一个供应商信息
                        DosageFormBean.FormList.FormDetailMsg firstProvider = formList.getList().get(0);

                        // 更新 FormDetailMsg 对象
                        mFormDetailMsg = formList.getList().get(0);
                        formListBean = formList;

                        // 更新供应商相关信息
                        drugProviderId = firstProvider.getId();
                        drugType = firstProvider.getProductSubType();
                        dosageForm = firstProvider.getProductSubTypeName();
                        supplierName = firstProvider.getName();
                        costDesc = firstProvider.getCostDesc();
                        standard = firstProvider.getStandard();
                        standardDesc = firstProvider.getStandardDesc();

                        // 更新位置信息
                        leftPosition = 0;
                        rightPosition = mFormDetailMsg.getId();

                        // 更新界面显示
                        setDosageForm(formList.getDrugFormName());
                        mSupplierName.setText("(" + supplierName + ")");

                        showHintView();

                        // 保存到 SharedPreference
                        String selfSupport = "";
                        if (mFormDetailMsg != null) {
                            selfSupport = mFormDetailMsg.getSelfSupport();
                        }
                        saveDrugType(mContext, drugType, drugProviderId, drugForm, selfSupport);
                    }
                    break;
                }
            }
        }
    }

    /**
     * @param medicines      药材列表
     * @param medicinesTable 药材容器
     *                       添加药材显示view
     */
    @SuppressLint("SetTextI18n")
    private void addMedicineView(final List<MedicineDetailMsgBean> medicines, final TableLayout medicinesTable, String addMedicineType) {
        if (isChangedDrugForm) { //判断药材剂型是否改变了，若改变后之前添加的药材列表先清空
            mMedicineList.clear();
            mMedicinesTable.removeAllViews();
            mMedicineIdList.clear();
            isChangedDrugForm = false;
        }
        for (int i = 0; i < medicines.size(); i++) {
//            final int localIndex = i + mMedicineList.size();
            final View view = LayoutInflater.from(this).inflate(R.layout.medicine_gv_item_layout, null);
            ImageView delete_img = ((ImageView) view.findViewById(R.id.delete_img));
            TextView name_tv = ((TextView) view.findViewById(R.id.name_tv));
            TextView unit_tv = ((TextView) view.findViewById(R.id.unit_tv));
            TextView understock_tv = ((TextView) view.findViewById(R.id.understock_tv));//库存暂缺
            TextView boil_medicine_tv = ((TextView) view.findViewById(R.id.boil_medicine_tv));//煎药备注
            TextView price_tv = ((TextView) view.findViewById(R.id.price_tv));
            BRGradientView gradientView = ((BRGradientView) view.findViewById(R.id.gradient_view));
            final EditText doseEt = ((EditText) view.findViewById(R.id.dose_et));
            final MedicineDetailMsgBean medicine = medicines.get(i);

            //显示药材名称及单位
            name_tv.setText(TextUtils.isEmpty(medicine.getDrugName()) ? "" : medicine.getDrugName());//药材名称
            unit_tv.setText(TextUtils.isEmpty(medicine.getUnit()) ? "g" : medicine.getUnit());//药材单位

            // 在添加view到medicinesTable之前,应用当前的字体缩放
            if (mCurrentFontSizeScale != 1.0f) {
//                TextView nameTv = view.findViewById(R.id.name_tv);
//                TextView unitTv = view.findViewById(R.id.unit_tv);
//                TextView understockTv = view.findViewById(R.id.understock_tv);
//                TextView boilMedicineTv = view.findViewById(R.id.boil_medicine_tv);
//                TextView priceTv = view.findViewById(R.id.price_tv);

                // 应用保存的字体缩放
                if (name_tv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_name_text_size);
                    name_tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * mCurrentFontSizeScale);
                }
                if (unit_tv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_unit_text_size);
                    unit_tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * mCurrentFontSizeScale);
                }
                if (understock_tv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_understock_text_size);
                    understock_tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * mCurrentFontSizeScale);
                }
                if (boil_medicine_tv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_boil_text_size);
                    boil_medicine_tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * mCurrentFontSizeScale);
                }
                if (price_tv != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_price_text_size);
                    price_tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * mCurrentFontSizeScale);
                }
                if (doseEt != null) {
                    float originalSize = getResources().getDimension(R.dimen.medicine_dose_text_size);
                    doseEt.setTextSize(TypedValue.COMPLEX_UNIT_PX, originalSize * mCurrentFontSizeScale);
                }
            }


            // 实时计算并更新药价
            doseEt.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    if (isIterateChangeDose){
                        return;
                    }
                    updateMedicinePrice(medicine, doseEt, price_tv);
                }
            });

            //显示库存信息
            showStockMsg(addMedicineType, medicine.getStock(), i, understock_tv, boil_medicine_tv, medicine);

            //添加某味药材逻辑处理
            addMedicineItem(medicinesTable, addMedicineType, view, doseEt, medicine, medicine.getDrugId());

            //点击药材名称，选择特殊煎法
            onNameClick(view, name_tv, boil_medicine_tv, medicine, isExit);

            //删除此药
            delete_img.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    deleteMedicineAt(view);
                }
            });



            //view添加长按响应
            view.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
//                    ToastUtils.showShortMsg(mContext, "长按编辑药材index = " + (int)v.getTag());
                    //显示弹窗，编辑方式修改
                    showEditModePop((int)v.getTag(), gradientView);
                    return true;
                }
            });

            //处理修改了剂量，没有点击数字键盘上的确认按钮时剂型量的更新及提示
            updateDoseOnNoFocus(/*doseEt, medicine*/);
            //实时计算药价
            countPriceOnRealTime(doseEt);

        }
        //选择模版、粘贴药材、切换剂型、继续编辑药材进入此界面后剂量改变的提示显示
        if (doseIsChanged) {
            ToastUtils.showShortMsg(mContext, "部分药材已自动调整剂量");
            doseIsChanged = false;
        }
        //添加完药材后恢复添加药材状态
        isChangedDrugForm = false;
        isPaste = false;
        isCache = false;
        //选择模板、粘贴药材、切换剂型、继续编辑药材进入此界面添加完药材后弹出输入框及键盘、计算单付药价
        if (!ADDMEDICINETYPE_ADD.equals(addMedicineType)) {
            startThread();//启动计算单付药价的线程
            if (numKeyboard != null) {
                if (!numKeyboard.isShown()) {
                    showInputKeyBoard();
                }
            }
        }
        //显示共几味药
        tvMedicineNum.setText(createSpannable("/共", mMedicineList.size() + "", "味"));
        //选择模板、粘贴药材、切换剂型、继续编辑药材进入此界面添加完药材后滑动到最底部
        scrollToBottom(medicineScrollview, scrollInnerView);
        //添加完药材后清空搜索框
        mInputEt.setText("");
        //已填加药材后清空按钮变蓝色并可点击
        tvClear.setTextColor(getResources().getColor(R.color.br_color_theme));
        tvClear.setClickable(true);
    }

    // 新增方法：更新药材价格
    @SuppressLint({"SetTextI18n", "DefaultLocale"})
    private void updateMedicinePrice(MedicineDetailMsgBean medicine, EditText doseEt, TextView price_tv) {
        String doseStr = doseEt.getText().toString();
        if (!TextUtils.isEmpty(doseStr) && !TextUtils.isEmpty(medicine.getPrice())) {
            try {
                BigDecimal dose = new BigDecimal(doseStr);
                BigDecimal unitPrice = new BigDecimal(medicine.getPrice());
                BigDecimal totalPrice = dose.multiply(unitPrice).setScale(3, RoundingMode.HALF_UP);
                price_tv.setText(String.format("¥%.3f", totalPrice));
            } catch (NumberFormatException e) {
                // 处理输入无效的情况
                price_tv.setText("¥0.00");
            }
        } else {
            price_tv.setText("¥0.00");
        }
    }

    /**
     * 实时计算药价
     *
     * @param doseEt
     */
    private void countPriceOnRealTime(final EditText doseEt) {
        doseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (isIterateChangeDose) {
                    return;
                }
                upDateMedicineDose(doseEt);//更新药材剂量
                //启动实时计算药价的线程计算单付药材价格
                startThread();
                if (getCurrentFocus() != null && getCurrentFocus() != doseEt) {
                    getCurrentFocus().setFocusable(false);
                }
                doseEt.setFocusable(true);
                doseEt.requestFocus();
            }
        });
    }

    /**
     * 剂量框失去焦点时更新药材计量
     */
    private void updateDoseOnNoFocus(/*final EditText doseEt1, final MedicineDetailMsgBean medicine*/) {
        numKeyboard.setOnViewFocusChangeListener(new NumberKeyboardView.OnViewFocusChangeListener() {
            @Override
            public boolean onViewFocusChange(View view, boolean hasFocus) {
                EditText doseEt = (EditText) view;
                MedicineDetailMsgBean medicine = (MedicineDetailMsgBean) doseEt.getTag();
                if (!hasFocus) {
                    if (PublicParams.DOSAGEFORM_GRANULE.equals(dosageForm)) {//此药方剂型为颗粒或用颗粒制作的其他剂型
                        String inputDose = TextUtils.isEmpty(doseEt.getText().toString().trim()) ? "0" : doseEt.getText().toString().trim();
                        String minSaleUnit = TextUtils.isEmpty(medicine.getMinSaleUnit()) ? "0" : medicine.getMinSaleUnit();
                        String rem = (DecimalUtils.rem(inputDose, minSaleUnit));
                        if (TextUtils.isEmpty(rem) || !rem.equals("0.00")) {//输入剂量是规格的整数倍
                            doseEt.setText("0"/*TextUtils.isEmpty(medicine.getMinSaleUnit()) ? "0" : medicine.getMinSaleUnit()*/);
                            doseEt.setSelection(doseEt.getText().toString().length());
                            return false;
                        }
                    }
                }
                return true;
            }
        });
    }

    /**
     * 计算单付药价:向上保留两位小数
     *
     * @param medicines
     */
    private String countMedicinePrice(List<MedicineDetailMsgBean> medicines) {
        String price = "0.00";
        if (medicines.size() > 0) {
            for (MedicineDetailMsgBean drug : medicines) {
                price = DecimalUtils.adding(price, DecimalUtils.multiply(drug.getPrice(), drug.getDose()));
                LogUtils.i(AddMedicineActivity.class, "medicineName" + drug.getDrugName() + "     dose==" + drug.getDose() + "\nsalePrice======" + drug.getPrice() + "\n 药价综合 price======" + price);
            }
            price = DecimalUtils.format(price, 3);
            LogUtils.i(AddMedicineActivity.class, "药价综合向上取整2位结果==" + price);
        }
//        if (!isShowMedicalServiceFee) {
//        price = DecimalUtils.division(price, "0.7");
        LogUtils.i(AddMedicineActivity.class, "标准药费====" + price);
//        }
        return DecimalUtils.format(price, 3);
    }

    /**
     * 添加某味药材逻辑处理
     *
     * @param medicinesTable  显示药材的容器
     * @param addMedicineType 添加药材的类型
     * @param view            显示药材的item
     * @param doseEt          剂量显示view
     * @param medicine        要添加的药材
     * @param medicineId      药材id
     */
    private void addMedicineItem(TableLayout medicinesTable, String addMedicineType, View view, EditText doseEt, MedicineDetailMsgBean medicine, String medicineId) {
        if (mMedicineIdList.contains(medicineId)) {//去重操作，若是调模板，粘贴药方直接过滤
            if (ADDMEDICINETYPE_ADD.equals(addMedicineType)) {//是药材的时候提示，
                ToastUtils.showShortMsg(mContext, "“" + medicine.getDrugName() + "”已存在，无需重复添加");
            }
        } else {
            if (ADDMEDICINETYPE_ADD.equals(addMedicineType)) {//添加药材
                useMethodMap.put(medicine.getDrugId(), medicine.getUseMethod());
                hideKeyBoard(mInputEt);
                mBottomRl.setVisibility(View.GONE);//隐藏搜药输入框及药材横向显示条
                rlPasteClear.setVisibility(View.GONE);//隐藏清空、粘贴按钮
                numKeyboard.bindEditText(doseEt, getPointScale(medicine));//绑定数字键盘
                doseEt.setTag(medicine);
                showDoseHintView(medicine, doseEt);
            } else {//选择剂型或者添加药方中的药
                doseEt.setTag(medicine);
                numKeyboard.bindEditText(doseEt, getPointScale(medicine), false);//绑定数字键盘
                numKeyboard.hideKeyboard();
            }
//            mMedicineList.add(medicine);
//            mMedicineIdList.add(medicineId);
//            medicinesTable.addView(view);


            // 根据编辑模式插入药品
            switch (mEditMode) {
                case NORMAL:
                    // 正常模式，添加到末尾
                    mMedicineList.add(medicine);
                    mMedicineIdList.add(medicine.getDrugId());
                    medicinesTable.addView(view);
                    break;
                case FORWARD:
                    // 向前插入，插入到 mEditIndex 之前
                    if (mEditIndex >= 0 && mEditIndex < mMedicineList.size()) {
                        mMedicineList.add(mEditIndex, medicine);
                        mMedicineIdList.add(mEditIndex, medicine.getDrugId());
                        medicinesTable.addView(view, mEditIndex);
                    } else {
                        // 如果索引不合法，则默认添加到末尾
                        mMedicineList.add(medicine);
                        mMedicineIdList.add(medicine.getDrugId());
                        medicinesTable.addView(view);
                    }
                    break;
                case BACKWARD:
                    // 向后插入，插入到 mEditIndex 之后
                    if (mEditIndex >= 0 && mEditIndex < mMedicineList.size()) {
                        mMedicineList.add(mEditIndex + 1, medicine);
                        mMedicineIdList.add(mEditIndex + 1, medicine.getDrugId());
                        medicinesTable.addView(view, mEditIndex + 1);
                    } else {
                        // 如果索引不合法，则默认添加到末尾
                        mMedicineList.add(medicine);
                        mMedicineIdList.add(medicine.getDrugId());
                        medicinesTable.addView(view);
                    }
                    break;
                case REPLACE:
                    // 替换模式，替换 mEditIndex 位置的药品
                    if (mEditIndex >= 0 && mEditIndex < mMedicineList.size()) {
                        mMedicineList.set(mEditIndex, medicine);
                        mMedicineIdList.set(mEditIndex, medicine.getDrugId());
                        medicinesTable.removeViewAt(mEditIndex);
                        medicinesTable.addView(view, mEditIndex);
                    } else {
                        // 如果索引不合法，则默认添加到末尾
                        mMedicineList.add(medicine);
                        mMedicineIdList.add(medicine.getDrugId());
                        medicinesTable.addView(view);
                    }
                    break;
            }

            //更新为正常编辑模式
//            mEditMode = EditMode.NORMAL;
//            mEditIndex = -1;
//            if (mEditGradientView != null) {
//                mEditGradientView.setVisibility(View.GONE);
//                mEditGradientView = null;
//            }

            resetToNormalMode();

            //剂量显示
            setMedicineDose(medicine, addMedicineType, doseEt, medicine.getDrugId());

            doseMap.put(medicineId, doseEt.getText().toString());
        }
    }

    /**
     * 显示库存信息
     *
     * @param addMedicineType  添加药材类型
     * @param stock            库存
     * @param i
     * @param understock_tv    “库存暂缺”显示
     * @param boil_medicine_tv “特殊煎药方式”显示
     * @param medicine         要添加的药材
     * @return
     */
    private void showStockMsg(String addMedicineType, String stock, int i, TextView understock_tv, TextView boil_medicine_tv, MedicineDetailMsgBean medicine) {
        if ("1".equals(stock)) {//有库存
            isExit = true;
            understock_tv.setVisibility(View.GONE);

            // 添加空值检查，防止空指针异常
            String currentDrugForm = "";
            if (formListBean != null) {
                currentDrugForm = formListBean.getDrugFormName();
            } else if (!TextUtils.isEmpty(drugForm)) {
                currentDrugForm = drugForm;
            }

            if (PublicParams.DOSAGEFORM_SLICES.equals(currentDrugForm) ||
                    PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(currentDrugForm)) {//剂型为饮片的时候可以选择特殊煎药法
                showSpecialUseMethod(addMedicineType, i, boil_medicine_tv, medicine);
            } else {
                boil_medicine_tv.setVisibility(View.GONE);
                boil_medicine_tv.setText("");
            }
        } else if (!TextUtils.isEmpty(stock) && "0".equals(stock)) {
            //没有库存的时候没必要显示特殊煎药法
            isExit = false;
            understock_tv.setVisibility(View.VISIBLE);
            understock_tv.setText("库存暂缺，长按替换");
            boil_medicine_tv.setVisibility(View.GONE);
        }
    }

    /**
     * @param addMedicineType  添加药材的类型
     * @param i                该药材在添加的集合中的位置
     * @param boil_medicine_tv 显示药材特殊剪药方法的view
     * @param medicine         要添加的药材
     */
    private void showSpecialUseMethod(String addMedicineType, int i, TextView boil_medicine_tv, MedicineDetailMsgBean medicine) {
        if (addMedicineType.equals(ADDMEDICINETYPE_ADD)) {//添加药材
            if (!TextUtils.isEmpty(medicine.getUseMethod())) {//特殊煎药法不为空显示特殊煎药法
                boil_medicine_tv.setVisibility(View.VISIBLE);
                boil_medicine_tv.setText(medicine.getUseMethod());
            } else {
                medicine.setUseMethod("");
                boil_medicine_tv.setVisibility(View.GONE);
                boil_medicine_tv.setText("");
            }
        } else {//粘贴复制的药材、切换剂型、选择模板时的特殊药法处理
            if (!TextUtils.isEmpty(useMethodMap.get(medicine.getDrugId()))) {//该药材存在本地的特殊煎药法不为空（用于切换剂型时）
                medicine.setUseMethod(useMethodMap.get(medicine.getDrugId()));
                boil_medicine_tv.setVisibility(View.VISIBLE);
                boil_medicine_tv.setText(useMethodMap.get(medicine.getDrugId()));
            } else {
                medicine.setUseMethod("");
                boil_medicine_tv.setVisibility(View.GONE);
                boil_medicine_tv.setText("");
            }
        }
    }

    /**
     * 滑到已填加药材底部
     *
     * @param scroll
     * @param inner
     */
    public void scrollToBottom(final View scroll, final View inner) {
        Handler mHandler = new Handler();
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (scroll == null || inner == null) {
                    return;
                }
                int offset = inner.getMeasuredHeight() - scroll.getHeight();
                if (offset < 0) {
                    offset = 0;
                }

                scroll.scrollTo(0, offset);
            }
        }, 500);
    }

    /**
     * 显示药材剂量
     *
     * @param medicine
     * @param addMedicineType
     * @param doseEt
     * @param medicineId
     */
    private void setMedicineDose(MedicineDetailMsgBean medicine, String addMedicineType, EditText doseEt, String medicineId) {
        String oldDose = "";
        if (ADDMEDICINETYPE_ADD.equals(addMedicineType)) {//药材的时候
            doseEt.setText("0");//剂量默认显示0g
            doseEt.setSelection(0, doseEt.getText().length());
        } else if (ADDMEDICINETYPE_CHANGE.equals(addMedicineType) || ADDMEDICINETYPE_PASTE.equals(addMedicineType) || ADDMEDICINETYPE_CACHE.equals(addMedicineType)) {//修改剂型,粘贴复制的药材
            if (doseMap.containsKey(medicineId)) {
                oldDose = doseMap.get(medicineId);//切换剂型前显示的剂量、复制的药材计量
            } else {
                oldDose = "0";
            }
            if (!TextUtils.isEmpty(medicine.getStock()) && "1".equals(medicine.getStock())) {//有库存，走剂量调整的逻辑
                doseEt.setText(doseFormat(oldDose, medicine.getMinSaleUnit()));
            } else {//没有库存是显示0,考虑到特殊单位的药材
                doseEt.setText("0");
            }
            String inputDose = doseEt.getText().toString().trim();
            if (!oldDose.equals(inputDose)) {//剂量有改变
                doseIsChanged = true;
            }
        } else if (ADDMEDICINETYPE_TEMPLATE.equals(addMedicineType)) {//选择模板
            oldDose = medicine.getDose();
            if (!TextUtils.isEmpty(medicine.getStock()) && "1".equals(medicine.getStock())) {//有库存，走剂量调整的逻辑
                doseEt.setText(doseFormat(oldDose, medicine.getMinSaleUnit()));
            } else {//没有库存是显示0，,考虑到特殊单位的药材
                doseEt.setText("0");
            }
            if (!oldDose.equals(doseEt.getText().toString().trim())) {//剂量有改变
                doseIsChanged = true;
            }
        }
        medicine.setDose(doseEt.getText().toString().trim());
    }

    /**
     * 剂量转换规则：剂量/最小销售单位=（1、整数：剂量不变 2、大于1的非整数：向上取整最小销售单位的倍数 3、小于1：显示默认最小销售单位）
     *
     * @param oldDose     原有的剂量
     * @param minSaleUnit 最小销售单位
     */
    private String doseFormat(String oldDose, String minSaleUnit) {
        int mul = 0;//倍数
        String rem;//余数
        String strDose = "";
        String dose;//需要的剂量
        if (TextUtils.isEmpty(minSaleUnit)) {
            minSaleUnit = "1";//从0.01改为1为了避免出现特殊单位药材不使用的情况，比如0.01袋、0.01瓶这样
        }
        if (TextUtils.isEmpty(oldDose)) {
            oldDose = "0";
        }
        float oldDoseF = Float.parseFloat(oldDose);
        float minSaleUnitF = Float.parseFloat(minSaleUnit);
        mul = (int) (oldDoseF / minSaleUnitF);
        rem = DecimalUtils.rem(oldDose, minSaleUnit);
        if (mul < 1) {//倍数<1，不足规格，默认显示规格
            strDose = minSaleUnit;
//            return minSaleUnit;
        } else {
            if ("0.00".equals(rem)) {//整数倍的，计量不变
                strDose = oldDose;
            } else {
                dose = DecimalUtils.mul(minSaleUnit, String.valueOf(mul + 1));
                strDose = DecimalUtils.format(dose, DecimalUtils.getScale(dose));
//                return DecimalUtils.format(dose, DecimalUtils.getScale(dose));
            }
        }
        return strDose;
    }

    /**
     * @param medicine
     * @return 小数位数
     */
    public int getPointScale(MedicineDetailMsgBean medicine) {
        int scale = 0;//默认0位小数
        if (medicine != null && !TextUtils.isEmpty(medicine.getMinSaleUnit())) {
            scale = DecimalUtils.getScale(medicine.getMinSaleUnit());
        }
        return scale;
    }

    /**
     * 显示剂量提示view
     *
     * @param medicineMsg
     * @param dose_et
     */
    private void showDoseHintView(MedicineDetailMsgBean medicineMsg, View dose_et) {
        if (medicineMsg == null) {
            return;
        }
        //最小销售单位，即规格
        String minSaleUnit = TextUtils.isEmpty(medicineMsg.getMinSaleUnit()) ? "0" : medicineMsg.getMinSaleUnit();
        String demicalStandard = "";//格式化后相应倍数的规格
        setInputViewVisible(false);//药材输入框布局及键盘隐藏
        SpecialUnitBean specialUnitBean = medicineMsg.getSpecialUnit();
        if (PublicParams.DOSAGEFORM_SLICES.equals(dosageForm) ||
                PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm)) {//药方剂型为饮片或用饮片制作的其他剂型
            if (specialUnitBean != null && !TextUtils.isEmpty(specialUnitBean.getRemark())) {
//                if (!TextUtils.isEmpty(specialUnitBean.getUnit()) && !TextUtils.isEmpty(specialUnitBean.getDose())) {
                mDoseHintLl.setVisibility(View.VISIBLE);//提示的布局
                mSpecialUnitTv.setVisibility(View.VISIBLE);//提示文字
                mDoseContainer.setVisibility(View.GONE);//剂量提示容器
//                if (!TextUtils.isEmpty(specialUnitBean.getRemark())) {
                mSpecialUnitTv.setText(medicineMsg.getDrugName() + ": " + specialUnitBean.getRemark());
//                } else {
//                    mSpecialUnitTv.setText(medicineMsg.getDrugName() + ": " + specialUnitBean.getDose() + "约" + specialUnitBean.getUnit());
//                }
//                } else {
//                    mDoseHintLl.setVisibility(View.GONE);//提示的布局
//                }
            } else {
                mDoseHintLl.setVisibility(View.GONE);//提示的布局
            }
        } else if (PublicParams.DOSAGEFORM_GRANULE.equals(dosageForm)) {//此药方剂型为颗粒或用颗粒制作的其他剂型
            String unit = TextUtils.isEmpty(medicineMsg.getUnit()) ? "g" : medicineMsg.getUnit();
            String standardStr = minSaleUnit + unit;
            List<String> standards = new ArrayList<>();
//            if (TextUtils.isEmpty(minSaleUnit) || "0".equals(minSaleUnit) || Double.parseDouble(minSaleUnit) < 1) {
            if (TextUtils.isEmpty(minSaleUnit) || "0".equals(minSaleUnit) || "1".equals(minSaleUnit) || "0.1".equals(minSaleUnit) || "0.01".equals(minSaleUnit)) {
                mDoseContainer.removeAllViews();
                mDoseHintLl.setVisibility(View.GONE);//提示的布局
                mSpecialUnitTv.setVisibility(View.GONE);//提示文字
                mDoseContainer.setVisibility(View.GONE);//剂量提示容器
            } else {
                mDoseContainer.removeAllViews();
                mDoseHintLl.setVisibility(View.VISIBLE);//提示的布局
                mSpecialUnitTv.setVisibility(View.VISIBLE);//提示文字
                mDoseContainer.setVisibility(View.VISIBLE);//剂量提示容器
                mSpecialUnitTv.setText(medicineMsg.getDrugName() + ":包装规格为" + standardStr + ",剂量请输入" + standardStr + "的倍数！");
                for (int i = 1; i <= 5; i++) {
                    demicalStandard = DecimalUtils.mul(minSaleUnit, i + "");
                    standards.add(DecimalUtils.format(demicalStandard, DecimalUtils.getScale(minSaleUnit)) + unit);
                }
                addHintDose(standards, mDoseContainer, dose_et, unit);
            }

        } else {//此药方剂型为饮片或用饮片制作的其他剂型，药材为普通药材
            mDoseHintLl.setVisibility(View.GONE);//不显示剂量提示
        }

    }

    /**
     * @param standards     剂量list
     * @param doseContainer 显示剂量的布局
     */
    private void addHintDose(final List<String> standards, LinearLayout doseContainer, final View editText, final String unit) {
        if (standards.size() > 0) {
            for (int i = 0; i < standards.size(); i++) {
                TextView tv = getDoseTextView(standards.get(i));
                doseContainer.addView(tv);

                final int finalI = i;
                tv.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ((EditText) editText).setText(MedicineUnit.removeUnit(standards.get(finalI), unit));
                        setDoseHintViewVisible(false);
                        setInputViewVisible(true);

                        mInputEt.setText("");
                        MedicineDetailMsgBean drug = (MedicineDetailMsgBean) editText.getTag();
//                        NettyUtils.getAssociativeMedicine(drug.getDrugId());
                        NettyUtils.getAssociativeMedicine(getAssociativeDrugId(), drugType, drugForm, drugProviderId);

                    }
                });
            }
        }
    }

    /**
     * 点击药材名称选择特殊煎法
     *
     * @param name_tv  显示药材名称view
     * @param medicine 该药材
     */
    private void onNameClick(View view,View name_tv, final View boil_tv, final MedicineDetailMsgBean medicine, boolean isExit) {
        // 添加空值检查，防止空指针异常
        String currentDrugForm = "";
        if (formListBean != null) {
            currentDrugForm = formListBean.getDrugFormName();
        } else if (!TextUtils.isEmpty(drugForm)) {
            currentDrugForm = drugForm;
        }

        if (PublicParams.DOSAGEFORM_SLICES.equals(currentDrugForm) ||
        PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(currentDrugForm)) {//剂型为饮片的时候可以选择特殊煎药法
            if (isExit) {//有库存的时候可点击选择特殊煎药法
                if (name_tv != null) {
//                    name_tv.setOnClickListener(new View.OnClickListener() {
//                        @Override
//                        public void onClick(View v) {
//                            if (boilPopItems != null && boilPopItems.size() > 0) {
//                                showBoilPop(medicine, boil_tv);//显示特殊煎药方式弹框
//                            } else {
//                                getBoilWay();
//                            }
//                        }
//                    });

                    view.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (boilPopItems != null && boilPopItems.size() > 0) {
                                showBoilPop(medicine, boil_tv);//显示特殊煎药方式弹框
                            } else {
                                getBoilWay();
                            }
                        }
                    });
                }
            } else {
                return;
            }

        } else {
            return;
        }
    }

    /**
     * 显示特殊煎药方式弹框
     *
     * @param medicine
     * @param boil_tv
     */
    private void showBoilPop(final MedicineDetailMsgBean medicine, final View boil_tv) {
        hideKeyBoard(mInputEt);
        mBottomPopWindow.setPopTitle(medicine.getDrugName());
        mBottomPopWindow.setPopContentData(boilPopItems);
        
        // 显示问号图标
        mBottomPopWindow.setQuestionIconVisible();
        
        // 设置问号图标点击事件
        mBottomPopWindow.setOnQuestionIconClickListener(new BottomPopWindow.OnQuestionIconClickListener() {
            @Override
            public void onQuestionIconClick() {
                // 显示提示语
                final AlertDialog dialog = AlertDialog.getInstance(mContext);
                dialog.setDialogTitle("特殊煎法说明")
                        .setDialogContent("标有【*】的为系统默认，不可修改")
                        .setPositiveText("知道了")
                        .setOnPositiveBtnClickedListener(new AlertDialog.OnPositiveBtnClickedListener() {
                            @Override
                            public void onPositiveBtnClicked(View view) {
                                dialog.dismiss();
                            }
                        });
                dialog.show();
            }
        });
        
        mBottomPopWindow.showPop();
        mBottomPopWindow.setBottomViewVisible();
        mBottomPopWindow.setBottomText("自定义");
        mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
            @Override
            public void onPopItemClick(int tag, PopItem popItem) {
                upDataUseMethod(popItem.getName(), medicine, boil_tv);
                mBottomPopWindow.dimissPop();
                showInputKeyBoard();
            }
        });

        mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
            @Override
            public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                //自定义特殊煎法
//                showCustomBoilWayDialog();
                Intent intent = new Intent(mContext, CustomBoilyWayActivity.class);
                launcher.launch(intent);
            }
        });
    }

    /**
     * 显示编辑模式修改的弹窗方式
     */
    private void showEditModePop(int position, BRGradientView gradientView) {
        hideKeyBoard(mInputEt);

        //popItem数组
        List<PopItem> items = new ArrayList<>();
        PopItem popItem = new PopItem();
        popItem.setName("向前添加");
        popItem.setPosition(0);
        items.add(popItem);

        popItem = new PopItem();
        popItem.setName("向后添加");
        popItem.setPosition(1);
        items.add(popItem);

        popItem = new PopItem();
        popItem.setName("替换药味");
        popItem.setPosition(2);
        items.add(popItem);

        popItem = new PopItem();
        popItem.setName("正常添加");
        popItem.setPosition(3);
        items.add(popItem);

        mBottomPopWindow.setPopTitle("编辑模式");
        mBottomPopWindow.setPopContentData(items);
        // 长按的编辑模式弹窗不应显示问号图标
        mBottomPopWindow.setQuestionIconGone();
        mBottomPopWindow.setBottomViewGone();
//        mBottomPopWindow.setBottomViewVisible();
//        mBottomPopWindow.setBottomText("正常");
        mBottomPopWindow.showPop();
        mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
            @Override
            public void onPopItemClick(int tag, PopItem popItem) {
                int index = popItem.getPosition();

                mBottomPopWindow.dimissPop();

                if (index == 3){
                    resetToNormalMode();
                } else {
                    //修改编辑模式
                    if (index == 0){
                        mEditMode = EditMode.FORWARD;
                    } else if (index == 1) {
                        mEditMode = EditMode.BACKWARD;
                    } else if (index == 2) {
                        mEditMode = EditMode.REPLACE;
                    }

                    //当前编辑的位置
                    mEditIndex = position;

//                ToastUtils.showShortMsg(mContext, "当前编辑索引index = " + mEditIndex);

                    if (mEditGradientView != null) {
                        mEditGradientView.setVisibility(View.GONE);
                    }
                    mEditGradientView = gradientView;
                    gradientView.setVisibility(View.VISIBLE);
                }
            }
        });

//        mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
//            @Override
//            public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
//                //恢复正常
////                mEditMode = EditMode.NORMAL;
////                mEditIndex = -1;
////                mBottomPopWindow.dimissPop();
////
////                if (mEditGradientView != null) {
////                    mEditGradientView.setVisibility(View.GONE);
////                    mEditGradientView = null;
////                }
////                gradientView.setVisibility(View.GONE);
//
//                resetToNormalMode();
//
//            }
//        });

    }

    /**
     * 自定义特殊煎法
     */
    private void showCustomBoilWayDialog() {
        if (mDialog == null) {
            mDialog = InputDialog.getInstance(AddMedicineActivity.this);
        }
        mDialog.setInputTitle("自定义煎法")
                .setInputHint("请输入煎法(10字以内)")
                .setInputMaxLength(10)
                .show();
        mDialog.setOnButtonClickListener(new OnButtonClickListener() {
            @Override
            public void onPositiveClick(View view, CharSequence editText) {
                if (TextUtils.isEmpty(editText.toString())) {
                    ToastUtils.showShortMsg(mContext, "请输入特殊煎药方式");
                } else {
                    if (!stringFilter(editText.toString())) {
                        ToastUtils.showShortMsg(mContext, "只支持输入汉字、数字");
                    } else {
                        mCustomBoilWay = editText.toString().trim();
                        addBoilWay(editText.toString().trim());
                    }
                }
            }

            @Override
            public void onNegativeClick(View view, CharSequence
                    editText) {
                mDialog.dismiss();
            }
        });
    }

    /**
     * 限制输入框只输入汉字，数字
     *
     * @param str
     * @return
     * @throws PatternSyntaxException
     */
    public boolean stringFilter(String str) throws PatternSyntaxException {
        // 只允许数字和汉字,空格
        String regEx = "[\\d\u4E00-\u9FA5\\s]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.matches();
    }

    /**
     * 更新药材煎药方式
     *
     * @param boilWay
     * @param medicine
     * @param boil_tv
     */
    private void upDataUseMethod(String boilWay, MedicineDetailMsgBean medicine, View boil_tv) {
        if (!TextUtils.isEmpty(boilWay) && !"无*".equals(boilWay)) {
            String saveBoilWay = boilWay;
            
            // 如果是系统煎法（带*号），则去掉*号
            if (boilWay.endsWith("*")) {
                saveBoilWay = boilWay.substring(0, boilWay.length() - 1);
            }

            medicine.setUseMethod(saveBoilWay);//更新已添加的药材中的数据，去掉*
            //更新布局显示，显示不带*的原始文本
            boil_tv.setVisibility(View.VISIBLE);
            ((TextView) boil_tv).setText(saveBoilWay);
            useMethodMap.put(medicine.getDrugId(), saveBoilWay);
        } else {
            medicine.setUseMethod("");//更新已添加的药材中的数据
            //更新布局显示
            boil_tv.setVisibility(View.GONE);
            useMethodMap.remove(medicine.getDrugId());
        }
    }


    /**
     * 删除某味药材
     *
     * @param medicineView
     */
    private void deleteMedicineAt(final View medicineView) {
        if (mConfirmDialog == null) {
            mConfirmDialog = ConfirmDialog.getInstance(this);
        }
        
        // 获取药材名称
        TextView name_tv = medicineView.findViewById(R.id.name_tv);
        String medicineName = "";
        if (name_tv != null && !TextUtils.isEmpty(name_tv.getText())) {
            medicineName = name_tv.getText().toString();
        }
        
        String dialogContent = "是否删除药材【" + medicineName + "】？";
        mConfirmDialog.setDialogContent(dialogContent).setPositiveText("确认").setNavigationText("取消").show();
        mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
            @Override
            public void onNavigationBtnClicked(View view) {
                mConfirmDialog.dismiss();
            }

            @Override
            public void onPositiveBtnClicked(View view) {
                doDeleteMedicine(medicineView);
                scrollToBottom(medicineScrollview, scrollInnerView);

            }
        });
    }

    /**
     * 删除操作
     *
     * @param medicineView
     */
    private void doDeleteMedicine(View medicineView) {
        try {
            int position = mMedicinesTable.indexOfChild(medicineView);
            //删除已添加的某味药材
            mMedicineList.remove(position);
            mMedicineIdList.remove(position);
            mMedicinesTable.removeView(medicineView);
            mInputEt.setText("");

            //重置当前编辑模式
            resetToNormalMode();

            startThread();//启动计算药价的线程
            //显示共几味药
            tvMedicineNum.setText(createSpannable("/共", mMedicineList.size() + "", "味"));
            //数字键盘隐藏，显示搜药键盘
            setDoseHintViewVisible(false);
            setInputViewVisible(true);
            //当药材都已删除后，清空按钮置灰
            if (mMedicineList.size() < 1) {
                tvClear.setTextColor(getResources().getColor(R.color.br_color_et_hint));
                tvClear.setClickable(false);
            }
            mConfirmDialog.dismiss();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param isVisible 是否显示，true 时显示 false时不显示
     *                  设置输入框布局显示与隐藏
     */
    private void setInputViewVisible(boolean isVisible) {
        if (isVisible) {
            if (!mBottomRl.isShown()) {
                mBottomRl.setVisibility(View.VISIBLE);
                rlPasteClear.setVisibility(View.VISIBLE);
            }
            showInputKeyBoard();

        } else {
            if (mBottomRl.isShown()) {
                mBottomRl.setVisibility(View.GONE);
                rlPasteClear.setVisibility(View.GONE);
            }
            hideKeyBoard(mInputEt);
        }

    }

    /**
     * 显示输入键盘，控制输入键盘与数字键盘互斥，解决键盘不弹出问题
     */
    private void showInputKeyBoard() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (numKeyboard != null && !numKeyboard.isShown()) {
                    mInputEt.requestFocus();
                    showKeyBoard(mInputEt);
                }
            }
        }, 300);
    }

    /**
     * 显示输入键盘，解决键盘不弹出问题
     */
    private void showInputKeyBoardDelayMore() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (numKeyboard != null && !numKeyboard.isShown()) {
                    mInputEt.requestFocus();
                    showKeyBoard(mInputEt);
                }
            }
        }, 500);
    }


    /**
     * @param isVisible 是否显示，true 时显示 false时不显示
     *                  设置剂量提醒view的显示与影藏
     */
    private void setDoseHintViewVisible(boolean isVisible) {
        if (isVisible) {
            if (!mDoseHintLl.isShown()) {
                mDoseHintLl.setVisibility(View.VISIBLE);
            }
        } else {
            numKeyboard.hideKeyboard();
            if (mDoseHintLl.isShown()) {
                mDoseHintLl.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 显示提示文字
     */
    private void showHintView() {

        if (PublicParams.DOSAGEFORM_SLICES.equals(drugForm) || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm)) {  //剂型为饮片的时候
            Date date = new Date();
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String currentDateTime = format.format(date);

//            String saveDateTime = SharedPreferenceUtils.getString(this, PublicParams.DATE_TIME);
            String saveDateTime = SharedPreferenceForeverUtils.getString(this, PublicParams.DATE_TIME);
            if (!currentDateTime.equals(saveDateTime)) {
//                SharedPreferenceUtils.putString(this, PublicParams.DATE_TIME, currentDateTime);
                SharedPreferenceForeverUtils.putString(this, PublicParams.DATE_TIME, currentDateTime);
                if (isFirst) {
                    isFirst = false;
                    setRlHintFromVisiablletoInInvisable();
                }
            } else {
                mHintRl.setVisibility(View.GONE);
            }
        } else if (PublicParams.DOSAGEFORM_GRANULE.equals(drugForm)) {  //剂型为颗粒的时候
            showGranuleHintView();
        } else {
            mHintRl.setVisibility(View.GONE);
        }
    }

    public void setRlHintFromVisiablletoInInvisable() {//设置mHintRl，从可见，到5s后不可见
//        mHintRl.setVisibility(View.VISIBLE);
        showSliceHintView();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (mHintRl != null) {
                    mHintRl.setVisibility(View.GONE);
                }
            }
        }, 5000);
    }


    @OnClick({R.id.back_img, R.id.change_taking_way, R.id.save_tv, R.id.colse_hint, R.id.down_arrow,
            R.id.up_arrow, R.id.common_template_tv, R.id.tv_clear, R.id.tv_paste, R.id.btn_know,
            R.id.guide_layout, R.id.up_slide_medicines_rl, R.id.ll_change_dosage, R.id.tv_showSetting,
    R.id.tv_adjustByMultiple, R.id.iv_top_mark})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.back_img:
                doBackLogic();
                break;
            case R.id.change_taking_way://改变剂型
            case R.id.ll_change_dosage:
                hideKeyBoard(mInputEt);
                showDosageFormsPop();
                break;
            case R.id.save_tv://保存数据
                numKeyboard.clearFocus();
                hideKeyBoard(mInputEt);

                //延时是为了解决修改已填加药材列表中最后一味药材剂量时，确保修改的剂量已更新
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        saveMedicineData();
                    }
                }, 300);

                break;
            case R.id.colse_hint:
                mHintRl.setVisibility(View.GONE);

                //保存当前显示的时间
                if (showNoticeTimeString != null && !showNoticeTimeString.isEmpty()){
                    SharedPreferenceForeverUtils.putString(AddMedicineActivity.this, PublicParams.GRANULE_DATE_TIME, showNoticeTimeString);
                }

                break;
            case R.id.down_arrow:
                hideUpSlideView();
                break;
            case R.id.up_arrow:
                showUpSlideView();
                break;
            case R.id.common_template_tv:
                //重置编辑模式
                resetToNormalMode();

                hideKeyBoard(mInputEt);
                Intent intent = new Intent(AddMedicineActivity.this, ChooseCommonPrescriptionActivity.class);
                intent.putExtra(PublicParams.DRUG_TYPE, drugType);
                intent.putExtra(PublicParams.DRUG_PROVIDERID, drugProviderId);
                startActivityForResult(intent, 100);
                break;
            case R.id.tv_clear://清空药材
                clearMedicineList();
                break;
            case R.id.tv_paste://粘贴药材
                boolean isShowedPasteToast = SharedPreferenceForeverUtils.getBoolean(this, PublicParams.IS_SHOWED_PASTE_TOAST, false);
                if (!isShowedPasteToast) {//是否显示过用药粘贴的提示
                    //没有显示过，先toast提示
                    ToastUtils.showShortMsg(this, "患者医案中历史药方可复制到此");
                    SharedPreferenceForeverUtils.putBoolean(this, PublicParams.IS_SHOWED_PASTE_TOAST, true);
                    //return;
                }
                getPasteMedicineDetail();//获取粘贴药材的详情
                break;
            case R.id.tv_showSetting:
                //显示设置
                //隐藏键盘
                hideKeyBoard(mInputEt);
                //获取当前的单双列
                boolean isSingleColumn = SharedPreferenceForeverUtils.getBoolean(mContext, PublicParams.IS_SINGLE_COLUMN, false);
                LogUtils.i(AddMedicineActivity.class,"点击了输入框上面显示设置");
                float scale = SharedPreferenceForeverUtils.getFloat(mContext, PublicParams.ADDMEDICINE_FONT_SIZE_SCALE, 1.0f);
                customShowPopup.setDefaultLayoutOption(isSingleColumn);
                customShowPopup.setDefaultFontSizeOption(scale);
                customShowPopup.showPopupWindow();
                break;
            case R.id.tv_adjustByMultiple:
                //按倍改量
            {
                hideKeyBoard(mInputEt);
                adjustByMultiple();
            }
                break;
            case R.id.btn_know://隐藏引导页
//                getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
                SharedPreferenceForeverUtils.putBoolean(this, PublicParams.IS_FIRST_SHOW_ADDMEDICNE_GUIDE, true);
                guideLayout.setVisibility(View.GONE);
                showHintView();//显示特殊煎药法引导提示
                showInputKeyBoardDelayMore();
                break;
            case R.id.iv_top_mark:
                LogUtils.i(AddMedicineActivity.class, "点击了iv_top_mark");
                handleIvTopMarkClick();
                break;
        }
    }

    private void handleIvTopMarkClick() {
        String text = "";
        String drugForm = mDosageForm.getText().toString();
        boolean isLeft = false;
        LogUtils.i(AddMedicineActivity.class, "处理的 drugForm = " + drugForm);
        if (PublicParams.DOSAGEFORM_GRANULE.equals(drugForm)){
            text = "按照传统正常饮片一天剂量开方后，点击【按倍改量】按钮，可快速将传统配方颗粒剂量切换为国省标剂量【0.5倍，0.6倍，0.7倍，0.8倍，默认0.6倍】。";
        } else if (PublicParams.DOSAGEFORM_SLICES.equals(drugForm) ||
                PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm)) {
            text = "1、点击药名可设置特殊煎法；长按药名可编辑药材顺序、选择替换药。\n\n2、药名后( )内容为药味别名，【】内容为药味规格。";
            isLeft = true;
        } else if (PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(drugForm) ||
        PublicParams.DOSAGEFORM_WATERED_PILL.equals(drugForm) ||
        PublicParams.DOSAGEFORM_HONEYED_PILL.equals(drugForm) ||
        PublicParams.DOSAGEFORM_POWDER.equals(drugForm) ||
        PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(drugForm)) {
            text = "按照一天剂量开方后，点击【按倍改量】按钮，可快速修改剂量【7倍，14倍，30倍，60倍】，剂量修改为【7天量，14天量,30天量，60天量】。";
        }

        //针对剂型 颗粒 (必然甄选)平台定制 常见单独设置提示语
        if (PublicParams.DOSAGEFORM_GRANULE.equals(drugForm) &&
                supplierName.equals("必然甄选【平台定制】")){
            text = "该颗粒为合煎颗粒(药味浓缩干燥)，非国标配方颗粒，不需要减量开方，按照正常饮片处方即可。";
        }

        if (text.isEmpty()){
            return;
        }

        //键盘消失
        hideKeyBoard(mInputEt);

        final AlertDialog dialog = AlertDialog.getInstance(mContext);
        dialog.setDialogTitle("提示信息")
                .setDialogContent(text)
                .setContentGravity(isLeft ? Gravity.LEFT : Gravity.CENTER)
                .setPositiveText("知道了")
                .setOnPositiveBtnClickedListener(view -> {
                    dialog.dismiss();
                });
        dialog.show();
    }

    private void handleMultipleButtonClick(View view) {
        String multiple = ((TextView) view).getText().toString().replace("倍", "");
        etMultiple.setText(multiple);
    }

    /**
     * 按倍改量方法
     */
    private void adjustByMultiple() {

        if (mMedicineList.isEmpty()){
            ToastUtils.showShortMsg(mContext, "请添加药材");
            return;
        }

        View customView = LayoutInflater.from(mContext).inflate(R.layout.dialog_adjust_by_multiple, null);
        etMultiple = customView.findViewById(R.id.et_multiple);
        etMultiple.addTextChangedListener(new MultipleTextWatcher(etMultiple));
        //点击减号
        Button btnMinus = customView.findViewById(R.id.btn_decrease);
        //点击加号
        Button btnAdd = customView.findViewById(R.id.btn_increase);

        // 初始化所有倍数按钮
        int[] buttonIds = {R.id.btn_0_5, R.id.btn_0_6, R.id.btn_0_7, R.id.btn_0_8,
                R.id.btn_7, R.id.btn_14, R.id.btn_30, R.id.btn_60};
        multipleButtons = new TextView[buttonIds.length];

        for (int i = 0; i < buttonIds.length; i++) {
            multipleButtons[i] = (TextView) customView.findViewById(buttonIds[i]);
            multipleButtons[i].setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    handleMultipleButtonClick(v);
                }
            });
            multipleButtons[i].setClickable(true);
        }

        btnMinus.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                LogUtils.i(AddMedicineActivity.class, "Clicked minus button");
                // Decrease etMultiple value by 0.1, minimum value is 0.1
                String multiple = etMultiple.getText().toString();
                BigDecimal currentValue = new BigDecimal(multiple);
                BigDecimal minValue = new BigDecimal("0.1");
                BigDecimal decrement = new BigDecimal("0.1");

                if (currentValue.compareTo(minValue) > 0) {
                    BigDecimal newValue = currentValue.subtract(decrement);
                    etMultiple.setText(newValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
                }
            }
        });

        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                LogUtils.i(AddMedicineActivity.class, "Clicked plus button");
                // Increase etMultiple value by 0.1, maximum value is 100
                String multiple = etMultiple.getText().toString();
                // Print current value
                LogUtils.i(AddMedicineActivity.class, "Current multiple = " + multiple);

                BigDecimal currentValue = new BigDecimal(multiple);
                BigDecimal maxValue = new BigDecimal("100");
                BigDecimal increment = new BigDecimal("0.1");

                if (currentValue.compareTo(maxValue) < 0) {
                    BigDecimal newValue = currentValue.add(increment);
                    LogUtils.i(AddMedicineActivity.class, "Setting multiple = " + newValue);
                    etMultiple.setText(newValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
                }
            }
        });


        new CustomDialogPlus.Builder(mContext)
                .setTitle("按倍改量")
                .setSubtitle("所有药材按倍改量后将四舍五入取整")
                .setCustomView(customView)
                .setPositiveButton("修改", new CustomDialogPlus.OnButtonClickListener() {
                    @Override
                    public void onPositiveButtonClick(CustomDialogPlus dialog) {
                        LogUtils.i(AddMedicineActivity.class, "点击了修改");
                        // 处理确认按钮点击
                        String multiple = etMultiple.getText().toString();
                        //如果输入内容最后一个字符为 . ，则去掉
                        if (multiple.endsWith(".")) {
                            multiple = multiple.substring(0, multiple.length() - 1);
                        }
                        //校验输入是否合法
                        if (TextUtils.isEmpty(multiple) || Double.parseDouble(multiple) <= 0) {
                            ToastUtils.showShortMsg(mContext, ("最小输入倍数为0.1"));
                            LogUtils.i(AddMedicineActivity.class, "最小输入倍数为0.1");
                            return;
                        }
                        //设置倍数 打印获取的倍数
                        LogUtils.i(AddMedicineActivity.class, "倍数 = " + multiple);
                        //隐藏弹窗
                        dialog.dismiss();
                        //隐藏键盘
                        hideKeyBoard(etMultiple);

                        //将multiple转为BigDecimal
                        BigDecimal multipleBigDecimal = new BigDecimal(multiple);
                        //计算所有添加的药材剂量
                        calculateByMultiple(multipleBigDecimal);
                    }

                    @Override
                    public void onNegativeButtonClick(CustomDialogPlus dialog) {
                        // 这个方法不会被调用
                    }
                })
                .setNegativeButton("取消", new CustomDialogPlus.OnButtonClickListener() {
                    @Override
                    public void onPositiveButtonClick(CustomDialogPlus dialog) {
                        // 这个方法不会被调用
                    }

                    @Override
                    public void onNegativeButtonClick(CustomDialogPlus dialog) {
                        dialog.dismiss();

                        //隐藏键盘
                        hideKeyBoard(etMultiple);
                    }
                })
                .show();

        //设置 etMultiple 为焦点
        etMultiple.postDelayed(new Runnable() {
            @Override
            public void run() {
                etMultiple.requestFocus();
                InputMethodManager imm = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(etMultiple, InputMethodManager.SHOW_IMPLICIT);

                // 将光标移动到文本末尾
                etMultiple.setSelection(etMultiple.getText().length());
            }
        }, 300);
    }

    /**
     * 根据按倍改量倍数进行计算所有添加的药材剂量
     */
    private void calculateByMultiple(BigDecimal multiple) {
        //循环获取 mMedicineList 中dose数据
        for (int i = 0; i < mMedicineList.size(); i++) {
            BigDecimal dose = new BigDecimal(mMedicineList.get(i).getDose());
            // 根据multiple计算新的dose
            BigDecimal newDose = dose.multiply(multiple);

            // 确保结果最小为1
            newDose = newDose.max(BigDecimal.ONE);

            // 四舍五入到整数
            newDose = newDose.setScale(0, RoundingMode.HALF_UP);

            mMedicineList.get(i).setDose(newDose.toString());
        }

        //刷新列表数据
        refreshMedicineListDisplay();
    }

    public void refreshMedicineListDisplay() {

        isIterateChangeDose = true;

        for (int i = 0; i < mMedicinesTable.getChildCount(); i++) {
            View medicineView = mMedicinesTable.getChildAt(i);
            TextView nameTv = medicineView.findViewById(R.id.name_tv);
            TextView unitTv = medicineView.findViewById(R.id.unit_tv);
            EditText doseEt = medicineView.findViewById(R.id.dose_et);
            TextView priceTv = medicineView.findViewById(R.id.price_tv);

            MedicineDetailMsgBean medicine = (MedicineDetailMsgBean) doseEt.getTag();

            // 在 mMedicineList 中查找匹配的药材
            for (MedicineDetailMsgBean updatedMedicine : mMedicineList) {
                if (updatedMedicine.getDrugId().equals(medicine.getDrugId())) {
                    // 更新显示内容
                    nameTv.setText(updatedMedicine.getDrugName());
                    unitTv.setText(updatedMedicine.getUnit());
                    doseEt.setText(updatedMedicine.getDose());
                    updateMedicinePrice(updatedMedicine, doseEt, priceTv);
                    // 更新 tag 中的数据
                    doseEt.setTag(updatedMedicine);
//                    doseEt.clearFocus();
                    break;
                }
            }
        }

        //delay 0.3s
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                isIterateChangeDose = false;
            }
        }, 300);

        // 更新药材数量显示
        tvMedicineNum.setText(createSpannable("/共", mMedicineList.size() + "", "味"));

        // 重新计算药价
        startThread();
    }

    /**
     * 饮片类型特殊煎药方式提示
     * 显示  点击药材名选择特殊煎药方式  文字
     */
    private void showSliceHintView() {

        //如果不是饮片
        if (!PublicParams.DOSAGEFORM_SLICES.equals(drugForm) && !PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm)){
            return;
        }

        mHintTv.setText("点击药材名选择特殊煎药方式");
        mHintRl.setVisibility(View.VISIBLE);
    }

    /**
     * 颗粒国标省标提示
     */
    private void showGranuleHintView() {

        //如果不为颗粒
        if (!PublicParams.DOSAGEFORM_GRANULE.equals(drugForm)){
            return;
        }

        if (standardDesc == null || standardDesc.isEmpty()) {
            mHintRl.setVisibility(View.GONE);
            return;
        }

        //判断当天是否提示并关闭过
        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String currentDateTime = format.format(date);

        String saveDateTime = SharedPreferenceForeverUtils.getString(this, PublicParams.GRANULE_DATE_TIME);

        if (currentDateTime.equals(saveDateTime)){
            //当天已经保存过
            mHintRl.setVisibility(View.GONE);
            return;
        }

        //保存记录当前的颗粒显示时间
        showNoticeTimeString = currentDateTime;

        mHintTv.setText(standardDesc);
        mHintRl.setVisibility(View.VISIBLE);
    }

    /**
     * 展开显示药材列表
     */
    private void showUpSlideView() {
        rlHorizontalLayout.setVisibility(View.GONE);
        rlUpSlideMedicines.setVisibility(View.VISIBLE);
        llPasteClearPriceLayout.setVisibility(View.GONE);
    }

    /**
     * 清空已填加药材
     */
    private void clearMedicineList() {
        if (mMedicineList.size() > 0) {
            if (mConfirmDialog == null) {
                mConfirmDialog = ConfirmDialog.getInstance(mContext);
            }
            mConfirmDialog.setNavigationText("取消").setPositiveText("删除").setDialogContent("是否删除已添加的药材列表？").show();
            mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    mConfirmDialog.dismiss();
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    //删除已添加的药材列表
                    mMedicineList.clear();
                    mMedicineIdList.clear();
                    mMedicinesTable.removeAllViews();
                    mInputEt.setText("");
                    tvSingleDrugPrice.setText("￥0.00");
                    showInputKeyBoard();
                    tvClear.setTextColor(getResources().getColor(R.color.br_color_et_hint));
                    tvClear.setClickable(false);
                    //显示共几味药
                    tvMedicineNum.setText(createSpannable("/共", mMedicineList.size() + "", "味"));

                    //重置编辑模式
                    resetToNormalMode();

                    mConfirmDialog.dismiss();
                }
            });
        }
    }

    private ConfirmDialog addMedicationDialog;//粘贴药材时的本地已有药材的提示框

    private void initDialog() {
        addMedicationDialog = ConfirmDialog.getInstance(this)
                .setDialogContent("列表中已添加药材，请选择\"覆盖\"还是\"新增\"")
                .setPositiveText("新增")
                .setNavigationText("覆盖");
        addMedicationDialog.setCancelable(false);
        addMedicationDialog.setCanceledOnTouchOutside(false);
    }

    /**
     * 获取要粘贴药材的详情s
     */
    private void getPasteMedicineDetail() {
        if (clipMedicatioinList != null && clipMedicatioinList.size() > 0) {
            if (mMedicinesTable.getChildCount() > 0) {
                initDialog();
                addMedicationDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        addMedicationDialog.dismiss();
                        mMedicineList.clear();
                        mMedicinesTable.removeAllViews();
                        mMedicineIdList.clear();
                        //重置编辑模式
                        resetToNormalMode();

                        addPasteMedicine();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        addMedicationDialog.dismiss();
                        addPasteMedicine();
                    }
                });
                addMedicationDialog.show();
                return;
            }
            addPasteMedicine();
        }
    }

    /**
     * 添加粘贴的药材
     */
    private void addPasteMedicine() {
        StringBuilder idBuilder = new StringBuilder();
        for (int i = 0; i < clipMedicatioinList.size(); i++) {
            //存复制的药材剂量
            doseMap.put(clipMedicatioinList.get(i).getId(), clipMedicatioinList.get(i).getAmount());
            //存复制的药材特殊煎药方式
            useMethodMap.put(clipMedicatioinList.get(i).getId(), clipMedicatioinList.get(i).getType());
            //拼接复制的药材id，"，"号隔开
            idBuilder.append(clipMedicatioinList.get(i).getId() + ",");
        }
        //用需要粘贴的药材id去请求这些药材在此厂商剂型下的详情
        String idStr = idBuilder.toString();
        if (!TextUtils.isEmpty(idStr)) {
            isPaste = true;
            addMedicineType = ADDMEDICINETYPE_PASTE;
            NettyUtils.getMedicineDetailMsg(drugType, idStr.substring(0, idStr.length() - 1), drugProviderId, drugForm);
        } else {
            isPaste = false;
        }
        mInputEt.setText("");
    }

    /**
     * 添加缓存中的药材
     */
    private void addCacheMedicines(List<MedicineDetailMsgBean> medicines) {
        StringBuilder idBuilder = new StringBuilder();
        for (int i = 0; i < medicines.size(); i++) {
            //存缓存的药材剂量
            doseMap.put(medicines.get(i).getDrugId(), medicines.get(i).getDose());
            //存缓存的药材特殊煎药方式
            useMethodMap.put(medicines.get(i).getDrugId(), medicines.get(i).getUseMethod());
            //拼接缓存的药材id，"，"号隔开
            idBuilder.append(medicines.get(i).getDrugId() + ",");
        }
        //用需要缓存的药材id去请求这些药材在此厂商剂型下的详情
        String idStr = idBuilder.toString();
        if (!TextUtils.isEmpty(idStr)) {
            if (!TextUtils.isEmpty(idBuilder.toString())) {
                NettyUtils.getMedicineDetailMsg(drugType, idBuilder.toString().substring(0, idBuilder.toString().length() - 1), drugProviderId, drugForm);
            }
        }
        mInputEt.setText("");
    }

    /**
     * 保存药方数据
     */
    private void saveMedicineData() {
        boolean isSave = true;
//        mInputEt.requestFocus();
        if (mMedicineList.isEmpty()) {
            isSave = false;
            ToastUtils.showShortMsg(mContext, "请添加药材");
        } else {

            //如果 supplierName 包含 "青庐药局" 并且 剂型为 代煎 或者饮片 
            if (supplierName.contains("青庐药局") && (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm) || PublicParams.DOSAGEFORM_SLICES.equals(drugForm))) {
                if (mMedicineList.size() < 2) {
                    //弹窗提示 基于此药房规范，单一药味无法调剂，请选择其他厂商或继续添加药味
                    isSave = false;
                    final AlertDialog dialog = AlertDialog.getInstance(mContext);
                    dialog.setDialogTitle("药材不足提示")
                            .setDialogContent("基于此药房规范，单一药味无法调剂，请选择其他厂商或继续添加药味")
                            .setPositiveText("知道了")
                            .setOnPositiveBtnClickedListener(new AlertDialog.OnPositiveBtnClickedListener() {
                                @Override
                                public void onPositiveBtnClicked(View view) {
                                    dialog.dismiss();
                                }
                            });
                    dialog.show();
                }
            } else {
                for (int i = 0; i < mMedicineList.size(); i++) {
                    String inputDose = TextUtils.isEmpty(mMedicineList.get(i).getDose()) ? "0" : mMedicineList.get(i).getDose();
                    String rem = (DecimalUtils.rem(inputDose, mMedicineList.get(i).getMinSaleUnit()));
                    if ("0".equals(mMedicineList.get(i).getStock())) {
                        isSave = false;
                        ToastUtils.showShortMsg(mContext, "“" + mMedicineList.get(i).getDrugName() + "”库存暂缺");
                        break;
                    } else if (TextUtils.isEmpty(mMedicineList.get(i).getDose()) ||
                            "0".equals(mMedicineList.get(i).getDose()) ||
                            "0.00".equals(mMedicineList.get(i).getDose()) ||
                            "0.0".equals(mMedicineList.get(i).getDose())) {
                        isSave = false;
                        ToastUtils.showShortMsg(mContext, "请输入“" + mMedicineList.get(i).getDrugName() + "”剂量");
                        break;
                    }
//                else if (!rem.endsWith("0.00")) {//不是规格的整数倍
//                    isSave = false;
//                    ToastUtils.showShortMsg(mContext, "请按规格输入“" + mMedicineList.get(i).getDrugName() + "”的剂量");
//                    break;
//                }
                }
            }
        }
        if (isSave) {
            getMedicineToxiticyMsg(jsonToStr(mMedicineList, ""));
        }
    }

    /**
     * @param list
     * @return 生成保存药方参数(校验毒性)
     */
    public String jsonToStr(List<MedicineDetailMsgBean> list, String preName) {
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setIndex(i + "");
        }
        String listDetail = JSON.toJSONString(list, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty);
        String jsonStr =
                " {" +
                        " \"drugType\":\"" + drugType + '\"' +
                        ", \"preDetailList\":" + listDetail +
                        ", \"drugForm\":\"" + drugForm + '\"' +
                        ", \"isPregnant\":\"" + isPregnant + '\"' +
                        ", \"preName\":\"" + preName + '\"' +
                        '}';

        return jsonStr;

    }

    /**
     * 获取毒性信息
     *
     * @param medicineMsg
     */
    public void getMedicineToxiticyMsg(String medicineMsg) {
        HashMap param = new HashMap();
        param.put("order", medicineMsg);
        param.put("takerId", takerId);
        getMedicineToxiticyMsgCallBack = addHttpPostRequest(HttpUrlManager.CHECK_MEDICINES_TOXITICY, param, CheckMedicineToxiticyBean.class, this);

    }

    /**
     * 显示选择剂型弹框
     */
    private void showDosageFormsPop() {//因为每次都需要显示缺药的情况，所以每次都请求数据， 韩瑞峰 修改
        /*if (mDosageFormList != null && mDosageFormList.size() > 0) {
            changeDosageForm();//改变剂型
        } else {*/
        isChangedDrugForm = false;
        //showDosageFormPop = true;
        getDosageFormList();
        //}
    }

    /**
     * 显示剂型类表弹框并改剂型
     */
    private void changeDosageForm() {
        if (mDosageFormsPop == null) {
            mDosageFormsPop = DosageFormsPop.getInstance(this);
        }
        //showDosageFormPop = false;

        mDosageFormsPop.isShowShortMedication(mMedicineList.size() == 0 ? false : true);//==0说明没有药材，不需要显示药材情况

        mDosageFormsPop.setData(mDosageFormList);
        mDosageFormsPop.setLeftSelection(leftPosition);
        mDosageFormsPop.setRightSelection(rightPosition);
        mDosageFormsPop.show();
        mDosageFormsPop.setOnPopItemSelectedListener(new DosageFormsPop.PopItemSelectedListener() {
            @Override
            public void onPopItemSelected(DosageFormBean.FormList formList, DosageFormBean.FormList.FormDetailMsg formDetailMsg, int leftP, int rightP) {
                setDoseHintViewVisible(false);
                setInputViewVisible(true);
                leftPosition = leftP;
                rightPosition = formDetailMsg.getId();
                mFormDetailMsg = formDetailMsg;
                formListBean = formList;
                drugType = formDetailMsg.getProductSubType();
                drugProviderId = formDetailMsg.getId();
                dosageForm = formDetailMsg.getProductSubTypeName();
                drugForm = formList.getDrugFormName();
                supplierName = formDetailMsg.getName();
                costDesc = formDetailMsg.getCostDesc();//制作描述
                saveDrugType(mContext, drugType, drugProviderId, drugForm, formDetailMsg.getSelfSupport());
//                mDosageForm.setText(formList.getDrugFormName() + "");
                setDosageForm(formList.getDrugFormName());
                mSupplierName.setText("(" + formDetailMsg.getName() + ")");

                standard = formDetailMsg.getStandard();
                standardDesc = formDetailMsg.getStandardDesc();

                //是否显示“点击药材名称可以选择特殊煎法”
                String formatedCurrentTime = DateUtils.formatDate(new Date());
                String savedTime = SharedPreferenceForeverUtils.getString(AddMedicineActivity.this, PublicParams.DATE_TIME);
//                if (!PublicParams.DOSAGEFORM_SLICES.equals(tag_drugForm) &&
//                        PublicParams.DOSAGEFORM_SLICES.equals(drugForm) &&
//                        !formatedCurrentTime.equals(savedTime)) {
                Boolean changeToSlices = !PublicParams.DOSAGEFORM_SLICES.equals(tag_drugForm) && PublicParams.DOSAGEFORM_SLICES.equals(drugForm);
                Boolean changeToReplaceDecoction = !PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(tag_drugForm) && PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm);
                if ( (changeToSlices || changeToReplaceDecoction) && !formatedCurrentTime.equals(savedTime)){
                    setRlHintFromVisiablletoInInvisable();
                    SharedPreferenceForeverUtils.putString(AddMedicineActivity.this, PublicParams.DATE_TIME, formatedCurrentTime);
                } else if (PublicParams.DOSAGEFORM_GRANULE.equals(drugForm)) {
                    //颗粒
                    showGranuleHintView();
                } else {
                    mHintRl.setVisibility(View.GONE);
                }

                // 重置编辑模式
                resetToNormalMode();

                getMedicineDetailMsgForChangeDosage();//切换剂型后重新获取药材详情信息
            }
        });
    }

    /**
     * 切换剂型后重新获取药材详情信息
     */
    private void getMedicineDetailMsgForChangeDosage() {
        StringBuilder idBuilder = new StringBuilder();
        for (int i = 0; i < mMedicineList.size(); i++) {
            idBuilder.append(mMedicineList.get(i).getDrugId() + ",");
        }
        //更改了剂型，重新请求已添加的药材
        String idStr = idBuilder.toString();
        if (!TextUtils.isEmpty(idStr)) {
            isChangedDrugForm = true;
            NettyUtils.getMedicineDetailMsg(drugType, idStr.substring(0, idStr.length() - 1), drugProviderId, drugForm);
        } else {
            isChangedDrugForm = false;
        }
        mInputEt.setText("");
    }

    /**
     * SharedPreference 存储剂型、厂商等信息
     *
     * @param context
     * @param drugType2      K/Y
     * @param drugProviderId 厂商id
     * @param drugForm       剂型（颗粒、饮片、蜜丸、水丸……）
     * @param selfSupport    是否自营
     */
    private void saveDrugType(Context context, String drugType2, String drugProviderId, String drugForm, String selfSupport) {
        SharedPreferenceUtils.putString(context, PublicParams.DRUG_TYPE, drugType2);
        SharedPreferenceUtils.putString(context, PublicParams.DRUG_PROVIDERID, drugProviderId);
        SharedPreferenceUtils.putString(context, PublicParams.DRUG_FROM, drugForm);
        SharedPreferenceUtils.putString(context, PublicParams.SELFSUPPORT, selfSupport);
    }

    /**
     * 获取服药方式数据
     */
    private List<PopItem> initTakingWays() {
        takingWayPopItems.clear();
        String[] ways = new String[]{"煎汤", "外用"};
        for (int i = 0; i < ways.length; i++) {
            PopItem item = new PopItem();
            item.setPosition(i);
            item.setName(ways[i]);
            takingWayPopItems.add(item);
        }
        return takingWayPopItems;
    }

    /**
     * 获取特殊煎法数据
     */
//    private List<PopItem> initBoilWays(List<String> boilWays, List<String> customBoilWays) {
//        boilPopItems.clear();
//        if (customBoilWays != null && boilWays.size() > 0) {
//            for (int i = 0; i < customBoilWays.size(); i++) {
//                PopItem item = new PopItem();
//                item.setPosition(i);
//                item.setName(customBoilWays.get(i));
//                item.setCustom(true);
//                boilPopItems.add(item);
//            }
//        }
//        if (boilWays != null && boilWays.size() > 0) {
//            for (int i = 0; i < boilWays.size(); i++) {
//                PopItem item = new PopItem();
//                item.setPosition(i);
//                item.setCustom(false);
//                item.setName(boilWays.get(i));
//                boilPopItems.add(item);
//            }
//        }
//
//        return boilPopItems;
//    }

    /**
     * 发送已添加药材信息到用药界面
     *
     * @receiver {@link MedicationFragment#onReceiveTemplateSelectedResult(FormAndOrderMsgBean)}
     */
    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.BOIL_WAY:
                if (result != null && result.isRequestSuccessed()) {
                    BoilWayBean boilWayBean = (BoilWayBean) result.getBodyObject();
                    if (boilWayBean != null) {
                        List<String> boilWays = boilWayBean.getDecoction1();
                        List<String> customBoilWays = boilWayBean.getDecoction2();

                        // 重新初始化煎法列表
                        boilPopItems.clear();

                        // 添加自定义煎法
                        if (customBoilWays != null && customBoilWays.size() > 0) {
                            for (String customWay : customBoilWays) {
                                PopItem item = new PopItem();
                                item.setName(customWay);
                                item.setCustom(true);
                                boilPopItems.add(item);
                            }
                        }

                        // 添加普通煎法
                        if (boilWays != null && boilWays.size() > 0) {
                            for (String way : boilWays) {
                                PopItem item = new PopItem();
                                // 修改显示：为系统煎法名称添加 "*" 标记
                                item.setName(way + "*");
                                item.setCustom(false);
                                boilPopItems.add(item);
                            }
                        }

                        // 如果弹窗还在显示，则更新弹窗内容
                        if (mBottomPopWindow != null && mBottomPopWindow.isShowing()) {
                            mBottomPopWindow.upDatePopContent(boilPopItems);
                        }
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                showInputKeyBoard();
                break;
            case HttpUrlManager.ADD_BOIL_WAY:
                if (result != null && result.isRequestSuccessed()) {
                    if (mDialog != null && mDialog.isShowing()) {
                        mDialog.dismiss();
                    }
                    //更新煎药方式数据
                    PopItem item = new PopItem();
                    item.setName(mCustomBoilWay);
                    item.setCustom(true);//标记为自定义的煎法

                    if (boilPopItems.size() > 1) {//自定义的煎法只保存两条
                        if (boilPopItems.get(1).isCustom()) {
                            boilPopItems.remove(1);
                        }
                    }
                    boilPopItems.add(0, item);
                    mBottomPopWindow.upDatePopContent(boilPopItems);

                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.DOSAGE_FORM_LIST://获取剂型列表
                if (result != null && result.isRequestSuccessed()) {
                    DosageFormBean dosageFormBean = (DosageFormBean) result.getBodyObject();
                    if (dosageFormBean != null) {
                        mDosageFormList = (List<DosageFormBean.FormList>) dosageFormBean.getList();
                    }
                   /* if (showDosageFormPop) {
                        showDosageFormsPop();
                    }*/
                    if (isNeedGetSupplierList) {
                        isNeedGetSupplierList = false;
                        //检查缓存中的供应商是否过期
                        checkProviderId();

                        //根据最新的信息进行药材匹配
                        addCacheMedicines(tempMedicines);

                    } else {
                        changeDosageForm();
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.CHECK_MEDICINES_TOXITICY:
                if (result != null && result.isRequestSuccessed()) {
                    CheckMedicineToxiticyBean toxiticyBean = (CheckMedicineToxiticyBean) result.getBodyObject();
                    if (toxiticyBean != null) {
                        List<CheckMedicineToxiticyBean.ValidataRuleListBean> validataRules = toxiticyBean.getValidataRuleList();
                        hintToxiticyRules(validataRules);
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.GET_PRERSCRIPTION_PRICEMSG:
                if (result.isRequestSuccessed()) {
                    orderMsgBean = (OrderMsgBean) result.getBodyObject();
                    if (orderMsgBean != null) {
                        //广播通知跳转到用药界面
                        sendChatMainPositionBroadcast(false);
                        //eventBus发送药材信息到用药界面
                        EventBusUtils.post(setEventBeanMsg());
                        finishActivity();
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.GET_DRUGS_BY_BATCH_QUERY:
                if (result.isRequestSuccessed()) {
                    LogUtils.i(AddMedicineActivity.class, "获取药材信息成功");

                    DrugsBatchQueryBean drugsBatchQueryBean = (DrugsBatchQueryBean) result.getBodyObject();
                    if (drugsBatchQueryBean != null) {
                        List<MedicineOrTempleBean> list = drugsBatchQueryBean.getData();
                        if (list != null && !list.isEmpty()) {
//                            List<MedicineOrTempleBean> sortedList = new ArrayList<>();

//                            // 检查是否有重复药材
//                            boolean hasExistingMedicine = false;
//                            if (mMedicineList != null && !mMedicineList.isEmpty()) {
//                                for (MedicineOrTempleBean medicine : list) {
//                                    for (MedicineDetailMsgBean existMedicine : mMedicineList) {
//                                        if (existMedicine.getDrugId().equals(medicine.getId())) {
//                                            hasExistingMedicine = true;
//                                            break;
//                                        }
//                                    }
//                                    if (hasExistingMedicine) {
//                                        break;
//                                    }
//                                }
//                            }

                            if (!mMedicineList.isEmpty()) {
                                // 如果已经有药材，则显示对话框
                                initDialog();
                                final List<MedicineOrTempleBean> finalList = list;
                                addMedicationDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                                    @Override
                                    public void onNavigationBtnClicked(View view) {
                                        // 覆盖操作
                                        addMedicationDialog.dismiss();
                                        processMedicineList(finalList, true);
                                    }

                                    @Override
                                    public void onPositiveBtnClicked(View view) {
                                        // 新增操作
                                        addMedicationDialog.dismiss();
                                        processMedicineList(finalList, false);
                                    }
                                });
                                addMedicationDialog.show();
                            } else {
                                // 没有重复药材，直接处理
                                processMedicineList(list, false);
                            }
                        }
                    }

                }

        }
    }

    // 添加处理药材列表的方法
    private void processMedicineList(List<MedicineOrTempleBean> list, boolean isOverride) {
        List<MedicineOrTempleBean> sortedList = new ArrayList<>();

        //重置编辑模式
        resetToNormalMode();

        // 首先按smartInputList的顺序添加匹配的药材
        for (HerbInfo herb : smartInputList) {
            for (MedicineOrTempleBean medicine : list) {
                if (medicine.getName().equals(herb.getDrugName())) {
                    if (herb.getDose() != null) {
                        doseMap.put(medicine.getId(), herb.getDose());
                    }

                    String method = herb.getMethod();
                    if (method != null && !method.isEmpty() &&
                            (drugForm.equals(PublicParams.DOSAGEFORM_SLICES) ||
                                    drugForm.equals(PublicParams.DOSAGEFORM_REPLACE_DECOCTION))) {
                        useMethodMap.put(medicine.getId(), method);
                    }
                    sortedList.add(medicine);
                    break;
                }
            }
        }

        // 添加未匹配的药材
        for (MedicineOrTempleBean medicine : list) {
            boolean exists = false;
            for (MedicineOrTempleBean sorted : sortedList) {
                if (sorted.getName().equals(medicine.getName())) {
                    exists = true;
                    break;
                }
            }
            if (!exists) {
                sortedList.add(medicine);
            }
        }

        // 如果是覆盖操作，先清空现有列表
        if (isOverride) {
            mMedicineList.clear();
            mMedicinesTable.removeAllViews();
            mMedicineIdList.clear();
        }

        // 组装药材ID字符串并请求详情
        StringBuilder idBuilder = new StringBuilder();
        for (MedicineOrTempleBean medicine : sortedList) {
            idBuilder.append(medicine.getId()).append(",");
        }
        String idStr = idBuilder.toString();

        isPaste = true;
        NettyUtils.getMedicineDetailMsg(drugType, idStr.substring(0, idStr.length() - 1), drugProviderId, drugForm);
    }

    /**
     * 关闭页面
     */
    private void finishActivity() {
        hideKeyBoard(mInputEt);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                AddMedicineActivity.this.finish();
            }
        }, 300);
    }

    /**
     * EventBus传到用药界面的bean
     *
     * @return
     */
    @NonNull
    private FormAndOrderMsgBean setEventBeanMsg() {
        FormAndOrderMsgBean bean = new FormAndOrderMsgBean(from);

        // 检查当前供应商是否存在于当前剂型中
        boolean isProviderIdExist = false;
        if (mDosageFormList != null && !mDosageFormList.isEmpty()) {
            for (DosageFormBean.FormList formList : mDosageFormList) {
                if (formList.getDrugFormName().equals(drugForm)) {
                    if (formList.getList() != null) {
                        for (DosageFormBean.FormList.FormDetailMsg provider : formList.getList()) {
                            if (provider.getId().equals(drugProviderId)) {
                                isProviderIdExist = true;
                                break;
                            }
                        }
                    }

                    // 只有当前供应商不存在时,才使用此剂型的第一个供应商信息
                    if (!isProviderIdExist && formList.getList() != null
                            && !formList.getList().isEmpty()) {
                        mFormDetailMsg = formList.getList().get(0);
                        formListBean = formList;
                    }
                    break;
                }
            }
        }

        // 添加空值检查，防止空指针异常
        if (mFormDetailMsg != null) {
            mFormDetailMsg.setRightPosition(rightPosition);
            bean.setFormDetailMsg(mFormDetailMsg);
        } else {
            LogUtils.e(AddMedicineActivity.class, "mFormDetailMsg 为空，无法设置 FormDetailMsg");
        }

        if (formListBean != null) {
            formListBean.setLeftPosition(leftPosition);
            bean.setFormList(formListBean);
        } else {
            LogUtils.e(AddMedicineActivity.class, "formListBean 为空，无法设置 FormList");
        }

        bean.setOrderMsgBean(setOrderParames(orderMsgBean));
        return bean;
    }

    /**
     * 分险提示列表
     *
     * @param validataRules 分险列表
     */

    private void hintToxiticyRules(List<CheckMedicineToxiticyBean.ValidataRuleListBean> validataRules) {
        if (validataRules != null && validataRules.size() > 0) {
            SpannableStringBuilder builder = new SpannableStringBuilder();
            for (int i = 0; i < validataRules.size(); i++) {
                if (i == validataRules.size() - 1) {
                    builder.append(i + 1 + "、");
                    builder.append(createToxiticySpannable(validataRules.get(i).getTitleText()
                            , validataRules.get(i).getDrugName()));
                    builder.append("。\n");

                } else {
                    builder.append(i + 1 + "、");
                    builder.append(createToxiticySpannable(validataRules.get(i).getTitleText()
                            , validataRules.get(i).getDrugName()));
                    builder.append("；\n");
                }

            }
//            String value = builder.toString().trim();
            if (validataRules != null && validataRules.size() > 0) {
                showToxiticyDialog(builder);//显示提示框
            }
        } else {
            getMedicinePriceMsg();//获取药费、总重及制作费
        }
    }

    /**
     * 显示用药分险提示框
     *
     * @param value 分险提示语
     */
    private void showToxiticyDialog(CharSequence value) {
        if (toxiticyDialog == null) {
            toxiticyDialog = ToxiticyDialog.getInstance(this);
        }
        //.setDialogTitle("用药风险提示")
//                .setPositiveText("下一步")
//                .setNavigationText("返回修改")
//                .setContentGravity(Gravity.LEFT)
        toxiticyDialog.setDialogContent(value);
        toxiticyDialog.setOnBtnClickedListener(new ToxiticyDialog.OnButtonClickedListener() {
            @Override
            public void onNavigationBtnClicked(View view) {
                toxiticyDialog.dismiss();
                showKeyBoard(mInputEt);
            }

            @Override
            public void onPositiveBtnClicked(View view) {
                getMedicinePriceMsg();//获取药费、总重及制作费
                toxiticyDialog.dismiss();
            }
        });
        toxiticyDialog.show();
    }

    /**
     * 获取药材总重，制作费等信息
     */
    private void getMedicinePriceMsg() {
        HashMap params = new HashMap();
        params.put("order", setJsonParams(mMedicineList, mFormDetailMsg));
        params.put("apiVersion", "3");
        getMedicinePriceMsgCallBack = addHttpPostRequest(HttpUrlManager.GET_PRERSCRIPTION_PRICEMSG, params, OrderMsgBean.class, this);
    }

    /**
     * @param list
     * @param formDetailMsg
     * @return 生成请求药材总重、制作费等信息的参数
     */
    public String setJsonParams(List<MedicineDetailMsgBean> list, DosageFormBean.FormList.FormDetailMsg formDetailMsg) {
//        String listDetail = JSON.toJSONString(list);
        String jsonStr =
                " {" +
                        " \"drugType\":\"" + formDetailMsg.getProductSubType() + '\"' +
                        ", \"preDetailList\":" + JSON.toJSONString(list) +
                        ", \"providerDrugformId\":\"" + formDetailMsg.getProviderDrugformId() + '\"' +
                        ", \"providerId\":\"" + formDetailMsg.getId() + '\"' +
                        ", \"patientId\":\"" + patientId + '\"' +
                        ", \"userId\":\"" + userId + '\"' +
                        '}';
        return jsonStr;
    }

    /**
     * @return 组装开药方需要的参数数据，回传到用药界面
     */
    private OrderMsgBean setOrderParames(OrderMsgBean orderMsgBean) {
        OrderMsgBean order = orderMsgBean;

        // 添加空值检查，防止空指针异常
        if (formListBean != null) {
            order.setDrugForm(formListBean.getDrugFormName());
        } else if (!TextUtils.isEmpty(drugForm)) {
            order.setDrugForm(drugForm);
        } else {
            LogUtils.e(AddMedicineActivity.class, "formListBean 和 drugForm 都为空，无法设置剂型");
            order.setDrugForm("未知剂型");
        }

        order.setProviderId(drugProviderId);
        order.setProductSubType(drugType);
        order.setTakerIsPregnant(isPregnant);
        order.setPreDetailList(mMedicineList);
//        order.setProviderName(supplierName);
//        order.setMakeDescription(costDesc);//制作描述
        order.setMakeDesc(TextUtils.isEmpty(orderMsgBean.getMakeDesc()) ? "" : orderMsgBean.getMakeDesc());//制作描述
        order.setMakeCost(TextUtils.isEmpty(orderMsgBean.getMakeCost()) ? "" : orderMsgBean.getMakeCost());//制作费
        order.setBalanceWeight(TextUtils.isEmpty(orderMsgBean.getBalanceWeight()) ? "" : orderMsgBean.getBalanceWeight());//总重
        order.setMakeDays(TextUtils.isEmpty(orderMsgBean.getMakeDays()) ? "" : orderMsgBean.getMakeDays());
        return order;
    }

    /**
     * @param title 自定义煎药方式
     */
    private void addBoilWay(String title) {
        HashMap map = new HashMap();
        map.put("title", title);
        addBoilWayCallBack = addHttpPostRequest(HttpUrlManager.ADD_BOIL_WAY, map, ResponseResult.class, this);
    }

    /**
     * 搜索药材，联想搜药返回结果、请求药材详情及药方详情返回结果处理方法
     *
     * @param result 返回结果
     * @sender {@link DataReceiverImpl#sendEventBus(NettyResult)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void medicineSearchResult(NettyResult result) {
        if (result != null && result.getResult() != null) {
            if (NettyResultCode.SUCCESS.equals(result.getResultCode())) {
                if (result.getResultType() != null) {
                    if (ResultType.SEARCH_MEDICINE.equals(result.getResultType()) || ResultType.ASSOCIATIVE_MEDICINE.equals(result.getResultType())) {//搜索药材
                        parseSearchedMedicines(result);//解析并显示搜索到的药材
                    } else if (ResultType.MEDICINE_DETAILS.equals(result.getResultType()) || ResultType.TEMPLATE_DETAILS.equals(result.getResultType())) {//药材详情 药方详情
                        parseMedicineOrTemplateDetail(result);//药材或药方详情的解析及添加操作
                    }
//                    else if (ResultType.ASSOCIATIVE_MEDICINE.equals(result.getResultType())) {//联想药材
//                        parseAssociativedMedicine(result);//联想到药材的解析及显示
//                    }
                }
            }
        }
    }

    /**
     * 联想到药材的解析及显示
     *
     * @param result
     */
    private void parseAssociativedMedicine(NettyResult result) {
        HashMap<String, Object> map = (HashMap<String, Object>) result.getResult();
        JSONObject jsonObject = (JSONObject) map.get("result");
        if (jsonObject != null) {
            List<HashMap<String, Object>> dataMap = (List<HashMap<String, Object>>) jsonObject.get("data");
            if (dataMap != null) {
                try {
                    String jsonStr = JSON.toJSONString(dataMap);
                    List<MedicineOrTempleBean> medicines = JSON.parseArray(jsonStr, MedicineOrTempleBean.class);
                    if (medicines.size() > 0) {
                        rlHorizontalLayout.setVisibility(View.VISIBLE);
                    } else {
                        rlHorizontalLayout.setVisibility(View.GONE);
                    }
                    mMedicineOrTemples.clear();
                    mMedicineOrTemples.addAll(medicines);
                    mMedicineHLvAdapter.updateData(mMedicineOrTemples);
                    showUpViewData(mMedicineOrTemples, mUpSlideMedicines);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * /药材或药方详情的解析及添加操作
     *
     * @param result
     */
    private void parseMedicineOrTemplateDetail(NettyResult result) {
        HashMap<String, Object> map = (HashMap<String, Object>) result.getResult();
        String taskType = (String) map.get("id");
        JSONObject jsonObject = (JSONObject) map.get("result");
        if (jsonObject != null) {
            List<HashMap<String, Object>> dataMap = (List<HashMap<String, Object>>) jsonObject.get("data");
            if (dataMap != null) {
                try {
                    String jsonStr = JSON.toJSONString(dataMap);
                    //向上弹出的布局中点击名称请求了详情后就隐藏该布局，
                    hideUpSlideView();
                    final List<MedicineDetailMsgBean> medicines = JSON.parseArray(jsonStr, MedicineDetailMsgBean.class);
                    setAddMedicineType(result);//设置添加药材时的类型
                    if (isLongClick.equals(taskType)) {//长按显示药方详情弹框
                        showTemplateDetailPop(medicines);
                    } else {//点击添加药材

                        /**
                         * 判断何时弹出提示框
                         * 1.已有药材显示
                         * 2.要添加的药材数量大于1
                         * 3.忽略修改剂型弹出提示框
                         * 4.忽略从缓存获取药材弹出提示框
                         */
                        if (mMedicinesTable.getChildCount() > 0 && medicines.size() > 1 &&
                                !addMedicineType.equals(ADDMEDICINETYPE_CHANGE) &&
                                !addMedicineType.equals(ADDMEDICINETYPE_CACHE) &&
                                !addMedicineType.equals(ADDMEDICINETYPE_PASTE)) {
                            initDialog();
                            addMedicationDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                                @Override
                                public void onNavigationBtnClicked(View view) {
                                    addMedicationDialog.dismiss();
                                    mMedicineList.clear();
                                    mMedicinesTable.removeAllViews();
                                    mMedicineIdList.clear();
                                    addMedicineView(medicines, mMedicinesTable, addMedicineType);
                                }

                                @Override
                                public void onPositiveBtnClicked(View view) {
                                    addMedicationDialog.dismiss();
                                    addMedicineView(medicines, mMedicinesTable, addMedicineType);
                                }
                            });
                            addMedicationDialog.show();
                            return;
                        }
                        addMedicineView(medicines, mMedicinesTable, addMedicineType);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {//返回药材列表为空时，此次请求已完成，添加药材状态恢复初始值
                isChangedDrugForm = false;
                isPaste = false;
            }
        }
    }

    /**
     * 设置添加药材的类型
     *
     * @param result
     */
    private void setAddMedicineType(NettyResult result) {
        if (result.getResultType().equals(ResultType.MEDICINE_DETAILS)) {
            if (isChangedDrugForm) {//切换剂型
                addMedicineType = ADDMEDICINETYPE_CHANGE;
            } else if (isPaste) {//粘贴药方
                addMedicineType = ADDMEDICINETYPE_PASTE;
            } else if (isCache) {//临时保存的药方
                addMedicineType = ADDMEDICINETYPE_CACHE;
            } else {//添加药材
                addMedicineType = ADDMEDICINETYPE_ADD;
            }
        } else if (result.getResultType().equals(ResultType.TEMPLATE_DETAILS)) {
            addMedicineType = ADDMEDICINETYPE_TEMPLATE;//批量添加药方中的药材
        }
    }

    /**
     * 显示长按显示药材或药方详情的弹框
     *
     * @param medicines
     */
    private void showTemplateDetailPop(final List<MedicineDetailMsgBean> medicines) {
        if (medicines.size() > 0) {
            if (templateListPop == null) {
                templateListPop = TemplateListPopWindow.getInstance(AddMedicineActivity.this);
            }
            templateListPop.setPopTitle(templateName);
            templateListPop.initPopContentData(medicines);
            templateListPop.showPopFromBottom(mLayoutRoot);
            templateListPop.OnSureBtnClickListener(new TemplateListPopWindow.OnSureBtnClickListener() {
                @Override
                public void onSureBtnClick(final List<MedicineDetailMsgBean> list) {
                    if (mMedicinesTable.getChildCount() > 0 && list.size() > 1) {
                        initDialog();
                        addMedicationDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                addMedicationDialog.dismiss();
                                mMedicineList.clear();
                                mMedicinesTable.removeAllViews();
                                mMedicineIdList.clear();
                                addMedicineView(list, mMedicinesTable, addMedicineType);
                                templateListPop.dimissPop();
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                addMedicationDialog.dismiss();
                                addMedicineView(list, mMedicinesTable, addMedicineType);
                                templateListPop.dimissPop();
                            }
                        });
                        addMedicationDialog.show();
                        return;
                    }
                    addMedicineView(list, mMedicinesTable, addMedicineType);
                    templateListPop.dimissPop();
                }
            });
        }
    }

    /**
     * 解析并显示搜索到的药材
     *
     * @param result
     */
    private void parseSearchedMedicines(NettyResult result) {
        HashMap<String, Object> map1 = (HashMap<String, Object>) result.getResult();
        JSONObject jsonObject = (JSONObject) map1.get("result");
        if (jsonObject != null) {
            List<HashMap<String, Object>> dataMap = (List<HashMap<String, Object>>) jsonObject.get("data");
            if (dataMap != null) {
                try {
                    String jsonStr = JSON.toJSONString(dataMap);
                    List<MedicineOrTempleBean> datas = JSON.parseArray(jsonStr, MedicineOrTempleBean.class);
                    //控制显示药材布局的显示与隐藏
                    if (datas.size() > 0) {
                        rlHorizontalLayout.setVisibility(View.VISIBLE);
                    } else {
                        rlHorizontalLayout.setVisibility(View.GONE);
                    }
                    mMedicineOrTemples.clear();
                    horizontalLv.setSelection(0);
                    mMedicineOrTemples.addAll(datas);
                    mMedicineHLvAdapter.updateData(mMedicineOrTemples);
                    showUpViewData(mMedicineOrTemples, mUpSlideMedicines);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                mMedicineOrTemples.clear();
                mMedicineHLvAdapter.updateData(mMedicineOrTemples);
                mUpSlideMedicines.removeAllViews();
            }

        }
    }

    /**
     * 隐藏向上弹出平铺的布局
     */
    private void hideUpSlideView() {
        rlUpSlideMedicines.setVisibility(View.GONE);
        rlHorizontalLayout.setVisibility(View.VISIBLE);
        llPasteClearPriceLayout.setVisibility(View.VISIBLE);
        rlPasteClear.setVisibility(View.VISIBLE);
    }

    /**
     * 调取常用方及历史模板数据处理
     *
     * @sender {@link CommonPrescriptionFragment#onRequestFinished(String, ResponseResult)}
     * @sender {@link PrescriptionHistoryFragment#onRVItemClick(ViewGroup, View, int)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReciveTemplateSelectedResult(final List<TemplateDrugBean> data) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                addMedicineType = ADDMEDICINETYPE_TEMPLATE;
                final List<MedicineDetailMsgBean> templatemedicines = new ArrayList<>();
                if (data != null && data.size() > 0) {
                    if (mMedicinesTable.getChildCount() > 0) {
                        initDialog();
                        addMedicationDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                addMedicationDialog.dismiss();
                                mMedicineList.clear();
                                mMedicinesTable.removeAllViews();
                                mMedicineIdList.clear();
                                //重置编辑模式
                                resetToNormalMode();

                                for (int i = 0; i < data.size(); i++) {
                                    MedicineDetailMsgBean medicine = new MedicineDetailMsgBean();
                                    medicine.setDrugName(data.get(i).getDrugName());
                                    medicine.setDose(data.get(i).getDose());
                                    medicine.setUnit(data.get(i).getUnit());
                                    medicine.setDrugId(data.get(i).getDrugId());
                                    medicine.setStock(data.get(i).getStock());
                                    medicine.setMinSaleUnit(data.get(i).getMinSaleUnit());
                                    medicine.setProductSubType(data.get(i).getProductSubType());
                                    medicine.setSpecialUnit(data.get(i).getSpecialUnit());
                                    medicine.setPrice(data.get(i).getPrice());
//                                    medicine.setRecipelSalePrice(data.get(i).getRecipelSalePrice());
                                    medicine.setUseMethod(data.get(i).getUseMethod());
                                    templatemedicines.add(medicine);
                                    useMethodMap.put(medicine.getDrugId(), medicine.getUseMethod());
                                }
                                if (templatemedicines.size() > 0) {
                                    addMedicineView(templatemedicines, mMedicinesTable, addMedicineType);
                                }
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                addMedicationDialog.dismiss();
                                for (int i = 0; i < data.size(); i++) {
                                    MedicineDetailMsgBean medicine = new MedicineDetailMsgBean();
                                    medicine.setDrugName(data.get(i).getDrugName());
                                    medicine.setDose(data.get(i).getDose());
                                    medicine.setUnit(data.get(i).getUnit());
                                    medicine.setDrugId(data.get(i).getDrugId());
                                    medicine.setStock(data.get(i).getStock());
                                    medicine.setMinSaleUnit(data.get(i).getMinSaleUnit());
                                    medicine.setProductSubType(data.get(i).getProductSubType());
                                    medicine.setSpecialUnit(data.get(i).getSpecialUnit());
                                    medicine.setPrice(data.get(i).getPrice());
//                                    medicine.setRecipelSalePrice(data.get(i).getRecipelSalePrice());
                                    medicine.setUseMethod(data.get(i).getUseMethod());
                                    templatemedicines.add(medicine);
                                    useMethodMap.put(medicine.getDrugId(), medicine.getUseMethod());
                                }
                                if (templatemedicines.size() > 0) {
                                    addMedicineView(templatemedicines, mMedicinesTable, addMedicineType);
                                }
                            }
                        });
                        addMedicationDialog.show();
                        return;
                    }
                    for (int i = 0; i < data.size(); i++) {
                        MedicineDetailMsgBean medicine = new MedicineDetailMsgBean();
                        medicine.setDrugName(data.get(i).getDrugName());
                        medicine.setDose(data.get(i).getDose());
                        medicine.setUnit(data.get(i).getUnit());
                        medicine.setDrugId(data.get(i).getDrugId());
                        medicine.setStock(data.get(i).getStock());
                        medicine.setMinSaleUnit(data.get(i).getMinSaleUnit());
                        medicine.setProductSubType(data.get(i).getProductSubType());
                        medicine.setSpecialUnit(data.get(i).getSpecialUnit());
                        medicine.setPrice(data.get(i).getPrice());
//                        medicine.setRecipelSalePrice(data.get(i).getRecipelSalePrice());
                        medicine.setUseMethod(data.get(i).getUseMethod());
                        templatemedicines.add(medicine);
                        useMethodMap.put(medicine.getDrugId(), medicine.getUseMethod());
                    }
                    if (templatemedicines.size() > 0) {
                        addMedicineView(templatemedicines, mMedicinesTable, addMedicineType);
                    }
                }
            }
        }, 500);


    }


    /**
     * @param cotentData
     * @param containers 显示 向上的view数据
     */
    private void showUpViewData(final List<MedicineOrTempleBean> cotentData, FlowLayout containers) {
        if (cotentData != null && cotentData.size() > 0) {
            containers.removeAllViews();
            for (int i = 0; i < cotentData.size(); i++) {
                final String type = cotentData.get(i).getType();
                final String ids = cotentData.get(i).getId();
                String stock = cotentData.get(i).getStock();
                View tv = getMedicineTextView(cotentData.get(i).getName(), type, stock, cotentData.get(i).getPrice(), cotentData.get(i).getUnit());

                tv.setOnClickListener(new View.OnClickListener() {//点击名称请求详情
                    @Override
                    public void onClick(View v) {
                        requestMedicineOrTemplateMsg(ids, type, "isClick");
                    }
                });
                final int finalI = i;
                tv.setOnLongClickListener(new View.OnLongClickListener() {
                    @Override
                    public boolean onLongClick(View v) {
                        getTemplateList(cotentData.get(finalI));
                        return true;
                    }
                });
                containers.addView(tv);
            }
        } else {
            return;
        }


    }

    /**
     * @param template 获取药方列表
     */
    private void getTemplateList(MedicineOrTempleBean template) {
        String type = template.getType();
        if (("2").equals(type) || ("3").equals(type)) {
            templateName = template.getName();
            NettyUtils.getTemplateDetailMsg(template.getId(), type, isLongClick, drugType, drugProviderId, drugForm);
        }
    }

    /**
     * @param text
     * @return 动态添加textView 此方法用于点击向上箭头显示药材
     */
    @NonNull
    private View getMedicineTextView(String text, String type, String stock, String salePrice, String unit) {
        View view = LayoutInflater.from(this).inflate(R.layout.medicine_item_layout, null, false);
        FlowLayout.LayoutParams params = new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT, FlowLayout.LayoutParams.WRAP_CONTENT);
        params.setMargins(DensityUtils.dip2px(mContext, 5), DensityUtils.dip2px(mContext, 5), DensityUtils.dip2px(mContext, 5), DensityUtils.dip2px(mContext, 5));
        view.setLayoutParams(params);
        view.setPadding(DensityUtils.dip2px(mContext, 8), DensityUtils.dip2px(mContext, 5), DensityUtils.dip2px(mContext, 7), DensityUtils.dip2px(mContext, 5));
        view.setBackgroundResource(R.drawable.bg_white_radius3);
        TextView nameTv = view.findViewById(R.id.name_tv);
        TextView descTv = ((TextView) view.findViewById(R.id.desc_tv));
        nameTv.setText(text);
        if (!TextUtils.isEmpty(type)) {
            if ("2".equals(type) || "3".equals(type)) {
                nameTv.setTextColor(getResources().getColor(R.color.br_color_theme));
                if ("2".equals(type)) {
                    descTv.setText("常用方");
                } else {
                    descTv.setText("经典方");
                }
                descTv.setTextColor(mContext.getResources().getColor(R.color.br_color_black_999));
            } else if ("1".equals(type)) {
                if ("0".equals(stock)) {//无库存，
                    nameTv.setTextColor(mContext.getResources().getColor(R.color.br_color_et_hint));
                    descTv.setText("缺药");
                    descTv.setTextColor(mContext.getResources().getColor(R.color.br_color_red_ff8585));
                } else {
                    nameTv.setTextColor(mContext.getResources().getColor(R.color.br_color_theme_text));
                    descTv.setText("￥" + salePrice + "/" + unit);
                    descTv.setTextColor(mContext.getResources().getColor(R.color.br_color_black_999));
                }
            }
        }
        //            tv.setTextSize(TypedValue.COMPLEX_UNIT_PX,DensityUtils.sp2px(mContext,15));
        return view;
    }

    /**
     * @param text
     * @return 动态添加textView 此方法用于剂量提示的显示
     */
    @NonNull
    private TextView getDoseTextView(String text) {
        TextView tv = new TextView(this);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        params.setMargins(DensityUtils.dip2px(mContext, 5), 0, DensityUtils.dip2px(mContext, 5), 0);
        tv.setLayoutParams(params);
        tv.setPadding(DensityUtils.dip2px(mContext, 8), DensityUtils.dip2px(mContext, 5), DensityUtils.dip2px(mContext, 7), DensityUtils.dip2px(mContext, 5));
        tv.setTextColor(getResources().getColor(R.color.br_color_theme_text));
        tv.setBackgroundResource(R.drawable.bg_white_radius3);
        tv.setText(text);
        tv.setTextSize(15);
        //            tv.setTextSize(TypedValue.COMPLEX_UNIT_PX,DensityUtils.sp2px(mContext,15));
        return tv;
    }

    /**
     * 更改药材剂量
     */
    private void upDateMedicineDose(EditText view) {
        MedicineDetailMsgBean drug = (MedicineDetailMsgBean) view.getTag();
        for (int j = 0; j < mMedicineList.size(); j++) {
            if (mMedicineList.get(j).getDrugId().equals(drug.getDrugId())) {
                mMedicineList.get(j).setDose(view.getText().toString());
            }
        }
        doseMap.put(drug.getDrugId(), view.getText().toString());
    }

    /**
     * @param keyCode 数字键盘按键监听事件
     */
    @Override
    public void onKeyDown(int keyCode, EditText view) {
        MedicineDetailMsgBean drug = (MedicineDetailMsgBean) view.getTag();
        String minSaleUnit = TextUtils.isEmpty(drug.getMinSaleUnit()) ? "0" : drug.getMinSaleUnit();
        if (keyCode == NumberKeyboardView.KEYCODE_BACK) {//删除

        } else if (keyCode == NumberKeyboardView.KEYCODE_POINT) {//小数点
        } else if (keyCode == NumberKeyboardView.KEYCODE_SURE) {//确定键
            if (PublicParams.DOSAGEFORM_GRANULE.equals(dosageForm)) {//此药方剂型为颗粒或用颗粒制作的其他剂型
                inputDoseBySpecification(view, drug, minSaleUnit);//确认按规格输入剂量
            } else {
                sureInputDose(drug, view.getText().toString(), drugType, drugForm, drugProviderId);//确认输入的计量
            }
            upDateMedicineDose(view);//跟新药材剂量
        }

    }

    /**
     * 确认输入的剂量
     *
     * @param drug           该药材
     * @param value          输入的剂量
     * @param drugType       K/Y
     * @param drugForm       剂型（颗粒、饮片、蜜丸、水丸……）
     * @param drugProviderId 厂商id
     */
    private void sureInputDose(MedicineDetailMsgBean drug, String value, String drugType, String drugForm, String drugProviderId) {
        doseMap.put(drug.getDrugId(), value);
        numKeyboard.hideKeyboard();//数字键盘隐藏
        mDoseHintLl.setVisibility(View.GONE);//剂量提示布局隐藏
        mBottomRl.setVisibility(View.VISIBLE);//输入框布局显示
        rlPasteClear.setVisibility(View.VISIBLE);
        mInputEt.requestFocus();//输入框获取焦点弹出拼音键盘、
        showKeyBoard(mInputEt);
        mInputEt.setText("");//添加完一味药材后，输入框值“”
        NettyUtils.getAssociativeMedicine(getAssociativeDrugId(), drugType, drugForm, drugProviderId);//请求联想药材
    }

    /**
     * 确认按规格输入剂量
     *
     * @param view        剂量输入框
     * @param drug        该药材
     * @param minSaleUnit 规格
     */
    private void inputDoseBySpecification(EditText view, MedicineDetailMsgBean drug, String minSaleUnit) {
        String inputDose = TextUtils.isEmpty(view.getText().toString().trim()) ? "0" : view.getText().toString().trim();
        String rem = (DecimalUtils.rem(inputDose, minSaleUnit));
        if (!TextUtils.isEmpty(rem) && rem.endsWith("0.00")) {//输入剂量是规格的整数倍
            sureInputDose(drug, inputDose, drugType, drugForm, drugProviderId);
        } else {
            ToastUtils.showShortMsg(mContext, "请按提示输入剂量。");
            view.setText(/*TextUtils.isEmpty(drug.getMinSaleUnit()) ? "0" : drug.getMinSaleUnit()*/"0");
            view.setSelection(0, view.getText().toString().length());
            view.setSelectAllOnFocus(true);
        }
    }

    /**
     * 联想药材请求药材id
     *
     * @return
     */
    private String getAssociativeDrugId() {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < mMedicineIdList.size(); i++) {
            builder.append(mMedicineIdList.get(i) + ",");
        }
//        String drugIds = builder.toString().substring(0, builder.toString().length() - 1);
        return builder.toString().substring(0, builder.toString().length() - 1);
    }

    @Override
    protected void onStop() {
        if (mMedicineList != null && mMedicineList.size() > 0) {
            DataCacheDaoUtil.getInstance().upDateData(setCacheMedicineMsg());
        }
        super.onStop();
    }

    /**
     * @return 组装要缓存的数据
     */
    private DataCache setCacheMedicineMsg() {
        String cacheType;
        String cacheKey;
        if (from == FormAndOrderMsgBean.FROM_QUICK_MEDICINE) {
            cacheType = DataCacheType.CACHE_QUICK_PRESCRIPTION_MSG;
            cacheKey = userId + "_" + takerName;
        } else {
            cacheType = DataCacheType.CACHE_PRESCRIPTION_MSG;
            cacheKey = userId + "_" + patientId;
        }

        DataCache cachePresMsg = DataCacheDaoUtil.getInstance().select(cacheType, cacheKey);
        if (cachePresMsg != null && cachePresMsg.getValue() != null) {//已有部分缓存信息
            OrderMsgBean cacheBean = JSON.parseObject(cachePresMsg.getValue(), OrderMsgBean.class);
            if (cacheBean == null) {
                cacheBean = new OrderMsgBean();
            }
            cachePresMsg.setValue(JSON.toJSONString(setMedicineBeanMsg(cacheBean)));
        } else {//初次缓存
            cachePresMsg = new DataCache();
            OrderMsgBean cacheBean = new OrderMsgBean();
            cachePresMsg.setKey(cacheKey);
            cachePresMsg.setType(cacheType);
            cachePresMsg.setValue(JSON.toJSONString(setMedicineBeanMsg(cacheBean)));
        }
        return cachePresMsg;
    }

    /**
     * @param cacheBean 存放缓存信息的bean
     * @return 设置要缓存的药材信息
     */
    private OrderMsgBean setMedicineBeanMsg(OrderMsgBean cacheBean) {
        if (cacheBean != null) {
            cacheBean.setFormDetailMsg(mFormDetailMsg);
            cacheBean.setFormList(formListBean);
            cacheBean.setLeftP(leftPosition);
            cacheBean.setRightP(rightPosition);
            cacheBean.setPreDetailList(mMedicineList);
            cacheBean.setDrugForm(drugForm);
            cacheBean.setPreDetailList(mMedicineList);
        } else {
            cacheBean = new OrderMsgBean();
        }

        return cacheBean;
    }

    @Override
    protected void onDestroy() {
        if (mDosageFormsPop != null) {
            mDosageFormsPop.dismiss();
            mDosageFormsPop = null;
        }
        EventBusUtils.unRegister(this);
        cancelRequest(getMedicineToxiticyMsgCallBack, getMedicinePriceMsgCallBack, addBoilWayCallBack, getBoilWayCallBack, getDosageFormListCallBack);
        if (countPriceThread != null) {
            stopThread();
        }
        try {
            mHandler.removeCallbacksAndMessages(null);
        } catch (Exception e) {
            LogUtils.e(AddMedicineActivity.class, e.getMessage());
        }
        super.onDestroy();
    }

    /**
     * 剂量选择框的onTouch事件处理
     * 当剂量输入框获取焦点时，显示数字键盘，及计量提示布局，隐藏输入框布局及字母键盘
     */
    @Override
    public void onViewTouch(View view) {
        hideKeyBoard(mInputEt);
        MedicineDetailMsgBean medicine = (MedicineDetailMsgBean) view.getTag();
        showDoseHintView(medicine, (EditText) view);

    }

    /**
     * 处理点击返回键的逻辑
     */
    private void doBackLogic() {
        if (mMedicineList.size() > 0) {
            if (mConfirmDialog == null) {
                mConfirmDialog = ConfirmDialog.getInstance(this);
            }
            mConfirmDialog.setDialogTitle("")
                    .setDialogContent("已添加药材，是否保存？")
                    .setContentGravity(Gravity.CENTER)
                    .setPositiveText("保存")
                    .setNavigationText("取消");
            mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                /**
                 * @param view
                 */
                @Override
                public void onNavigationBtnClicked(View view) {
                    mConfirmDialog.dismiss();
                    if (isHandleCacheMedicine) {
                        mMedicineList.clear();

                        //通知添加药材界面恢复默认值
                        sendChatMainPositionBroadcast(true);
                    } else {
                        sendChatMainPositionBroadcast(false);
                    }
                    finishActivity();
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    mConfirmDialog.dismiss();
                    numKeyboard.clearFocus();
                    //重置编辑模式
                    resetToNormalMode();

                    saveMedicineData();
                }
            });
            mConfirmDialog.show();
        } else {
            EventBusUtils.post(setEventBeanMsg());
            sendChatMainPositionBroadcast(false);
            finishActivity();
        }
    }

    /**
     * 发送到交流主界面的广播，判断显示哪一个fragment
     */
    private void sendChatMainPositionBroadcast(boolean isReplace) {
        Intent intent = new Intent(BroadcastAction.SET_CHAT_MAIN_POSITION);
        intent.putExtra(ChatMainActivity.POSITION, 2);
        intent.putExtra(ChatMainActivity.ISREPLACE, isReplace);
        sendBroadcast(intent);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        showInputKeyBoard();
    }

    @Override
    public void onBackPressed() {
        doBackLogic();
    }

    /**
     * 启动线程
     */
    private void startThread() {
        synchronized (lock) {
            try {
                if (countPriceThread != null) {
                    countPriceThread.isStop = true;
                    countPriceThread = null;
                }

                countPriceThread = new CountPriceThread();
                countPriceThread.start();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 停止线程
     */
    private void stopThread() {
        synchronized (lock) {
            try {
                if (countPriceThread != null) {
                    countPriceThread.isStop = true;
                    countPriceThread = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 实时计算单幅药价的子线程
     */
    class CountPriceThread extends Thread {
        private volatile boolean isStop = false;

        @Override
        public void run() {
            if (!isStop) {
                Message msg = new Message();
                msg.what = COUNT_MEDICINE_PRICE;
                msg.obj = countMedicinePrice(mMedicineList);
                mHandler.sendMessage(msg);
            }
        }
    }

    /**
     * 生成，如 “共 5 味”的样式（5为红色，且字体较大）
     *
     * @param leftContent
     * @param centerContent
     * @param rightContent
     * @return
     */
    public SpannableStringBuilder createSpannable(String leftContent, String centerContent, String rightContent) {
        SpannableStringBuilder ssb = new SpannableStringBuilder(leftContent);
        if (!TextUtils.isEmpty(centerContent)) {
            ssb.append("" + centerContent + "");
            ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_red_ef4d3b)), leftContent.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            ssb.setSpan(getResources().getDimension(R.dimen.textsize_16), leftContent.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        if (!TextUtils.isEmpty(rightContent)) {
            ssb.append(rightContent);
        }
        return ssb;
    }

    /**
     * 生成，如 “丁公藤有毒”的样式（丁公藤为红色）
     *
     * @param content 分险提示语：如“丁公藤有毒”
     * @param keyWord 分险提示中的药名： 如"丁公藤"
     * @return
     */
    public SpannableStringBuilder createToxiticySpannable(String content, String keyWord) {
        SpannableStringBuilder ssb = new SpannableStringBuilder(content);
        if (!TextUtils.isEmpty(keyWord)) {
            String[] keyArray = keyWord.split(",");
            if (keyArray != null) {
                for (String s : keyArray) {
                    if (content.contains(s)) {
                        int index = 0;
                        while (content.indexOf(s, index) >= 0) {
                            int start = content.indexOf(s, index);
                            int end = start + s.length();
                            ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_red_ef4d3b)), start, end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
//                  ssb.setSpan(getResources().getDimension(R.dimen.textsize_16), leftContent.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                            index = start + s.length();
                        }
                    }
                }
            }
        }

        return ssb;
    }

    /**
     * 刷新煎法数据
     */
    private void refreshBoilWayData() {
        // 请求煎法列表数据
        HashMap<String, String> params = new HashMap<>();
        getBoilWayCallBack = addHttpPostRequest(HttpUrlManager.BOIL_WAY, params, BoilWayBean.class, this);
    }

}
