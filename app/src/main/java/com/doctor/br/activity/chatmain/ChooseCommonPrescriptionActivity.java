package com.doctor.br.activity.chatmain;

import android.os.Bundle;
import androidx.core.view.ViewCompat;
import androidx.viewpager.widget.ViewPager;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.adapter.InvitePagerAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.fragment.chatmain.ClassicPrescriptionFragment;
import com.doctor.br.fragment.chatmain.CommonPrescriptionFragment;
import com.doctor.br.fragment.chatmain.PrescriptionHistoryFragment;
import com.doctor.br.utils.UIHelper;
import com.doctor.br.utils.UserPreferenceUtils;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.network.HttpRequestTask;
import org.newapp.ones.base.network.OkHttpUtils;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.LogUtils;

import butterknife.BindView;
import butterknife.OnClick;
/**
 * <AUTHOR>
 * @version 4.0.3
 * @project BrZhongYiAndroid
 * @description  选择常用方界面
 * @createTime 2017/11/30.
 */
public class ChooseCommonPrescriptionActivity extends ActionBarActivity {


    @BindView(R2.id.rl_title)
    RelativeLayout rlTitle;
    @BindView(R2.id.view_indicator)
    View viewIndicator;
    @BindView(R2.id.viewpager)
    ViewPager viewpager;
    @BindView(R2.id.tv_common_prescription)
    TextView tvCommonPrescription;
    @BindView(R2.id.tv_prescription_history)
    TextView tvPrescriptionHistory;
    @BindView(R.id.tv_classic_prescription)
    TextView tvClassicPrescription;
    @BindView(R2.id.hint_rl)
    RelativeLayout mHintRl;
    @BindView(R2.id.hint_tv)
    TextView mHintTv;
    @BindView(R2.id.colse_hint)
    ImageView mColseHint;
    private InvitePagerAdapter mainAdapter;
    private CommonPrescriptionFragment commonPrescriptionFragment;
    private PrescriptionHistoryFragment prescriptionHistoryFragment;
    private int commonPrescriptionWidth;
    private int prescriptionHistoryWidth;
    private int classicPrescriptionWidth;//经典方标题的宽度
    private float tabPadding;//历史药方距离常用方的距离
    //private String userId;
    public static final String RESULT_EXTRA_TYPE ="result_extra_type";//返回是历史药方，还是常用方的判断
    public static final int RESULT_COMMON_PRESCRIPTION = 2;//常用方
    public static final int RESULT_PRESCRIPTION_HISTORY = 3;//历史药方

    public static final String RESULT_EXTRA_BEAN = "result_extra_bean";//存储的javabean
    
    // 用于保存提示关闭状态的常量 - 分别记录经典方和常用方
    private static final String PREF_KEY_CLASSIC_HINT_CLOSED = "classic_prescription_hint_closed";
    private static final String PREF_KEY_COMMON_HINT_CLOSED = "common_prescription_hint_closed";
    
    private String drugType;
    private String drugProviderId;
    private ClassicPrescriptionFragment classicPrescriptionFragment;

    //private String patientId;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_choose_common_prescription);
        setActionBarTitle("药方模板");
        //patientId = getIntent().getStringExtra(PublicParams.PATIENT_USRE_ID);
        drugType = getIntent().getStringExtra(PublicParams.DRUG_TYPE);//药材类型 Y/K 颗粒或饮片
        drugProviderId = getIntent().getStringExtra(PublicParams.DRUG_PROVIDERID);//厂商id
        getTabParams();
        init();
        setListener();

    }

    /**
     * 获取TAB的一些字段信息
     */
    private void getTabParams() {//获取tab需要的一些参数
        tvClassicPrescription.measure(0,0);
        classicPrescriptionWidth = tvClassicPrescription.getMeasuredWidth();
        tvCommonPrescription.measure(0,0);
        commonPrescriptionWidth = tvCommonPrescription.getMeasuredWidth();
        tvPrescriptionHistory.measure(0,0);
        prescriptionHistoryWidth = tvPrescriptionHistory.getMeasuredWidth();

        tabPadding = getResources().getDimension(R.dimen.margin_tab_common_prescription);

        LogUtils.i(OkHttpUtils.class, commonPrescriptionWidth +"..........."+ prescriptionHistoryWidth+"...."+tabPadding);
    }

    protected void init() {//初始化
        setViewPagerAdapter();
        //刚进来需要更新标题
        updateTitle();

        setListener();

    }

    /**
     * 设置viewpager
     */
    private void setViewPagerAdapter() {
        mainAdapter = new InvitePagerAdapter(getSupportFragmentManager());

        classicPrescriptionFragment = ClassicPrescriptionFragment.getInstance();
        mainAdapter.addFragment(classicPrescriptionFragment, getResources().getString(R.string.title_classic_prescription));
        commonPrescriptionFragment = CommonPrescriptionFragment.getInstance();
        mainAdapter.addFragment(commonPrescriptionFragment, getResources().getString(R.string.title_common_prescription));
        prescriptionHistoryFragment =PrescriptionHistoryFragment.getInstance(drugType,drugProviderId);
        mainAdapter.addFragment(prescriptionHistoryFragment, getResources().getString(R.string.title_prescription_history));
        //1.给ViewPager填充Fragment
        viewpager.setAdapter(mainAdapter);
        viewpager.setOffscreenPageLimit(2);
        viewpager.setCurrentItem(1);//默认打开常用方
        
        // 检查常用方的提示是否已被关闭（默认打开常用方页面）
        boolean isCommonHintClosed = UserPreferenceUtils.getUserBoolean(this, PREF_KEY_COMMON_HINT_CLOSED, false);
        if (!isCommonHintClosed) {
            mHintRl.setVisibility(View.VISIBLE);
        } else {
            mHintRl.setVisibility(View.GONE);
        }
    }


    private void setListener() {//设置监听
        viewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                LogUtils.i(HttpRequestTask.class, "onPageScrolled:  position: " + position + "  positionOffset:" + positionOffset
                        + "  positionOffsetPixels: " + positionOffsetPixels);

                //1.计算线需要滚动到的距离
                float maxMoveLength = tabPadding+commonPrescriptionWidth;
                float targetX =maxMoveLength*position+ positionOffsetPixels * maxMoveLength * 1.0f / DensityUtils.getScreenWidth(AppContext.getContext());
                //2.让线滚动到指定的距离
                ViewCompat.setTranslationX(viewIndicator, targetX);
            }

            @Override
            public void onPageSelected(int position) {
                updateTitle();
                if(prescriptionHistoryFragment!=null && position == 2){
                    prescriptionHistoryFragment.onHistoryFragmentSelect();
                }
                if(position == 0){//判断是否显示搜索按钮
                    setActionBarRightBtnImg(R.drawable.search);
                }else{
                    setActionBarRightBtnImg(0);
                }
                
                // 显示或隐藏提示标语 - 分别检查经典方和常用方的关闭状态
                if (position == 0) { // 经典方
                    boolean isClassicHintClosed = UserPreferenceUtils.getUserBoolean(ChooseCommonPrescriptionActivity.this, PREF_KEY_CLASSIC_HINT_CLOSED, false);
                    mHintRl.setVisibility(isClassicHintClosed ? View.GONE : View.VISIBLE);
                } else if (position == 1) { // 常用方
                    boolean isCommonHintClosed = UserPreferenceUtils.getUserBoolean(ChooseCommonPrescriptionActivity.this, PREF_KEY_COMMON_HINT_CLOSED, false);
                    mHintRl.setVisibility(isCommonHintClosed ? View.GONE : View.VISIBLE);
                } else { // 历史药方
                    mHintRl.setVisibility(View.GONE);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

    }

    /**
     * 更新标题的状态
     */
    private void updateTitle() {
        //1.获取当前ViewPager选择的页
        int currentItem = viewpager.getCurrentItem();

        //2.根据当前页来设置标题的颜色
        tvClassicPrescription.setSelected(currentItem == 0);
        tvClassicPrescription.getPaint().setFakeBoldText(currentItem == 0?true:false);

        tvCommonPrescription.setSelected(currentItem == 1);
        tvCommonPrescription.getPaint().setFakeBoldText(currentItem == 1?true:false);

        tvPrescriptionHistory.setSelected(currentItem == 2);
        tvPrescriptionHistory.getPaint().setFakeBoldText(currentItem == 2?true:false);


        RelativeLayout.LayoutParams lineParams = (RelativeLayout.LayoutParams) viewIndicator.getLayoutParams();
        if(currentItem == 0){//设置滑动条的宽度
            lineParams.width = classicPrescriptionWidth;
        }
        if(currentItem == 1){
            lineParams.width = commonPrescriptionWidth;
        }else if(currentItem == 2){
            lineParams.width = prescriptionHistoryWidth;
        }
        viewIndicator.setLayoutParams(lineParams);

    }

    @Override
   @OnClick({R.id.tv_classic_prescription,R.id.tv_common_prescription,R.id.tv_prescription_history,R2.id.colse_hint})
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_classic_prescription://经典方
                viewpager.setCurrentItem(0);
                break;
            case R.id.tv_common_prescription://常用方
                viewpager.setCurrentItem(1);
                break;
            case R.id.tv_prescription_history://历史药方
                viewpager.setCurrentItem(2);
                break;
            case R2.id.colse_hint://关闭提示
                mHintRl.setVisibility(View.GONE);
                // 根据当前页面保存对应的关闭状态
                int currentPosition = viewpager.getCurrentItem();
                if (currentPosition == 0) { // 经典方
                    UserPreferenceUtils.putUserBoolean(this, PREF_KEY_CLASSIC_HINT_CLOSED, true);
                } else if (currentPosition == 1) { // 常用方
                    UserPreferenceUtils.putUserBoolean(this, PREF_KEY_COMMON_HINT_CLOSED, true);
                }
                break;
        }
    }

    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        if(viewpager!=null && viewpager.getCurrentItem() == 0){//添加点击事件
            UIHelper.openSearchClassicPrescription(this);
        }

    }

    @Override
    protected void onDestroy() {

        prescriptionHistoryFragment = null;
        commonPrescriptionFragment = null;
        classicPrescriptionFragment = null;
        super.onDestroy();
    }
}
