package com.doctor.br.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.doctor.br.activity.chatmain.ChatMainActivity;
import com.doctor.br.adapter.ContactsAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.ContactsResult;
import com.doctor.br.bean.PatientTagsBean;
import com.doctor.br.db.entity.Contacts;
import com.doctor.br.db.entity.Session;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.view.FlowLayout;
import com.doctor.greendao.gen.SessionDao;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 搜索联系人的页面
 * @createTime 2017/8/23
 */
public class ContactsSearchActivity extends ActionBarActivity implements TextView.OnEditorActionListener, AdapterView.OnItemClickListener, EmptyView.OnReloadListener {
    @BindView(R.id.lv_contacts_search)
    ListView lvContacts;
    @BindView(R.id.mEmptyView)
    EmptyView mEmptyView;

    @BindView(R.id.container)
    ConstraintLayout container;
    @BindView(R.id.tags_layout)
    FlowLayout tagsLayout;
    @BindView(R.id.more_img)
    ImageView moreImg;
    private boolean isMoreShow;//是否为展开状态

    private SessionDao sessionDao;
    private List<Contacts> contactsList;
    private ContactsAdapter contactsAdapter;

    private PatientTagsBean localPatientBean;//网络请求标签列表返回值

    private boolean currentSearchIsTag;//当前搜索是否为tag

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarType(ActionBarView.ACTIONBAR_SEARCH);
        setActionBarContentView(R.layout.activity_contacts_search);
        setActionBarSearchInputHint("姓名、手机号、诊断、标签");
        setOnSearchInputEditorActionListener(this);
        sessionDao = AppContext.getInstances().getDaoSession().getSessionDao();
        initViews();
        getPatientTags();
    }

    /**
     * 初始化View
     */
    private void initViews() {
        mEmptyView.setOnReloadListener(this);
        contactsList = new ArrayList<Contacts>();

        contactsAdapter = new ContactsAdapter(mContext, contactsList, R.layout.item_layout_contacts, true);
        lvContacts.setAdapter(contactsAdapter);
        lvContacts.setOnItemClickListener(this);

        moreImg.setOnClickListener(this);
        getBaseActionBar().getActionbarSearchInput().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getBaseActionBar().setActionbarSearchInputText("");
                if (localPatientBean == null) {
                    getPatientTags();
                    return;
                }
                if (localPatientBean.getDatas() == null || localPatientBean.getDatas().size() == 0) {
                    container.setVisibility(View.GONE);
                    return;
                }
                container.setVisibility(View.VISIBLE);
                contactsList.clear();
                contactsAdapter.notifyDataSetChanged();
            }
        });
    }

    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        currentSearchIsTag = false;
        search(getActionbarSearchInputText());
        hideKeyBoard(getActionbarSearchInput());
    }

    @Override
    public void onClick(View view) {
        if (view instanceof TextView && view.getTag(R.id.img_url) != null && view.getTag(R.id.img_url) instanceof String) {
            String text = (String) view.getTag(R.id.img_url);
            getBaseActionBar().setActionbarSearchInputText(text);
            currentSearchIsTag = true;
            search(getActionbarSearchInputText());
            return;
        }
        switch (view.getId()) {
            case R.id.more_img:
                if (isMoreShow) {
                    moreImg.setRotation(0);
                    isMoreShow = false;
                } else {
                    moreImg.setRotation(180f);
                    isMoreShow = true;
                }
                refreshTagVisible();
                break;
            default:
                break;
        }
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        if (actionId == EditorInfo.IME_ACTION_SEARCH) {
            currentSearchIsTag = false;
            search(getActionbarSearchInputText());
            hideKeyBoard(getActionbarSearchInput());
            return true;
        }
        return false;
    }

    @Override
    public void onReload() {
        search(getActionbarSearchInputText());
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        if (position < 0 || position >= contactsList.size()) {
            return;
        }
        Contacts contacts = contactsList.get(position);
        SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_ID, contacts.getRosterUserId());
        SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_HEAD_URL, contacts.getHeadImgUrl());
        Session session = sessionDao.load(contacts.getLocalId());
        if (session != null) {
            session.setNotReadCount(0);
            sessionDao.update(session);
        }
        Intent intent = new Intent(mContext, ChatMainActivity.class);
        intent.putExtra(PublicParams.PATIENT_USRE_ID, contacts.getRosterUserId());
        intent.putExtra(PublicParams.PATIENT_USRE_NNAME, contacts.getNickName());
        intent.putExtra(PublicParams.PATIENT_USRE_MOBILE, contacts.getMobile());
        startActivity(intent);
    }

    /**
     * 更新是否显示多余标签的状态
     */
    private void refreshTagVisible() {
        if (isMoreShow) {
            for (int i = 0; i < tagsLayout.getChildCount(); i++) {
                if (i >= 20) {
                    tagsLayout.getChildAt(i).setVisibility(View.VISIBLE);
                }
            }
        } else {
            for (int i = 0; i < tagsLayout.getChildCount(); i++) {
                if (i >= 20) {
                    tagsLayout.getChildAt(i).setVisibility(View.GONE);
                }
            }
        }
    }


    /**
     * 根据关键字搜索患者
     *
     * @param keyword 搜索关键字
     */
    private void search(String keyword) {
        if (!TextUtils.isEmpty(keyword.trim())) {
            HashMap<String, String> params = new HashMap<>();
            if (currentSearchIsTag) {
                params.put("searchType", "tag");
            }
            params.put("keyword", keyword);
            addHttpPostRequest(HttpUrlManager.GET_CONTACTS_BY_KEYWORD, params, ContactsResult.class, this);
        }
    }

    /**
     * 网络请求获取患者标签
     */
    private void getPatientTags() {
        addHttpPostRequest(HttpUrlManager.GET_PATIENT_LABEL, null, PatientTagsBean.class, this);
    }


    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.GET_PATIENT_LABEL://获取所有患者标签
                if (result.isRequestSuccessed()) {
                    localPatientBean = (PatientTagsBean) result.getBodyObject();
                    addTagView();
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.GET_CONTACTS_BY_KEYWORD://搜索结果
                contactsList.clear();
                if (result.isRequestSuccessed()) {
                    ContactsResult contactsResult = (ContactsResult) result.getBodyObject();
                    if (contactsResult != null) {
                        if (contactsResult.isTag()) {
                            refreshTagView();
                        }
                        if (contactsResult.getItems() != null) {
                            contactsList.addAll(contactsResult.getItems());
                        }
                    }
                    if (contactsList.size() == 0) {
                        mEmptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        mEmptyView.setEmptyText("未搜索到相关患者");
                    } else {
                        mEmptyView.hide();
                    }
                    container.setVisibility(View.GONE);
                } else {
                    mEmptyView.show();
                }
                contactsAdapter.notifyDataSetChanged();
                break;
            default:
                break;
        }
    }

    /**
     * 网络请求获取标签后添加到tagslayout中
     */
    private void addTagView() {
        if (localPatientBean == null || localPatientBean.getDatas() == null || localPatientBean.getDatas().size() == 0) {
            container.setVisibility(View.GONE);
            return;
        }
        container.setVisibility(View.VISIBLE);
        if (tagsLayout.getChildCount() > 0) {
            tagsLayout.removeAllViews();
        }
        for (int i = 0; i < localPatientBean.getDatas().size(); i++) {
            if (i >= 40) {
                break;
            }
            TextView textView = (TextView) LayoutInflater.from(this)
                    .inflate(R.layout.item_search_contacts_header,
                            tagsLayout, false);
            textView.setText(localPatientBean.getDatas().get(i));
            //设置标志
            textView.setTag(R.id.img_url, localPatientBean.getDatas().get(i));
            textView.setOnClickListener(this);
            if (i >= 20 && !isMoreShow) {
                textView.setVisibility(View.GONE);
            }
            tagsLayout.addView(textView);
        }
    }

    /**
     * 如果搜索的关键字是标签的话，刷新标签列表
     */
    private void refreshTagView() {
        boolean isContains = false;//是否已经存在
        TextView delTv = null;//需要删除的textview
        //查找是否存在
        for (int i = 0; i < tagsLayout.getChildCount(); i++) {
            delTv = (TextView) tagsLayout.getChildAt(i);
            if (getActionbarSearchInputText().equals(delTv.getText().toString())) {
                if (i == 0) {
                    //第一个标签位置不用改变
                    return;
                }
                isContains = true;
                break;
            }
        }
        //删除已经存在的标签
        if (isContains) {
            tagsLayout.removeView(delTv);
        } else if (tagsLayout.getChildCount() >= 40) {
            tagsLayout.removeViewAt(39);
        }
        //添加新标签到第一个
        TextView textView = (TextView) LayoutInflater.from(this)
                .inflate(R.layout.item_search_contacts_header,
                        tagsLayout, false);
        textView.setText(getActionbarSearchInputText());
        //设置标志
        textView.setTag(R.id.img_url, getActionbarSearchInputText());
        textView.setOnClickListener(this);
        tagsLayout.addView(textView, 0);
        //判断如果为收起状态，多余20的要隐藏
        refreshTagVisible();
    }

}
