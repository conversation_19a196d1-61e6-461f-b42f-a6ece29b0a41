package com.doctor.br.activity.mine;

import android.os.Bundle;
import com.google.android.material.tabs.TabLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.doctor.br.bean.event.ModifyPhoneRefreshStateEvent;
import com.doctor.br.fragment.mine.PasswordModifyPhoneFragment;
import com.doctor.br.fragment.mine.SmsModifyPhoneFragment;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;

import butterknife.BindView;

/**
 * 类描述：更换手机号
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/4
 */

public class ModifyPhoneActivity extends ActionBarActivity {
    //界面下的控件
    @BindView(R.id.tab_layout)
    TabLayout tabLayout;
    @BindView(R.id.view_pager)
    ViewPager viewPager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_modify_phone);
        initView();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("更换手机号");
        tabLayout.setupWithViewPager(viewPager);

        viewPager.setAdapter(new FragmentPagerAdapter(getSupportFragmentManager()) {
            @Override
            public Fragment getItem(int position) {
                if (position == 0) {
                    return new PasswordModifyPhoneFragment();
                }
                return new SmsModifyPhoneFragment();
            }

            @Override
            public int getCount() {
                return 2;
            }

            @Override
            public CharSequence getPageTitle(int position) {
                if (position == 0) {
                    return "密码方式";
                }
                return "验证码方式";
            }
        });
    }

    /**
     * 重置 "密码方式更换手机号界面" 和 "验证码方式更换手机号界面"
     *
     * @sender {@link ModifyPhoneActivity#refreshFragment(int)}
     * @receiver {@link PasswordModifyPhoneFragment#refreshState(ModifyPhoneRefreshStateEvent)}
     * @receiver {@link SmsModifyPhoneFragment#refreshState(ModifyPhoneRefreshStateEvent)}
     */
    private void refreshFragment(int position) {
        if (0 == position) {
            EventBusUtils.post(new ModifyPhoneRefreshStateEvent(true, false));
        } else if (1 == position) {
            EventBusUtils.post(new ModifyPhoneRefreshStateEvent(false, true));
        }
    }
}
