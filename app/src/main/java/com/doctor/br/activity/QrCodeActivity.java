package com.doctor.br.activity;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import android.text.TextUtils;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.DoctorInfoBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.BitmapUtils;
import com.doctor.br.utils.MediaStoreManager;
import com.doctor.br.utils.MyPathManager;
import com.doctor.br.utils.ShareUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.MD5Utils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.EmptyView;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 二维码邀请页面
 * @createTime 2017/8/28
 */

public class QrCodeActivity extends ActionBarActivity {

    private static final int CODE_PERMISSION = 102;
    @BindView(R.id.siv_photo)
    ImageView sivPhoto;
    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.ll_person)
    LinearLayout llPerson;
    @BindView(R.id.iv_qr_code)
    ImageView ivQrCode;
    @BindView(R.id.ll_weixin)
    LinearLayout llWeixin;
    @BindView(R.id.ll_attention)
    LinearLayout llAttention;
    @BindView(R.id.layout_card)
    RelativeLayout layoutCard;
    @BindView(R.id.mEmptyView)
    EmptyView mEmptyView;
    @BindView(R.id.btn_share_card)
    Button btnShareCard;
    private RequestCallBack doctorDetailsCallback;
    private String weiXinUrl;
    private DoctorInfoBean doctorInfoBean;
    private String name;
    private String headUrl;
    private String userId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_qr_code);
        setActionBarTitle("电子名片");
        initViews();
    }

    /**
     * 初始化view
     */
    private void initViews() {
        AppContext appContext = AppContext.getInstances();
        mEmptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
            @Override
            public void onReload() {
                requestDoctorDetails();
            }
        });
        userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
       /* userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        name = SharedPreferenceUtils.getString(this, PublicParams.USER_NAME);
        headUrl = SharedPreferenceUtils.getString(this, PublicParams.USER_HEAD_URL);
        weiXinUrl = SharedPreferenceUtils.getString(this, PublicParams.USER_WEIXIN_URL);
        if(TextUtils.isEmpty(name) || TextUtils.isEmpty(weiXinUrl) || TextUtils.isEmpty(headUrl)){*/
        requestDoctorDetails();
       /* }else{
            initData(null, false);
        }*/

    }

    /**
     * 网络请求获取大夫详情
     */
    private void requestDoctorDetails() {
        Map<String, String> map = new HashMap<>();
        doctorDetailsCallback = addHttpPostRequest(HttpUrlManager.DOCTOR_INFO, map, DoctorInfoBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.DOCTOR_INFO:
                if (result.isRequestSuccessed()) {
                    DoctorInfoBean doctorInfoBean = (DoctorInfoBean) result.getBodyObject();
                    if (doctorInfoBean == null) return;
                    if (!TextUtils.isEmpty(doctorInfoBean.getName())) {
                        name = doctorInfoBean.getName();
                        SharedPreferenceUtils.putString(this, PublicParams.USER_NAME, name);
                    }
                    if (!TextUtils.isEmpty(doctorInfoBean.getHandleUrl())) {
                        headUrl = doctorInfoBean.getHandleUrl();
                        SharedPreferenceUtils.putString(this, PublicParams.USER_HEAD_URL, headUrl);
                    }
                    if (!TextUtils.isEmpty(doctorInfoBean.getWeiXinUrl())) {
                        weiXinUrl = doctorInfoBean.getWeiXinUrl();
                        SharedPreferenceUtils.putString(this, PublicParams.USER_WEIXIN_URL, weiXinUrl);
                    }
                    SharedPreferenceUtils.putString(this, PublicParams.USER_TYPE, doctorInfoBean.getIsDiTui());
                    initData(doctorInfoBean, false);
                    mEmptyView.hide();
                } else {
                    mEmptyView.show();
                }

                break;
        }
    }

    /**
     * 初始化数据
     *
     * @param doctorInfoBean
     * @param isLoadData
     */
    private void initData(DoctorInfoBean doctorInfoBean, boolean isLoadData) {
        this.doctorInfoBean = doctorInfoBean;
        //if (isLoadData && (TextUtils.isEmpty(name) || TextUtils.isEmpty(weiXinUrl) || TextUtils.isEmpty(headUrl))) {
        // requestDoctorDetails();
        // } else {
        View view = LayoutInflater.from(this).inflate(R.layout.view_save_card, null);
        setActionBarRightView(view);
        tvName.setText(name);
        mGlideUtils.loadCircleImage(headUrl, this, sivPhoto, R.drawable.default_head_img);
        mGlideUtils.loadImage(weiXinUrl, this, ivQrCode, R.drawable.default_qr_code, new LoadImageListener(isLoadData));
        btnShareCard.setVisibility(View.VISIBLE);
        // }
    }

    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {//有权限
            getAndSaveCurrentImage();
        } else {
            //申请权限
            requestPermissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, CODE_PERMISSION,"必然中医需要使用存储权限用于保存文件，是否同意使用？");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if(requestCode==CODE_PERMISSION){
            if(grantResults.length>0&&grantResults[0]== PackageManager.PERMISSION_GRANTED){
                getAndSaveCurrentImage();
            }
        }
    }

    /**
     * 保存二维码图片到相册
     *
     * @param context
     * @param bmp
     */
    public void saveImageToGallery(Context context, Bitmap bmp) {
        // 首先保存图片
        String file = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).getAbsoluteFile().getAbsolutePath();//注意小米手机必须这样获得public绝对路径
        String filesName = PublicParams.CACHE_DIR_VCARD;
        File appDir = new File(file, filesName);
        if (!appDir.exists()) {
            appDir.mkdirs();
        }
        String fileName = "brzhongyi_" + MD5Utils.MD5Lower16(userId) + ".jpg";
        final File currentFile = new File(appDir, fileName);

        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(currentFile);
            bmp.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            fos.flush();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        // 最后通知图库更新
        context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                Uri.fromFile(new File(currentFile.getPath()))));
        ivQrCode.post(new Runnable() {
            @Override
            public void run() {
                ToastUtils.showShortMsg(QrCodeActivity.this, "已保存至本地相册");
            }
        });
    }

    /**
     * 获取和保存当前屏幕的截图
     */
    private void getAndSaveCurrentImage() {
        //1.构建Bitmap
        WindowManager windowManager = getWindowManager();
        Display display = windowManager.getDefaultDisplay();
        int w = display.getWidth();
        int h = display.getHeight();

        Bitmap bmp = null;

        //2.获取屏幕
//        View decorview = this.getWindow().getDecorView();
        layoutCard.setDrawingCacheEnabled(true);
        bmp = layoutCard.getDrawingCache();
        if (bmp == null) {
            bmp = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_4444);
        }

        if (bmp == null) {
            ToastUtils.showShortMsg(QrCodeActivity.this, "保存至本地相册失败");
            return;
        }
        String userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        Uri saveUri;
        String extend = "";
        int index = 0;
        do {
            String fileName = "brzhongyi_" + MD5Utils.MD5Lower16(userId) + extend + ".png";
            saveUri = MediaStoreManager.DIYIconPack.savedUri(fileName, "card");
            index++;
            extend = "_" + index;
        } while (saveUri == null);
        if (saveUri == null) {
            ToastUtils.showShortMsg(this, "保存至本地相册失败");
            return;
        }
        boolean saveSuccess = BitmapUtils.saveBitmap(this, saveUri, bmp, Bitmap.CompressFormat.JPEG, 99);
        if (saveSuccess) {
            ToastUtils.showShortMsg(this, "已保存至本地相册");
        } else {
            ToastUtils.showShortMsg(this, "保存至本地相册失败");
        }
    }

    /**
     * 获取SDCard的目录路径功能
     *
     * @return
     */
    private String getSDCardPath() {
        String sdcardDir = null;
        //判断SDCard是否存在
        boolean sdcardExist = Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED);
        if (sdcardExist) {
            sdcardDir = MyPathManager.getSaveDir("");
        }
        return sdcardDir.toString();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    @OnClick(R.id.btn_share_card)
    public void onViewClicked() {
        shareToWeixin();
    }

    /**
     * 分享到微信平台
     */
    private void shareToWeixin() {
        if (TextUtils.isEmpty(name) && doctorInfoBean != null) {
            name = doctorInfoBean.getName();
        }
        if (TextUtils.isEmpty(headUrl) && doctorInfoBean != null) {
            headUrl = doctorInfoBean.getHandleUrl();
        }
        new ShareUtils(this).shareToWeiXin("我是" + name + "大夫，欢迎关注我的中医工作室！", "通过微信关注我的工作室，直接向我发起在线咨询。",
                headUrl, BaseConfig.getShareInvitePatientUrl() + userId);
    }

    class LoadImageListener implements RequestListener {

        private boolean isLoadData;

        public LoadImageListener(boolean isLoadData) {
            this.isLoadData = isLoadData;
        }

        @Override
        public boolean onLoadFailed(@Nullable GlideException e, Object model, Target target, boolean isFirstResource) {
            if (isLoadData) {
                requestDoctorDetails();
            }
            return false;
        }

        @Override
        public boolean onResourceReady(Object resource, Object model, Target target, DataSource dataSource, boolean isFirstResource) {
            return false;
        }
    }


    @Override
    protected void onDestroy() {
        cancelRequest(doctorDetailsCallback);
        super.onDestroy();
    }
}
