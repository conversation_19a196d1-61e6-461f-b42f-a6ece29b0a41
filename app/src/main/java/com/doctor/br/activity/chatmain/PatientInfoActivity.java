package com.doctor.br.activity.chatmain;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.activity.ActionBarWebViewActivity;
import com.doctor.br.adapter.DossierAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.AuthState;
import com.doctor.br.bean.AuthStateResult;
import com.doctor.br.bean.DossierBean;
import com.doctor.br.bean.PatientMsgBean;
import com.doctor.br.bean.PopItem;
import com.doctor.br.bean.event.PatientInfoChangeEvent;
import com.doctor.br.db.entity.Contacts;
import com.doctor.br.db.entity.Session;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.NettyMsgNotify;
import com.doctor.br.netty.NotifyType;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.UIHelper;
import com.doctor.br.view.BottomPopWindow;
import com.doctor.br.view.FlowLayout;
import com.doctor.br.view.InputDialog;
import com.doctor.br.view.RemarkInputDialog;
import com.doctor.br.view.refreshlayout.OnRefreshListener;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.greendao.gen.ContactsDao;
import com.doctor.greendao.gen.SessionDao;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.listener.OnButtonClickListener;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.ActivityManager;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.EmptyView;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bingoogolapple.androidcommon.adapter.BGAOnItemChildClickListener;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 患者信息页面
 * @createTime 2017/8/23
 */
public class PatientInfoActivity extends ActionBarActivity implements OnRefreshListener, BGAOnItemChildClickListener {
    private final int REQUEST_CODE_WEBVIEW = 101;
    private final String ADD_PATIENT_LABEL = "add";//增加患者备注
    private final String DEL_PATIENT_LABEL = "del";//删除患者备注
    private final String UPDATE_PATIENT_LABEL = "update";//修改患者备注
    @BindView(R2.id.iv_head)
    ShapeImageView ivHead;
    @BindView(R2.id.tv_name)
    TextView tvName;
    @BindView(R2.id.iv_sex)
    ImageView ivSex;
    @BindView(R2.id.tv_age)
    TextView tvAge;
    @BindView(R2.id.tv_order_count)
    TextView tvOrderCount;
    @BindView(R2.id.tv_pay_count)
    TextView tvPayCount;
    @BindView(R2.id.tv_order_price)
    TextView tvOrderPrice;
    @BindView(R2.id.tv_patient_name)
    TextView tvPatientName;
    @BindView(R2.id.tv_name_tip)
    TextView tvNameTip;
    @BindView(R2.id.tv_filter)
    TextView tvFilter;
    @BindView(R2.id.rv_data)
    RecyclerView rvData;
    @BindView(R2.id.refreshLayout)
    RefreshLayout refreshLayout;
    @BindView(R2.id.textView)
    TextView textView;
    @BindView(R2.id.ll_layout_order)
    LinearLayout llLayoutOrder;
    @BindView(R2.id.line1)
    View line1;
    @BindView(R2.id.rl_filter)
    RelativeLayout rlFilter;
    @BindView(R2.id.line2)
    ImageView line2;
    @BindView(R2.id.mEmptyView)
    EmptyView mEmptyView;
    @BindView(R2.id.btn_send_msg)
    TextView btnSendMsg;
    @BindView(R2.id.empty_view_list)
    EmptyView emptyViewList;
    @BindView(R.id.sex_age_ll)
    LinearLayout sexAgeLl;
    @BindView(R.id.tv_remark)
    TextView tvRemark;
    @BindView(R.id.iv_edit_remark)
    ImageView ivEditRemark;
    @BindView(R.id.content_container)
    FlowLayout contentContainer;
    @BindView(R.id.complaint_layout)
    LinearLayout complaintLayout;
    private BottomPopWindow mBottomPopWindow;
    private List<PopItem> popItems;
    private RequestCallBack mRequestCallBack;
    private String patientId;
    private String mCurrentPatientId;
    private int currentPage = 1;
    private int pageSize = 20;
    private DossierAdapter dossierAdapter;
    private boolean isFinishPatients;
    private boolean isFinishPatientInfo;
    private boolean isDossierData;
    private ConfirmDialog addBlackDialog;
    private ConfirmDialog delLabelDialog;
    private InputDialog updateLabelDialog;
    private RemarkInputDialog addRemarkDialog;
    private ContactsDao contactsDao;
    private SessionDao sessionDao;
    private PatientMsgBean.PatientsBean patientsBean;
    private boolean isFromChatMain;
    private String userId;
    private int requestCount;
    private String fromPage;
    private RequestCallBack requestAuthStateCallBack;
    private RequestCallBack getDossierDataCallBack;
    private RequestCallBack getPatientInfoCallBack;
    private RequestCallBack getPatientsCallBack;
    private RequestCallBack addToBlackListCallBack;
    private RequestCallBack addPatientsRemarkCallBack;
    private RequestCallBack updatePatientLabelCallBack;
    private String nickName;
    private String loaclType;
    private String localTag;
    private String localOldTag;//当前标签
    private String loaclRemark;
    private ImageView blackImg;
    private TextView blackText;
    private LinearLayout llAddBlacklist;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_patient_info);
        setActionBarTitle("患者信息");
        getBaseActionBar().setActionBarChangeListener(this);
        setActionBarBottomLineVisibility(false);
        contactsDao = AppContext.getInstances().getDaoSession().getContactsDao();
        sessionDao = AppContext.getInstances().getDaoSession().getSessionDao();
        patientId = getIntent().getStringExtra(PublicParams.PATIENT_USRE_ID);
        userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        isFromChatMain = getIntent().getBooleanExtra(PublicParams.IS_FROM_CHAT_MAIN_ACTIVITY, false);
        fromPage = getIntent().getStringExtra(PublicParams.WEBVIEW_FROM_PAGE);
        mCurrentPatientId = patientId;
        initView();
        initData();
    }

    /**
     * 初始化View
     */
    private void initView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        rvData.setLayoutManager(layoutManager);
        popItems = new ArrayList<>();
        dossierAdapter = new DossierAdapter(rvData);
        dossierAdapter.setOnItemChildClickListener(this);
        rvData.setAdapter(dossierAdapter);
        refreshLayout.setOnRefreshListener(this);
        contentContainer.setOnClickListener(this);
        mEmptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
            @Override
            public void onReload() {
                initData();
            }
        });
        tvFilter.setClickable(true);

        View view = View.inflate(this, R.layout.view_black_list, null);
        blackImg = (ImageView) view.findViewById(R.id.iv_black);
        blackText = (TextView) view.findViewById(R.id.tv_black_text);
        llAddBlacklist = (LinearLayout) view.findViewById(R.id.ll_add_blacklist);
        blackImg.setImageResource(R.drawable.icon_add_blacklist);
        blackText.setText("黑名单");
        getBaseActionBar().setRightView(view);
    }

    /**
     * 初始化数据
     */
    private void initData() {
        isFinishPatients = false;
        isFinishPatientInfo = false;
        isDossierData = false;
        getPatients();
        getPatientInfo();
        getDossierData();
    }

    /**
     * 设置标签初始数据
     */
    public void initLabelData(PatientMsgBean.PatientsBean patientsBean) {
        contentContainer.removeAllViews();
        initLabelView(patientsBean);
    }

    /**
     * 添加标签成功之后的回调
     *
     * @param s 要添加的标签
     */
    private void addTagViewSuccess(String s) {
        addTagMethod(s, contentContainer.getChildCount() - 1);
        if (contentContainer.getChildCount() > 5) {
            contentContainer.getChildAt(contentContainer.getChildCount() - 1).setVisibility(View.GONE);
        }
    }

    /**
     * 删除标签成功之后的回调
     *
     * @param s 要删除的标签
     */
    private void deleteTagViewSuccess(String s) {
        for (int i = 0; i < contentContainer.getChildCount(); i++) {
            View itemVIew = contentContainer.getChildAt(i);
            TextView textView = (TextView) itemVIew.getTag();
            if (textView != null && s.equals(textView.getText())) {
                contentContainer.removeViewAt(i);
                break;
            }
        }
        if (contentContainer.getChildCount() <= 5) {
            contentContainer.getChildAt(contentContainer.getChildCount() - 1).setVisibility(View.VISIBLE);
        }
    }

    /**
     * 修改标签成功之后的回调
     *
     * @param olds 旧标签
     * @param s    新标签
     */
    private void updateTagViewSuccess(String olds, String s) {
        for (int i = 0; i < contentContainer.getChildCount(); i++) {
            View itemVIew = contentContainer.getChildAt(i);
            TextView textView = (TextView) itemVIew.getTag();
            if (textView != null && olds.equals(textView.getText())) {
                textView.setText(s);
                break;
            }
        }
    }

    /**
     * 添加标签方法
     *
     * @param index 添加的位置，-1为末尾
     */
    private void addTagMethod(final String tag, int index) {
        final View itemView = LayoutInflater.from(mContext).inflate(R.layout.item_patient_label_layout, contentContainer, false);
        final TextView tvLabel = (TextView) itemView.findViewById(R.id._label);
        tvLabel.setText(tag);
        contentContainer.addView(itemView, index);
        itemView.setTag(tvLabel);
        itemView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                //长按删除患者标签
                if (delLabelDialog == null) {
                    delLabelDialog = ConfirmDialog.getInstance(PatientInfoActivity.this);
                    delLabelDialog.setCanceledOnTouchOutside(false);
                }
                delLabelDialog.setDialogContent("是否删除？")
                        .setPositiveText("是")
                        .setNavigationText("否")
                        .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                delLabelDialog.dismiss();
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                delLabelDialog.dismiss();
                                updatePatientLabelRequest(tvLabel.getText().toString(), DEL_PATIENT_LABEL, null);
                            }
                        }).show();

                return true;
            }
        });
        itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击修改标签
                if (updateLabelDialog == null) {
                    updateLabelDialog = InputDialog.getInstance(PatientInfoActivity.this);
                }
                final TextView tv = (TextView) itemView.getTag();
                updateLabelDialog.setInputTitle("修改标签")
                        .setInputContent(tv.getText())
                        .setInputMaxLength(5)
                        .setInputHint("请输入标签内容（5字以内）")
                        .setPositiveText("保存")
                        .setNegativeText("取消")
                        .setOnButtonClickListener(new OnButtonClickListener() {
                            @Override
                            public void onPositiveClick(View view, CharSequence editText) {
                                updatePatientLabelRequest(editText.toString().trim(), UPDATE_PATIENT_LABEL, tv.getText().toString());
                                updateLabelDialog.dismiss();
                            }

                            @Override
                            public void onNegativeClick(View view, CharSequence editText) {
                                updateLabelDialog.dismiss();
                            }
                        }).show();
            }
        });
    }

    /**
     * 添加最后一个添加标签的view
     */
    private void addLastView() {

        View view = LayoutInflater.from(this).inflate(R.layout.item_add_label_tips, contentContainer, false);
        contentContainer.addView(view);
        //  添加患者标签
        view.findViewById(R.id.ll_add_label).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (updateLabelDialog == null) {
                    updateLabelDialog = InputDialog.getInstance(PatientInfoActivity.this);
                }
                updateLabelDialog.setInputTitle("添加标签")
                        .setInputMaxLength(5)
                        .setInputHint("请输入标签内容（5字以内）")
                        .setPositiveText("保存")
                        .setNegativeText("取消")
                        .setOnButtonClickListener(new OnButtonClickListener() {
                            @Override
                            public void onPositiveClick(View view, CharSequence editText) {
                                updateLabelDialog.dismiss();
                                updatePatientLabelRequest(editText.toString().trim(), ADD_PATIENT_LABEL, null);
                            }

                            @Override
                            public void onNegativeClick(View view, CharSequence editText) {
                                updateLabelDialog.dismiss();
                            }
                        }).show();
            }
        });
    }

    private void initLabelView(final PatientMsgBean.PatientsBean patientsBean) {
        if (patientsBean.getTags() != null) {
            for (final String s : patientsBean.getTags()) {
                addTagMethod(s, -1);
            }
            if (patientsBean.getTags().size() < 5) {
                addLastView();
            }
            if (patientsBean.getTags().size() == 5) {
                addLastView();
                contentContainer.getChildAt(contentContainer.getChildCount() - 1).setVisibility(View.GONE);
            }
        }
    }

    /**
     * 请求认证状态
     */
    private void requestAuthState() {
        requestAuthStateCallBack = addHttpPostRequest(HttpUrlManager.AUTHENTICATION_STATE_CODE, null, AuthStateResult.class, this);
    }

    /**
     * 添加黑名单
     *
     * @param view
     */
    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        addToBlackDialog();
    }

    /**
     * @param view
     */
    @OnClick({R.id.ll_add_blacklist, R.id.tv_filter, R.id.btn_send_msg, R.id.iv_edit_remark, R.id.content_container, R.id.complaint_layout})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ll_add_blacklist:
                addToBlackDialog();
                break;
            case R.id.tv_filter:
                showSelectPatientsPop();
                break;
            case R.id.btn_send_msg:
                if (isFromChatMain) {
                    finish();
                } else {
                    if (patientsBean != null) {
                        SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_ID, patientId);
                        SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_HEAD_URL, patientsBean.getHandImgUrl());
                        Intent intent = new Intent(mContext, ChatMainActivity.class);
                        intent.putExtra(PublicParams.PATIENT_USRE_ID, patientId);
                        intent.putExtra(PublicParams.PATIENT_USRE_NNAME, patientsBean.getName());
                        intent.putExtra(PublicParams.PATIENT_USRE_MOBILE, patientsBean.getMobile());
                        startActivity(intent);
                    }
                }
                break;
            case R.id.iv_edit_remark://修改添加备注 弹框
                showAddRemarkDialog();
                break;
            case R.id.complaint_layout:
                ComplaintActivity.start(this, patientId, (patientsBean == null ? "" : patientsBean.getMobile()), (patientsBean == null ? "" : patientsBean.getName()));
                break;
        }
    }

    /**
     * 显示增加患者备注弹框
     */
    private void showAddRemarkDialog() {
        if (addRemarkDialog == null) {
            addRemarkDialog = RemarkInputDialog.getInstance(PatientInfoActivity.this);
        }

        addRemarkDialog.setInputMaxLength(5)
                .setInputTitle("修改信息")
                .setInputHint("请输入姓名备注，患者不可见")
                .setPositiveText("保存")
                .setNegativeText("取消")
                .setOnButtonClickListener(new OnButtonClickListener() {
                    @Override
                    public void onPositiveClick(View view, CharSequence editText) {
                        addRemarkDialog.dismiss();
                        addRemarkRequest(editText.toString().trim());
                    }

                    @Override
                    public void onNegativeClick(View view, CharSequence editText) {
                        addRemarkDialog.dismiss();
                    }
                }).show();
    }

    /**
     * 添加备注成功后的回调
     *
     * @param remark 修改后的备注
     */
    private void addRemarkViewSuccess(String remark) {
        if (TextUtils.isEmpty(remark)) {
            tvRemark.setText("");
            tvRemark.setVisibility(View.GONE);
            tvName.setText(patientsBean.getName().length() > 5 ? patientsBean.getName().substring(0, 5) + "…" : patientsBean.getName());
            tvName.setTextSize(18);
        } else {
            tvRemark.setText(remark);
            tvName.setText("(" + (patientsBean.getName().length() > 3 ? patientsBean.getName().substring(0, 3) + "…" : patientsBean.getName()) + ")");
            tvName.setTextSize(16);
            tvRemark.setVisibility(View.VISIBLE);
        }

    }

    /**
     * 加入到黑名单确认弹窗
     */
    private void addToBlackDialog() {
        if (addBlackDialog == null) {
            addBlackDialog = ConfirmDialog.getInstance(this);
        }
        addBlackDialog.setDialogContent("是否将该患者加入黑名单？")
                .setPositiveText("确认")
                .setNavigationText("取消")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        addBlackDialog.dismiss();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        addToBlackList();
                        addBlackDialog.dismiss();
                    }
                }).show();
    }


    /**
     * 显示选择开方患者弹框
     */
    private void showSelectPatientsPop() {
        if (mBottomPopWindow == null) {
            mBottomPopWindow = new BottomPopWindow(this);
        }
        if (popItems == null || popItems.size() == 0) {
            getPatients();
            return;
        }
        mBottomPopWindow.setPopContentData(popItems);
        mBottomPopWindow.setPopTitle("选择开方患者");
        mBottomPopWindow.showPop();
        mBottomPopWindow.setBottomViewGone();
        mBottomPopWindow.setBottomText("新增患者");
        mBottomPopWindow.setBottomIcon(R.drawable.white_head_icon);
        mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
            @Override
            public void onPopItemClick(int position, PopItem popItem) {
                //// TODO: 2017/11/18 设置患者信息
                if (popItem.isMark()) {
                    tvNameTip.setVisibility(View.VISIBLE);
                } else {
                    tvNameTip.setVisibility(View.GONE);
                }
                String name = popItem.getName();
                if (!TextUtils.isEmpty(name)) {
                    tvPatientName.setText((name.length() > 4 ? name.substring(0, 4) + "…" : name) + "");
                } else {
                    tvPatientName.setText("");
                }
                mBottomPopWindow.dimissPop();
                if (popItem.getId() != null && !popItem.getId().equalsIgnoreCase(mCurrentPatientId)) {
                    mCurrentPatientId = popItem.getId();

                    dossierAdapter.setData(null);
                    currentPage = 1;
                    //请求数据
                    getDossierData();
                }
            }
        });

    }

    /**
     * 获取医案列表
     */
    private void getDossierData() {
        getDossierDataCallBack = addHttpPostRequest(HttpUrlManager.DOSSIER_LIST, RequestParams.getDossierListParams(mCurrentPatientId, currentPage, pageSize, userId), DossierBean.class, this);
    }

    /**
     * 获取患者基本信息
     */
    private void getPatientInfo() {
        HashMap map = new HashMap();
        map.put("patientId", patientId);
        getPatientInfoCallBack = addHttpPostRequest(HttpUrlManager.GET_PATIENT_INFO, map, PatientMsgBean.PatientsBean.class, this);
    }

    /**
     * 获取开方患者
     */
    private void getPatients() {
        HashMap map = new HashMap();
        map.put("patientId", patientId);
        getPatientsCallBack = addHttpPostRequest(HttpUrlManager.SELECT_PATIENT, map, PatientMsgBean.class, this);
    }

    /**
     * 加入黑名单
     */
    private void addToBlackList() {
        HashMap map = new HashMap();
        map.put("patientId", patientId);
        map.put("optType", "1");//标识（1 加入黑名单，2移除黑名单）
        addToBlackListCallBack = addHttpPostRequest(HttpUrlManager.REMOVE_BLACK_LIST, map, ResponseResult.class, this);
    }

    /**
     * 获取添加、删除、修改患者标签
     */
    private void updatePatientLabelRequest(String tag, String type, String oldTag) {
        localOldTag = oldTag;
        localTag = tag;
        loaclType = type;
        HashMap map = new HashMap();
        map.put("patientId", patientId);
        map.put("userId", userId);
        map.put("tag", tag);
        map.put("type", type);
        map.put("oldTag", oldTag);
        updatePatientLabelCallBack = addHttpPostRequest(HttpUrlManager.ADD_PATIENT_LABEL, map, ResponseResult.class, this);
    }

    /**
     * 获取患者备注
     */
    private void addRemarkRequest(String remark) {
        loaclRemark = remark;
        HashMap map = new HashMap();
        map.put("patientId", patientId);
        map.put("remark", remark);
        addPatientsRemarkCallBack = addHttpPostRequest(HttpUrlManager.ADD_PATIENT_REMARK, map, ResponseResult.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (isFinishing()) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.SELECT_PATIENT://获取此患者开方的列表
                if (result.isRequestSuccessed()) {
                    PatientMsgBean patientMsgBean = (PatientMsgBean) result.getBodyObject();
                    List<PatientMsgBean.PatientsBean> mPatients = patientMsgBean.getPatients();
                    initPatientsData(mPatients);
                    isFinishPatients = true;
                    hideEmpty();
                } else {
                    if (mEmptyView != null) {
                        mEmptyView.show();
                    }
                }

                break;
            case HttpUrlManager.GET_PATIENT_INFO://获取患者基本信息
                if (result.isRequestSuccessed()) {
                    patientsBean = (PatientMsgBean.PatientsBean) result.getBodyObject();
                    initPatientsInfo(patientsBean);
                    isFinishPatientInfo = true;
                    hideEmpty();
                } else {
                    if (mEmptyView != null) {
                        mEmptyView.show();
                    }
                }

                break;
            case HttpUrlManager.DOSSIER_LIST://获取医案列表

                if (result.isRequestSuccessed()) {
                    DossierBean dossierBean = (DossierBean) result.getBodyObject();
                    if (dossierBean.getConsilias() != null) {
                        if (currentPage == 1) {
                            dossierAdapter.setData(dossierBean.getConsilias());
                        } else {
                            dossierAdapter.addMoreData(dossierBean.getConsilias());
                        }
                    }
                    if (dossierBean.getPage() != null && !dossierBean.getPage().equalsIgnoreCase(dossierBean.getTotalPage())) {
                        refreshLayout.setCanLoadMore(true);
                    } else {
                        refreshLayout.setCanLoadMore(false);
                    }
                    isDossierData = true;
                    dossierAdapter.notifyDataSetChanged();
                    if (dossierAdapter.getData() == null || dossierAdapter.getData().size() == 0) {
                        emptyViewList.setEmptyText("暂无医案记录");
                        emptyViewList.show();
                    } else {
                        emptyViewList.hide();
                    }
                    hideEmpty();
                } else {
                    if (mEmptyView != null) {
                        mEmptyView.show();
                    }
                }
                refreshLayout.endRefreshing();
                refreshLayout.endLoadingMore();
                break;
            case HttpUrlManager.REMOVE_BLACK_LIST://加入或移出黑名单
                if (result.isRequestSuccessed()) {
                    String count = SharedPreferenceUtils.getString(PatientInfoActivity.this, PublicParams.CONTACT_NUM);
                    int num = Integer.parseInt(count) - 1;
                    SharedPreferenceUtils.putString(PatientInfoActivity.this, PublicParams.CONTACT_NUM, String.valueOf(num));
                    contactsDao.deleteByKey(patientId + "_" + userId);
                    EventBusUtils.post(new NettyMsgNotify(NotifyType.UPDATE_CONTACTS_LIST));
                    sessionDao.deleteByKey(patientId + "_" + userId);
                    EventBusUtils.post(new NettyMsgNotify(NotifyType.SESSION_LIST));
                    ToastUtils.showShortMsg(this, "已将该患者加入黑名单");
                    ActivityManager.getInstance().removeActivity(ChatMainActivity.class);
                    ActivityManager.getInstance().removeActivity(ChatHistoryRecordsActivity.class);
                    PatientInfoActivity.this.finish();
                } else {
                    ToastUtils.showShortMsg(this, "加入黑名单失败");
                }
                break;
            case HttpUrlManager.ADD_PATIENT_REMARK://修改患者备注
                if (result.isRequestSuccessed()) {
                    Session session = sessionDao.load(patientId + "_" + userId);
                    if (session != null) {
                        session.setRemark(loaclRemark);
                        sessionDao.update(session);
                    }
                    tvRemark.setText(loaclRemark);
                    addRemarkViewSuccess(loaclRemark);
                    EventBusUtils.post(new NettyMsgNotify(NotifyType.LOAD_CONTACTS_LIST));
                    EventBusUtils.post(new PatientInfoChangeEvent(patientId, PatientInfoChangeEvent.CHANGE_REMARK));
                    EventBusUtils.post(new NettyMsgNotify(NotifyType.UPDATE_CONTACTS_LIST));
                }
                break;
            case HttpUrlManager.AUTHENTICATION_STATE_CODE:
                if (result.isRequestSuccessed()) {
                    AuthStateResult authStateResult = (AuthStateResult) result.getBodyObject();
                    if (authStateResult == null
                            || (!AuthState.AUTH_STATE_SUCCESSFUL.equalsIgnoreCase(authStateResult.getIsAuthentication())
                            && AuthState.AUTH_STATE_FAILED.equalsIgnoreCase(authStateResult.getCurrentAuthenticationState())
                            && "0".equalsIgnoreCase(authStateResult.getStockDay()))) {
                        emptyViewList.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyViewList.setEmptyText("认证通过后才可查看医案");
                        tvFilter.setClickable(false);
                    } else {
                        getDossierData();
                        tvFilter.setClickable(true);
                    }
                } else {
                    if (requestCount < 3) {
                        requestCount++;
                        requestAuthState();
                    } else {
                        RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                    }
                }
                break;
            case HttpUrlManager.ADD_PATIENT_LABEL://患者标签
                if (result.isRequestSuccessed()) {
                    Contacts contacts = contactsDao.load(patientId + "_" + userId);
                    if (contacts == null) {
                        return;
                    }
                    if (ADD_PATIENT_LABEL.equals(loaclType)) {
                        //添加成功
                        if (contacts.getTags() == null) {
                            contacts.setTags(new ArrayList<String>());
                        }
                        contacts.getTags().add(localTag);
                        addTagViewSuccess(localTag);
                        ToastUtils.showShortMsg(this, "添加成功");
                    } else if (DEL_PATIENT_LABEL.equals(loaclType)) {

                        contacts.getTags().remove(localTag);
                        ToastUtils.showShortMsg(this, "删除成功");
                        deleteTagViewSuccess(localTag);
                    } else if (UPDATE_PATIENT_LABEL.equals(loaclType)) {
                        int index = contacts.getTags().indexOf(localOldTag);
                        if (index == -1) {
                            return;
                        }
                        contacts.getTags().set(index, localTag);
                        updateTagViewSuccess(localOldTag, localTag);
                        ToastUtils.showShortMsg(this, "修改成功");
                    }
                    contactsDao.update(contacts);
                    EventBusUtils.post(new PatientInfoChangeEvent(patientId, PatientInfoChangeEvent.CHANGE_TAGS));
                } else {
                    if (ADD_PATIENT_LABEL.equals(loaclType)) {
                        if ("0002".equals(result.getCode())) {
                            ToastUtils.showShortMsg(this, "标签已存在");
                        } else {
                            ToastUtils.showShortMsg(this, "添加失败");
                        }
                    } else if (DEL_PATIENT_LABEL.equals(loaclType)) {
                        ToastUtils.showShortMsg(this, "删除失败");
                    } else if (UPDATE_PATIENT_LABEL.equals(loaclType)) {
                        if ("0002".equals(result.getCode())) {
                            ToastUtils.showShortMsg(this, "标签已存在");
                        } else {
                            ToastUtils.showShortMsg(this, "修改失败");
                        }
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 隐藏重新加载的布局
     */
    private void hideEmpty() {
        if (mEmptyView != null && isFinishPatientInfo && isDossierData && isFinishPatients) {
            mEmptyView.hide();
        }
    }

    /**
     * 初始化患者信息数据
     *
     * @param patientsBean
     */
    private void initPatientsInfo(PatientMsgBean.PatientsBean patientsBean) {
        if (patientsBean != null) {
            initLabelData(patientsBean);
            mGlideUtils.loadCircleImage(getImgUrl(patientsBean.getHandImgUrl()), this, ivHead, R.drawable.default_head_img);
            if (!TextUtils.isEmpty(patientsBean.getRemark())) {
                tvRemark.setVisibility(View.VISIBLE);
                tvRemark.setText(patientsBean.getRemark() + "");
                nickName = patientsBean.getName().toString();
                tvName.setText("(" + (nickName.length() > 3 ? nickName.substring(0, 3) + "…" : nickName) + ")");
                tvName.setTextSize(16);
            } else {
                tvRemark.setText("");
                tvRemark.setVisibility(View.GONE);
                tvName.setText(patientsBean.getName().length() > 5 ? patientsBean.getName().substring(0, 5) + "…" : patientsBean.getName());
                tvName.setTextSize(18);
            }
//            tvName.setText(patientsBean.getName() + "");
            tvAge.setText(patientsBean.getAge() + "");
            if ("1".equalsIgnoreCase(patientsBean.getSex())) {
                sexAgeLl.setBackgroundResource(R.drawable.sex_age_blue_bg);
                ivSex.setImageResource(R.drawable.male_icon_white);
            } else {
                sexAgeLl.setBackgroundResource(R.drawable.sex_age_red_bg);
                ivSex.setImageResource(R.drawable.female_icon_white);
            }
            if (!TextUtils.isEmpty(patientsBean.getAge()) && !"0".equals(patientsBean.getAge())) {
                tvAge.setVisibility(View.VISIBLE);
                tvAge.setText(patientsBean.getAge() + "");
            } else {
                tvAge.setVisibility(View.GONE);
            }
            if (TextUtils.isEmpty(patientsBean.getSex()) && TextUtils.isEmpty(patientsBean.getAge())) {
                sexAgeLl.setBackgroundResource(R.color.br_color_white);
            }

            tvOrderCount.setText(patientsBean.getOrderNum() + "");
            tvPayCount.setText(patientsBean.getPayOrderNum() + "");
            tvOrderPrice.setText("¥" + patientsBean.getInPrice());
        }
    }

    /**
     * 初始化选择患者列表数据
     */
    private void initPatientsData(List<PatientMsgBean.PatientsBean> patients) {
        popItems.clear();
        if (patients != null && patients.size() > 0) {
            for (int i = 0; i < patients.size(); i++) {
                PopItem popItem = new PopItem();
                popItem.setId(patients.get(i).getId() + "");
                popItem.setName(patients.get(i).getName() + "");
                popItem.setAge(patients.get(i).getAge() + "");
                popItem.setSex(patients.get(i).getSex() + "");
                popItem.setIsPregnant(patients.get(i).getIsPregnant() + "");
                popItem.setPosition(i);
                if ("1".equals(patients.get(i).getIsSelf())) {//是本人
                    popItem.setMark(true);
                    popItem.setResId(R.drawable.is_self_icon);
                    String name = patients.get(i).getName();
                    if (!TextUtils.isEmpty(name)) {
                        tvPatientName.setText((name.length() > 4 ? name.substring(0, 4) + "…" : name) + "");
                    } else {
                        tvPatientName.setText("");
                    }
                    tvNameTip.setText("（本人）");
                } else {
                    popItem.setMark(false);
                }
                popItems.add(popItem);
            }

        }
    }


    @Override
    public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {
        currentPage = 1;
        getDossierData();
    }

    @Override
    public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {
        currentPage++;
        getDossierData();
    }

    @Override
    protected void onDestroy() {
        if (mRequestCallBack != null) {
            mRequestCallBack.cancel();
        }
        if (mBottomPopWindow != null) {
            mBottomPopWindow.dimissPop();
            mBottomPopWindow = null;
        }
        cancelRequest(requestAuthStateCallBack, getDossierDataCallBack, getPatientInfoCallBack, getPatientsCallBack, addToBlackListCallBack,
                addPatientsRemarkCallBack, updatePatientLabelCallBack);
        super.onDestroy();
    }

    @Override
    public void onItemChildClick(ViewGroup parent, View childView, int position) {
        String doctorId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        if (childView.getId() == R.id.ll_container) {
            DossierBean.ConsiliasBean consiliasBean = dossierAdapter.getData().get(position);
            if (DossierAdapter.TYPE_INQUERY.equals(consiliasBean.getType())) {  //首诊单
                Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                //url : "http://www.haoniuzhongyi.cn/easydoctorv2-ws/wxWzd/skimFinishFirstReport?wzdId=2018010410583190219&docTel=15200000000&openId=ouK-vwisFA5ndLwGpj7ptVU8DX94"
                //拼接复诊单的url,没有直接使用后台的url地址，因为不知道后台返回的地址是否分（测试地址和正式地址）
                String url = consiliasBean.getUrl() + "&flag=0";
                LogUtils.log("url.............." + url);
                intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, url);
                intent.putExtra(PublicParams.WEBVIEW_TITLE, "问诊单");
                intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                startActivityForResult(intent, REQUEST_CODE_WEBVIEW);
            } else if (DossierAdapter.TYPE_RETURN_VISIT.equals(consiliasBean.getType())) {//复诊单
                //拼接复诊单的url,没有直接使用后台的url地址，因为不知道后台返回的地址是否分（测试地址和正式地址）
                String url = consiliasBean.getUrl() + "&flag=0";
                Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, url);
                intent.putExtra(PublicParams.WEBVIEW_TITLE, "复诊单");
                intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                startActivityForResult(intent, REQUEST_CODE_WEBVIEW);

            } else if (DossierAdapter.TYPE_MEDICINE.equals(consiliasBean.getType())) {//用药详情
                UIHelper.openMedicationDetailActivity(mContext, consiliasBean.getId());
            }
        }
    }


    /**
     * @param requestCode
     * @param resultCode
     * @param data
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_CODE_WEBVIEW:
                if (resultCode == RESULT_OK) {
                    if (isFromChatMain) {
                        finish();
                    } else {
                        if (patientsBean != null) {
                            SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_ID, patientId);
                            SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_HEAD_URL, patientsBean.getHandImgUrl());
                            Intent intent = new Intent(mContext, ChatMainActivity.class);
                            intent.putExtra(PublicParams.PATIENT_USRE_ID, patientId);
                            intent.putExtra(PublicParams.PATIENT_USRE_NNAME, patientsBean.getName());
                            intent.putExtra(PublicParams.PATIENT_USRE_MOBILE, patientsBean.getMobile());
                            intent.putExtra(ChatMainActivity.POSITION, 2);
                            intent.putExtra(PublicParams.TO_MEDICATION_FROM_PAGE, PublicParams.FROM_WZD_OR_FZD);
                            startActivity(intent);
                        }
                    }
                }
                break;
        }
    }


}
