package com.doctor.br.activity.mine.agent.task;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.mine.TaskRecyclerAdapter;
import com.doctor.br.bean.AgentTastListBean;
import com.doctor.br.bean.event.RefreshPendingDataEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：任务分配界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/13
 */

public class TaskActivity extends ActionBarActivity implements RecyclerItemItemClickListener {
    //上个界面传递过来的数据
    public static final String IS_DITUI = "isDitui";
    private String isDitui;//经纪人身份
    //界面下的控件
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private int currentPage = 1;//当前显示的页码
    private int totalPage = -1;//总页码
    private RequestCallBack taskListCallBack;//网络请求任务分配列表回调

    private List<AgentTastListBean.TasksBean> list;
    private TaskRecyclerAdapter taskRecyclerAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_task);
        EventBusUtils.register(this);
        getIntentData(savedInstanceState);
        initView();
        getTaskListRequest("1");
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(IS_DITUI, isDitui);
    }

    private void getIntentData(Bundle saveInstanceState) {
        if (saveInstanceState != null) {
            isDitui = saveInstanceState.getString(IS_DITUI);
        } else {
            isDitui = getIntent().getStringExtra(IS_DITUI);
        }
        if (TextUtils.isEmpty(isDitui)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("任务分配");

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                getTaskListRequest("1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        mLoadingDialog = null;
                        getTaskListRequest(currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        bgaRefreshLayout.setRefreshViewHolder(new BGANormalRefreshViewHolder(this, true));

        recyclerView.setHasFixedSize(true);
        list = new ArrayList<>();
        taskRecyclerAdapter = new TaskRecyclerAdapter(this, list, this);
        recyclerView.setAdapter(taskRecyclerAdapter);
    }

    @Override
    public void itemViewClick(int position, View view) {
        Intent intent = new Intent(this, SelectAgentActivity.class);
        intent.putExtra(SelectAgentActivity.TASK_ID, list.get(position).getId());
        intent.putExtra(SelectAgentActivity.NAME, list.get(position).getName());
        intent.putExtra(SelectAgentActivity.IS_DITUI, isDitui);
        intent.putExtra(SelectAgentActivity.NODE_ID, list.get(position).getNodeId());
        startActivity(intent);
    }

    /**
     * 网络请求获取任务分配的列表
     *
     * @param page 想要请求的页码
     */
    private void getTaskListRequest(String page) {
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        map.put("page", page);
        map.put("pageSize", "20");
        map.put("type", "3");//1待接受 2待扫描 3待分配
        taskListCallBack = addHttpPostRequest(HttpUrlManager.TASK_LIST, map, AgentTastListBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (emptyView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.TASK_LIST:
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    AgentTastListBean agentTastListBean = (AgentTastListBean) result.getBodyObject();
                    totalPage = agentTastListBean.getTotalPage();
                    currentPage = agentTastListBean.getPage();
                    if (currentPage == 1) {
                        list.clear();
                    }
                    list.addAll(agentTastListBean.getTasks());
                    taskRecyclerAdapter.notifyDataSetChanged();
                    if (currentPage == 1 && list.size() == 0) {
                        emptyView.setVisibility(View.VISIBLE);
                    }
                } else {
                    if (totalPage == -1) {
                        emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                        emptyView.setVisibility(View.VISIBLE);
                        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                            @Override
                            public void onReload() {
                                getTaskListRequest("1");
                            }
                        });
                    }
                    RequestErrorToast.showError(this,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 刷新待扫描、待接收、待处理、待审核、待分配数量
     *
     * @send {@link com.doctor.zxing.activity.CaptureActivity#refreshData()}
     * @send {@link com.doctor.br.fragment.mine.BeReceiveFragment#refreshData()}
     * @send {@link com.doctor.br.activity.mine.agent.DispatchActivity#refreshNumber()}
     * @send {@link com.doctor.br.fragment.mine.BeScanFragment#sendRefresh()}
     * @send {@link SelectAgentActivity#sendRefreshNumber()}
     * @send {@link SelectAgentSearchActivity#sendRefreshNumber()}
     * @receive {@link SelectAgentActivity#refreshList(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.fragment.mine.BeScanFragment#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.AgentActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.PendingActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link TaskActivity#refreshData(RefreshPendingDataEvent)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshData(RefreshPendingDataEvent refreshPendingDataEvent) {
        if (refreshPendingDataEvent != null && refreshPendingDataEvent.isFreshTask()) {
            getTaskListRequest("1");
        }
    }


    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        if (taskListCallBack != null) {
            taskListCallBack.cancel();
        }
        super.onDestroy();
    }


}
