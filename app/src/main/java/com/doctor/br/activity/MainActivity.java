package com.doctor.br.activity;

import android.content.ActivityNotFoundException;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.KeyEvent;

import com.doctor.br.activity.dialog.ActiveListDialogActivity;
import com.doctor.br.activity.dialog.NoticeDialogActivity;
import com.doctor.br.activity.dialog.UpdateDownloadActivity;
import com.doctor.br.activity.medical.AddMedicineActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.ActiveListResult;
import com.doctor.br.bean.CheckUpdateResult;
import com.doctor.br.bean.ContactsResult;
import com.doctor.br.bean.DoctorInfoBean;
import com.doctor.br.bean.UserInfo;
import com.doctor.br.db.entity.Contacts;
import com.doctor.br.db.entity.Notice;
import com.doctor.br.db.entity.Session;
import com.doctor.br.fragment.main.ContactsFragment;
import com.doctor.br.fragment.main.ContactsFragment2;
import com.doctor.br.fragment.main.InviteFragment;
import com.doctor.br.fragment.main.ManageFragment;
import com.doctor.br.fragment.main.MyCenterFragment;
import com.doctor.br.fragment.main.NewsFragment;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.NettyMsgNotify;
import com.doctor.br.netty.NotifyType;
import com.doctor.br.netty.client.NettyClient;
import com.doctor.br.netty.impl.DataReceiverImpl;
import com.doctor.br.netty.impl.MsgReceiveFactory;
import com.doctor.br.netty.utils.ReshipData;
import com.doctor.br.receiver.OnePixelReceiver;
import com.doctor.br.service.NettyMessageService;
import com.doctor.br.utils.BadgeUtils;
import com.doctor.br.utils.BroadcastAction;
import com.doctor.br.utils.DateUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.RegularUtils;
import com.doctor.br.utils.SessionUtils;
import com.doctor.greendao.gen.ContactsDao;
import com.doctor.greendao.gen.NoticeDao;
import com.doctor.greendao.gen.SessionDao;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.TabHostActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.AppUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页Activity
 */
public class MainActivity extends TabHostActivity {
    private static final int INTENT_REQUEST_UPDATE = 120;
    private static final int INTENT_REQUEST_NOTICE = 121;
    private static final int INTENT_REQUEST_ACTIVE_LIST = 123;

    private BroadcastReceiver msgCountReceiver;
    private OnePixelReceiver mOnepxReceiver;

    private NoticeDao noticeDao;
    private ContactsDao contactsDao;
    private SessionDao sessionDao;
    private String userId;
    private boolean isShowUpdate;
    private int loadDoctorInfoCount = 0;
    private int loadCount;
    private Handler mHandler = new Handler();
    private RequestCallBack contactsListCallBack;
    private RequestCallBack checkUpdateCallBack;
    private RequestCallBack requestDoctorInfoCallBack;
    private RequestCallBack getActiveInfoCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initTabBar();
        EventBusUtils.register(this);
        AppContext.getInstances().initConflictDialog(this);
//        Intent intent = new Intent(MainActivity.this, NettyMessageService.class);
//        startService(intent);
        initData();
        initReceiver();

        NettyClient mNettyClient = AppContext.getInstances().getNettyClient();
        if (mNettyClient != null) {
            DataReceiverImpl impl = (DataReceiverImpl) mNettyClient.getDataReceiver();
            impl.setMessageListener(new MsgReceiveFactory(this));
        }
        noticeDao = AppContext.getInstances().getDaoSession().getNoticeDao();
        checkUpdate();

    }

    private void initData() {
        contactsDao = AppContext.getInstances().getDaoSession().getContactsDao();
        sessionDao = AppContext.getInstances().getDaoSession().getSessionDao();
        userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        loadContactsData();
    }


    private void initTabBar() {
//        setRepleace(true);
        addTabBar("消息", R.drawable.bar_news, new NewsFragment());
        addTabBar("患者", R.drawable.bar_constact, new ContactsFragment2());
        addTabBar("管理", R.drawable.bar_manage, new ManageFragment());
        addTabBar("邀请", R.drawable.bar_invite, new InviteFragment());
        addTabBar("我的", R.drawable.bar_my, new MyCenterFragment());
    }

    /**
     * 注册更新未读消息数的广播
     */
    private void initReceiver() {
        //注册监听屏幕的广播
        mOnepxReceiver = new OnePixelReceiver();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("android.intent.action.SCREEN_OFF");
        intentFilter.addAction("android.intent.action.SCREEN_ON");
        intentFilter.addAction("android.intent.action.USER_PRESENT");
        registerReceiver(mOnepxReceiver, intentFilter);


        msgCountReceiver = new BroadcastReceiver() {

            @Override
            public void onReceive(Context context, Intent intent) {
                if (BroadcastAction.UPDATE_NOT_READ_MSG_COUNT.equals(intent.getAction())) {
                    int position = intent.getIntExtra(PublicParams.MAIN_RED_NOTICE_POSITION, -1);
                    int count = intent.getIntExtra(PublicParams.MAIN_RED_NOTICE_COUNT, 0);
                    if (position >= 0 && position < 5) {
                        setMsgCount(position, count);
                    }
                    if (position == 0) {//消息
                        BadgeUtils.setBadgeCount(MainActivity.this, count);
                    }
                } else if (BroadcastAction.MSG_SHOW_NOTICE_DIALOG.equals(intent.getAction())) {
                    initNoticeData();
                }
            }
        };
        IntentFilter filter = new IntentFilter(BroadcastAction.UPDATE_NOT_READ_MSG_COUNT);
        filter.addAction(BroadcastAction.MSG_SHOW_NOTICE_DIALOG);
        registerReceiver(msgCountReceiver, filter);
    }

    @Override
    protected void onStart() {
        super.onStart();
        requestDoctorInfo();
        //是否有待付款订单
        int notPayOrderNumber = SharedPreferenceUtils.getInt(this, PublicParams.NOT_PAY_ORDER_NUM);
        if (notPayOrderNumber > 0) {
            notPayOrderNumber = -1;
        }
        setMsgCount(2, notPayOrderNumber);

        //我的界面new是否查看过
        boolean isClicked = SharedPreferenceUtils.getBoolean(this, PublicParams.IS_OPEN_NEW);
        if (!isClicked) {
            setMsgCount(4, -1);
        } else {
            setMsgCount(4, 0);
        }
//        try {//加延迟是因为，后台服务有10秒延迟，否则自动登录设备号不能及时更新会导致错误的登录冲突问题。
//            mHandler.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    Intent intent = new Intent(MainActivity.this, NettyMessageService.class);
//                    startService(intent);
//                }
//            }, 1000 * 11);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        try {
            Intent intent = new Intent(MainActivity.this, NettyMessageService.class);
            startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }


    /**
     * 检查App是否有更新
     */
    private void checkUpdate() {
        boolean isShowUpdateDialog = SharedPreferenceUtils.getBoolean(this, PublicParams.IS_SHOW_UPDATE_DIALOG, true);
        if (isShowUpdateDialog) {
            isShowUpdate = true;
            HashMap<String, String> params = new HashMap<>();
            params.put("systemType", "1");//1表示Android平台，2表示iOS平台
            params.put("userId", SharedPreferenceUtils.getString(this, PublicParams.USER_ID));
            checkUpdateCallBack = mRequestTask.addHttpPostRequest(HttpUrlManager.APP_CHECK_UPDATE, params, CheckUpdateResult.class, this);
        } else {
            initNoticeData();
        }
        SharedPreferenceUtils.putBoolean(this, PublicParams.IS_SHOW_UPDATE_DIALOG, true);
    }

    /**
     * 获取登录的医生信息
     */
    private void requestDoctorInfo() {
        requestDoctorInfoCallBack = mRequestTask.addHttpPostRequest(HttpUrlManager.DOCTOR_INFO, null, DoctorInfoBean.class, this);
    }

    /**
     * 获取活动信息
     */
    private void getActiveInfo() {
        String preShowDate = SharedPreferenceForeverUtils.getString(this, PublicParams.SHOW_ACTIVE_TIME);
        if (TextUtils.isEmpty(preShowDate)) {
            preShowDate = "1970-01-01";
        }
        HashMap<String, String> params = new HashMap<>();
        params.put("preShowDate", preShowDate);
        params.put("srcType", "1");
        getActiveInfoCallBack = mRequestTask.addHttpPostRequest(HttpUrlManager.GET_ACTIVE_LIST, params, ActiveListResult.class, this);
    }


    /**
     * 加载联系人数据
     */
    private void loadContactsData() {
        String startTime = "";
        LogUtils.i(LoadDataActivity.class, "加载联系人数据");
        startTime = SharedPreferenceUtils.getString(mContext, PublicParams.UPDATE_CONTACTS_LIST_TIME);
        Map<String, String> params = new HashMap<>();
        if (TextUtils.isEmpty(startTime)) {
            startTime = getHalfYearTime();
        }
        params.put("startTime", startTime);
        contactsListCallBack = mRequestTask.addHttpPostRequest(HttpUrlManager.GET_CONTACTS_LIST, params, ContactsResult.class, this);
    }

    /**
     * 获取过去半年的时间
     *
     * @return
     */
    private String getHalfYearTime() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        //过去六个月
        c.setTime(new Date());
        c.add(Calendar.MONTH, -6);
        Date m = c.getTime();
        String mon = format.format(m);
        return mon;
    }

    @Override
    public void onRequestFinished(String taskId, final ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.APP_CHECK_UPDATE:
                if (result.isRequestSuccessed()) {
                    CheckUpdateResult checkUpdateResult = (CheckUpdateResult) result.getBodyObject();
                    String local = AppUtils.getVersion(this);
                    if (checkUpdateResult != null && !TextUtils.isEmpty(checkUpdateResult.getVersionNo())) {
                        LogUtils.i(MainActivity.class, "版本号比较：" + local.compareToIgnoreCase(checkUpdateResult.getVersionNo()));
                        if (local.compareToIgnoreCase(checkUpdateResult.getVersionNo()) < 0) {
                            //TODO
                            LogUtils.i(MainActivity.class, "版本需要更新" + local.compareToIgnoreCase(checkUpdateResult.getVersionNo()));
                            Intent intent = new Intent(this, UpdateDownloadActivity.class);
                            intent.putExtra(PublicParams.UPDATE_APP_BEAN, checkUpdateResult);
                            startActivityForResult(intent, INTENT_REQUEST_UPDATE);
                        } else {
                            isShowUpdate = false;
                        }
                    } else {
                        isShowUpdate = false;
                    }
                } else {
                    isShowUpdate = false;
                }
                initNoticeData();
                break;
            case HttpUrlManager.DOCTOR_INFO:
                if (result.isRequestSuccessed()) {
                    DoctorInfoBean bean = (DoctorInfoBean) result.getBodyObject();
                    UserInfo userInfo = AppContext.getInstances().getLoginInfo();
                    SharedPreferenceUtils.putString(this, PublicParams.USER_TYPE, bean.getIsDiTui());
                    if (userInfo != null) {
                        userInfo.setName(bean.getName());
                        userInfo.setHandleUrl(bean.getHandleUrl());
                        userInfo.setQrCodeUrl(bean.getWeiXinUrl());
                        userInfo.setWxOpenId(bean.getWxOpenId());
                        userInfo.setWxOpenIdHome(bean.getWxOpenIdHome());
                        userInfo.setSupportWxHome(bean.getSupportWxHome());
                        AppContext.getInstances().saveLoginInfo(userInfo);
                    }
                } else {
                    if (loadDoctorInfoCount < 3) {
                        loadDoctorInfoCount++;
                        requestDoctorInfo();
                    }
                }
                break;
            case HttpUrlManager.GET_ACTIVE_LIST:
                if (result.isRequestSuccessed()) {
                    ActiveListResult activeListResult = (ActiveListResult) result.getBodyObject();
                    if (activeListResult != null && activeListResult.getList() != null && activeListResult.getList().size() > 0) {//有活动，需要显示
                        Intent intent = new Intent(this, ActiveListDialogActivity.class);
                        intent.putExtra(PublicParams.ACTIVE_LIST_RESULT, activeListResult);
                        startActivityForResult(intent, INTENT_REQUEST_ACTIVE_LIST);
                    }
                } else {

                }
                break;
            case HttpUrlManager.GET_CONTACTS_LIST:
                if (result.isRequestSuccessed()) {
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            ContactsResult contactsResult = (ContactsResult) result.getBodyObject();
                            if (contactsResult != null) {
                                boolean isUpdate = false;
                                if (contactsResult.getItems() != null) {
                                    SharedPreferenceUtils.putString(mContext, PublicParams.UPDATE_CONTACTS_LIST_TIME, DateUtils.getNowDate());
                                    saveContactsData(contactsResult.getItems());
                                    if (!isUpdate && (contactsResult.getItems().size() > 1 || (contactsResult.getItems().size() == 1 && !"1000".equals(contactsResult.getItems().get(0).getRosterUserId())))) {
                                        isUpdate = true;
                                    }
                                }
                                if (contactsResult.getBlacklistIds() != null) {
                                    deleteBlackContacts(contactsResult.getBlacklistIds());
                                    if (!isUpdate && contactsResult.getBlacklistIds().size() > 0) {
                                        isUpdate = true;
                                    }
                                }
                                SharedPreferenceUtils.putString(MainActivity.this, PublicParams.CONTACT_NUM, contactsResult.getRosterCount());
                                if (isUpdate) {
                                    sendEventBusUpdateContacts();
                                }
                            }
                            LogUtils.i(LoadDataActivity.class, "加载联系人数据完毕");
                            //通知更新交流主界面标题的患者信息显示
//                            EventBusUtils.post(new NettyMsgNotify(NotifyType.UPDATA_CHATMAIN_TITILE_MSG));
                        }
                    }).start();
                } else {
                    if (loadCount < 3) {
                        loadCount++;
                        loadContactsData();
                    }
                }
                break;
        }
    }

    /**
     * 发送消息，更新联系人列表
     *
     * @receiver {@link ContactsFragment#onNotifyContactsList(NettyMsgNotify)}
     */
    private void sendEventBusUpdateContacts() {
        NettyMsgNotify notify = new NettyMsgNotify(NotifyType.UPDATE_CONTACTS_LIST);
        EventBusUtils.post(notify);
    }


    /**
     * 保存联系人信息
     *
     * @param list
     */
    private void saveContactsData(final List<Contacts> list) {
        List<Session> sessionList = new ArrayList<>();
        SessionUtils sessionUtils = SessionUtils.getInstance();
        for (int i = 0; i < list.size(); i++) {
            Contacts contacts = list.get(i);
            if (contacts != null) {
                contacts.setUserId(userId);
                contacts = ReshipData.reshipContacts(list.get(i));
                if ("#".equalsIgnoreCase(RegularUtils.letterFilter(contacts.getFirstSpell()))) {
                    contacts.setFirstSpell("|");
                }
                Session session = sessionUtils.updateSessionContactsInfo(contacts);
                if (session != null) {
                    sessionList.add(session);
                }
            }
        }
        contactsDao.insertOrReplaceInTx(list);
        sessionDao.insertOrReplaceInTx(sessionList);
        sessionUtils.sendEventBus(new Session());
    }


    /**
     * 把黑名单中的人员根据id删除掉
     *
     * @param blacklistIds
     */
    private void deleteBlackContacts(List<String> blacklistIds) {
        try {
            if (blacklistIds == null) {
                return;
            }
            String userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
            List<String> idLists = new ArrayList<>();
            for (int i = 0; i < blacklistIds.size(); i++) {
                idLists.add(blacklistIds.get(i) + "_" + userId);
            }
            contactsDao.deleteByKeyInTx(idLists);
            sessionDao.deleteByKeyInTx(idLists);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 初始化数据
     */
    private void initNoticeData() {
        if (isShowUpdate) {
            return;
        }
        List<Notice> noticeList = noticeDao.queryBuilder()
                .orderDesc(NoticeDao.Properties.Id)
                .limit(1)
                .build().list();
        if (noticeList != null && noticeList.size() > 0) {
            Notice notice = noticeList.get(0);
            if (notice.getIsShow().intValue() == 0) {
                Intent intent = new Intent(MainActivity.this, NoticeDialogActivity.class);
//                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.putExtra("notice", notice);
                startActivityForResult(intent, INTENT_REQUEST_NOTICE);
            } else {
                getActiveInfo();
            }
        } else {
            getActiveInfo();
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case INTENT_REQUEST_UPDATE:
                isShowUpdate = false;
                initNoticeData();
                break;
            case INTENT_REQUEST_NOTICE:
                getActiveInfo();
                break;
        }
    }

    /**
     * 更新联系人列表
     *
     * @param notify
     * @sender {@link com.doctor.br.utils.SessionUtils#sendEventBusUpdateContacts()}
     * @sender {@link com.doctor.br.activity.chatmain.PatientInfoActivity#onRequestFinished(String, ResponseResult)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNotifyContactsList(NettyMsgNotify notify) {
        if (NotifyType.LOAD_CONTACTS_LIST == notify.getNotifyType()) {
//            mHandler.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    loadContactsData();
//                }
//            }, 1000);
            loadContactsData();
        }
    }

    @Override
    protected void onDestroy() {
        if (mOnepxReceiver != null) {
            unregisterReceiver(mOnepxReceiver);
        }
        if (msgCountReceiver != null) {
            unregisterReceiver(msgCountReceiver);
        }
        EventBusUtils.unRegister(this);
        cancelRequest(contactsListCallBack,
                checkUpdateCallBack,
                requestDoctorInfoCallBack,
                getActiveInfoCallBack);
        try {
            mHandler.removeCallbacksAndMessages(null);
        } catch (Exception e) {
            LogUtils.e(MainActivity.class, e.getMessage());
        }
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
//        super.onBackPressed();

        activityToBackground();
//        moveTaskToBack(false);
    }

    /**
     * 将app在后台运行
     */
    private void activityToBackground() {
        PackageManager pm = getPackageManager();
        ResolveInfo homeInfo = pm.resolveActivity(new Intent(Intent.ACTION_MAIN)
                .addCategory(Intent.CATEGORY_HOME), 0);
        ActivityInfo ai = homeInfo.activityInfo;
        Intent startIntent = new Intent(Intent.ACTION_MAIN);
        startIntent.addCategory(Intent.CATEGORY_LAUNCHER);
        startIntent.setComponent(new ComponentName(ai.packageName,
                ai.name));
        startActivitySafely(startIntent);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_BACK:
                activityToBackground();
                break;
            case KeyEvent.KEYCODE_MENU:
                break;
            case KeyEvent.KEYCODE_HOME:
                activityToBackground();
                // 收不到
                break;
            case KeyEvent.KEYCODE_APP_SWITCH:
                // 收不到
                break;
            default:
                break;
        }

        return super.onKeyDown(keyCode, event);

    }

    private void startActivitySafely(Intent intent) {
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            startActivity(intent);
        } catch (ActivityNotFoundException e) {
            e.printStackTrace();
        } catch (SecurityException e) {
            e.printStackTrace();
            LogUtils.i(MainActivity.class,
                    "Launcher does not have the permission to launch "
                            + intent
                            + ". Make sure to create a MAIN intent-filter for the corresponding activity "
                            + "or use the exported attribute for this activity.");
        }
    }
}
