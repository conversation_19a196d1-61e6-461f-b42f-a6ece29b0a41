package com.doctor.br.activity.mine;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.View;
import android.widget.AdapterView;

import com.doctor.br.activity.LoadDataActivity;
import com.doctor.br.activity.MainActivity;
import com.doctor.br.activity.ShowImageActivity;
import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.mine.QualificationAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.CommitAuthenticationBean;
import com.doctor.br.bean.GetAuthenticationBean;
import com.doctor.br.bean.UploadFileBean;
import com.doctor.br.bean.event.FinishQualificationSuccessActivityEvent;
import com.doctor.br.bean.event.RefreshDoctorDataEvent;
import com.doctor.br.db.entity.ServerConfig;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.FileIOUtils;
import com.doctor.br.utils.PopUtils;
import com.doctor.br.utils.SelectImgUtils;
import com.doctor.br.view.SelectPhotoDialog;
import com.doctor.greendao.gen.ServerConfigDao;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ParseData;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.HttpRequestTask;
import org.newapp.ones.base.network.OnRequestListener;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.ImageUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import butterknife.BindView;

/**
 * 类描述：资质认证界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/30
 */
public class QualificationActivity extends ActionBarActivity implements RecyclerItemItemClickListener {
    //上个界面传递过来的数据
    public static final String HISTORY_STATE = "historyState";
    public static final String STATE = "state";
    public static final String CLASS = "fromClass";
    private String historyState;//之前是否认证成功过 0 没有 1 成功过
    private String currentAuthenticationState;//当前认证状态 1 成功 2 审核中 3 未认证 4 审核失败
    private String name, title, hospitalName;
    private String fromClass;//从哪个页面跳转过来，非必传
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;


    private RequestCallBack getImgListCallBack;//网络请求获取资质认证图片回调
    private RequestCallBack commitCallBack;//网络请求提交审核
    private RequestCallBack[] updateImgCallBacks = new RequestCallBack[9];//网络请求上传图片回调
    private GetAuthenticationBean netBean;//网络请求获取认证信息返回值

    /**
     * 区分那个按钮调取的相机或相册
     * 1 医师资格证书第一张 2 医师资格证书第二张 3 医师资格证书第三张
     * 4 医师执业证书第一张 5 医师执业证书第二张 6 医师执业证书第三张
     * 7 职称证书第一张 8 职称证书第二张 9 职称证书第三张
     */
    private int clickWitch = -1;
    private PopUtils photoPop;//选择图片

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_qualification);
        getIntentData(savedInstanceState);
        initView();
        getImgListRequest();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(STATE, currentAuthenticationState);
        outState.putString(HISTORY_STATE, historyState);
        outState.putString(CLASS, fromClass);
        outState.putString(QualificationSuccessActivity.NAME, name);
        outState.putString(QualificationSuccessActivity.TITLE, title);
        outState.putString(QualificationSuccessActivity.HOSPITAL_NAME, hospitalName);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            fromClass = savedInstanceState.getString(CLASS);
            currentAuthenticationState = savedInstanceState.getString(STATE);
            historyState = savedInstanceState.getString(HISTORY_STATE);
            name = savedInstanceState.getString(QualificationSuccessActivity.NAME);
            title = savedInstanceState.getString(QualificationSuccessActivity.TITLE);
            hospitalName = savedInstanceState.getString(QualificationSuccessActivity.HOSPITAL_NAME);
        } else {
            fromClass = getIntent().getStringExtra(CLASS);
            currentAuthenticationState = getIntent().getStringExtra(STATE);
            historyState = getIntent().getStringExtra(HISTORY_STATE);
            name = getIntent().getStringExtra(QualificationSuccessActivity.NAME);
            title = getIntent().getStringExtra(QualificationSuccessActivity.TITLE);
            hospitalName = getIntent().getStringExtra(QualificationSuccessActivity.HOSPITAL_NAME);
        }
        if (TextUtils.isEmpty(currentAuthenticationState)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }

    private void initView() {
        setActionBarTitle("认证");
        getBaseActionBar().setRightButton("查看示例");
        getBaseActionBar().setRightButtonColor(ContextCompat.getColor(this, R.color.br_color_theme));
        getBaseActionBar().setActionBarChangeListener(this);

        String[] strings = {"拍照", "从相册选取"};
        photoPop = new PopUtils(this, R.string.personal_select, Arrays.asList(strings), new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                switch (position) {
                    case 0://拍照
                        SelectImgUtils.gotoCarema(QualificationActivity.this);
                        break;
                    case 1://相册
                        SelectImgUtils.gotoPhoto(QualificationActivity.this);
                        break;
                    default:
                        break;
                }
                photoPop.dismiss();
            }
        });

    }

    @Override
    public void onRightBtnClick(View view) {
        startActivity(new Intent(this, CertificatExampleActivity.class));
    }


    @Override
    public void itemViewClick(int position, View view) {
        if (netBean == null) {
            getImgListRequest();
//            return;
        }
        if ("2".equals(currentAuthenticationState)) {
            //审核中，不可点击
            return;
        }
        ArrayList<String> imgList = new ArrayList<>();
        if ("4".equals(currentAuthenticationState)) {
            //认证失败的情况下，点击未认证失败的图片可以查看大图
            //将不是认证失败的图片添加到集合中
            for (GetAuthenticationBean.WorkCardUrlBean workCardUrlBean : netBean.getWorkCardUrl()) {
                if (!TextUtils.isEmpty(workCardUrlBean.getImgUrl()) && !"4".equals(workCardUrlBean.getResult())) {
                    imgList.add(workCardUrlBean.getImgUrl());
                }
            }
            for (GetAuthenticationBean.QualificationsCardUrlBean qualificationsCardUrlBean : netBean.getQualificationsCardUrl()) {
                if (!TextUtils.isEmpty(qualificationsCardUrlBean.getImgUrl()) && !"4".equals(qualificationsCardUrlBean.getResult())) {
                    imgList.add(qualificationsCardUrlBean.getImgUrl());
                }
            }
            for (GetAuthenticationBean.PracticeCardUrlBean practiceCardUrlBean : netBean.getPracticeCardUrl()) {
                if (!TextUtils.isEmpty(practiceCardUrlBean.getImgUrl()) && !"4".equals(practiceCardUrlBean.getResult())) {
                    imgList.add(practiceCardUrlBean.getImgUrl());
                }
            }
        }
        //点击位置左中右的flag标志，0左，1中，2右
        int clickImg = 0;
        switch (view.getId()) {
            case R.id.left_add_img:
                clickImg = 0;
                break;
            case R.id.center_add_img:
                clickImg = 1;
                break;
            case R.id.right_add_img:
                clickImg = 2;
                break;
            default:
                break;
        }
        String url = "";//点击的图片url
        switch (position) {
            case 1://医师资格证书的那一行
                clickWitch = position + clickImg;
                for (int i = 0; i < netBean.getWorkCardUrl().size(); i++) {
                    if (i == clickImg) {
                        if (!TextUtils.isEmpty(netBean.getWorkCardUrl().get(i).getImgUrl()) &&
                                !"4".equals(netBean.getWorkCardUrl().get(i).getResult())) {
                            url = netBean.getWorkCardUrl().get(i).getImgUrl();
                        }
                    }
                }
                failClick(position, imgList, url, clickImg);
                break;
            case 2://医师执业证书的那一行
                clickWitch = position + 2 + clickImg;
                for (int i = 0; i < netBean.getQualificationsCardUrl().size(); i++) {
                    if (i == clickImg) {
                        if (!TextUtils.isEmpty(netBean.getQualificationsCardUrl().get(i).getImgUrl()) &&
                                !"4".equals(netBean.getQualificationsCardUrl().get(i).getResult())) {
                            url = netBean.getQualificationsCardUrl().get(i).getImgUrl();
                        }
                    }
                }
                failClick(position, imgList, url, clickImg);
                break;
            case 3://职称证书的那一行
                clickWitch = position + 4 + clickImg;
                for (int i = 0; i < netBean.getPracticeCardUrl().size(); i++) {
                    if (i == clickImg) {
                        if (!TextUtils.isEmpty(netBean.getPracticeCardUrl().get(i).getImgUrl()) &&
                                !"4".equals(netBean.getPracticeCardUrl().get(i).getResult())) {
                            url = netBean.getPracticeCardUrl().get(i).getImgUrl();
                        }
                    }
                }
                failClick(position, imgList, url, clickImg);
                break;
            case 4://提交审核那一行
                commitRequest();
            default:
                clickWitch = -1;
                break;
        }
    }

    /**
     * 认证失败的情况下点击图片时的交互
     *
     * @param position 点击的第几行，除了header，从1开始
     * @param imgList  存储图片地址的集合
     * @param url      点击当前图片的url
     * @param clickImg 点击图片的位置 0,1,2
     */
    private void failClick(int position, ArrayList<String> imgList, String url, int clickImg) {
        if ("4".equals(currentAuthenticationState)) {
            if (clickImg >= failSizes[position - 1]) {
                //点击的位置没有图片
                photoPop.showPopupWindow();
                return;
            }
            if (failPositions.contains("" + position + clickImg)) {
                //如果是认证失败状态，并且点击的为认证失败的图片，点击修改照片
                photoPop.showPopupWindow();
                return;
            }
            //如果是认证失败状态，并且不是认证失败的图片，点击查看大图
            if (imgList.size() > 0 && imgList.contains(url)) {
                ShowImageActivity.showImage(this, imgList, url);
            }
        } else {
            photoPop.showPopupWindow();
        }
    }

    @Override
    protected void onActivityResult(final int requestCode, int resultCode, Intent data) {
        if (clickWitch < 1 || clickWitch > 9) {
            return;
        }
        if (resultCode == RESULT_OK) {
//            String filePath = null;//获取图片的真实路径
            Uri uri = null;
            switch (requestCode) {
                case SelectPhotoDialog.REQUEST_CAPTURE: //调用系统相机返回
                    uri = Uri.fromFile(SelectImgUtils.tempFile);
//                    filePath = SelectImgUtils.getRealFilePathFromUri(this, Uri.fromFile(SelectImgUtils.tempFile));
                    break;
                case SelectPhotoDialog.REQUEST_PICK:  //调用系统相册返回
                    uri = data.getData();
//                    filePath = SelectImgUtils.getRealFilePathFromUri(this, data.getData());
                    break;
                default:
                    break;
            }
            if (uri == null) {
                return;
            }
            if (mLoadingDialog != null && !mLoadingDialog.isShowing()) {
                mLoadingDialog.show();
            }
            //压缩图片文件
            Bitmap dimenBitmap = ImageUtils.createBitmapFromUri(this,uri, 720, 1280);//大小压缩后的图片
            SelectImgUtils.compressBitmapFile = new File(getExternalCacheDir(), "compressImg.jpg");//最后压缩完成存储的图片路径
            try {
                SelectImgUtils.compressBitmapFile.createNewFile();
            } catch (IOException e) {
                LogUtils.i(QualificationActivity.class, e.toString());
                return;
            }
            //将大小压缩后的图片进行质量压缩后，写入文件中准备上传；质量压缩大小限制上限为500k;
            FileIOUtils.writeFileFromBytesByStream(SelectImgUtils.compressBitmapFile,
                    ImageUtils.compressByQuality(dimenBitmap, 500 * 1024L, true), false);
            File protraitFile = SelectImgUtils.compressBitmapFile;

            Map<String, String> params = new HashMap<>();
            params.put("resourceType", "image");
            Map<String, File> fileParams = new HashMap<>();
            fileParams.put(protraitFile.getAbsolutePath(), protraitFile);
            //先上传后显示
            updateImgCallBacks[clickWitch - 1] = HttpRequestTask.getInstance()
                    .addHttpPostFileRequest(HttpUrlManager.UPLOAD_FILE, params, fileParams, UploadFileBean.class, new OnRequestListener() {
                        @Override
                        public void onRequestFinished(String taskId, boolean successed, String responseStr) {

                        }

                        @Override
                        public void onRequestFinished(String taskId, ResponseResult result) {
                            if (recyclerView == null) {
                                return;
                            }
                            if (result.isRequestSuccessed()) {
                                UploadFileBean uploadFileBean = (UploadFileBean) result.getBodyObject();
                                upImg(uploadFileBean, clickWitch);
                            } else {
                                RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                            }
                            if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                                mLoadingDialog.dismiss();
                            }
                        }
                    });
        }
    }

    /**
     * 上传图片完成后的操作
     *
     * @param uploadFileBean 上传图片服务器返回值
     */
    private void upImg(UploadFileBean uploadFileBean, int clickWitch) {
        if (clickWitch < 1) {
            return;
        }
        if (clickWitch < 4) {
            if (netBean.getWorkCardUrl().size() <= clickWitch - 1) {
                netBean.getWorkCardUrl().add(new GetAuthenticationBean.WorkCardUrlBean(uploadFileBean.getUrl(), null));
            } else {
                netBean.getWorkCardUrl().set(clickWitch - 1, new GetAuthenticationBean.WorkCardUrlBean(uploadFileBean.getUrl(), null));
            }
        } else if (clickWitch < 7) {
            if (netBean.getQualificationsCardUrl().size() <= clickWitch - 4) {
                netBean.getQualificationsCardUrl().add(new GetAuthenticationBean.QualificationsCardUrlBean(uploadFileBean.getUrl(), null));
            } else {
                netBean.getQualificationsCardUrl().set(clickWitch - 4, new GetAuthenticationBean.QualificationsCardUrlBean(uploadFileBean.getUrl(), null));
            }
        } else {
            if (netBean.getPracticeCardUrl().size() <= clickWitch - 7) {
                netBean.getPracticeCardUrl().add(new GetAuthenticationBean.PracticeCardUrlBean(uploadFileBean.getUrl(), null));
            } else {
                netBean.getPracticeCardUrl().set(clickWitch - 7, new GetAuthenticationBean.PracticeCardUrlBean(uploadFileBean.getUrl(), null));
            }
        }
        if ("3".equals(currentAuthenticationState) || "4".equals(currentAuthenticationState)) {
            SharedPreferenceUtils.putString(this, PublicParams.QUALIFICATION_IMGS, ParseData.parseObject(netBean));
        }
        recyclerView.setAdapter(new QualificationAdapter(QualificationActivity.this, netBean, currentAuthenticationState, QualificationActivity.this));

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        SelectImgUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults);
    }

    //网络请求获取资质认证图片信息
    private void getImgListRequest() {
        if (TextUtils.isEmpty(currentAuthenticationState) || "3".equals(currentAuthenticationState)
                || "1".equals(currentAuthenticationState)) {
            //如果是未认证状态或认证成功状态，就不用网络请求了。直接查看本地有没有之前未提交审核的图片，没有就算了
            try {
                String json = SharedPreferenceUtils.getString(this, PublicParams.QUALIFICATION_IMGS);
                netBean = ParseData.parseString(json, GetAuthenticationBean.class);
                if (netBean == null) {
                    netBean = new GetAuthenticationBean();
                }
            } catch (Exception e) {
                netBean = new GetAuthenticationBean();
            }

            recyclerView.setAdapter(new QualificationAdapter(this, netBean, currentAuthenticationState, this));
            return;
        }
        getImgListCallBack = addHttpPostRequest(HttpUrlManager.GET_AUTHENTICATION_IMGS, null, GetAuthenticationBean.class, this);
    }

    //网络请求提交审核
    private void commitRequest() {
        Map<String, String> map = new HashMap<>();
        if (netBean.getWorkCardUrl().size() == 0 &&
                netBean.getQualificationsCardUrl().size() == 0 &&
                netBean.getPracticeCardUrl().size() == 0) {
            ToastUtils.showShortMsg(this, "请上传证书！");
            return;
        }

        StringBuilder workCardUrl = new StringBuilder();
        StringBuilder qualificationsCardUrl = new StringBuilder();
        StringBuilder practiceCardUrl = new StringBuilder();
        for (GetAuthenticationBean.WorkCardUrlBean workCardUrlBean : netBean.getWorkCardUrl()) {
            if (addPram(workCardUrl, workCardUrlBean.getImgUrl(), workCardUrlBean.getResult())) {
                return;
            }
        }
        for (GetAuthenticationBean.QualificationsCardUrlBean qualificationsCardUrlBean : netBean.getQualificationsCardUrl()) {
            if (addPram(qualificationsCardUrl, qualificationsCardUrlBean.getImgUrl(), qualificationsCardUrlBean.getResult())) {
                return;
            }
        }
        for (GetAuthenticationBean.PracticeCardUrlBean practiceCardUrlBean : netBean.getPracticeCardUrl()) {
            if (addPram(practiceCardUrl, practiceCardUrlBean.getImgUrl(), practiceCardUrlBean.getResult())) {
                return;
            }
        }
        if (workCardUrl.length() > 0) {
            workCardUrl.deleteCharAt(workCardUrl.length() - 1);
        }
        if (qualificationsCardUrl.length() > 0) {
            qualificationsCardUrl.deleteCharAt(qualificationsCardUrl.length() - 1);
        }

        if (practiceCardUrl.length() > 0) {
            practiceCardUrl.deleteCharAt(practiceCardUrl.length() - 1);
        }
        map.put("workCardUrl", workCardUrl.toString());
        map.put("qualificationsCardUrl", qualificationsCardUrl.toString());
        map.put("practiceCardUrl", practiceCardUrl.toString());
        map.put("type", "2");//2 是提交审核
        commitCallBack = addHttpPostRequest(HttpUrlManager.SAVE_AUTHENTICATION_IMGS, map, CommitAuthenticationBean.class, this);
    }

    /**
     * 拼接上传参数
     *
     * @return false表示正常，true表示有不合格的证书
     */
    private boolean addPram(StringBuilder stringBuilder, String url, String result) {
        if (!TextUtils.isEmpty(url)) {
            stringBuilder.append(url).append(",");
        }
        if ("4".equals(result)) {
            ToastUtils.showShortMsg(this, "您有不合格的证书！");
            return true;
        }
        return false;
    }

    //审核失败的情况下，存放可以点击的图片，第一位为position，第二位为图片位置
    //例如："10"表示第一行的第一张图片为审核失败的图片
    private Set<String> failPositions = new HashSet<>();
    //存放图片的size数组
    private int[] failSizes = new int[3];

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (recyclerView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.GET_AUTHENTICATION_IMGS://获取服务器图片列表
                if (result.isRequestSuccessed()) {
                    netBean = (GetAuthenticationBean) result.getBodyObject();
                    if ("4".equals(currentAuthenticationState)) {//审核失败
                        //如果用户在修改完图片后直接退出而没有提交审核，则去本地读取是否有存储的图片，
                        // 有的话就显示本地存储的图片，并修改状态，没有就使用网络返回的图片
                        GetAuthenticationBean localBean;
                        try {
                            String json = SharedPreferenceUtils.getString(this, PublicParams.QUALIFICATION_IMGS);
                            localBean = ParseData.parseString(json, GetAuthenticationBean.class);
                        } catch (Exception e) {
                            localBean = new GetAuthenticationBean();
                        }
                        failSizes[0] = netBean.getWorkCardUrl().size();
                        failSizes[1] = netBean.getQualificationsCardUrl().size();
                        failSizes[2] = netBean.getPracticeCardUrl().size();
                        for (int i = 0; i < netBean.getWorkCardUrl().size(); i++) {
                            if ("4".equals(netBean.getWorkCardUrl().get(i).getResult())) {
                                failPositions.add("1" + i);
                                try {
                                    if (!TextUtils.isEmpty(localBean.getWorkCardUrl().get(i).getImgUrl())) {
                                        netBean.getWorkCardUrl().set(i, localBean.getWorkCardUrl().get(i));
                                    }
                                } catch (Exception ignored) {

                                }
                            }
                        }
                        for (int i = 0; i < netBean.getQualificationsCardUrl().size(); i++) {
                            if ("4".equals(netBean.getQualificationsCardUrl().get(i).getResult())) {
                                failPositions.add("2" + i);
                                try {
                                    if (!TextUtils.isEmpty(localBean.getQualificationsCardUrl().get(i).getImgUrl())) {
                                        netBean.getQualificationsCardUrl().set(i, localBean.getQualificationsCardUrl().get(i));
                                    }
                                } catch (Exception ignored) {

                                }
                            }
                        }
                        for (int i = 0; i < netBean.getPracticeCardUrl().size(); i++) {
                            if ("4".equals(netBean.getPracticeCardUrl().get(i).getResult())) {
                                failPositions.add("3" + i);
                                try {
                                    if (!TextUtils.isEmpty(localBean.getPracticeCardUrl().get(i).getImgUrl())) {
                                        netBean.getPracticeCardUrl().set(i, localBean.getPracticeCardUrl().get(i));
                                    }
                                } catch (Exception ignored) {

                                }
                            }
                        }
                    }
                    recyclerView.setAdapter(new QualificationAdapter(this, netBean, currentAuthenticationState, this));
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.SAVE_AUTHENTICATION_IMGS://提交认证
                if (result.isRequestSuccessed()) {
                    final CommitAuthenticationBean commitAuthenticationBean = (CommitAuthenticationBean) result.getBodyObject();
                    closeQualificationSuccessActivity();
                    refreshMyCenterFragment(commitAuthenticationBean);
                    SharedPreferenceUtils.removeString(this, PublicParams.QUALIFICATION_IMGS);
                    ToastUtils.showShortMsg(this, "提交成功");

                    Intent intent;
                    if ("1".equals(commitAuthenticationBean.getState())) {
                        intent = new Intent(QualificationActivity.this, QualificationSuccessActivity.class);
                        intent.putExtra(QualificationSuccessActivity.NAME, name);
                        intent.putExtra(QualificationSuccessActivity.TITLE, title);
                        intent.putExtra(QualificationSuccessActivity.HOSPITAL_NAME, hospitalName);
                    } else {
                        intent = new Intent(QualificationActivity.this, QualificationingActivity.class);
                    }
                    intent.putExtra(QualificationActivity.CLASS, fromClass);
                    intent.putExtra(QualificationingActivity.HISTORY_STATE, historyState);
                    startActivity(intent);
                    finish();
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 从资质认证界面更新我的界面个人信息
     *
     * @sender {@link com.doctor.br.activity.mine.QualificationActivity#refreshMyCenterFragment(CommitAuthenticationBean)}
     * @recevier {@link com.doctor.br.fragment.main.MyCenterFragment#refreshDoctorInfo}
     * @recevier {@link com.doctor.br.activity.medical.PreviewMedicationActivity}
     */
    private void refreshMyCenterFragment(CommitAuthenticationBean commitAuthenticationBean) {
        EventBusUtils.post(new RefreshDoctorDataEvent(commitAuthenticationBean.getState()));
    }

    /**
     * 关闭之前的认证成功界面
     *
     * @sender {@link QualificationActivity#closeQualificationSuccessActivity()}
     * @receiver {@link QualificationSuccessActivity#onReceive(FinishQualificationSuccessActivityEvent)}
     */
    private void closeQualificationSuccessActivity() {
        EventBusUtils.post(new FinishQualificationSuccessActivityEvent(true));
    }

    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    private ConfirmDialog mBackDialog;

    @Override
    public void onBackPressed() {
//        if (TextUtils.isEmpty(currentAuthenticationState) || "3".equals(currentAuthenticationState)) {
//            GetAuthenticationBean localBean;
//            try {
//                String json = SharedPreferenceUtils.getString(this, PublicParams.QUALIFICATION_IMGS);
//                localBean = ParseData.parseString(json, GetAuthenticationBean.class);
//                //判断是否需要弹出提示框
//                boolean isDialog = false;
//                for (GetAuthenticationBean.WorkCardUrlBean workCardUrlBean : localBean.getWorkCardUrl()) {
//                    if (!TextUtils.isEmpty(workCardUrlBean.getImgUrl())) {
//                        isDialog = true;
//                        break;
//                    }
//                }
//                if (!isDialog) {
//                    for (GetAuthenticationBean.QualificationsCardUrlBean qualificationsCardUrlBean : localBean.getQualificationsCardUrl()) {
//                        if (!TextUtils.isEmpty(qualificationsCardUrlBean.getImgUrl())) {
//                            isDialog = true;
//                            break;
//                        }
//                    }
//                }
//                if (!isDialog) {
//                    for (GetAuthenticationBean.PracticeCardUrlBean practiceCardUrlBean : localBean.getPracticeCardUrl()) {
//                        if (!TextUtils.isEmpty(practiceCardUrlBean.getImgUrl())) {
//                            isDialog = true;
//                            break;
//                        }
//                    }
//                }
//                if (isDialog) {
//                    mBackDialog = ConfirmDialog.getInstance(this)
//                            .setDialogTitle("提示")
//                            .setDialogContent("尚未提交审核，是否退出并保存？")
//                            .setPositiveText("确定")
//                            .setNavigationText("取消")
//                            .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
//                                @Override
//                                public void onNavigationBtnClicked(View view) {
//                                    mBackDialog.dismiss();
//                                }
//
//                                @Override
//                                public void onPositiveBtnClicked(View view) {
//                                    mBackDialog.dismiss();
//                                    toMainOrBack();
//                                }
//                            });
//                    mBackDialog.show();
//                    return;
//                }
//            } catch (Exception ignored) {
//                toMainOrBack();
//            }
//        }
        toMainOrBack();
    }

    /**
     * 新注册用户认证返回到主页面或返回到上一页面
     */
    private void toMainOrBack() {
        if (!TextUtils.isEmpty(fromClass) && fromClass.equals(PersonalDataActivity.class.getSimpleName())) {
            Intent intent;
            if (isLoadData()) {
                intent = new Intent(QualificationActivity.this, LoadDataActivity.class);
            } else {
                intent = new Intent(QualificationActivity.this, MainActivity.class);
            }
            startActivity(intent);
        }
        finish();
    }

    /**
     * 判断是否加载数据
     *
     * @return
     */
    private boolean isLoadData() {
        ServerConfigDao serverConfigDao = AppContext.getInstances().getDaoSession().getServerConfigDao();
        List<ServerConfig> list = serverConfigDao.loadAll();
        if (list != null && list.size() > 0) {
            ServerConfig config = list.get(0);
            if (config != null) {
                if (!TextUtils.isEmpty(config.getArea_addressVersion()) && !config.getArea_addressVersion().equalsIgnoreCase(config.getArea_addressVersionLocal())) {
                    return true;
                }

                if (!TextUtils.isEmpty(config.getProduct_drugVersion()) && !config.getProduct_drugVersion().equalsIgnoreCase(config.getProduct_drugVersionLocal())) {
                    return true;
                }
            }
        }
        String updateContactsListTime = SharedPreferenceUtils.getString(this, PublicParams.UPDATE_CONTACTS_LIST_TIME);
        if (TextUtils.isEmpty(updateContactsListTime)) {
            return true;
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        if (getImgListCallBack != null) {
            getImgListCallBack.cancel();
        }
        for (RequestCallBack callBack : updateImgCallBacks) {
            if (callBack != null) {
                callBack.cancel();
            }
        }
        if (commitCallBack != null) {
            commitCallBack.cancel();
        }
        super.onDestroy();
    }
}
