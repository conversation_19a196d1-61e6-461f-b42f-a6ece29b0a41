package com.doctor.br.activity.medical;

import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSON;
import com.doctor.br.bean.UploadFileBean;
import com.doctor.br.bean.medical.DrugsBatchQueryBean;
import com.doctor.br.bean.medical.HerbInfo;
import com.doctor.br.bean.medical.MedicineOrTempleBean;
import com.doctor.br.bean.medical.TCMMatchResult;
import com.doctor.br.bean.ocr.OCRResponse;
import com.doctor.br.bean.ocr.TextDetection;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.PopUtils;
import com.doctor.br.utils.SelectImgUtils;
import com.doctor.br.utils.TCPMappingParser;
import com.doctor.br.utils.TencentOCRRequest;
import com.doctor.br.view.DrugSpecSelectPop;
import com.doctor.yy.R;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.LoadingDialog;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import android.text.TextUtils;

import butterknife.BindView;
import butterknife.OnClick;

public class BRIntelligentEntryActivity extends NoActionBarActivity implements AdapterView.OnItemClickListener {

    // 库存信息查询回调接口
    public interface StockInfoCheckCallback {
        void onStockInfoChecked(List<TCMMatchResult> updatedResults);
    }

    private PopUtils choosePhotoPop;
    private RequestCallBack updateImgCallBack;
    private LoadingDialog mLoadingDialog;
    private String drugType = "";  // 药材类型
    private String drugForm = "";   // 剂型
    private String drugProviderId = "";  // 供应商ID

    // 需要添加以下成员变量用于保存回调相关信息
    private StockInfoCheckCallback checkStockInfoCallback;
    private List<TCMMatchResult> checkStockInfoResults;
    private Map<String, HerbInfo> checkStockInfoDrugInfoMap;
    private String checkStockInfoRequestId;


    @BindView(R.id.back_img)
    ImageView backImg;

    @BindView(R.id.input_text)
    TextView inputText;
    @BindView(R.id.save_tv)
    TextView saveTv;

    @BindView(R.id.clear_btn)
    TextView clearBtn;

    @BindView(R.id.scan_btn)
    LinearLayout scanBtn;

    @OnClick(R.id.save_tv)
    void onSaveClick() {
        // 保存按钮点击事件
        LogUtils.i(BRIntelligentEntryActivity.class, "Clicked save button");

        String text = inputText.getText().toString().trim();
        if (text.isEmpty()) {
            ToastUtils.showShortMsg(this, "请拍照识别或者粘贴处方");
            return;
        }

        TCPMappingParser parser = new TCPMappingParser(mContext);
        List<TCMMatchResult> results = parser.parsePrescription(text);

        // 新增：检查是否需要选择规格
        boolean needSelect = false;
        for (TCMMatchResult result : results) {
            if (result.getHerbInfos().size() > 1) {
                needSelect = true;
                break;
            }
        }

        // 新增：根据是否需要选择规格判断下一步操作
        if (needSelect) {
            // 需要查询库存信息再显示选择弹窗
            checkStockInfoForMatchResults(results, new StockInfoCheckCallback() {
                @Override
                public void onStockInfoChecked(List<TCMMatchResult> updatedResults) {
                    // 显示带库存信息的弹窗
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            DrugSpecSelectPop pop = new DrugSpecSelectPop(BRIntelligentEntryActivity.this, updatedResults);
                            pop.setOnBottomViewClickListener(new DrugSpecSelectPop.OnBottomViewClickListener() {
                                @Override
                                public void onBottomViewClick(List<HerbInfo> selectedHerbs) {
                                    handleSelectedHerbs(selectedHerbs);
                                }
                            });
                            pop.showPop();
                        }
                    });
                }
            });
        } else {
            // 新增：不需要选择规格时，直接使用第一个规格
            List<HerbInfo> herbList = new ArrayList<>();
            for (TCMMatchResult result : results) {
                herbList.add(result.getHerbInfos().get(0));
            }
            handleSelectedHerbs(herbList);
        }
    }

    /**
     * 查询药材库存信息
     * @param results 原始的药材匹配结果
     * @param callback 查询完成回调
     */
    private void checkStockInfoForMatchResults(List<TCMMatchResult> results, StockInfoCheckCallback callback) {
        // 创建所有需要查询库存的drugName数组
        List<String> drugNames = new ArrayList<>();
        Map<String, HerbInfo> drugInfoMap = new HashMap<>();

        // 遍历所有需要选择规格的药材
        for (TCMMatchResult result : results) {
            if (result.getHerbInfos() != null && result.getHerbInfos().size() > 1) {
                for (HerbInfo herbInfo : result.getHerbInfos()) {
                    // 对于每个药材规格，我们需要查询其库存
                    if (!TextUtils.isEmpty(herbInfo.getDrugName())) {
                        drugNames.add(herbInfo.getDrugName());
                        drugInfoMap.put(herbInfo.getDrugName(), herbInfo);
                    }
                }
            }
        }

        if (drugNames.isEmpty()) {
            // 没有需要查询的药材，直接返回原结果
            if (callback != null) {
                callback.onStockInfoChecked(results);
            }
            return;
        }

        // 显示加载对话框
        if (mLoadingDialog != null && !mLoadingDialog.isShowing()) {
            mLoadingDialog.setTitle("正在查询库存信息...");
            mLoadingDialog.show();
        }

        try {
            // 构建请求参数
            Map<String, String> requestParams = new HashMap<>();
            requestParams.put("method_code", "000451");
            requestParams.put("appVer", "1.0"); // 版本号
            requestParams.put("drugType", drugType);
            requestParams.put("drugForm", drugForm);
            requestParams.put("drugProviderId", drugProviderId);

            // 将drugNames数组转为JSON格式
            JSONArray drugNamesArray = new JSONArray();
            for (String name : drugNames) {
                drugNamesArray.put(name);
            }
            requestParams.put("nameList", drugNamesArray.toString());
            requestParams.put("dcId", "");

            // 发起网络请求，使用HttpUrlManager.GET_DRUGS_BY_BATCH_QUERY接口
            RequestCallBack requestCallBack = addHttpPostRequest(
                    HttpUrlManager.GET_DRUGS_BY_BATCH_QUERY,
                    requestParams,
                    DrugsBatchQueryBean.class,
                    this
            );

            // 存储回调和请求相关信息，以便在onRequestFinished中处理
            checkStockInfoCallback = callback;
            checkStockInfoResults = results;
            checkStockInfoDrugInfoMap = drugInfoMap;
            checkStockInfoRequestId = HttpUrlManager.GET_DRUGS_BY_BATCH_QUERY;

        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.e(BRIntelligentEntryActivity.class, "构建请求参数错误: " + e.getMessage());

            if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                mLoadingDialog.dismiss();
            }

            if (callback != null) {
                callback.onStockInfoChecked(results);
            }
        }
    }
    // 新增：抽取共同的处理逻辑为单独的方法
    private void handleSelectedHerbs(List<HerbInfo> selectedHerbs) {
        for (HerbInfo herb : selectedHerbs) {
            Log.d("TAG", "Selected herb: " + herb.getDrugName());
        }
        Intent intent = new Intent();
        ArrayList<HerbInfo> herbList1 = new ArrayList<>(selectedHerbs);
        intent.putExtra("key", "BRIntelligent");
        intent.putParcelableArrayListExtra("herbList", herbList1);
        setResult(RESULT_OK, intent);
        finish();
    }

    @OnClick(R.id.clear_btn)
    void onClearClick() {
        // 清除按钮点击事件
        LogUtils.i(BRIntelligentEntryActivity.class, "Clicked clear button");
        inputText.setText("");
    }

    @OnClick(R.id.scan_btn)
    void onScanClick() {
        // 扫描按钮点击事件
        LogUtils.i(BRIntelligentEntryActivity.class, "Clicked scan button");
        choosePhotoPop.showPopupWindow();
        //键盘退出
        InputMethodManager imm = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(inputText.getWindowToken(), 0);
    }

    @OnClick(R.id.back_img)
    void onBackClick() {
        finish();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setActionBarContentView(R.layout.activity_intelligent_entry);

        mLoadingDialog = LoadingDialog.getInstance(mContext);
        
        // 从 Intent 中获取套餐信息
        Intent intent = getIntent();
        if (intent != null) {
            drugType = intent.getStringExtra("drugType");
            drugForm = intent.getStringExtra("drugForm");
            drugProviderId = intent.getStringExtra("drugProviderId");
        }

        //设置choosePhotoPop
        choosePhotoPop = new PopUtils(this, R.string.hint_take_photo, Arrays.asList("拍照【建议手机竖拍】", "从相册选取"), this);

        // 设置图片选择监听器，跳过裁剪直接处理图片
        SelectImgUtils.setOnImageSelectedListener(new SelectImgUtils.OnImageSelectedListener() {
            @Override
            public void onImageSelected(Uri uri) {
                if (uri == null) {
                    return;
                }

                // Show loading dialog
                if (mLoadingDialog != null && !mLoadingDialog.isShowing()) {
                    mLoadingDialog.show();
                }

                String path = SelectImgUtils.getRealFilePathFromUri(BRIntelligentEntryActivity.this, uri);
                LogUtils.i(BRIntelligentEntryActivity.class, "path==>" + path);

                File imageFile = new File(path);
                // ... 压缩和上传逻辑保持不变 ...
                //图片如果大于7M，则压缩到7M以下
                long maxSize = 7 * 1024 * 1024; // 7MB in bytes
                long fileSize = imageFile.length();
                
                if (fileSize > maxSize) {
                    try {
                        // Load bitmap from file
                        Bitmap bitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), Uri.fromFile(imageFile));
                        
                        // Start with quality 100 and reduce until file size is under maxSize
                        int quality = 100;
                        ByteArrayOutputStream baos;
                        byte[] imageData;
                        
                        do {
                            baos = new ByteArrayOutputStream();
                            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);
                            imageData = baos.toByteArray();
                            quality -= 5;
                        } while (imageData.length > maxSize && quality > 0);
                        
                        // Write compressed image back to file
                        FileOutputStream fos = new FileOutputStream(imageFile);
                        fos.write(imageData);
                        fos.close();
                        
                        LogUtils.i(BRIntelligentEntryActivity.class, "Compressed image from " + fileSize + " to " + imageFile.length() + " bytes");
                        
                    } catch (IOException e) {
                        LogUtils.e(BRIntelligentEntryActivity.class, "Error compressing image: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                
                String secretId = "AKIDNaaMvHPHwNbaJwLWkx1yACC1jKo6A1jM";
                String secretKey = "XwJWVG7PTODuPubWVPTcechdeYFoAJJA";
                // Load bitmap for OCR
                Bitmap bitmap = null;
                try {
                    bitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), Uri.fromFile(imageFile));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }

                // Call Tencent OCR API
                TencentOCRRequest.generalHandwritingOCR(bitmap, secretId, secretKey, new TencentOCRRequest.OCRRequestCallback() {
                    @Override
                    public void onSuccess(JSONObject response) {
                        // Hide loading dialog
                        if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                            mLoadingDialog.dismiss();
                        }
                        
                        try {
                            OCRResponse ocrResponse = JSON.parseObject(response.toString(), OCRResponse.class);
                            if (ocrResponse != null && ocrResponse.getResponse() != null) {
                                List<TextDetection> detections = ocrResponse.getResponse().getTextDetections();
                                // 将识别结果显示在inputText中
                                StringBuilder sb = new StringBuilder();
                                for (TextDetection detection : detections) {
                                    sb.append(detection.getDetectedText());
                                }
                                inputText.setText(sb.toString());
                            }
                        } catch (Exception e) {
                            LogUtils.e(BRIntelligentEntryActivity.class, "Parse OCR response error: " + e.getMessage());
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onError(Exception error) {
                        // Hide loading dialog
                        if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                            mLoadingDialog.dismiss();
                        }
                        
                        LogUtils.e(BRIntelligentEntryActivity.class, "OCR Error: " + error.getMessage());
                        error.printStackTrace();
                    }
                });



            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清除监听器
        SelectImgUtils.setOnImageSelectedListener(null);
        
        // Dismiss and clean up loading dialog
        if (mLoadingDialog != null) {
            if (mLoadingDialog.isShowing()) {
                mLoadingDialog.dismiss();
            }
            mLoadingDialog = null;
        }
    }

    @Override
    public void onItemClick(AdapterView<?> adapterView, View view, int position, long id) {
        switch (position) {
            case 0://拍照
                SelectImgUtils.gotoCarema(BRIntelligentEntryActivity.this);
                break;
            case 1://相册
                SelectImgUtils.gotoPhoto(BRIntelligentEntryActivity.this);
                break;
            default:
                break;
        }
        choosePhotoPop.dismiss();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        SelectImgUtils.onActivityResult(this, requestCode, resultCode, data);
    }

    // 实现onRequestFinished方法处理请求结果
    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);

        switch (taskId) {
            case HttpUrlManager.UPLOAD_FILE:
                if (result.isRequestSuccessed()) {
                    UploadFileBean uploadFileBean = (UploadFileBean) result.getBodyObject();
                    ToastUtils.showShortMsg(BRIntelligentEntryActivity.this, "上传成功");
                    //打印上传的头像地址
                    LogUtils.i(BRIntelligentEntryActivity.class, "头像地址==>" + uploadFileBean.getUrl());
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.GET_DRUGS_BY_BATCH_QUERY:
                if (checkStockInfoCallback != null) {
                    // 关闭加载对话框
                    if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                        mLoadingDialog.dismiss();
                    }

                    if (result.isRequestSuccessed() && result.getBodyObject() instanceof DrugsBatchQueryBean) {
                        DrugsBatchQueryBean batchQueryBean = (DrugsBatchQueryBean) result.getBodyObject();
                        List<MedicineOrTempleBean> medicineList = batchQueryBean.getData();

                        // 创建药材名称到库存状态的映射
                        Map<String, String> stockInfo = new HashMap<>();
                        if (medicineList != null) {
                            for (MedicineOrTempleBean medicine : medicineList) {
                                // 确保stock值正确，默认为"0"，有库存时为"1"
                                String stockValue = "0";
                                if (!TextUtils.isEmpty(medicine.getStock()) && medicine.getStock().equals("1")) {
                                    stockValue = "1";
                                }
                                stockInfo.put(medicine.getName(), stockValue);

                                // 打印日志以便调试
                                LogUtils.i(BRIntelligentEntryActivity.class,
                                        "药材名称: " + medicine.getName() + ", 库存状态: " + stockValue);
                            }
                        }

                        // 更新药材规格列表，添加库存信息
                        List<TCMMatchResult> updatedResults = new ArrayList<>();

                        for (TCMMatchResult matchResult : checkStockInfoResults) {
                            TCMMatchResult updatedResult = new TCMMatchResult();
                            updatedResult.setMatchedName(matchResult.getMatchedName());
                            updatedResult.setPosition(matchResult.getPosition());

                            List<HerbInfo> updatedHerbInfos = new ArrayList<>();

                            if (matchResult.getHerbInfos() != null) {
                                for (HerbInfo herbInfo : matchResult.getHerbInfos()) {
                                    // 创建新的HerbInfo对象，保留所有原始信息
                                    HerbInfo updatedHerbInfo = new HerbInfo();
                                    updatedHerbInfo.setDrugName(herbInfo.getDrugName());
                                    updatedHerbInfo.setDose(herbInfo.getDose());
                                    updatedHerbInfo.setMethod(herbInfo.getMethod());
                                    updatedHerbInfo.setPosition(herbInfo.getPosition());

                                    // 设置库存信息
                                    String stock = stockInfo.get(herbInfo.getDrugName());
                                    if (stock != null) {
                                        updatedHerbInfo.setStock(stock);
                                    } else {
                                        updatedHerbInfo.setStock("0");
                                    }

                                    updatedHerbInfos.add(updatedHerbInfo);
                                }
                            }

                            updatedResult.setHerbInfos(updatedHerbInfos);
                            updatedResults.add(updatedResult);
                        }

                        // 打印日志以便调试
                        for (TCMMatchResult result1 : updatedResults) {
                            for (HerbInfo herb : result1.getHerbInfos()) {
                                LogUtils.i(BRIntelligentEntryActivity.class,
                                        "更新后药材: " + herb.getDrugName() + ", 库存状态: " + herb.getStock());
                            }
                        }

                        checkStockInfoCallback.onStockInfoChecked(updatedResults);
                    } else {
                        // 请求失败，返回原始结果
                        checkStockInfoCallback.onStockInfoChecked(checkStockInfoResults);
                    }

                    // 清除回调相关信息
                    checkStockInfoCallback = null;
                    checkStockInfoResults = null;
                    checkStockInfoDrugInfoMap = null;
                    checkStockInfoRequestId = null;
                }
                break;
            default:
                break;
        }
    }
}
