package com.doctor.br.activity.chatmain;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;

import com.doctor.br.bean.CommonProblemList;
import com.doctor.br.bean.QuickReplyBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.BaseObject;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 添加常见问题编辑页面
 * @createTime 2017/10/26
 */

public class CommonProblemEditTextActivity extends ActionBarActivity {
    //上个界面传递过来的数据
    public static final String TEXT = "text";//要显示的文字
    public static final String HINT = "hint";//内容为空的提示语
    public static final String TITLE = "title";//该界面标题
    public static final String IS_QUICK_REPLAY = "is_quick_replay";//是否快捷回复
    public static final String QUICK_REPLAY_TYPE = "quick_replay_type";
    public static final String QUICK_REPLAY_ID = "quick_replay_id";
    private String text, hint, title;
    //界面下的控件
    @BindView(R2.id.edit_text)
    EditText editText;
    @BindView(R2.id.clear_img)
    ImageView clearImg;
    private ConfirmDialog backDialog;
    private boolean isUpdated = false;//标识位，记录该常见问题是否进行过编辑
    private boolean isQuickReplay;
    private String quickReplayType;
    private String quickReplayId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.common_problem_edit);
        getIntentData();
        initView();
    }

    private void getIntentData() {
        text = getIntent().getStringExtra(TEXT);
        hint = getIntent().getStringExtra(HINT);
        title = getIntent().getStringExtra(TITLE);
        isQuickReplay = getIntent().getBooleanExtra(IS_QUICK_REPLAY, false);
        quickReplayType = getIntent().getStringExtra(QUICK_REPLAY_TYPE);
        quickReplayId = getIntent().getStringExtra(QUICK_REPLAY_ID);
    }

    private void initView() {
        setActionBarTitle(title);
        getBaseActionBar().setRightButton("保存");
        getBaseActionBar().setActionBarChangeListener(this);

        editText.setHint(hint);
        editText.setText(text);
        editText.setTextSize(16);
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() <= 0) {
                    clearImg.setVisibility(View.INVISIBLE);
                } else {
                    clearImg.setVisibility(View.VISIBLE);
                }
            }
        });
        editText.setSelection(text == null ? 0 : text.length());
        clearImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (clearImg.getVisibility() == View.VISIBLE) {
                    editText.setText("");
                }
            }
        });
    }


    /**
     * 系统返回键
     */
    @Override
    public void onBackPressed() {
        closeActivity();
    }


    /**
     * 导航栏返回箭头
     *
     * @param activity
     */
    @Override
    public void onBack(Activity activity) {
        closeActivity();
    }

    /**
     * 处理关闭页面的逻辑
     */
    private void closeActivity() {
        String currentText = editText.getText().toString();
        if (!currentText.equalsIgnoreCase(text)) {
            if (backDialog == null) {
                backDialog = ConfirmDialog.getInstance(this);
            }
            backDialog.setDialogContent(title + "未保存，是否退出？")
                    .setNavigationText("取消")
                    .setPositiveText("退出")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
                            backDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            backDialog.dismiss();
                            CommonProblemEditTextActivity.this.finish();
                        }
                    }).show();
        } else {
            finish();
        }
    }

    @Override
    public void onRightBtnClick(View view) {
        String currentText = editText.getText().toString().trim();
        if (TextUtils.isEmpty(currentText)) {
            ToastUtils.showShortMsg(this, "请输入问题内容！");
            return;
        }
        if (isQuickReplay) {
            updateQuickReplay(currentText);

        } else {
            Intent intent = new Intent();
            intent.putExtra(TEXT, editText.getText().toString());
            setResult(RESULT_OK, intent);
            finish();
        }

    }

    private void updateQuickReplay(String content) {
        Map<String, String> param = new HashMap<>();
        param.put("userId", SharedPreferenceUtils.getString(this, PublicParams.USER_ID));
        param.put("content",content);
        param.put("type",quickReplayType);
        if (!TextUtils.isEmpty(quickReplayId)){
            param.put("quickId",quickReplayId);
        }
        param.put("quickId",quickReplayId);
        addHttpPostRequest(HttpUrlManager.UPDATE_QUICK_REPLY, param, QuickReplyBean.QuickReplyList.ReplyContent.class, this);
    }


    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId){
            case HttpUrlManager.UPDATE_QUICK_REPLY:
                if (result.isRequestSuccessed()) {
                    Intent intent = new Intent();
                    QuickReplyBean.QuickReplyList.ReplyContent content = (QuickReplyBean.QuickReplyList.ReplyContent)result.getBodyObject();
                    intent.putExtra(TEXT,content.getContent());
                    intent.putExtra(QUICK_REPLAY_ID,content.getQuickId());
                    intent.putExtra(QUICK_REPLAY_TYPE,quickReplayType);
                    setResult(RESULT_OK, intent);
                    finish();
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
        }

    }
}
