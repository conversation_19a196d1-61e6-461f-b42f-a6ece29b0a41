package com.doctor.br.activity.mine.agent.achievement;

import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.AdapterView;
import android.widget.TextView;

import com.doctor.br.adapter.mine.PrescriptionNumbersRecyclerAdapter;
import com.doctor.br.bean.AgentOrderNumberBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.PopUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;
import org.newapp.ones.base.widgets.LoadingDialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：药方数量
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/10 11:41
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/10 11:41
 */

public class PrescriptionNumbersActivity extends ActionBarActivity {
    private final String TYPE_ALL = "All";//全部
    private final String TYPE_THISMONTH = "thisMonth";//本月
    private final String TYPE_LASTMONTH = "lastMonth";//上月
    //界面下的控件
    @BindView(R.id.right_tv)
    TextView rightTv;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private PopUtils bottomPop;//选择全部本月上月的popwindow

    private int totalPage = -1;//总页码
    private int currentPage = 1;//当前页码

    private RequestCallBack getListCallBack;//网络请求获取列表回调

    private List<AgentOrderNumberBean.ListBean> list;//列表
    private PrescriptionNumbersRecyclerAdapter prescriptionNumbersRecyclerAdapter;
    private String type = TYPE_ALL;//当前显示的类型

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_patient_numbers);
        initView();
        initPop();
        getPerformanceListRequest(type, "1");
    }

    private void initView() {
        setActionBarTitle("药方数量");
        getBaseActionBar().setRightButton("全部");
        getBaseActionBar().setRightButtonColor(ContextCompat.getColor(this, R.color.br_color_theme));
        getBaseActionBar().setActionBarChangeListener(this);

        rightTv.setText("药方数量（单）");

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                getPerformanceListRequest(type, "1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        mLoadingDialog = null;
                        getPerformanceListRequest(type, currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        bgaRefreshLayout.setRefreshViewHolder(new BGANormalRefreshViewHolder(this, true));

        recyclerView = (RecyclerView) findViewById(R.id.recycler_view);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setHasFixedSize(true);

        list = new ArrayList<>();
        prescriptionNumbersRecyclerAdapter = new PrescriptionNumbersRecyclerAdapter(this, list, null);
        recyclerView.setAdapter(prescriptionNumbersRecyclerAdapter);

        emptyView.setEmptyBackgroundColor(ContextCompat.getColor(this,R.color.br_color_white));
    }

    private void initPop() {
        final String[] strings = {"全部", "本月", "上月"};
        bottomPop = new PopUtils(this, R.string.select_month, Arrays.asList(strings), new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if(mLoadingDialog==null){
                    mLoadingDialog = LoadingDialog.getInstance(PrescriptionNumbersActivity.this);
                }
                switch (position) {
                    case 0:
                        getPerformanceListRequest(TYPE_ALL, "1");
                        break;
                    case 1:
                        getPerformanceListRequest(TYPE_THISMONTH, "1");
                        break;
                    case 2:
                        getPerformanceListRequest(TYPE_LASTMONTH, "1");
                        break;
                    default:
                        break;
                }
                bottomPop.dismiss();
            }
        });
    }

    @Override
    public void onRightBtnClick(View view) {
        bottomPop.showPopupWindow();
    }

    /**
     * 网络请求获取药方数量列表
     *
     * @param type 类型，All 全部 thisMonth 本月 lastMonth 上月
     * @param page 想要请求的页码
     */
    private void getPerformanceListRequest(String type, String page) {
        emptyView.setVisibility(View.GONE);
        bgaRefreshLayout.setVisibility(View.VISIBLE);
        Map<String, String> map = new HashMap<>();
        map.put("type", type);
        map.put("page", page);
//        map.put("userId", "2928");//测试使用
        map.put("pageSize", "20");
        getListCallBack = addHttpPostRequest(HttpUrlManager.ORDER_NUMBER, map, AgentOrderNumberBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if(bgaRefreshLayout==null){
            return;
        }
        switch (taskId) {
            case HttpUrlManager.ORDER_NUMBER:
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    AgentOrderNumberBean agentOrderNumberBean = (AgentOrderNumberBean) result.getBodyObject();
                    type = agentOrderNumberBean.getType();
                    if (TYPE_THISMONTH.equals(type)) {
                        getBaseActionBar().setRightButton("本月");
                    } else if (TYPE_LASTMONTH.equals(type)) {
                        getBaseActionBar().setRightButton("上月");
                    } else {
                        getBaseActionBar().setRightButton("全部");
                    }
                    totalPage = agentOrderNumberBean.getTotalPageSize();
                    currentPage = agentOrderNumberBean.getCurrentPage();
                    if (currentPage == 1) {
                        list.clear();
                    }
                    list.addAll(agentOrderNumberBean.getList());
                    prescriptionNumbersRecyclerAdapter.notifyDataSetChanged();
                    if (currentPage == 1 && list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setVisibility(View.VISIBLE);
                        bgaRefreshLayout.setVisibility(View.GONE);
                    }
                } else {
                    if (totalPage == -1) {
                        emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                        emptyView.setVisibility(View.VISIBLE);
                        bgaRefreshLayout.setVisibility(View.GONE);
                        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                            @Override
                            public void onReload() {
                                getPerformanceListRequest(type, "1");
                            }
                        });
                    }
                    RequestErrorToast.showError(this,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (getListCallBack != null) {
            getListCallBack.cancel();
        }
        super.onDestroy();
    }
}
