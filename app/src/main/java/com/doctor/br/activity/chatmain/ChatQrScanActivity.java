package com.doctor.br.activity.chatmain;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.doctor.br.utils.BitmapUtils;
import com.doctor.br.utils.SelectImgUtils;
import com.doctor.br.view.SelectPhotoDialog;
import com.doctor.yy.R;
import com.doctor.zxing.camera.CameraManager;
import com.doctor.zxing.utils.BeepManager;
import com.doctor.zxing.utils.InactivityTimer;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.PlanarYUVLuminanceSource;
import com.google.zxing.RGBLuminanceSource;
import com.google.zxing.ReaderException;
import com.google.zxing.Result;
import com.google.zxing.common.HybridBinarizer;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import static android.app.Activity.RESULT_OK;
import static com.doctor.zxing.decode.DecodeThread.BARCODE_BITMAP;

/**
 * 聊天主界面专用的二维码扫描Activity
 */
public final class ChatQrScanActivity extends ActionBarActivity implements SurfaceHolder.Callback {

    private static final String TAG = ChatQrScanActivity.class.getSimpleName();
    public static final String EXTRA_QR_RESULT = "qr_result";
    public static final String EXTRA_QR_SECOND_PART = "qr_second_part";
    public static final String EXTRA_QR_THIRD_PART = "qr_third_part";
    
    // 二维码格式前缀（与iOS版本保持一致）
    private static final String QR_CODE_PREFIX = "brzy-web://login?";

    private CameraManager cameraManager;
    private Handler handler;
    private InactivityTimer inactivityTimer;
    private BeepManager beepManager;
    private CustomDecodeThread decodeThread;

    private SurfaceView scanPreview = null;
    private RelativeLayout scanContainer;
    private RelativeLayout scanCropView;
    private ImageView scanLine;
    private ImageView pictureImg;
    private View exportHintLayout;
    private android.widget.TextView exportHintTitle;
    private android.widget.TextView exportHintUrl;
    private android.widget.TextView tvCopyLink;

    private Rect mCropRect = null;
    private boolean isHasSurface = false;

    public Handler getHandler() {
        return handler;
    }

    public CameraManager getCameraManager() {
        return cameraManager;
    }

    @Override
    public void onCreate(Bundle icicle) {
        Window window = getWindow();
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        super.onCreate(icicle);
        setActionBarContentView(R.layout.activity_capture_zxing);
        initView();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("扫描二维码");

        scanPreview = (SurfaceView) findViewById(R.id.capture_preview);
        scanContainer = (RelativeLayout) findViewById(R.id.capture_container);
        scanCropView = (RelativeLayout) findViewById(R.id.capture_crop_view);
        scanLine = (ImageView) findViewById(R.id.capture_scan_line);
        pictureImg = (ImageView) findViewById(R.id.picture_img);
        exportHintLayout = findViewById(R.id.ll_export_hint);
        exportHintTitle = (android.widget.TextView) findViewById(R.id.tv_export_hint_title);
        exportHintUrl = (android.widget.TextView) findViewById(R.id.tv_export_hint_url);
        tvCopyLink = (android.widget.TextView) findViewById(R.id.tv_copy_link);
        pictureImg.setOnClickListener(v -> onOpenPhoto());

        inactivityTimer = new InactivityTimer(this);
        beepManager = new BeepManager(this);

        TranslateAnimation animation = new TranslateAnimation(Animation.RELATIVE_TO_PARENT, 0.0f, 
                Animation.RELATIVE_TO_PARENT, 0.0f, Animation.RELATIVE_TO_PARENT, 0.0f, 
                Animation.RELATIVE_TO_PARENT, 0.9f);
        animation.setDuration(4500);
        animation.setRepeatCount(-1);
        animation.setRepeatMode(Animation.RESTART);
        scanLine.startAnimation(animation);

        // 根据来源决定是否显示导出提示
        boolean fromDossierExport = getIntent().getBooleanExtra("extra_from_dossier_export", false);
        if (fromDossierExport && exportHintLayout != null) {
            exportHintLayout.setVisibility(View.VISIBLE);
            if (exportHintTitle != null) {
                exportHintTitle.setText("请在电脑浏览器打开");
            }
            if (exportHintUrl != null) {
                // 按需求：网址单独一行，字体稍大（布局中已调大），不转大写
                String url = "https://yian.haoniuzhongyi.top";
                exportHintUrl.setText(url);
            }
            // 设置复制链接点击事件
            if (tvCopyLink != null) {
                tvCopyLink.setOnClickListener(v -> copyUrlToClipboard());
            }
        } else if (exportHintLayout != null) {
            exportHintLayout.setVisibility(View.GONE);
        }
    }

    private void onOpenPhoto() {
        SelectImgUtils.gotoPhoto(this);
    }

    /**
     * 复制URL到剪贴板
     */
    private void copyUrlToClipboard() {
        if (exportHintUrl != null && !TextUtils.isEmpty(exportHintUrl.getText())) {
            String url = exportHintUrl.getText().toString();
            ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
            ClipData clip = ClipData.newPlainText("医案导出链接", url);
            clipboard.setPrimaryClip(clip);
            ToastUtils.showShortMsg(this, "已复制到粘贴板");
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // CameraManager must be initialized here, not in onCreate(). This is
        // necessary because we don't
        // want to open the camera driver and measure the screen size if we're
        // going to show the help on
        // first launch. That led to bugs where the scanning rectangle was the
        // wrong size.
        cameraManager = new CameraManager(getApplication());

        handler = null;

        if (isHasSurface) {
            // The activity was paused but not stopped, so the surface still
            // exists. Therefore
            // surfaceCreated() won't be called, so init the camera here.
            initCamera(scanPreview.getHolder());
        } else {
            // Install the callback and wait for surfaceCreated() to init the
            // camera.
            scanPreview.getHolder().addCallback(this);
        }

        inactivityTimer.onResume();
    }

    @Override
    protected void onPause() {
        // 先停止解码线程，再清空handler，避免空指针异常
        if (decodeThread != null) {
            Handler decodeHandler = decodeThread.getHandler();
            if (decodeHandler != null) {
                android.os.Message quit = android.os.Message.obtain(decodeHandler, R.id.quit);
                quit.sendToTarget();
                try {
                    decodeThread.join(500L);
                } catch (InterruptedException e) {
                    // continue
                }
            }
            decodeThread = null;
        }
        
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
            handler = null;
        }
        
        inactivityTimer.onPause();
        beepManager.close();
        cameraManager.stopPreview();
        cameraManager.closeDriver();
        if (!isHasSurface) {
            scanPreview.getHolder().removeCallback(this);
        }
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        inactivityTimer.shutdown();
        super.onDestroy();
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        if (holder == null) {
            Log.e(TAG, "*** WARNING *** surfaceCreated() gave us a null surface!");
        }
        if (!isHasSurface) {
            isHasSurface = true;
            initCamera(holder);
        }
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        isHasSurface = false;
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
    }

    /**
     * 处理扫码结果
     */
    public void handleDecode(Result rawResult, Bundle bundle) {
        inactivityTimer.onActivity();
        beepManager.playBeepSoundAndVibrate();

        String qrcodeUrl = rawResult.getText();
        handleQrCodeResult(qrcodeUrl);
    }

    /**
     * 验证二维码格式是否合法（与iOS版本保持一致）
     * 格式：brzy-web://login?session=xxx&server=xxx
     * 例如：brzy-web://login?session=ec174344-e773-4077-a3ed-219bb61fb71d&server=http://example.com
     */
    private boolean isValidQrCode(String qrCode) {
        if (TextUtils.isEmpty(qrCode)) {
            return false;
        }
        // 检查是否为网页登录二维码格式
        if (!qrCode.startsWith(QR_CODE_PREFIX)) {
            return false;
        }
        try {
            Uri uri = Uri.parse(qrCode);
            String sessionId = uri.getQueryParameter("session");
            String serverUrl = uri.getQueryParameter("server");
            return !TextUtils.isEmpty(sessionId) && !TextUtils.isEmpty(serverUrl);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 处理二维码扫描结果（与iOS版本保持一致）
     */
    private void handleQrCodeResult(String qrCode) {
        if (TextUtils.isEmpty(qrCode)) {
            ToastUtils.showShortMsg(this, "二维码内容为空");
            restartPreviewAfterDelay(2000);
            return;
        }

        // 验证二维码格式
        if (isValidQrCode(qrCode)) {
            // 格式正确，解析并返回结果
            Uri uri = Uri.parse(qrCode);
            String sessionId = uri.getQueryParameter("session");
            String serverUrl = uri.getQueryParameter("server");
            
            Intent resultIntent = new Intent();
            resultIntent.putExtra(EXTRA_QR_RESULT, qrCode);
            resultIntent.putExtra(EXTRA_QR_SECOND_PART, sessionId);
            resultIntent.putExtra(EXTRA_QR_THIRD_PART, serverUrl);
            setResult(RESULT_OK, resultIntent);
            finish();
        } else {
            // 格式不正确，提示用户
            ToastUtils.showShortMsg(this, "这不是医案导出二维码");
            restartPreviewAfterDelay(2000);
        }
    }

    private void initCamera(SurfaceHolder surfaceHolder) {
        if (surfaceHolder == null) {
            throw new IllegalStateException("No SurfaceHolder provided");
        }
        if (cameraManager.isOpen()) {
            Log.w(TAG, "initCamera() while already open -- late SurfaceView callback?");
            return;
        }
        try {
            cameraManager.openDriver(surfaceHolder);
            if (handler == null) {
                // 创建自定义的解码线程和Handler
                decodeThread = new CustomDecodeThread();
                decodeThread.start();
                handler = new CustomScanHandler();
                
                // 开始预览和解码
                cameraManager.startPreview();
                restartPreviewAndDecode();
            }
            initCrop();
        } catch (IOException ioe) {
            Log.w(TAG, ioe);
            displayFrameworkBugMessageAndExit();
        } catch (RuntimeException e) {
            Log.w(TAG, "Unexpected error initializing camera", e);
            displayFrameworkBugMessageAndExit();
        }
    }

    private ConfirmDialog confirmDialog;

    private void displayFrameworkBugMessageAndExit() {
        if (confirmDialog == null) {
            confirmDialog = ConfirmDialog.getInstance(this)
                    .setDialogTitle(getString(R.string.app_name))
                    .setDialogContent("相机打开出错，请检查是否授予\"相机\"权限")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
                            confirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            confirmDialog.dismiss();
                        }
                    });
            confirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    finish();
                }
            });
            confirmDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
                @Override
                public void onCancel(DialogInterface dialog) {
                    finish();
                }
            });
        }
        confirmDialog.show();
    }

    public void restartPreviewAfterDelay(long delayMS) {
        if (handler != null) {
            handler.sendEmptyMessageDelayed(R.id.restart_preview, delayMS);
        }
    }

    public Rect getCropRect() {
        return mCropRect;
    }

    /**
     * 初始化截取的矩形区域
     */
    private void initCrop() {
        scanCropView.post(new Runnable() {
            @Override
            public void run() {
                int cameraWidth = cameraManager.getCameraResolution().y;
                int cameraHeight = cameraManager.getCameraResolution().x;

                int cropLeft = scanCropView.getLeft();
                int cropTop = scanCropView.getTop();

                int cropWidth = scanCropView.getWidth();
                int cropHeight = scanCropView.getHeight();

                int containerWidth = scanContainer.getWidth();
                int containerHeight = scanContainer.getHeight();

                int x = cropLeft * cameraWidth / containerWidth;
                int y = cropTop * cameraHeight / containerHeight;

                int width = cropWidth * cameraWidth / containerWidth;
                int height = cropHeight * cameraHeight / containerHeight;

                mCropRect = new Rect(x, y, width + x, height + y);
            }
        });
    }

    /**
     * 解析图片中的二维码
     */
    public static String decodeQRCode(Bitmap srcBitmap) {
        try {
            Map<DecodeHintType, Object> hints = new EnumMap<>(DecodeHintType.class);
            hints.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
            hints.put(DecodeHintType.POSSIBLE_FORMATS, EnumSet.allOf(BarcodeFormat.class));

            int scaleFactor = 2;
            int scaledWidth = srcBitmap.getWidth() / scaleFactor;
            int scaledHeight = srcBitmap.getHeight() / scaleFactor;
            Bitmap scaledBitmap = Bitmap.createScaledBitmap(srcBitmap, scaledWidth, scaledHeight, true);

            int[] pixels = new int[scaledWidth * scaledHeight];
            scaledBitmap.getPixels(pixels, 0, scaledWidth, 0, 0, scaledWidth, scaledHeight);

            RGBLuminanceSource source = new RGBLuminanceSource(scaledWidth, scaledHeight, pixels);
            BinaryBitmap binaryBitmap = new BinaryBitmap(new HybridBinarizer(source));

            Result result = new MultiFormatReader().decode(binaryBitmap, hints);
            return result.getText();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent intent) {
        super.onActivityResult(requestCode, resultCode, intent);
        switch (requestCode) {
            case SelectPhotoDialog.REQUEST_PICK:
                if (resultCode == RESULT_OK) {
                    Uri uri = intent.getData();
                    Bitmap bitmap = BitmapUtils.getImage(uri);
                    if (bitmap == null) {
                        ToastUtils.showLongMsg(this, "未检测到二维码");
                        return;
                    }
                    String text = decodeQRCode(bitmap);
                    if (TextUtils.isEmpty(text)) {
                        ToastUtils.showLongMsg(this, "二维码识别失败，请重试");
                    } else {
                        handleQrCodeResult(text);
                    }
                }
                break;
        }
    }
    
    /**
     * 重新开始预览和解码
     */
    private void restartPreviewAndDecode() {
        if (cameraManager != null && decodeThread != null && handler != null) {
            Handler decodeHandler = decodeThread.getHandler();
            if (decodeHandler != null) {
                cameraManager.requestPreviewFrame(decodeHandler, R.id.decode);
            }
        }
    }
    
    /**
     * 自定义的扫码Handler
     */
    private class CustomScanHandler extends Handler {
        @Override
        public void handleMessage(android.os.Message message) {
            switch (message.what) {
                case R.id.restart_preview:
                    restartPreviewAndDecode();
                    break;
                case R.id.decode_succeeded:
                    Bundle bundle = message.getData();
                    handleDecode((Result) message.obj, bundle);
                    break;
                case R.id.decode_failed:
                    // 解码失败，继续下一次解码
                    restartPreviewAndDecode();
                    break;
                default:
                    break;
            }
        }
    }
    
    /**
     * 自定义的解码线程
     */
    private class CustomDecodeThread extends Thread {
        public static final String BARCODE_BITMAP = "barcode_bitmap";
        
        private Handler handler;
        private final CountDownLatch handlerInitLatch;
        private final Map<DecodeHintType, Object> hints;
        
        public CustomDecodeThread() {
            handlerInitLatch = new CountDownLatch(1);
            hints = new EnumMap<>(DecodeHintType.class);
            
            // 设置支持的解码格式
            Collection<BarcodeFormat> decodeFormats = new ArrayList<>();
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.AZTEC));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.PDF_417));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.QR_CODE));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.DATA_MATRIX));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.CODE_128));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.CODE_39));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.CODE_93));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.CODABAR));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.EAN_13));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.EAN_8));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.ITF));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.UPC_A));
            decodeFormats.addAll(EnumSet.of(BarcodeFormat.UPC_E));
            
            hints.put(DecodeHintType.POSSIBLE_FORMATS, decodeFormats);
        }
        
        public Handler getHandler() {
            try {
                handlerInitLatch.await();
            } catch (InterruptedException ie) {
                // continue
            }
            return handler;
        }
        
        @Override
        public void run() {
            Looper.prepare();
            handler = new CustomDecodeHandler(hints);
            handlerInitLatch.countDown();
            Looper.loop();
        }
    }
    
    /**
     * 自定义的解码Handler
     */
    private class CustomDecodeHandler extends Handler {
        private final Map<DecodeHintType, Object> hints;
        private final MultiFormatReader multiFormatReader;
        
        public CustomDecodeHandler(Map<DecodeHintType, Object> hints) {
            this.hints = hints;
            multiFormatReader = new MultiFormatReader();
            multiFormatReader.setHints(hints);
        }
        
        @Override
        public void handleMessage(android.os.Message message) {
            switch (message.what) {
                case R.id.decode:
                    decode((byte[]) message.obj, message.arg1, message.arg2);
                    break;
                case R.id.quit:
                    Looper.myLooper().quit();
                    break;
                default:
                    break;
            }
        }
        
        private void decode(byte[] data, int width, int height) {
            long start = System.currentTimeMillis();
            Result rawResult = null;
            
            // 检查handler是否为null，如果为null则直接返回，避免空指针异常
            if (ChatQrScanActivity.this.handler == null) {
                Log.d(TAG, "Handler is null, decode thread should be stopped");
                return;
            }
            
            // 构造基于平面的YUV亮度源，即灰度图像
            byte[] rotatedData = new byte[data.length];
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    rotatedData[x * height + height - y - 1] = data[x + y * width];
                }
            }
            int tmp = width;
            width = height;
            height = tmp;
            
            PlanarYUVLuminanceSource source = buildLuminanceSource(rotatedData, width, height);
            if (source != null) {
                BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
                try {
                    rawResult = multiFormatReader.decodeWithState(bitmap);
                } catch (ReaderException re) {
                    // continue
                } finally {
                    multiFormatReader.reset();
                }
            }
            
            // 再次检查handler是否为null，因为解码过程中可能Activity已经暂停
            if (ChatQrScanActivity.this.handler == null) {
                Log.d(TAG, "Handler became null during decode, stopping");
                return;
            }
            
            if (rawResult != null) {
                // 解码成功
                long end = System.currentTimeMillis();
                Log.d(TAG, "Found barcode in " + (end - start) + " ms");
                
                android.os.Message message = android.os.Message.obtain(ChatQrScanActivity.this.handler, R.id.decode_succeeded, rawResult);
                Bundle bundle = new Bundle();
                // Note: renderCroppedGreyscaleBitmap() method is not available in this ZXing version
                // The bitmap is not essential for QR code processing, so we skip it
                message.setData(bundle);
                message.sendToTarget();
            } else {
                // 解码失败
                android.os.Message message = android.os.Message.obtain(ChatQrScanActivity.this.handler, R.id.decode_failed);
                message.sendToTarget();
            }
        }
        
        private PlanarYUVLuminanceSource buildLuminanceSource(byte[] data, int width, int height) {
            Rect rect = getCropRect();
            if (rect == null) {
                return null;
            }
            
            // Go ahead and assume it's YUV rather than die.
            return new PlanarYUVLuminanceSource(data, width, height, rect.left, rect.top,
                    rect.width(), rect.height(), false);
        }
    }
} 