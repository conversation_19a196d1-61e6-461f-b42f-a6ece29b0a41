package com.doctor.br.activity.mine;

import android.graphics.Color;
import android.os.Bundle;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.yy.R;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import butterknife.BindView;

/**
 * 类描述：认证示例大图界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/2
 */

public class CertificatExampleDetailActivity extends NoActionBarActivity {
    public static final String IMGS = "img_list";
    public static final String CLICK_POSITION = "click_position";
    private int[] imgs;//上个界面传递过来的图片集合
    private int clickPos;//上个界面传递过来的点击的位置，非必须，默认为0
    //界面下的控件
    @BindView(R.id.backgound_view)
    RelativeLayout relativeLayout;
    @BindView(R.id.view_pager)
    ViewPager viewPager;
    @BindView(R.id.tv1)
    TextView tv1;
    @BindView(R.id.tv2)
    TextView tv2;

    private ShapeImageView[] imageViews;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_certificat_example_detail);
        getIntentData(savedInstanceState);
        initView();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putIntArray(IMGS, imgs);
        outState.putInt(CLICK_POSITION, clickPos);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            imgs = savedInstanceState.getIntArray(IMGS);
            clickPos = savedInstanceState.getInt(CLICK_POSITION,0);
        } else {
            imgs = getIntent().getIntArrayExtra(IMGS);
            clickPos = getIntent().getIntExtra(CLICK_POSITION, 0);
        }
        if (imgs == null) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }

    private void initView() {
        relativeLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        tv1.setText(clickPos + 1 + " / ");
        tv2.setText(imgs.length + "");

        imageViews = new ShapeImageView[imgs.length];
        for (int i = 0; i < imgs.length; i++) {
            ShapeImageView imageView = new ShapeImageView(this);
            int width = DensityUtils.getScreenWidth(this);
            int height = width * 526 / 750;

            viewPager.getLayoutParams().height = height;

            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(width, height);
            imageView.setLayoutParams(layoutParams);
            imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
            imageView.setImageResource(imgs[i]);

            imageView.setRoundRadius(DensityUtils.dip2px(this,8));
            imageView.setBorderSize(DensityUtils.dip2px(this,1));
            imageView.setBorderColor(Color.parseColor("#dcdcdc"));

            imageViews[i] = imageView;
        }

        viewPager.setAdapter(new PagerAdapter() {
            @Override
            public int getCount() {
                return imgs.length;
            }

            @Override
            public boolean isViewFromObject(View view, Object object) {
                return view == object;
            }

            @Override
            public Object instantiateItem(ViewGroup container, int position) {
                container.addView(imageViews[position]);
                return imageViews[position];
            }

            @Override
            public void destroyItem(ViewGroup container, int position, Object object) {
                container.removeView(imageViews[position]);
            }
        });
        viewPager.setCurrentItem(clickPos, false);
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                tv1.setText(position + 1 + " / ");
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }
}
