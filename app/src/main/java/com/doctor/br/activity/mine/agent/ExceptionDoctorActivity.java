package com.doctor.br.activity.mine.agent;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.doctor.br.activity.mine.agent.exceptionDoctor.FiveOrderActivity;
import com.doctor.br.activity.mine.agent.exceptionDoctor.TwoWeekNoOrderActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.AgentExceptionRecyclerAdapter;
import com.doctor.br.bean.AgentExceptionDoctorBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：经纪人-异常大夫页面
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/7
 */

public class ExceptionDoctorActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    //网络请求大夫状态列表回调
    private RequestCallBack doctorStateCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_exception_doctor);
        initView();
        getDoctorNumbers();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("异常大夫");

        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 2, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(gridLayoutManager);
        recyclerView.setHasFixedSize(true);

        recyclerView.setAdapter(
                new AgentExceptionRecyclerAdapter(this, new AgentExceptionDoctorBean(), this));
    }

    /**
     * 每个item的点击事件
     *
     * @param position 点击的item的String
     */
    @Override
    public void itemClick(int position) {
        switch (position) {
            case R.string.two_week_no_order://连续两周未开方
//                Toast.makeText(this, "连续两周未开方", Toast.LENGTH_SHORT).show();
                startActivity(new Intent(this, TwoWeekNoOrderActivity.class));
                break;
            case R.string.five_order://连续5张处方不超过100元
//                Toast.makeText(this, "连续5张处方不超过100元", Toast.LENGTH_SHORT).show();
                startActivity(new Intent(this, FiveOrderActivity.class));
                break;
            default:
                break;
        }
    }

    /**
     * 网络请求获取异常大夫数量
     */
    private void getDoctorNumbers() {
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        doctorStateCallBack = addHttpPostRequest(HttpUrlManager.EXCEPTION_DOCTOR, map, AgentExceptionDoctorBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (recyclerView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.EXCEPTION_DOCTOR:
                if (result.isRequestSuccessed()) {
                    AgentExceptionDoctorBean doctorBean = (AgentExceptionDoctorBean) result.getBodyObject();
                    recyclerView.setAdapter(new AgentExceptionRecyclerAdapter(this, doctorBean, this));
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }


    @Override
    protected void onDestroy() {
        if (doctorStateCallBack != null) {
            doctorStateCallBack.cancel();
        }
        super.onDestroy();
    }
}
