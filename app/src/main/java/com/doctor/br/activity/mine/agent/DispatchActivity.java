package com.doctor.br.activity.mine.agent;

import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;

import com.doctor.br.activity.mine.agent.task.SelectAgentActivity;
import com.doctor.br.activity.mine.agent.task.SelectAgentSearchActivity;
import com.doctor.br.activity.mine.agent.task.TaskActivity;
import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.mine.DispatchListAdapter;
import com.doctor.br.bean.AgentDispatchBean;
import com.doctor.br.bean.event.RefreshPendingDataEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.EmptyView;
import org.newapp.ones.base.widgets.LoadingDialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：调度审核界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/9
 */

public class DispatchActivity extends ActionBarActivity implements RecyclerItemItemClickListener {
    //前个界面传递过来的数据
    public static final String USER_ROLE = "userRole";
    private String userRole;//当前用户身份
    //界面下的控件
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private RequestCallBack dispatchListCallBack;//网络请求调度审核回调
    private RequestCallBack dispatchResultCallBack;//网络请求调度结果回调

    private int totalPage = -1;//总页码
    private int currentPage = 1;//当前页码

    private List<AgentDispatchBean.DataBean> list;//调度审核列表
    private DispatchListAdapter dispatchListAdapter;//列表适配器

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_dispatch);
        getIntentData(savedInstanceState);
        initView();
        getDispatchListRequest("1");
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(USER_ROLE, userRole);
    }

    private void getIntentData(Bundle bundle) {
        if (bundle == null) {
            userRole = getIntent().getStringExtra(USER_ROLE);
        } else {
            userRole = bundle.getString(USER_ROLE);
        }
        if (TextUtils.isEmpty(userRole) || !AgentActivity.ROLE_INSPECTOR.equals(userRole)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }


    private void initView() {
        getBaseActionBar().setActionBarTitle("调度审核");

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                getDispatchListRequest("1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        mLoadingDialog = null;
                        getDispatchListRequest(currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        bgaRefreshLayout.setRefreshViewHolder(new BGANormalRefreshViewHolder(this, true));

        recyclerView.setHasFixedSize(true);
        list = new ArrayList<>();
        dispatchListAdapter = new DispatchListAdapter(this, list, this);
        recyclerView.setAdapter(dispatchListAdapter);
    }

    private long mLastClickReceiveTime = 0L;//上次点击接受的时间
    private long mLastClickRefuseTime = 0L;//上次点击拒绝的时间
    private final long DOUBLE_CLICK = 1000L;//两次点击的间隔时间

    @Override
    public void itemViewClick(int position, View view) {
        if (mLoadingDialog == null) {
            mLoadingDialog = LoadingDialog.getInstance(this);
        }
        long currentTime = System.currentTimeMillis();
        switch (view.getId()) {
            case R.id.receive_btn:
                if (currentTime - mLastClickReceiveTime < DOUBLE_CLICK) {
                    return;
                }
                mLastClickReceiveTime = currentTime;
                dispatchResult("1", list.get(position).getId(), list.get(position).getTargetUserId(), userRole, null);
                break;
            case R.id.refuse_btn:
                if (currentTime - mLastClickRefuseTime < DOUBLE_CLICK) {
                    return;
                }
                mLastClickRefuseTime = currentTime;
                dispatchResult("2", list.get(position).getId(), list.get(position).getTargetUserId(), userRole, null);
                break;
            default:
                break;
        }
    }

    /**
     * 网络请求获取调度审核列表
     *
     * @param pageNum 想要请求的页码
     */
    private void getDispatchListRequest(String pageNum) {
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
//        map.put("userId", "0000000000001");//测试使用
        map.put("pageNum", pageNum);
        map.put("pageSize", "20");
        dispatchListCallBack = addHttpPostRequest(HttpUrlManager.DISPATCH_LIST, map, AgentDispatchBean.class, this);
    }

    /**
     * 网络请求接收或拒绝调度要求
     *
     * @param type         1 接收 2 拒绝
     * @param taskId       任务id
     * @param targetUserId 被调度人id
     * @param role         用户之前的职位
     * @param reason       拒绝理由，非必填
     */
    private void dispatchResult(String type, String taskId, String targetUserId, String role, String reason) {
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
//        map.put("userId", "0000000000001");//测试使用
        map.put("type", type);
        map.put("taskId", taskId);
        map.put("targetUserId", targetUserId);
        map.put("role", role);
//        map.put("role", "3");//测试使用
        map.put("reason", reason);
        dispatchResultCallBack = addHttpPostRequest(HttpUrlManager.DISPATCH_RESULT, map, null, this);
    }


    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (bgaRefreshLayout == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.DISPATCH_LIST://调度审核列表
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    AgentDispatchBean agentDispatchBean = (AgentDispatchBean) result.getBodyObject();
                    totalPage = agentDispatchBean.getTotalPage();
                    currentPage = agentDispatchBean.getCurrentPage();
                    if (currentPage == 1) {
                        list.clear();
                    }
                    list.addAll(agentDispatchBean.getData());
                    dispatchListAdapter.notifyDataSetChanged();
                    if (currentPage == 1 && list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setVisibility(View.VISIBLE);
                    }
                } else {
                    if (totalPage == -1) {
                        emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                        emptyView.setVisibility(View.VISIBLE);
                        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                            @Override
                            public void onReload() {
                                getDispatchListRequest("1");
                            }
                        });
                    }
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.DISPATCH_RESULT://调度审核接收或拒绝
                if (result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "操作成功");
                    getDispatchListRequest("1");
                    refreshNumber();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 刷新待扫描、待接收、待处理、待审核、待分配数量
     *
     * @send {@link com.doctor.zxing.activity.CaptureActivity#refreshData()}
     * @send {@link com.doctor.br.fragment.mine.BeReceiveFragment#refreshData()}
     * @send {@link com.doctor.br.activity.mine.agent.DispatchActivity#refreshNumber()}
     * @send {@link com.doctor.br.fragment.mine.BeScanFragment#sendRefresh()}
     * @send {@link SelectAgentActivity#sendRefreshNumber()}
     * @send {@link SelectAgentSearchActivity#sendRefreshNumber()}
     * @receive {@link SelectAgentActivity#refreshList(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.fragment.mine.BeScanFragment#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.AgentActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.PendingActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link TaskActivity#refreshData(RefreshPendingDataEvent)}
     */
    private void refreshNumber() {
        RefreshPendingDataEvent refreshPendingDataEvent = new RefreshPendingDataEvent();
        refreshPendingDataEvent.setFreshDispatch(true);
        EventBusUtils.post(refreshPendingDataEvent);
    }

    @Override
    protected void onDestroy() {
        if (dispatchResultCallBack != null) {
            dispatchResultCallBack.cancel();
        }
        if (dispatchListCallBack != null) {
            dispatchListCallBack.cancel();
        }
        super.onDestroy();
    }


}
