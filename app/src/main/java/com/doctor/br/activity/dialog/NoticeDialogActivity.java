package com.doctor.br.activity.dialog;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.app.AppContext;
import com.doctor.br.db.entity.Notice;
import com.doctor.br.utils.DateUtils;
import com.doctor.greendao.gen.NoticeDao;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.newapp.ones.base.activity.NoActionBarActivity;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 通知公告的Activity
 * @createTime 2017/11/30
 */
public class NoticeDialogActivity extends NoActionBarActivity {

    @BindView(R2.id.iv_close)
    ImageView ivClose;
    @BindView(R2.id.dialog_title)
    TextView dialogTitle;
    @BindView(R2.id.dialog_content)
    TextView dialogContent;
    @BindView(R2.id.tv_signature)
    TextView tvSignature;
    @BindView(R2.id.tv_notice_time)
    TextView tvNoticeTime;
    private Notice notice;
    private NoticeDao noticeDao;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.zoomin, R.anim.zoomout);
        setActionBarContentView(R.layout.activity_notice_dialog);
        notice = (Notice) getIntent().getSerializableExtra("notice");
        noticeDao = AppContext.getInstances().getDaoSession().getNoticeDao();
        initData();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        if (notice != null) {
            dialogTitle.setText(notice.getNoticeTitle() + "");
            dialogContent.setText(notice.getNoticeContent() + "");
            tvSignature.setText(notice.getNoticeSignature() + "");
            tvNoticeTime.setText(DateUtils.dateFormat("yyyy-MM-dd HH:mm:ss", "yyyy年MM月dd日", notice.getCreatedTime() + ""));
        }
        notice.setIsShow(1);
        noticeDao.insertOrReplace(notice);
    }

    @OnClick(R.id.iv_close)
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_close:
                this.finish();
                overridePendingTransition(R.anim.zoomin, R.anim.zoomout);
                break;
        }
    }


    @Override
    public void onBackPressed() {
//        super.onBackPressed();
    }

    @Override
    protected void onDestroy() {
        overridePendingTransition(R.anim.zoomin, R.anim.zoomout);
        super.onDestroy();
    }
}
