package com.doctor.br.activity.mine.agent.exceptionDoctor;

import android.content.Intent;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.doctor.br.activity.mine.agent.DoctorDetailsActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.TwoWeekNoOrderAdapter;
import com.doctor.br.bean.AgentNoPatientDoctorBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：经纪人-异常大夫-连续两周未开方大夫列表
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/12
 */

public class TwoWeekNoOrderActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //    private final String ALL = "0";
//    private final String THIS_MONTH = "1";
//    private final String LAST_MONTH = "2";
    //界面下的控件
    @BindView(R.id.doctor_recycler)
    RecyclerView doctorRecycler;
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

//    private PopUtils bottomPop;//底部popwindow

    private int currentPage = 1;//当前显示页
    private int totalPage = -1;//总页码
    private RequestCallBack doctorListCallBack;//网络请求大夫列表回调

    private List<AgentNoPatientDoctorBean.DoctorMessagesBean> list;//大夫列表
    private TwoWeekNoOrderAdapter adapter;//大夫列表适配器

//    private String currentType = ALL;//当前显示类型

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_my_doctor);
        initView();
        getDoctorListRequest("1");
    }

    private void initView() {
        setActionBarTitle("连续两周未开方");
        getBaseActionBar().setRightButtonColor(ContextCompat.getColor(this, R.color.br_color_theme));

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                getDoctorListRequest("1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        mLoadingDialog = null;
                        getDoctorListRequest(currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        bgaRefreshLayout.setRefreshViewHolder(new BGANormalRefreshViewHolder(this, true));

        list = new ArrayList<>();
        adapter = new TwoWeekNoOrderAdapter(this, list, this);
        doctorRecycler.setAdapter(adapter);
    }


    @Override
    public void itemClick(int position) {
        Intent intent = new Intent(this, DoctorDetailsActivity.class);
        intent.putExtra(DoctorDetailsActivity.DOCTOR_ID, list.get(position).getDoctorId());
        startActivity(intent);
    }

    /**
     * 网络请求获取连续两周未开方大夫列表
     *
     * @param page 想要请求的页码
     */
    private void getDoctorListRequest(String page) {
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
        map.put("page", page);
        map.put("pageSize", "20");
//        map.put("userId", "2928");//测试使用
        doctorListCallBack = addHttpPostRequest(HttpUrlManager.TWO_WEEK_NO_ORDER, map, AgentNoPatientDoctorBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (bgaRefreshLayout == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.TWO_WEEK_NO_ORDER:
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    AgentNoPatientDoctorBean doctorBean = (AgentNoPatientDoctorBean) result.getBodyObject();

                    currentPage = doctorBean.getPage();
                    totalPage = doctorBean.getTotalPage();

                    if (currentPage == 1) {
                        list.clear();
                    }
                    list.addAll(doctorBean.getDoctorMessages());
                    adapter.notifyDataSetChanged();
                    if (currentPage == 1 && list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setEmptyText("暂无大夫");
                        emptyView.setVisibility(View.VISIBLE);
                    }
                } else {
                    if (totalPage == -1) {
                        emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                        emptyView.setVisibility(View.VISIBLE);
                        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                            @Override
                            public void onReload() {
                                getDoctorListRequest("1");
                            }
                        });
                    }
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (doctorListCallBack != null) {
            doctorListCallBack.cancel();
        }
        super.onDestroy();
    }
}
