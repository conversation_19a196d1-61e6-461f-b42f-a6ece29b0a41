package com.doctor.br.activity.mine.agent;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.doctor.br.activity.mine.agent.doctorState.AgentQualificationFailedActivity;
import com.doctor.br.activity.mine.agent.doctorState.AgentQualificationedActivity;
import com.doctor.br.activity.mine.agent.doctorState.AgentQualificationingActivity;
import com.doctor.br.activity.mine.agent.doctorState.NoOrderActivity;
import com.doctor.br.activity.mine.agent.doctorState.NoPatientActivity;
import com.doctor.br.activity.mine.agent.doctorState.NoPayActivity;
import com.doctor.br.activity.mine.agent.doctorState.NotQualificationActivity;
import com.doctor.br.activity.mine.agent.doctorState.NotRegisterActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.AgentDoctorStateRecyclerAdapter;
import com.doctor.br.bean.AgentDoctorStateBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：经纪人大夫状态页面
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/6
 */

public class DoctorStateActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    //网络请求大夫状态列表回调
    private RequestCallBack doctorStateCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_doctor_state);
        initView();
        getDoctorNumbers();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("大夫状态");

        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 2, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(gridLayoutManager);
        recyclerView.setHasFixedSize(true);

        recyclerView.setAdapter(
                new AgentDoctorStateRecyclerAdapter(this, new AgentDoctorStateBean(), this));
    }

    /**
     * 每个item的点击事件
     *
     * @param name 点击的item的String字符串引用
     */
    @Override
    public void itemClick(int name) {
        switch (name) {
            case R.string.not_register://已邀请未注册
                startActivity(new Intent(this, NotRegisterActivity.class));
                break;
            case R.string.not_qualification://已注册未认证
                startActivity(new Intent(this, NotQualificationActivity.class));
                break;
            case R.string.qualificationing://认证中
                startActivity(new Intent(this, AgentQualificationingActivity.class));
                break;
            case R.string.qualification_failed://认证失败
                startActivity(new Intent(this, AgentQualificationFailedActivity.class));
                break;
            case R.string.not_patient://已认证，患者量低于5
                startActivity(new Intent(this, NoPatientActivity.class));
                break;
            case R.string.not_extract://未开方
                startActivity(new Intent(this, NoOrderActivity.class));
                break;
            case R.string.not_pay://开方未支付
                startActivity(new Intent(this, NoPayActivity.class));
                break;
            case R.string.qualification_success://已认证大夫
                startActivity(new Intent(this, AgentQualificationedActivity.class));
                break;
            default:
                break;
        }
    }

    /**
     * 网络请求获取大夫状态数量
     */
    private void getDoctorNumbers() {
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        doctorStateCallBack = addHttpPostRequest(HttpUrlManager.DOCTOR_STATE, map, AgentDoctorStateBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (recyclerView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.DOCTOR_STATE:
                if (result.isRequestSuccessed()) {
                    AgentDoctorStateBean doctorStateBean = (AgentDoctorStateBean) result.getBodyObject();
                    recyclerView.setAdapter(
                            new AgentDoctorStateRecyclerAdapter(this, doctorStateBean, this));
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }


    @Override
    protected void onDestroy() {
        if (doctorStateCallBack != null) {
            doctorStateCallBack.cancel();
        }
        super.onDestroy();
    }
}
