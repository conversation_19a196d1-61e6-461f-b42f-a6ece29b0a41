package com.doctor.br.activity.mine;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;

import android.provider.Settings;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.activity.ActionBarWebViewActivity;
import com.doctor.br.activity.MainActivity;
import com.doctor.br.activity.dialog.UpdateDownloadActivity;
import com.doctor.br.activity.mine.agent.AgentActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.CheckUpdateResult;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.BadgeUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.utils.ActivityManager;
import org.newapp.ones.base.utils.AppUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import butterknife.BindView;
import cn.jpush.android.api.JPushInterface;

import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_ASSISTANT;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_INSPECTOR;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_MINE;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_MINE_TEAM;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PART;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PART_TEAM;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PERSON;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PROVINCE;

import java.util.HashMap;

/**
 * 类描述：关于
 * 创建人：ShiShaoPo
 * 创建时间：2017/9/28 17:17
 * 修改人：ShiShaoPo
 * 修改时间：2017/9/28 17:17
 */

public class AboutActivity extends ActionBarActivity {
    //上个界面传递过来的数据
    public static final String IS_DITUI = "isDitui";
    public static final String NAME = "name";
    public static final String VIDEO_URL = "videoUrl";


    private String isDitui;//地推身份
    private String name;//姓名
    private String videoUrl;//视频地址
    //界面下的控件
    @BindView(R.id.playImg)
    ImageView playImg;
    @BindView(R.id.about_linear)
    LinearLayout aboutLinear;
    @BindView(R.id.delete_linear)
    LinearLayout deleteAccountLayout;
    @BindView(R.id.new_img)
    ImageView newImg;
    @BindView(R.id.version_tv)
    TextView versionTv;
    @BindView(R.id.agent_relative)
    RelativeLayout agentRelative;
    @BindView(R.id.login_out_btn)
    com.doctor.br.view.NoDoubleClickBtn loginOutBtn;
    @BindView(R.id.ua_pp)
    TextView uaAndPPTv;

    @BindView(R.id.version_linear)
    LinearLayout versionLayout;
    @BindView(R.id.version_new_img)
    ImageView versionNewImg;

    @BindView(R.id.switch_btn)
    SwitchCompat switchBtn;
    private ConfirmDialog logoutComfirmDialog;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_about);
        getIntentData(savedInstanceState);
        initView();
        checkUpdate(false);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(IS_DITUI, isDitui);
        outState.putString(NAME, name);
        outState.putString(VIDEO_URL, videoUrl);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            isDitui = savedInstanceState.getString(IS_DITUI);
            name = savedInstanceState.getString(NAME);
            videoUrl = savedInstanceState.getString(VIDEO_URL);
        } else {
            isDitui = getIntent().getStringExtra(IS_DITUI);
            name = getIntent().getStringExtra(NAME);
            videoUrl = getIntent().getStringExtra(VIDEO_URL);
        }
    }

    private void initView() {
        setActionBarTitle("关于");

        versionTv.setText(getAppVersionName());
        //通过身份判断经纪人按钮是否显示
        if (TextUtils.isEmpty(isDitui)) {
            //测试使用 VISIBLE
            agentRelative.setVisibility(View.GONE);
        } else if (ROLE_PERSON.equals(isDitui) || ROLE_PART.equals(isDitui)
                || ROLE_PART_TEAM.equals(isDitui) || ROLE_MINE.equals(isDitui)
                || ROLE_MINE_TEAM.equals(isDitui) || ROLE_PROVINCE.equals(isDitui)
                || ROLE_ASSISTANT.equals(isDitui) || ROLE_INSPECTOR.equals(isDitui)) {
            agentRelative.setVisibility(View.VISIBLE);
        } else {
            //测试使用 VISIBLE
            agentRelative.setVisibility(View.GONE);
        }

        aboutLinear.setOnClickListener(this);
        agentRelative.setOnClickListener(this);
        loginOutBtn.setOnClickListener(this);
        playImg.setOnClickListener(this);
        deleteAccountLayout.setOnClickListener(this);
        versionLayout.setOnClickListener(this);
        String privacyText = getString(R.string.shu_ming_hao, getString(R.string.privacy_policy_title));
        String termUseText = getString(R.string.shu_ming_hao, getString(R.string.user_agreement_title));
        String content = getString(R.string.ua_and_pp, privacyText, termUseText);


        int indexPrivacy = content.indexOf(privacyText);
        int indexTermUse = content.indexOf(termUseText);
        SpannableString span = new SpannableString(content);
        span.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                PrivacyPolicyActivity.startPrivacyPolicy(AboutActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(getResources().getColor(R.color.br_color_black_666));
                ds.clearShadowLayer();
            }
        }, indexPrivacy, indexPrivacy + privacyText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        span.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                PrivacyPolicyActivity.startUserAgreement(AboutActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(getResources().getColor(R.color.br_color_black_666));
                ds.clearShadowLayer();
            }
        }, indexTermUse, indexTermUse + termUseText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        uaAndPPTv.setText(span);
        uaAndPPTv.setMovementMethod(LinkMovementMethod.getInstance());

        switchBtn.setThumbResource(R.drawable.thumb);
        switchBtn.setTrackResource(R.drawable.track);

        //初始化通知当前的状态开关
        boolean isPushStopped = JPushInterface.isPushStopped(getApplicationContext());
        switchBtn.setChecked(!isPushStopped);

        switchBtn.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                handlePushNotificationToggle(isChecked);
            }
        });

    }

    /**
     * 处理推送通知开关的状态变更
     */
    private void handlePushNotificationToggle(boolean isChecked) {
        if (isChecked) {
            if (hasNotificationPermission(getApplicationContext())) {
                enablePushNotifications();
            } else {
                notifyUserToEnableNotifications();
            }
        } else {
            disablePushNotifications();
        }
    }

    /**
     * 启用推送通知
     */
    private void enablePushNotifications() {
        JPushInterface.resumePush(getApplicationContext());
        ToastUtils.showShortMsg(getApplicationContext(), "消息通知已开启");
    }

    /**
     * 禁用推送通知
     */
    private void disablePushNotifications() {
        JPushInterface.stopPush(getApplicationContext());
        ToastUtils.showShortMsg(getApplicationContext(), "消息通知已关闭");
    }

    /**
     * 提示用户去手动开启通知权限
     */
    private void notifyUserToEnableNotifications() {
        switchBtn.setChecked(false); // 重新设置开关为关闭状态
        openAppNotificationSettings(getApplicationContext());
        ToastUtils.showShortMsg(getApplicationContext(), "请在设置中开启通知权限");
    }

    /**
     * 检查应用是否具有通知权限
     */
    private boolean hasNotificationPermission(Context context) {
        return NotificationManagerCompat.from(context).areNotificationsEnabled();
    }

    /**
     * 打开应用的通知设置页面
     */
    private void openAppNotificationSettings(Context context) {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
        intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.getPackageName());
        context.startActivity(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 更新开关状态，以应对用户在系统设置中更改通知权限的情况
        boolean isPushStopped = JPushInterface.isPushStopped(getApplicationContext());
        switchBtn.setChecked(!isPushStopped);
    }

    private boolean isUserClickCheck = false;

    /**
     * 检查App是否有更新
     */
    private void checkUpdate(boolean isClick) {
        isUserClickCheck = isClick;
        HashMap<String, String> params = new HashMap<>();
        params.put("systemType", "1");//1表示Android平台，2表示iOS平台
        mRequestTask.addHttpPostRequest(HttpUrlManager.APP_CHECK_UPDATE, params, CheckUpdateResult.class, this);
    }

    @Override
    protected void onStart() {
        super.onStart();
        //是否已经点击过
        boolean isClicked = SharedPreferenceUtils.getBoolean(this, PublicParams.IS_OPEN_NEW);
        if (!isClicked) {
            newImg.setVisibility(View.VISIBLE);
        } else {
            newImg.setVisibility(View.GONE);
        }
    }

//    @OnClick(R.id.shareWx)
//    public void onViewClicked() {
//        String url = "https://www.baidu.com/s?wd=android";
//        ShareUtils shareUtils = new ShareUtils(this, ShareUtils.WECHAT);
//        shareUtils.showPopupWindow("分享到微信标题", "分享到微信文本", null, url);
//    }

    @Override
    public void onClick(View view) {
        Intent intent;
        switch (view.getId()) {
            case R.id.about_linear://关于必然中医
                intent = new Intent(this, ActionBarWebViewActivity.class);
                intent.putExtra(PublicParams.WEBVIEW_TITLE, "关于必然中医");
                intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.getAbortBRZYUrl());
                startActivity(intent);
                SharedPreferenceUtils.putBoolean(this, PublicParams.IS_OPEN_NEW, true);
                break;
            case R.id.agent_relative://经纪人
                intent = new Intent(this, AgentActivity.class);
                intent.putExtra(AgentActivity.NAME, name);
                intent.putExtra(AgentActivity.IS_DITUI, isDitui);
                startActivity(intent);
                break;
            case R.id.login_out_btn://退出登录
                if (logoutComfirmDialog == null) {
                    logoutComfirmDialog = ConfirmDialog.getInstance(this);
                }
                logoutComfirmDialog.setDialogContent("您确定要退出登录吗？")
                        .setNavigationText("取消")
                        .setPositiveText("确定")
                        .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                logoutComfirmDialog.dismiss();
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                logoutComfirmDialog.dismiss();
                                Logout();
                            }
                        }).show();
                break;
            case R.id.playImg:
                // 测试地址
//                String url = "https://raw.githubusercontent.com/danikula/AndroidVideoCache/master/files/orange2.mp4";
                intent = new Intent(this, VideoPlayActivity.class);
                intent.putExtra(VideoPlayActivity.VIDEO_URL, videoUrl);
                startActivity(intent);

//                intent = new Intent(this,VideoPlayActivity.class);
//                intent.putExtra(VideoPlayActivity.VIDEO_URL,videoUrl);
//                startActivity(intent);
                break;
            case R.id.delete_linear:
                intent = new Intent(this, DeleteAccountActivity.class);
                startActivity(intent);
                break;
            case R.id.version_linear:
                checkUpdate(true);
                break;
            default:
                break;
        }
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.APP_CHECK_UPDATE:
                if (result.isRequestSuccessed()) {
                    CheckUpdateResult checkUpdateResult = (CheckUpdateResult) result.getBodyObject();
                    String local = AppUtils.getVersion(this);
                    if (checkUpdateResult != null && !TextUtils.isEmpty(checkUpdateResult.getVersionNo())) {
                        LogUtils.i(MainActivity.class, "版本号比较：" + local.compareToIgnoreCase(checkUpdateResult.getVersionNo()));
                        if (local.compareToIgnoreCase(checkUpdateResult.getVersionNo()) < 0) {
                            versionNewImg.setVisibility(View.VISIBLE);
                            if (isUserClickCheck) {
                                LogUtils.i(MainActivity.class, "版本需要更新" + local.compareToIgnoreCase(checkUpdateResult.getVersionNo()));
                                Intent intent = new Intent(this, UpdateDownloadActivity.class);
                                intent.putExtra(PublicParams.UPDATE_APP_BEAN, checkUpdateResult);
                                startActivity(intent);
                            }
                        } else {
                            versionNewImg.setVisibility(View.INVISIBLE);
                            if (isUserClickCheck) {
                                ToastUtils.showShortMsg(this, "已经是最新版本");
                            }
                        }
                    } else {
                        if (isUserClickCheck) {
                            ToastUtils.showShortMsg(this, "检查失败，请稍后重试");
                        }
                    }
                } else {
                    if (isUserClickCheck) {
                        ToastUtils.showShortMsg(this, "检查失败，请稍后重试");
                    }
                }
                isUserClickCheck = false;
                break;
        }

    }


    /**
     * 退出登录
     */
    private void Logout() {
        BadgeUtils.setBadgeCount(AboutActivity.this, 0);
        AppContext.getInstances().getNettyClient().stop();
        AppContext.getInstances().logout();
        SharedPreferenceUtils.clearString(this);
        ActivityManager.getInstance().finishAllActivity();
        Intent i = getBaseContext().getPackageManager()
                .getLaunchIntentForPackage(getBaseContext().getPackageName());
        if (i != null) {
            i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        }
        startActivity(i);
    }


    /**
     * 获取 App 版本号
     *
     * @return App 版本号
     */
    private String getAppVersionName() {
        try {
            PackageManager pm = getPackageManager();
            PackageInfo pi = pm.getPackageInfo(getPackageName(), 0);
            String version = pi == null ? null : pi.versionName;
            if (TextUtils.isEmpty(version)) {
                return null;
            }
            return "v " + version;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return null;
        }
    }


}
