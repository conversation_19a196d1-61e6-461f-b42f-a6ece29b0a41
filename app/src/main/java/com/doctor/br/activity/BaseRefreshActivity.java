package com.doctor.br.activity;

import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.doctor.br.app.AppContext;
import com.doctor.br.view.refreshlayout.OnRefreshListener;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.List;

import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;

/**
 * Created by hanruifeng on 2017/11/15.
 */

public abstract class BaseRefreshActivity<T> extends ActionBarActivity implements OnRefreshListener,View.OnClickListener,EmptyView.OnReloadListener {
    protected RefreshLayout mRefreshLayout;
    protected RecyclerView mRvData;
    protected BGARecyclerViewAdapter<T> mRefreshAdapter;
    protected int currentPage = 1;
    protected  int pageCount = 1;//共有几页
    protected View headView;
    protected List<T> dataList;
    protected EmptyView emptyView;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(getLayoutId());
        init();
        setListener();

    }

    protected int getLayoutId() {
        return R.layout.activity_base_refresh_okhttp;
    }

    /**
     * 初始化
     */
    protected void init() {
        initRefreshLayout();
        setEmptyView();
    }
    /**
     * 初始化EmptyView
     */
    protected  void setEmptyView(){
        emptyView = (EmptyView) findViewById(R.id.emptyView);
        if(emptyView!=null){
            emptyView.setVisibility(View.GONE);//emptyView默认不可见
//            emptyView.setEmptyText("暂无数据");
//            emptyView.setEmptyImgResource(R.drawable.have_no_data);
            emptyView.setOnReloadListener(this);
        }

    }

    /**
     * 初始化RefreshLayout及adapter
     */
    private void initRefreshLayout() {
        dataList = new ArrayList<>();
        mRefreshLayout = (RefreshLayout) findViewById(R.id.refreshLayout);
        mRvData = (RecyclerView) findViewById(R.id.rv_data);

        if(mRefreshLayout==null || mRvData == null) return;

        mRefreshLayout.setOnRefreshListener(this);
        mRefreshAdapter = getAdapter();
        if(getHeadLayoutId()!=0){
            headView = View.inflate(AppContext.getContext(),getHeadLayoutId(), null);
            mRefreshAdapter.addHeaderView(headView);

        }

        if(getLayoutManager()!=null){
            mRvData.setLayoutManager(getLayoutManager());
        }
        if(mRefreshAdapter !=null){
            mRvData.setAdapter(mRefreshAdapter);
        }
    }
    /**
     * 设置监听,在init()之后执行
     */
    protected  void setListener(){

    }

    /**
     * 默认为线性布局
     * @return
     */
    public RecyclerView.LayoutManager getLayoutManager(){
        return new LinearLayoutManager(AppContext.getContext(),LinearLayoutManager.VERTICAL,false);
    }


    /**
     * 设置是否可以下拉刷新
     *
     * @return
     */
    public void setRefreshEnable(boolean isRefreshEnable) {
        if (mRefreshLayout != null) {
            mRefreshLayout.setPullDownRefreshEnable(isRefreshEnable);
        }
    }


    /**
     * 是否可以上拉加载
     *
     * @return
     */
    public void setCanLoadMore(boolean isCanLoadMore) {
        if (mRefreshLayout != null) {
            mRefreshLayout.setCanLoadMore(isCanLoadMore);
        }
    }

    /**
     * 返回头部布局的id
     * @return
     */
    public int getHeadLayoutId(){
        return 0;
    }

    public boolean hasMoreData(){
        boolean hasMore = true;
        //此处应该有是否有更多的逻辑
        if (currentPage > pageCount) {
            mRefreshLayout.endLoadingMore();
            //ToastUtils.showShortMsg(AppContext.getContext(), AppContext.getContext().getResources().getString(R.string.no_more_data));
            hasMore = false;

        }
        setCanLoadMore(hasMore);//设置是否可以加载更多
        return hasMore;
    }

    public void onRequestListSuccess(String taskId,List<T> currentDataList) {
        dataList.clear();
        dataList.addAll(currentDataList);
        if (currentPage == 1) {   //下拉刷新
            //设置empty的样式和内容
            if (dataList.size() == 0) {
                setEmptyState(EmptyView.TYPE_EMPTY);
            } else {
                setEmptyVisible(View.GONE);
            }
            addNewData();

        } else {
            setEmptyVisible(View.GONE);
            addMoreData();
        }
        if(mRefreshLayout.isCanLoadMore()){//有上啦加载更多功能后，更新pageNum
            currentPage = currentPage + 1;
        }

    }



    public void onRequestListError() {
        if (currentPage == 1) {//下拉刷新
            //设置empty的样式和内容
            setEmptyState(EmptyView.TYPE_RELOAD);
            mRefreshLayout.endRefreshing();
        } else {
            setEmptyVisible(View.GONE);
            mRefreshLayout.endLoadingMore();
        }
    }

    /**
     * 下拉刷新数据
     */
    protected void addNewData() {
        mRefreshLayout.endRefreshing();
        mRefreshAdapter.clear();
        mRefreshAdapter.addNewData(dataList);
        mRvData.smoothScrollToPosition(0);

    }

    /**
     * 上拉加载更多
     */
    protected void addMoreData() {
        mRefreshLayout.endLoadingMore();
        mRefreshAdapter.addMoreData(dataList);
    }

    /**
     * 刷新完成，将上拉加载，下拉刷新的状态更新为结束
     */
    public void onRefreshComplete() {
        if (mRefreshLayout != null) {
            mRefreshLayout.endRefreshing();
            mRefreshLayout.endLoadingMore();
        }
    }

    /**
     * 设置EmptyView的状态
     *
     * @param visible
     */
    public void setEmptyVisible(int visible) {
        if (emptyView != null) {
            emptyView.setVisibility(visible);
        }
    }

    /**
     * 设置EmptyView的类型
     *
     * @param type
     */
    public void setEmptyState(int type) {
        if (emptyView != null) {
            //设置empty的样式和内容
            emptyView.setVisibility(View.VISIBLE);
            emptyView.setEmptyType(type);
        }
    }

    /**
     * 设置pageSize的大小
     *
     * @return
     */
    protected int getPageSize() {
        return 20;
    }

    /**
     * 返回adapter
     *
     * @return
     */
    public abstract BGARecyclerViewAdapter<T> getAdapter();




}
