package com.doctor.br.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.doctor.br.activity.manage.medicineshop.MyMedicineShopActivity;
import com.doctor.br.fragment.main.ManageFragment;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.widgets.EmptyView;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 类描述：养生建议 合作协议
 * 创建人：YangYajun
 */

public class MyMedicineProtocolActivity extends ActionBarActivity {
    //  上个界面传回的参数
    public final static String IS_SHOW = "isShow";// 是否显示底部布局

    private boolean isShow = true;// 是否显示底部布局

    private boolean isLoadError;
    private RequestCallBack agreementCallBack;//协议请求回调


    @BindView(R.id.cb_select)
    CheckBox cbSelect;
    @BindView(R.id.btn_use)
    Button btnUse;
    @BindView(R.id.web_view)
    WebView webView;
    @BindView(R.id.mEmptyView)
    EmptyView mEmptyView;
    @BindView(R.id.ll_bottom)
    LinearLayout llBottom;
    @BindView(R.id.rl_container)
    RelativeLayout rlContainer;
    private String userId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_user_protocol);
        setActionBarTitle("合作协议");
        init();
        initView();
        isShow();
        loadData();
    }

    /**
     * 底部布局是否显示
     */
    protected void isShow() {
        isShow = getIntent().getBooleanExtra(IS_SHOW, true);
        if (!isShow) {
            rlContainer.setVisibility(View.GONE);
        } else {
            rlContainer.setVisibility(View.VISIBLE);
            isShow = true;
        }
    }

    protected void init() {
        userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        //  是否同意协议及按钮是否可点击
        cbSelect.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    cbSelect.setChecked(true);
                    btnUse.setEnabled(true);
                } else {
                    cbSelect.setChecked(false);
                    btnUse.setEnabled(false);
                }
            }
        });

    }

    @SuppressLint("JavascriptInterface")
    protected void initView() {

        if (mLoadingDialog != null && !mLoadingDialog.isShowing()) {
            mLoadingDialog.show();
        }
        new Thread() {
            @Override
            public void run() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (isFinishing()) {
                            return;
                        }
                        if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                            mLoadingDialog.dismiss();
                        }
                    }
                });
            }
        }.start();

        mEmptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
            @Override
            public void onReload() {
                mEmptyView.setEmptyType(EmptyView.TYPE_LOADING);
                webView.reload();
            }
        });
        WebSettings settings = webView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setAppCacheEnabled(true);
        settings.setBuiltInZoomControls(true);
        settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setSaveFormData(true);
        settings.setGeolocationEnabled(true);
        settings.setDomStorageEnabled(true);
        //不显示webview缩放按钮
        settings.setDisplayZoomControls(false);
        //不支持屏幕缩放
        settings.setSupportZoom(false);
        settings.setBuiltInZoomControls(false);
        webView.requestFocus();
        webView.addJavascriptInterface(this, "window");

        webView.setWebViewClient(new WebViewClient() {

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    view.loadUrl(request.getUrl().toString());
                }
                return super.shouldOverrideUrlLoading(view, request);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                isLoadError = true;
                mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);

            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
//                isLoadError = true;
//                mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                isLoadError = true;
                if (isLoadError) {
                    mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                } else {
                    mEmptyView.setEmptyType(EmptyView.TYPE_LOADING);
                    mEmptyView.hide();
                }
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.proceed();
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                if (isLoadError) {
                    mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                } else {
                    mEmptyView.setEmptyType(EmptyView.TYPE_LOADING);
                    mEmptyView.hide();
                }
            }
        });

        webView.setWebChromeClient(new WebChromeClient() {


            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
                if (!TextUtils.isEmpty(title) && title.toLowerCase().contains("error")) {
                    isLoadError = true;
                    // mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    if (isLoadError) {
                        mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    } else {
                        mEmptyView.hide();
                    }
                } else {
                    isLoadError = false;
                }
            }
        });

        webView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                return true;
            }
        });
    }

    /**
     * 加载养生建议合作协议
     */
    private void loadData() {
        webView.loadUrl(BaseConfig.MEDICATION_SHOP_PROTOCOL + userId);
    }

    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        if (agreementCallBack != null) {
            agreementCallBack.cancel();
            agreementCallBack = null;
        }
        super.onDestroy();
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (isFinishing()) {
            return;
        }
        if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
            mLoadingDialog.dismiss();
        }
    }

    @OnClick({R.id.btn_use, R.id.ll_bottom})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_use://进入养生铺
                Intent intent = new Intent(MyMedicineProtocolActivity.this, MyMedicineShopActivity.class);
                startActivity(intent);
                SharedPreferenceForeverUtils.putBoolean(mContext, ManageFragment.IS_FIRST+userId,true);
                finish();
                break;
            case R.id.ll_bottom://  是否同意协议及按钮是否可点击
                if (!cbSelect.isChecked()) {
                    btnUse.setEnabled(true);
                    cbSelect.setChecked(true);
                } else {
                    btnUse.setEnabled(false);
                    cbSelect.setChecked(false);
                }
                break;
            default:
                break;
        }
    }
}
