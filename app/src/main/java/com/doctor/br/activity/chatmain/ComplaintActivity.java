package com.doctor.br.activity.chatmain;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;

import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：投诉
 */

public class ComplaintActivity extends ActionBarActivity {
    //界面下的控件
    @BindView(R.id.spinner_title)
    Spinner titleSpinner;
    @BindView(R.id.content_et)
    EditText contentEt;
    @BindView(R.id.commit_btn)
    Button commitBtn;
    private String doctorId;
    private String doctorPhone;
    private String doctorName;
    private String patientId;
    private String patientPhone;
    private String patientName;


    private static final String EXTRA_PATIENT_ID = "extra_patient_id";
    private static final String EXTRA_PATIENT_PHONE = "extra_patient_phone";
    private static final String EXTRA_PATIENT_NAME = "extra_patient_name";

    public static void start(Context context, String patientId, String patientPhone, String patientName) {
        Intent intent = new Intent(context, ComplaintActivity.class);
        intent.putExtra(EXTRA_PATIENT_ID, patientId);
        intent.putExtra(EXTRA_PATIENT_PHONE, patientPhone);
        intent.putExtra(EXTRA_PATIENT_NAME, patientName);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_complaint);
        patientId = getIntent().getStringExtra(EXTRA_PATIENT_ID);
        patientPhone = getIntent().getStringExtra(EXTRA_PATIENT_PHONE);
        patientName = getIntent().getStringExtra(EXTRA_PATIENT_NAME);
        if (TextUtils.isEmpty(patientId)) {
            finish();
            return;
        }
        initView();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("投诉");
        //从本地取出登录的账号
        doctorId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        doctorPhone = SharedPreferenceUtils.getString(this, PublicParams.USER_TEL);
        doctorName = SharedPreferenceUtils.getString(this, PublicParams.USER_NAME);

        commitBtn.setOnClickListener(this);
    }


    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.commit_btn:
                commitBtn.setEnabled(false);
                commitBtn.setText("提交中，请稍候...");
                String title = (String) titleSpinner.getSelectedItem();
                String content = contentEt.getText().toString();
                Map<String, String> param = new HashMap<>();
                param.put("targetUserId", patientId);
                param.put("title", title);
                param.put("body", content);
                addHttpPostRequest(HttpUrlManager.USER_COMPLAINT, param, ResponseResult.class, this);
                break;
            default:
                break;
        }
    }


    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.USER_COMPLAINT:
                commitBtn.setEnabled(true);
                commitBtn.setText("提交");
                if (result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(getApplicationContext(), "投诉提交成功，请耐心等待管理员审核！");
                    finish();
                } else {
                    ToastUtils.showShortMsg(getApplicationContext(), "投诉提交失败，请稍后再试！");
                }
                break;
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

}
