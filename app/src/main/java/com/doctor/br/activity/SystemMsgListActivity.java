package com.doctor.br.activity;

import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.ListView;

import com.doctor.br.adapter.SystemMsgAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.MsgStatus;
import com.doctor.br.db.entity.Session;
import com.doctor.br.db.entity.SystemMsg;
import com.doctor.br.netty.NettyMsgNotify;
import com.doctor.br.netty.NotifyType;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.view.refreshlayout.OnRefreshListener;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.greendao.gen.SystemMsgDao;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

public class SystemMsgListActivity extends ActionBarActivity implements OnRefreshListener {

    @BindView(R.id.lv_list)
    ListView lvList;
    @BindView(R.id.refresh_layout)
    RefreshLayout refreshLayout;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private ConfirmDialog deleteDialog;
    private SystemMsgAdapter systemMsgAdapter;
    private List<SystemMsg> systemMsgList;
    private SystemMsgDao systemMsgDao;
    private String userId;
    private int page = 0;
    private int pageSize = 20;
    private Handler mHandler = new Handler();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_system_msg);
        EventBusUtils.register(this);
        setActionBarTitle("系统消息");
        setActionBarRightBtnImg(R.drawable.icon_delete);
        systemMsgDao = AppContext.getInstances().getDaoSession().getSystemMsgDao();
        userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        initView();
        initData();
        updateMsgToRead();
    }

    /**
     * 初始化view
     */
    private void initView() {
        lvList.setEmptyView(emptyView);
        systemMsgList = new ArrayList<>();
        systemMsgAdapter = new SystemMsgAdapter(this, systemMsgList, R.layout.item_system_msg);
        lvList.setAdapter(systemMsgAdapter);
        refreshLayout.setOnRefreshListener(this);
    }

    /**
     * 初始化数据
     */
    private void initData() {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                List<SystemMsg> list = systemMsgDao.queryBuilder()
                        .where(SystemMsgDao.Properties.To.eq(userId))
                        .offset(page * pageSize)
                        .limit(pageSize)
                        .orderDesc(SystemMsgDao.Properties.CreatedTime)
                        .build().list();
                if (page == 0) {
                    systemMsgList.clear();
                }
                systemMsgList.addAll(list);
                systemMsgAdapter.notifyDataSetChanged();
                refreshLayout.endRefreshing();
                refreshLayout.endLoadingMore();
            }
        });

    }

    /**
     * 更新消息为已读
     *
     * @receiver {@link com.doctor.br.fragment.main.NewsFragment#onSessionNotifyEvent(NettyMsgNotify)}
     */
    private void updateMsgToRead() {
        List<SystemMsg> list = systemMsgDao.queryBuilder()
                .where(SystemMsgDao.Properties.To.eq(userId))
                .where(SystemMsgDao.Properties.IsRead.eq(MsgStatus.NOT_READ))
                .list();
        for (SystemMsg systemMsg : list) {
            systemMsg.setIsRead(MsgStatus.IS_READ);
            systemMsgDao.update(systemMsg);
        }
        EventBusUtils.post(new NettyMsgNotify(NotifyType.SESSION_LIST, new Session()));
    }


    /**
     * @param view
     */
    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        if (deleteDialog == null) {
            deleteDialog = ConfirmDialog.getInstance(this);
        }
        deleteDialog.setDialogContent("是否清空系统消息？")
                .setPositiveText("清空")
                .setNavigationText("取消")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        deleteDialog.dismiss();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        deleteData();
                        deleteDialog.dismiss();
                        setResult(RESULT_OK);
                        finish();
                    }
                }).show();
    }

    /**
     * 删除系统消息数据
     */
    private void deleteData() {
        try {
            List<SystemMsg> list = systemMsgDao.queryBuilder()
                    .where(SystemMsgDao.Properties.To.eq(userId))
                    .build().list();
            systemMsgDao.deleteInTx(list);
            ToastUtils.showShortMsg(this, "系统消息已删除");
            page = 0;
            initData();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {
        page = 0;
        initData();
    }

    @Override
    public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {
        page++;
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(500);
                    initData();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUpdateSystemMsgListEvent(NettyMsgNotify notify) {
        if (NotifyType.UPDATE_SYSTEM_MSG_LIST == notify.getNotifyType()) {
            SystemMsg systemMsg = notify.getNotifySystemMsg();
            systemMsgList.add(0, systemMsg);
            systemMsgAdapter.notifyDataSetChanged();
        }
    }

    @Override
    protected void onDestroy() {
        updateMsgToRead();
        EventBusUtils.unRegister(this);
        super.onDestroy();
    }

}
