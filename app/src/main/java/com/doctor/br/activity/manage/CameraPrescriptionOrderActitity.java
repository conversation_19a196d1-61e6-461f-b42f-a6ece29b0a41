package com.doctor.br.activity.manage;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ImageSpan;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.adapter.manage.CameraPrescriptionOrderViewPagerAdapter;
import com.doctor.br.bean.event.PictureOrderListVisibleEvent;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.OrderAndWalletDialog;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.utils.DensityUtils;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * 类描述：拍照药方订单
 * 创建人：YangYanjun
 * 创建时间：2018/07/06
 */

public class CameraPrescriptionOrderActitity extends ActionBarActivity implements ViewPager.OnPageChangeListener {
    //上个界面传递过来的数据
    public static final String CLICK_POSITION = "click_position";
    private int clickPosition;//点击哪个button进来的。从而判断首先加载哪个fragment，非必须，默认为0
    public static final String IS_SHOW = "is_show";//是否显示订单
    private boolean isShow = false;//默认不显示

    // 界面下的控件
    @BindView(R.id.linear_container)
    LinearLayout linearContainer;
    @BindView(R.id.bottom_line)
    LinearLayout bottomLine;
    @BindView(R.id.view1)
    RelativeLayout view1;
    @BindView(R.id.view_pager)
    ViewPager viewPager;
    @BindView(R.id.gone_tv)
    TextView goneTv;

    private OrderAndWalletDialog orderAndWalletDialog;//控制订单显示与隐藏的对话框

    private List<int[]> paddingList;//设置bottomLine的padding

    private CameraPrescriptionOrderViewPagerAdapter cameraPrescriptionOrderViewPagerAdapter;//下面viewpager的适配器

    private String[] names = {"待发送", "待审核", "待转换"};

    public static final String PICTURE_WAIT_SEND = "WAIT_FOR_SEND";//拍照药方状态，待发送
    public static final String PICTURE_WAIT_CONVERSION = "WAIT_FOR_CONVERSION";//待转换
    public static final String PICTURE_WAIT_REVIEW = "WAIT_FOR_REVIEW";//待审核

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_camera_prescription_order);
        getIntentData(savedInstanceState);
        initView();
        setEye();
        setList();
    }


    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            clickPosition = savedInstanceState.getInt(CLICK_POSITION);
            isShow = savedInstanceState.getBoolean(IS_SHOW);
        } else {
            clickPosition = getIntent().getIntExtra(CLICK_POSITION, 0);
            isShow = getIntent().getBooleanExtra(IS_SHOW, true);
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(CLICK_POSITION, clickPosition);
        outState.putBoolean(IS_SHOW, isShow);
    }

    private void initView() {
        setActionBarTitle("拍照药方订单");
        getBaseActionBar().setActionBarChangeListener(this);

        cameraPrescriptionOrderViewPagerAdapter = new CameraPrescriptionOrderViewPagerAdapter(getSupportFragmentManager(), names.length, isShow);
        viewPager.setOffscreenPageLimit(names.length);
        viewPager.setAdapter(cameraPrescriptionOrderViewPagerAdapter);
        viewPager.addOnPageChangeListener(this);

        SpannableString spannableString = new SpannableString("显示用药订单，点击页面右上角的眼睛标识");
        Drawable drawable = ContextCompat.getDrawable(this, R.drawable.eye_open);
        drawable.setBounds(0, 0, DensityUtils.dip2px(this, 22), DensityUtils.dip2px(this, 22));
        ImageSpan imageSpan = new ImageSpan(drawable);
        spannableString.setSpan(imageSpan, spannableString.length() - 4, spannableString.length() - 2, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        goneTv.setText(spannableString);

        orderAndWalletDialog = new OrderAndWalletDialog(this, isShow,
                OrderAndWalletDialog.WhichActivity.PICTURE_ORDER_ACTIVITY, new OrderAndWalletDialog.VisibilityListener() {
            @Override
            public void changeSuccess(boolean isShow) {
                CameraPrescriptionOrderActitity.this.isShow = isShow;
                setEye();
                refreshOrderListVisibility();
                cameraPrescriptionOrderViewPagerAdapter.setShow(isShow);
            }
        });
    }

    /**
     * 设置右上角眼睛的状态
     */
    private void setEye() {
        View view = View.inflate(this, R.layout.view_eye_state, null);
        ImageView stateImg = (ImageView) view.findViewById(R.id.state_img);
        TextView stateTv = (TextView) view.findViewById(R.id.state_tv);
        if (isShow) {
            goneTv.setVisibility(View.GONE);
            stateImg.setImageResource(R.drawable.eye_close);
            stateTv.setText("隐藏");
        } else {
            goneTv.setVisibility(View.VISIBLE);
            stateImg.setImageResource(R.drawable.eye_open);
            stateTv.setText("显示");
        }
        getBaseActionBar().setRightView(view);
    }

    /**
     * 根据names设置文字，并获取左右边距
     */
    private void setList() {
        paddingList = new ArrayList<>(names.length);
        for (int i = 0; i < names.length; i++) {
            LinearLayout linearLayout = new LinearLayout(this);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT);
            layoutParams.weight = 1;
            linearLayout.setLayoutParams(layoutParams);
            linearLayout.setGravity(Gravity.CENTER);
            linearLayout.setTag(R.id.img_url, i);
            linearLayout.setOnClickListener(this);

            TextView textView = new TextView(this);
            LinearLayout.LayoutParams tvLp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            textView.setLayoutParams(tvLp);
            textView.setTextSize(16);
            textView.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme_text));
            textView.setText(names[i]);

            linearLayout.addView(textView);

            linearContainer.addView(linearLayout);
        }
        linearContainer.post(new Runnable() {
            @Override
            public void run() {
                int marginPx = DensityUtils.dip2px(CameraPrescriptionOrderActitity.this, 5f);
                for (int i = 0; i < names.length; i++) {
                    int[] leftRight = new int[2];//左边是到父布局左侧距离，右边是到父布局右侧距离
                    ViewGroup viewGroup = (ViewGroup) linearContainer.getChildAt(i);
                    leftRight[0] = viewGroup.getChildAt(0).getLeft() - marginPx;
                    leftRight[1] = viewGroup.getMeasuredWidth() - viewGroup.getChildAt(0).getRight() - marginPx;
                    paddingList.add(leftRight);
                }
                setSelectPosition();
            }
        });
    }

    /**
     * 根据当前选中的位置，设置底部bottomLine的内边距
     */
    private void setSelectPosition() {
        viewPager.setCurrentItem(clickPosition, false);
        if (paddingList.size() == 0) {
            return;
        }
        double eachWidth = bottomLine.getMeasuredWidth() / paddingList.size();
        double left = paddingList.get(clickPosition)[0] + clickPosition * eachWidth;
        double right = paddingList.get(clickPosition)[1] + (paddingList.size() - 1 - clickPosition) * eachWidth;
        bottomLine.setPadding((int) left, 0, (int) right, 0);
    }

    /**
     * 根据选中位置设置字体颜色
     *
     * @param position 当前选中的位置
     */
    private void switchColor(int position) {
        for (int i = 0; i < names.length; i++) {
            LinearLayout linearLayout = (LinearLayout) linearContainer.getChildAt(i);
            TextView textView = (TextView) linearLayout.getChildAt(0);
            if (i == position) {
                textView.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme));
            } else {
                textView.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme_text));
            }
        }
    }

    @Override
    public void onRightBtnClick(View view) {
        orderAndWalletDialog.show();
    }

    @Override
    public void onClick(View view) {
        if (view.getTag(R.id.img_url) instanceof Integer) {
            int clickPos = (int) view.getTag(R.id.img_url);
            viewPager.setCurrentItem(clickPos);
        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        setPaddingScroll(position, positionOffset);
    }

    @Override
    public void onPageSelected(int position) {
        switchColor(position);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    private void setPaddingScroll(int position, float positionOffset) {
        if (paddingList.size() == 0 || position == paddingList.size() - 1) {
            return;
        }
        double eachWidth = bottomLine.getMeasuredWidth() / paddingList.size();
        double left, right;
        double nextLeft = paddingList.get(position + 1)[0] + (position + 1) * eachWidth;
        double nowLeft = paddingList.get(position)[0] + position * eachWidth;
        double nextRight = paddingList.get(position + 1)[1] + (paddingList.size() - 2 - position) * eachWidth;
        double nowRight = paddingList.get(position)[1] + (paddingList.size() - 1 - position) * eachWidth;
        left = positionOffset * (nextLeft - nowLeft) + nowLeft;
        right = positionOffset * (nextRight - nowRight) + nowRight;
        bottomLine.setPadding((int) left, 0, (int) right, 0);
    }

    /**
     * 使用EventBus更新列表显示与否状态
     *
     * @sender {@link com.doctor.br.activity.manage.CameraPrescriptionOrderActitity#refreshOrderListVisibility()}拍照订单页面
     * @receiver {@link com.doctor.br.fragment.main.ManageFragment#refreshPictureOrderVisible(PictureOrderListVisibleEvent)}管理页面
     * {@link com.doctor.br.fragment.manage.pictureOrder.DaifasongFragment#refreshOrderListVisible(PictureOrderListVisibleEvent)}待发送页面
     */
    private void refreshOrderListVisibility() {
        EventBusUtils.post(new PictureOrderListVisibleEvent(isShow));
    }

    @Override
    protected void onDestroy() {
        if (orderAndWalletDialog != null) {
            orderAndWalletDialog.cancel();
        }
        super.onDestroy();
    }
}
