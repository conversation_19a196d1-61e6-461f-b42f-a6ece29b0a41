package com.doctor.br.activity.manage;

import android.app.Activity;
import android.os.Bundle;
import androidx.appcompat.widget.SwitchCompat;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;

import com.doctor.br.bean.ServiceSettingBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.NoEmojiEditText;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：服务设置
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/5
 */

public class ServiceSettingActivity extends ActionBarActivity {
    //界面下的控件

    @BindView(R.id.welcome_word_et)
    NoEmojiEditText welcomeWordEt;//欢迎语输入框
    @BindView(R.id.word_number_tv)
    TextView wordNumberTv;//欢迎语当前字数显示
    @BindView(R.id.fee_price_et)
    EditText feePriceEt;//诊费输入框
    @BindView(R.id.switch_btn)
    SwitchCompat switchBtn;//切换免打扰设置
    @BindView(R.id.hint_tv)
    TextView hintTv;//切换免打扰设置后的提示语
    @BindView(R.id.commit_btn)
    com.doctor.br.view.NoDoubleClickBtn commitBtn;//保存按钮

    private RequestCallBack serviceCallBack;//网络请求获取服务设置状态回调
    private RequestCallBack saveServiceCallBack;//网络请求保存服务设置状态回调

    private ServiceSettingBean netBean;//网络请求服务设置状态javabean

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_service_setting);
        initView();
        getServiceRequest();
//        ToastUtils.showShortMsg("等待接口联调");
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("服务设置");

        welcomeWordEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                wordNumberTv.setText(s.length() + "/200");
            }
        });

        feePriceEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                //处理0开头的情况
                if (s.toString().startsWith("0")) {
                    if (s.length() > 1) {
                        feePriceEt.setText(s.toString().substring(1, s.length()));
                        feePriceEt.setSelection(feePriceEt.getText().length());
                    }
                }

            }
        });

        switchBtn.setThumbResource(R.drawable.thumb);
        switchBtn.setTrackResource(R.drawable.track);
        switchBtn.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    hintTv.setText("“免打扰”模式现已开启，患者主动给您发消息时，需要预支付10元费用才可发起会话。");
                } else {
                    hintTv.setText("“免打扰”模式现已关闭，患者主动给您发消息时，将不会受到限制。");
                }
            }
        });

        commitBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(welcomeWordEt.getText())) {
                    ToastUtils.showShortMsg(ServiceSettingActivity.this, "请输入欢迎语");
                    return;
                }
                saveServiceRequest(welcomeWordEt.getText().toString(),
                        TextUtils.isEmpty(feePriceEt.getText()) ? "0" : feePriceEt.getText().toString(),
                        switchBtn.isChecked() ? "1" : "0");
            }
        });
    }

    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    private ConfirmDialog backDialog;

    @Override
    public void onBackPressed() {
        if (netBean == null) {
            super.onBackPressed();
            return;
        }
        ServiceSettingBean localBean = new ServiceSettingBean();
        localBean.setChangeSetPrice(feePriceEt.getText().toString());
        localBean.setChatSet(switchBtn.isChecked() ? "1" : "0");
        localBean.setWelcome(welcomeWordEt.getText().toString());
        if (!localBean.equals(netBean)) {
            backDialog = ConfirmDialog.getInstance(this)
                    .setDialogContent("设置的信息未保存，是否退出？")
                    .setPositiveText("退出")
                    .setNavigationText("取消")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
                            backDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            backDialog.dismiss();
                            ServiceSettingActivity.super.onBackPressed();
                        }
                    });
            backDialog.show();
        } else {
            super.onBackPressed();
        }
    }

    /**
     * 网络请求获取服务设置的数据
     */
    private void getServiceRequest() {
        serviceCallBack = addHttpPostRequest(HttpUrlManager.SERVICE_STATE, null, ServiceSettingBean.class, this);
    }

    /**
     * 网络请求保存服务设置
     *
     * @param welcome  欢迎语，非必须
     * @param feePrice 诊费，必须
     * @param chatSet  免打扰设置 0 关 1 开
     */
    private void saveServiceRequest(String welcome, String feePrice, String chatSet) {
        Map<String, String> map = new HashMap<>();
        map.put("welcome", welcome);
        map.put("changeSetPrice", feePrice);
        map.put("chatSet", chatSet);
        saveServiceCallBack = addHttpPostRequest(HttpUrlManager.SAVE_SERVICE_STATE, map, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if(welcomeWordEt==null){
            return;
        }
        switch (taskId) {
            case HttpUrlManager.SERVICE_STATE://获取服务设置数据
                if (result.isRequestSuccessed()) {
                    netBean = (ServiceSettingBean) result.getBodyObject();
                    welcomeWordEt.setText(netBean.getWelcome());
                    feePriceEt.setText(netBean.getChangeSetPrice());
                    if ("1".equals(netBean.getChatSet())) {
                        switchBtn.setChecked(true);
                    } else {
                        switchBtn.setChecked(false);
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.SAVE_SERVICE_STATE://更改服务设置数据
                if (result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "保存成功");
//                    new Handler().postDelayed(new Runnable() {
//                        @Override
//                        public void run() {
                    finish();
//                        }
//                    }, 1000);
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (serviceCallBack != null) {
            serviceCallBack.cancel();
        }
        if (saveServiceCallBack != null) {
            saveServiceCallBack.cancel();
        }
        super.onDestroy();
    }
}
