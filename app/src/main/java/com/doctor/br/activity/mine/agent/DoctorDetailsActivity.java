package com.doctor.br.activity.mine.agent;

import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.bean.AgentDoctorDetailsBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.EmptyView;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.HashMap;
import java.util.Map;

/**
 * 类描述：医生详情
 * 创建人：ShiShaoPo
 * 创建时间：2017/9/28 10:45
 * 修改人：ShiShaoPo
 * 修改时间：2017/9/28 10:45
 */

public class DoctorDetailsActivity extends ActionBarActivity {
    //上个界面传递过来的信息
    public static final String DOCTOR_ID = "doctorId";
    private String doctorId;//医生id
    //常量
    private final String SEX_MALE = "1";//男
    //界面下的控件
    private ShapeImageView headImg;
    private ImageView sexImg;
    private TextView nameTv;
    private TextView levelTv;
    private TextView phoneTv;
    private TextView areaTv;
    private TextView mechanismTv;
    private TextView goodAtTv;
    private TextView descTv;
    private EmptyView emptyView;

    private RequestCallBack doctorDetailsCallback;//用于取消网络请求

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_doctor_details);
        getIntentData(savedInstanceState);
        initView();
        requestDoctorDetails(doctorId);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(DOCTOR_ID, doctorId);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            doctorId = savedInstanceState.getString(DOCTOR_ID);
        } else {
            doctorId = getIntent().getStringExtra(DOCTOR_ID);
        }
        if (TextUtils.isEmpty(doctorId)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }

    private void initView() {
        setActionBarTitle(R.string.doctor_details);
        headImg = (ShapeImageView) findViewById(R.id.head_img);
        sexImg = (ImageView) findViewById(R.id.sex_img);
        nameTv = (TextView) findViewById(R.id.name_tv);
        levelTv = (TextView) findViewById(R.id.level_tv);
        phoneTv = (TextView) findViewById(R.id.phone_tv);
        areaTv = (TextView) findViewById(R.id.area_tv);
        mechanismTv = (TextView) findViewById(R.id.mechanism_tv);
        goodAtTv = (TextView) findViewById(R.id.good_at_tv);
        descTv = (TextView) findViewById(R.id.desc_tv);

        emptyView = (EmptyView) findViewById(R.id.empty_view);
        emptyView.setEmptyBackgroundColor(ContextCompat.getColor(this, R.color.br_color_white));
    }


    /**
     * 网络请求获取大夫详情
     *
     * @param id 大夫id
     */
    private void requestDoctorDetails(String id) {
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
        map.put("doctorId", id);
        doctorDetailsCallback = addHttpPostRequest(HttpUrlManager.DOCTOR_DETAILS, map, AgentDoctorDetailsBean.class, this);
    }


    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (headImg == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.DOCTOR_DETAILS:
                if (result.isRequestSuccessed()) {
                    AgentDoctorDetailsBean agentDoctorDetailsBean = (AgentDoctorDetailsBean) result.getBodyObject();
                    GlideUtils.getInstance().loadImage(agentDoctorDetailsBean.getHandImgUrl(), this, headImg, R.drawable.default_head_img);
                    if (SEX_MALE.equals(agentDoctorDetailsBean.getSex())) {
                        sexImg.setImageResource(R.drawable.male);
                    } else {
                        sexImg.setImageResource(R.drawable.female);
                    }
                    nameTv.setText(agentDoctorDetailsBean.getName());
                    String title = "其他".equals(agentDoctorDetailsBean.getTitle()) ? "中医师" : agentDoctorDetailsBean.getTitle();
                    levelTv.setText(title);
                    phoneTv.setText(agentDoctorDetailsBean.getMobile());
                    areaTv.setText(agentDoctorDetailsBean.getAddress());
                    mechanismTv.setText(agentDoctorDetailsBean.getHospitalName());
                    goodAtTv.setText(agentDoctorDetailsBean.getGoodAt());
                    descTv.setText(agentDoctorDetailsBean.getDescription());
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            requestDoctorDetails(doctorId);
                        }
                    });
                    emptyView.setVisibility(View.VISIBLE);
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (doctorDetailsCallback != null) {
            doctorDetailsCallback.cancel();
        }
        super.onDestroy();
    }
}
