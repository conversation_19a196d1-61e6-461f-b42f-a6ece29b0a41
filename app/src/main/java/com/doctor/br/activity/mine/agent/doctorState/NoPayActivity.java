package com.doctor.br.activity.mine.agent.doctorState;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.doctor.br.activity.mine.agent.DoctorDetailsActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.AgentNoPayAdapter;
import com.doctor.br.bean.AgentNoPatientDoctorBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：经纪人-大夫状态-开方未支付页面
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/12
 */

public class NoPayActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;
    //未加未支付大夫列表
    private List<AgentNoPatientDoctorBean.DoctorMessagesBean> doctorList;
    //列表适配器
    private AgentNoPayAdapter adapter;
    //网络请求未支付列表回调
    private RequestCallBack doctorListCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_agent_no_patient);
        initView();
        getNoPayDoctorList();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("开方未支付");

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setHasFixedSize(true);

        doctorList = new ArrayList<>();
        adapter = new AgentNoPayAdapter(this, doctorList, this);
        recyclerView.setAdapter(adapter);
    }

    /**
     * recyclerview每条item的点击事件
     *
     * @param position 点击的item
     */
    @Override
    public void itemClick(int position) {
        Intent intent = new Intent(this, DoctorDetailsActivity.class);
        intent.putExtra(DoctorDetailsActivity.DOCTOR_ID, doctorList.get(position).getDoctorId());
        startActivity(intent);
    }

    /**
     * 网络请求获取未支付大夫列表
     */
    private void getNoPayDoctorList() {
        emptyView.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        doctorListCallBack = addHttpPostRequest(HttpUrlManager.AGENT_NO_PAY, map, AgentNoPatientDoctorBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (recyclerView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.AGENT_NO_PAY:
                if (result.isRequestSuccessed()) {
                    AgentNoPatientDoctorBean doctorBean = (AgentNoPatientDoctorBean) result.getBodyObject();
                    doctorList.clear();
                    doctorList.addAll(doctorBean.getDoctorMessages());
                    adapter.notifyDataSetChanged();
                    if (doctorList.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        recyclerView.setVisibility(View.GONE);
                        emptyView.setVisibility(View.VISIBLE);
                        emptyView.setEmptyText("暂无大夫");
                    }

                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    recyclerView.setVisibility(View.GONE);
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            getNoPayDoctorList();
                        }
                    });
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (doctorListCallBack != null) {
            doctorListCallBack.cancel();
        }
        super.onDestroy();
    }

}
