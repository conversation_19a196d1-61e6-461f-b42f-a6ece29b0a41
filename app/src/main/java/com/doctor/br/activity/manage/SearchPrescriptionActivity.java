package com.doctor.br.activity.manage;

import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;

import com.doctor.br.activity.BaseRefreshActivity;
import com.doctor.br.adapter.manage.CommonPrescriptionAdapter;
import com.doctor.br.bean.CommonPrescriptionBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.DialogHelper;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.yy.R;

import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.List;
import java.util.Map;

import cn.bingoogolapple.androidcommon.adapter.BGAOnItemChildClickListener;
import cn.bingoogolapple.androidcommon.adapter.BGAOnItemChildLongClickListener;
import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;

/**
 * 搜索常用方界面
 */
public class SearchPrescriptionActivity extends BaseRefreshActivity<CommonPrescriptionBean.DatalistBean> implements BGAOnItemChildClickListener,BGAOnItemChildLongClickListener {
    private int currentLongClickPosition = -1;//当前的长点击位置
    private ConfirmDialog confirmDialog;
    private RequestCallBack prescriptionCallBack;
    private RequestCallBack deleteCallBack;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_base_refresh_okhttp;
    }

    @Override
    public BGARecyclerViewAdapter<CommonPrescriptionBean.DatalistBean> getAdapter() {//返回adapter
        return new CommonPrescriptionAdapter(mRvData);
    }

    /**
     * 初始化
     */
    @Override
    protected void init() {
        super.init();
        setActionBarType(ActionBarView.ACTIONBAR_SEARCH);
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarSearchInputHint("常用方名称搜索");

        setRefreshEnable(false);//不需要上啦刷新
        mRefreshLayout.setCanLoadMore(false);//不需要加载更多
        mRefreshAdapter.setOnItemChildClickListener(this);
        mRefreshAdapter.setOnItemChildLongClickListener(this);

    }

    public void requestData(int pageNum) {//网络请求 addHttp......
        if(TextUtils.isEmpty(getActionbarSearchInputText().trim())){
            ToastUtils.showShortMsg(this,"请输入常用方名称");
            return;
        }
        currentPage = pageNum;
        String content = getActionbarSearchInputText().trim();
        Map<String, String> params = RequestParams.getCommonPrescriptionParams(content, currentPage, getPageSize());
        prescriptionCallBack = addHttpPostRequest(HttpUrlManager.COMMON_PRESCRIPTION, params, CommonPrescriptionBean.class, SearchPrescriptionActivity.this);

    }



    @Override
    protected void setListener() {//设置监听,在init()之后执行
        super.setListener();
        if (emptyView != null) {
            emptyView.setEmptyText("未搜到相关内容");
            emptyView.setEmptyImgResource(R.drawable.no_commonprescription_data);
        }
        setOnSearchInputEditorActionListener(new TextView.OnEditorActionListener() {//键盘search的监听
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if(actionId == EditorInfo.IME_ACTION_SEARCH){
                  requestData(1);
                    return true;
                }
                return false;
            }
        });
        setActionBarSearchInputListener(new TextWatcher() {//添加数据框的实时监听
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String content = getActionbarSearchInputText().trim();
                if(TextUtils.isEmpty(content)){//清除之前的列表数据
                    if(mRefreshAdapter.getData()!=null && mRefreshAdapter.getData().size()>0)
                    mRefreshAdapter.clear();
                }
            }
        });

    }

    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        requestData(1);
    }

    @Override
    public void onItemChildClick(ViewGroup parent, View childView, int position) {//条目点击事件
        if (childView.getId() == R.id.rl_container) {
            //传递数据到编辑常用方界面
            List<CommonPrescriptionBean.DatalistBean> list =mRefreshAdapter.getData();
            Intent intent = new Intent(this, AddCommonPrescriptionActivity.class);
            intent.putExtra("title", "编辑常用方");
            intent.putExtra("templateMedicine",  list.get(position));
           // finish();
            startActivity(intent);
        }
    }

    @Override
    public boolean onItemChildLongClick(ViewGroup parent, View childView, int position) {
        if (childView.getId() == R.id.rl_container) {
            this.currentLongClickPosition = position;
            confirmDialog = DialogHelper.openConfirmDialog(this, "是否删除此常用方？", "确认", "取消"
                    , new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (currentLongClickPosition >= 0 && currentLongClickPosition < mRefreshAdapter.getData().size()) {
                        String templateId = mRefreshAdapter.getData().get(currentLongClickPosition).getTemplateId();
                        deleteCallBack = addHttpPostRequest(HttpUrlManager.DELETE_COMMON_PRESCRIPTION, RequestParams.getDeleteCommonPrescription(templateId)
                                , ResponseResult.class, SearchPrescriptionActivity.this);
                    }

                }
            });
            confirmDialog.show();
            return true;
        }
        return false;
    }
    @Override
    public void onClick(View v) {

    }



    @Override
    public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {

    }

    @Override
    public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {

    }

    @Override
    public void onReload() {
        requestData(1);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId){
            case HttpUrlManager.COMMON_PRESCRIPTION:
                if(result.isRequestSuccessed()){
                    CommonPrescriptionBean commonPrescriptionBean =(CommonPrescriptionBean)result.getBodyObject();
                    if(commonPrescriptionBean!=null){
                        pageCount = commonPrescriptionBean.getTotalPageSize();
                        onRequestListSuccess(taskId,commonPrescriptionBean.getDatalist());
                    }
                }else{
                    onRequestListError();
                    RequestErrorToast.showError(this,taskId,result.getCode(),result.getErrorMsg());
                }
                break;
            case HttpUrlManager.DELETE_COMMON_PRESCRIPTION://删除常用方
                if (result != null && result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "常用方删除成功");
                    if (confirmDialog != null) {
                        confirmDialog.dismiss();
                    }
                    //刷新数据
                    requestData(1);
                    currentLongClickPosition = -1;//重置position
                }else{
                    RequestErrorToast.showError(this,taskId,result.getCode(),result.getErrorMsg());
                }
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (confirmDialog != null) {
            confirmDialog.cancel();
            confirmDialog = null;
        }
        cancelRequest(deleteCallBack);
        cancelRequest(prescriptionCallBack);
        super.onDestroy();
    }
}
