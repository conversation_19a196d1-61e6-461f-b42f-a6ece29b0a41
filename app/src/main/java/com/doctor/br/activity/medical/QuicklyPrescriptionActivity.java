package com.doctor.br.activity.medical;


import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;

import androidx.appcompat.widget.SwitchCompat;
import androidx.fragment.app.Fragment;

import android.text.Editable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.util.Pair;
import android.view.Gravity;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.doctor.br.activity.BaiTaiActivity;
import com.doctor.br.bean.AuthState;
import com.doctor.br.bean.AuthStateResult;
import com.doctor.br.bean.DataCacheType;
import com.doctor.br.bean.PatientMsgBean;
import com.doctor.br.bean.PopItem;
import com.doctor.br.bean.ServiceSettingBean;
import com.doctor.br.bean.event.FormAndOrderMsgBean;
import com.doctor.br.bean.medical.DosageFormBean;
import com.doctor.br.bean.medical.OrderMsgBean;
import com.doctor.br.bean.medical.TakingTimeBean;
import com.doctor.br.db.entity.DataCache;
import com.doctor.br.db.entity.DrugInfo;
import com.doctor.br.db.utils.DataCacheDaoUtil;
import com.doctor.br.fragment.chatmain.MedicationFragment;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.AuthCheckHelper;
import com.doctor.br.utils.DecimalUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.view.BottomPopWindow;
import com.doctor.br.view.DosageFormsPop;
import com.doctor.br.view.FlowLayout;
import com.doctor.br.view.NumEditText;
import com.doctor.br.view.TabooTipsPopWindow;
import com.doctor.br.view.WordsNumEditText;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.fragment.BaseFragment;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.listener.OnButtonClickListener;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.AlertDialog;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.InputDialog;
import org.newapp.ones.base.widgets.NoEmojiEditText;
import org.newapp.ones.base.network.RequestCallBack;

import com.doctor.br.bean.medical.MedicineDetailMsgBean;

import org.newapp.ones.base.base.PublicParams;

import com.doctor.br.utils.EventBusUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.io.Serializable;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.OnClick;

import com.doctor.br.bean.medical.OrderMsgBean;

import android.widget.CompoundButton;

import com.doctor.br.utils.DecimalUtils;

import org.newapp.ones.base.utils.DensityUtils;
import org.w3c.dom.Text;

import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;

import com.doctor.br.bean.medical.TabooTipsBean;
import com.doctor.br.bean.medical.TakingTimeBean;

import java.util.regex.PatternSyntaxException;

public class QuicklyPrescriptionActivity extends ActionBarActivity implements RadioGroup.OnCheckedChangeListener {

    public static void launchPreDialog(Fragment context, String prescriptionType) {

        String text = "";
        if ("weixin".equals(prescriptionType)) {
            text = "微信开方提示";
        } else if ("sms".equals(prescriptionType)) {
            text = "短信开方提示";
        }

        final AlertDialog dialog = AlertDialog.getInstance(context.getContext());
        dialog.setDialogTitle(text)
                .setDialogContent("该功能仅适用于一次性诊断或年老患者。患者未扫码关注医生，不享有在线交流、管理医案、调用历史药方、自动提醒支付、管理患者等丰富功能。长期复诊患者建议【微信扫码开方】")
                .setPositiveText("知道了")
                .setOnPositiveBtnClickedListener(view -> {
                    // 获取当前活动上下文
                    Activity activity = context.getActivity();
                    if (activity == null) return;

                    // 获取用户ID
                    String userId = SharedPreferenceUtils.getString(activity, PublicParams.USER_ID, "");

                    // 从 SharedPreferences 读取用户邀请状态，使用带有用户ID的方法
                    boolean isInviteUser = SharedPreferenceUtils.getIsInviteUser(activity, userId);

                    if (isInviteUser) {
                        // 邀请用户直接打开页面
                        Intent intent = new Intent(activity, QuicklyPrescriptionActivity.class);
                        intent.putExtra("prescriptionType", prescriptionType);
                        context.startActivity(intent);
                    } else {
                        // 非邀请用户，使用AuthCheckHelper获取最新的认证状态
                        if (context instanceof BaseFragment) {
                            BaseFragment baseFragment = (BaseFragment) context;
                            // 先关闭对话框，避免多个弹窗同时显示
                            dialog.dismiss();
                            // 使用最新的认证状态进行权限判断
                            AuthCheckHelper.checkAuthFeatureWithLatestState(baseFragment, new AuthCheckHelper.AuthCheckCallback() {
                                @Override
                                public void onAuthCheckResult(boolean canUse, AuthStateResult authStateResult) {
                                    // 如果有权限使用，打开页面
                                    if (canUse) {
                                        Intent intent = new Intent(activity, QuicklyPrescriptionActivity.class);
                                        intent.putExtra("prescriptionType", prescriptionType);
                                        context.startActivity(intent);
                                    }

                                    // 如果是从ManageFragment进入的，更新其authStateResult
                                    if (context instanceof com.doctor.br.fragment.main.ManageFragment) {
                                        com.doctor.br.fragment.main.ManageFragment manageFragment = (com.doctor.br.fragment.main.ManageFragment) context;
                                        try {
                                            java.lang.reflect.Field field = manageFragment.getClass().getDeclaredField("authStateResult");
                                            field.setAccessible(true);
                                            field.set(manageFragment, authStateResult);
                                        } catch (Exception e) {
                                            Log.e("QuicklyPrescriptionActivity", "无法更新ManageFragment中的authStateResult: " + e.getMessage());
                                        }
                                    } else if (context instanceof com.doctor.br.fragment.main.ContactsFragment2) {
                                        com.doctor.br.fragment.main.ContactsFragment2 contactsFragment = (com.doctor.br.fragment.main.ContactsFragment2) context;
                                        try {
                                            java.lang.reflect.Field field = contactsFragment.getClass().getDeclaredField("authStateResult");
                                            field.setAccessible(true);
                                            field.set(contactsFragment, authStateResult);
                                        } catch (Exception e) {
                                            Log.e("QuicklyPrescriptionActivity", "无法更新ContactsFragment2中的authStateResult: " + e.getMessage());
                                        }
                                    }
                                }
                            });
                        }
                    }

                    dialog.dismiss();
                });
        dialog.show();
    }


    public static void launch(Fragment context, String prescriptionType) {
        Intent intent = new Intent(context.getContext(), QuicklyPrescriptionActivity.class);
        intent.putExtra("prescriptionType", prescriptionType);
        context.startActivity(intent);
    }

    @BindView(R.id.age_et)
    NumEditText ageEt;
    @BindView(R.id.name_et)
    NoEmojiEditText nameEt;
    @BindView(R.id.male_rb)
    RadioButton maleRb;
    @BindView(R.id.female_rb)
    RadioButton femaleRb;
    @BindView(R.id.rg_sex)
    RadioGroup rgSex;
    @BindView(R.id.tv_pregnant)
    TextView tvPregnant;
    @BindView(R.id.pregnant_rb)
    RadioButton pregnantRb;
    @BindView(R.id.no_pregnant_rb)
    RadioButton noPregnantRb;
    @BindView(R.id.pregnant_rg)
    RadioGroup pregnantRg;
    @BindView(R.id.diagnose_et)
    WordsNumEditText diagnoseEt;
    @BindView(R.id.clear_tv)
    TextView clearTv;
    @BindView(R.id.add_medicines_ll)
    LinearLayout addMedicinesLl;
    @BindView(R.id.medicine_form_supplier)
    TextView medicineFormSupplier;
    @BindView(R.id.medicine_container)
    FlowLayout medicineContainer;
    @BindView(R.id.edit_tv)
    TextView editTv;
    @BindView(R.id.medicine_mess_ll)
    LinearLayout medicineMessLl;
    @BindView(R.id.ll_edit)
    LinearLayout mEditTv;
    @BindView(R.id.total_dose_et)
    NumEditText totalDoseEt;
    @BindView(R.id.day_dose_et)
    NumEditText dayDoseEt;
    @BindView(R.id.pre_dose_time_et)
    NumEditText preDoseTimeEt;
    @BindView(R.id.special_total_dose_tv)
    TextView specialTotalDoseTv;
    @BindView(R.id.special_make_description_tv)
    TextView specialMakeDescriptionTv;
    @BindView(R.id.preday_dose_et)
    NumEditText predayDoseEt;
    @BindView(R.id.pretime_dose_et)
    NumEditText pretimeDoseEt;
    @BindView(R.id.capsule_num_tv)
    TextView capsuleNumTv;
    @BindView(R.id.take_day_tv)
    TextView takeDayTv;
    @BindView(R.id.special_usage_and_dosage_ll)
    LinearLayout specialUsageAndDosageLl;
    @BindView(R.id.tv_taking)
    TextView tvTaking;
    @BindView(R.id.select_taking_time_img)
    ImageView selectTakingTimeImg;
    @BindView(R.id.taking_time_tv)
    TextView takingTimeTv;
    @BindView(R.id.switch_btn)
    SwitchCompat switchBtn;
    @BindView(R.id.day_after)
    TextView dayAfter;
    @BindView(R.id.after_day_et)
    NumEditText afterDayEt;
    @BindView(R.id.daijian_switch_btn)
    SwitchCompat daiJianSwitch;
    @BindView(R.id.daijian_layout)
    RelativeLayout daiJianRl;
    @BindView(R.id.daijian_preference_layout)
    RelativeLayout daiJianPianhaoRl;
    @BindView(R.id.daijian_line)
    View daiJianLine;
    @BindView(R.id.daijian_preference_line)
    View daijianPianhaoLine;
    @BindView(R.id.daijian_select_tv)
    TextView daiJianPianhaoSelectTv;
    @BindView(R.id.taboo_tv)
    TextView tabooTv;
    @BindView(R.id.select_taboo)
    TextView selectTaboo;
    @BindView(R.id.direction_tv)
    TextView directionTv;
    @BindView(R.id.direction_et)
    NoEmojiEditText directionEt;
    @BindView(R.id.template_cb)
    CheckBox templateCb;
    @BindView(R.id.privary_cb)
    CheckBox privaryCb;

    @BindView(R.id.privary_dose_cb)
    CheckBox privaryDoseCb;

    @BindView(R.id.template_name_et)
    NoEmojiEditText templateNameEt;
    @BindView(R.id.note_et)
    WordsNumEditText noteEt;
    @BindView(R.id.price_detail_tv)
    TextView priceDetailTv;
    @BindView(R.id.show_price_detail_iv)
    ImageView showPriceDetailIv;
    @BindView(R.id.hide_price_detail_iv)
    ImageView hidePriceDetailIv;
    @BindView(R.id.medicine_totalprice_tv)
    TextView medicineTotalPriceTv;
    @BindView(R.id.base_medicine_price_tv)
    TextView baseMedicinePriceTv;
    @BindView(R.id.doctor_service_price_et)
    EditText doctorServicePriceEt;
    @BindView(R.id.make_medicine_price_tv)
    TextView makeMedicinePriceTv;
    @BindView(R.id.make_medicine_price_ll)
    LinearLayout makeMedicinePriceLl;
    @BindView(R.id.price_layout)
    LinearLayout medicinePriceLayout;
    @BindView(R.id.additional_charge_tv)
    TextView additionalChargeTv;
    @BindView(R.id.additional_charge_et)
    EditText additionalChargeEt;
    @BindView(R.id.total_price_tv)
    TextView totalPriceTv;
    @BindView(R.id.older_totalprice_tv)
    TextView olderTotalpriceTv;
    @BindView(R.id.sure_diavisiblesed_cb)
    CheckBox sureDiavisiblesedCb;
    @BindView(R.id.generate_basis_btn)
    Button generateBasisBtn;
    @BindView(R.id.scrollView)
    ScrollView scrollView;
    @BindView(R.id.layout_root)
    RelativeLayout mLayoutRoot;
    @BindView(R.id.dotted_line)
    ImageView mDottedLine;
    @BindView(R.id.normal_usage_and_dosage_ll)
    LinearLayout normalUsageAndDosageLl;
    @BindView(R.id.iv_mark)
    ImageView ivMark;
    @BindView(R.id.base_medicine_info_icon)
    ImageView baseMedicineInfoIcon;
    @BindView(R.id.additional_charge_info_icon)
    ImageView additionalChargeInfoIcon;
    @BindView(R.id.iv_sum_mark)
    ImageView ivSumMark;
    @BindView(R.id.btn_internal_use)
    Button btnInternalUse;
    @BindView(R.id.btn_external_use)
    Button btnExternalUse;
    @BindView(R.id.usage_method_layout)
    RelativeLayout usageMethodLayout;


    private String mPrescriptionType;//开方类型 'weixin'  或者 'sms'

    private RequestCallBack getMedicalFeeCallBack;
    private RequestCallBack getDosageFormListCallBack;
    private RequestCallBack getTabooTipsCallBack;
    private RequestCallBack addCustomTakingTimeCallBack;
    private RequestCallBack getTakingTimeCallBack;
    private RequestCallBack saveCommonTemplateCallBack;//新增常用方

    private List<DosageFormBean.FormList> mDosageFormList;
    private boolean showDosageFormPop;
    private DosageFormsPop mDosageFormsPop;
    private DosageFormBean.FormList.FormDetailMsg mFormDetailMsg;
    private DosageFormBean.FormList mFormListBean;
    private String sexStr;
    private String isPregnant;
    private ConfirmDialog mConfirmDialog;
    private List<MedicineDetailMsgBean> mMedicineList;
    private OrderMsgBean medicationParams;
    private String mDrugForm;
    private String medicineFee;
    private int leftP;
    private String rightP;
    private BottomPopWindow mBottomPopWindow;
    private String medicalFeeRemark = "";
    //当刚进入界面时用药时间列表数据获取失败，点击"选择用药时间"时就先请求数据，数据请求成功后显示弹框，默认请求成功不显示弹框
    private boolean showTimePop;
    private List<PopItem> timePopItems;
    private List<PopItem> daijianPianhaoPopItems;
    //当前代煎偏好选择索引
    private int daiJianPianhaoIndex = 0;
    private InputDialog mInputDialog;
    private TabooTipsPopWindow mTabooTipsPopWindow;
    private List<TabooTipsBean.TipBean> tipsItems;
    //当刚进入界面时服药禁忌列表数据获取失败，点击"选择服药禁忌"时就先请求数据，数据请求成功后显示弹框，默认请求成功不显示弹框
    private boolean showTipsPop;
    private String[] cacheTaboos;
    private int clickCount = 0;
    private boolean mIsAutoSend;//记录是否自动发送复诊单，用于退出时判断是否进行用药信息缓存
    private DataCacheDaoUtil dataCacheUtil;
    private String userId;
    private ArrayList<PatientMsgBean.PatientsBean> takerList;

    //输入胶囊药丸数量的弹窗
    private com.doctor.br.view.InputDialog capsuleNumInputDialog;

    //创建map用来保存对应的剂型的predayDoseEt输入的内容，下次切换的时候用来显示之前输入的内容
    private Map<String, String> predayDoseEtMap = new HashMap<String, String>();

    //创建map用来保存对应的剂型的pretimeDoseEt输入的内容，下次切换的时候用来显示之前输入的内容
    private Map<String, String> pretimeDoseEtMap = new HashMap<>();

    final String preDayDoseKey = "preDayDoseEt_";
    final String preTimeDoseKey = "preTimeDoseEt_";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //获取传入的开方类型
        mPrescriptionType = getIntent().getStringExtra("prescriptionType");
        setActionBarContentView(R.layout.activity_quickly_prescription);
        if(mPrescriptionType.equals("weixin")){
            setActionBarTitle("微信开方");
        }else if(mPrescriptionType.equals("sms")){
            setActionBarTitle("短信开方");
        }else {
            setActionBarTitle("快速开方");
        }
        setActionBarRightBtnImg(R.drawable.icon_help);
        EventBusUtils.register(this);
        takerList = getIntent().getParcelableArrayListExtra("takerList");
        if (takerList == null) {
            takerList = new ArrayList<>();
        }
        init();

    }

    @Override
    public void onRightBtnClick(View view) {
        String text = "提示";
        if (mPrescriptionType.equals("weixin")) {
            text = "微信开方提示";
        } else if (mPrescriptionType.equals("sms")) {
            text = "短信开方提示";
        }

        super.onRightBtnClick(view);
        final AlertDialog dialog = AlertDialog.getInstance(this);
        dialog.setDialogTitle(text)
                .setDialogContent("该功能仅适用于一次性诊断或年老患者。患者未扫码关注医生，不享有在线交流、管理医案、调用历史药方、自动提醒支付、管理患者等丰富功能。长期复诊患者建议【微信扫码开方】")
                .setPositiveText("知道了")
                .setOnPositiveBtnClickedListener(v -> {
                    dialog.dismiss();
                });
        dialog.show();
    }

    /**
     * 监听预计服用天数问号按钮
     */
    private void setDayAfterOnClickListener() {
        if (ivMark == null) {
            return;
        }
        ivMark.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final AlertDialog dialog = AlertDialog.getInstance(mContext);
                dialog.setDialogTitle("预计服用提示")
                        .setDialogContent("服用天数只是预估，最终以药房实际制作出药量为准。")
                        .setPositiveText("知道了")
                        .setOnPositiveBtnClickedListener(view -> {
                            dialog.dismiss();
                        });
                dialog.show();
            }
        });
    }

    /**
     * 监听基础药费提示按钮
     */
    private void setBaseFeeOnClickListener() {
        if (baseMedicineInfoIcon == null) {
            return;
        }
        baseMedicineInfoIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final AlertDialog dialog = AlertDialog.getInstance(mContext);
                dialog.setDialogTitle("基础药费提示")
                        .setDialogContent("患者支付订单后，请到“管理-->我的钱包”查看【医技服务费/诊后服务费】及【诊费】。")
                        .setPositiveText("知道了")
                        .setOnPositiveBtnClickedListener(view -> {
                            dialog.dismiss();
                        });
                dialog.show();
            }
        });
    }

    /**
     * 总计点击监听
     */
    private void setSumTipsOnClickListener() {
        if (ivSumMark == null) {
            return;
        }
        ivSumMark.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final AlertDialog dialog = AlertDialog.getInstance(mContext);
                dialog.setDialogTitle("提示信息")
                        .setDialogContent("1、患者支付订单时只显示总计费用，不显示诊费等明细。\n2、付款后24小时内顺丰快递发货(节假日、膏/丸/散/胶囊剂型除外)。\n3、总计费用(不含制作费、诊费)满100元包邮，不满100元收取15元快递费。\n4、由于处方调剂后无法再销售，非质量问题不办理退货退款，敬请谅解！")
                        .setContentGravity(Gravity.LEFT)
                        .setPositiveText("知道了")
                        .setOnPositiveBtnClickedListener(view -> {
                            dialog.dismiss();
                        });
                dialog.show();
            }
        });
    }

    /**
     * 监听附加诊费提示
     */
    private void setAppendFeeOnClickListener() {
        if (additionalChargeInfoIcon == null) {
            return;
        }
        additionalChargeInfoIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final AlertDialog dialog = AlertDialog.getInstance(mContext);
                dialog.setDialogTitle("附加诊费提示")
                        .setDialogContent("患者端不会单独显示诊费，只显示总计金额。")
                        .setPositiveText("知道了")
                        .setOnPositiveBtnClickedListener(view -> {
                            dialog.dismiss();
                        });
                dialog.show();
            }
        });
    }

    /**
     * 设置相关EditText的输入监听事件
     */
    private void setEditWatchListener() {
        //普通剂型的药费和发送问诊单时间随着"药方付数"的改变及时刷新
        addTotalDoseEtTextChangedListener();
        //普通剂型的发送问诊单时间随着"每日几剂"的改变及时刷新
        addDayDoseEtTextChangedListener();
        // 设置服用天数、特殊剂型发送问诊单时间随着"每天多少次"的改变及时刷新
        addPreDayDoseEtTextChangedListener();
        // 设置服用天数、特殊剂型发送问诊单时间随着"每次多少g"的改变及时刷新
        addPreTimeDoseEtTextChangedListener();
        //设置总计随着诊费的改变及时刷新
        addAddtionalChargeEtTextChangedListener();
        //录入姓名后查询缓存信息并显示
        addNameEtTextChangedListener();
    }

    private void addNameEtTextChangedListener() {
        nameEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (!TextUtils.isEmpty(s) && s.length() >= 2) {
                    //获取缓存的用药信息
                    DataCache cachePresMsg = DataCacheDaoUtil.getInstance()
                            .select(DataCacheType.CACHE_QUICK_PRESCRIPTION_MSG, userId + "_" + s.toString());
                    if (cachePresMsg != null) {
                        showCacheDialog(cachePresMsg);
                    }
                }

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    /**
     * 显示重新用药/继续编辑的弹框
     */
    private void showCacheDialog(DataCache cachePresMsg) {
        if (mConfirmDialog == null) {
            mConfirmDialog = ConfirmDialog.getInstance(mContext);
        }
        mConfirmDialog.setDialogContent(nameEt.getText() + "上次用药方案未完成，是否继续?")
                .setPositiveText("继续编辑")
                .setNavigationText("重新用药");
        mConfirmDialog.setCancelable(false);
        mConfirmDialog.setCanceledOnTouchOutside(false);
        mConfirmDialog.show();
        mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
            @Override
            public void onNavigationBtnClicked(View view) {
                DataCacheDaoUtil.getInstance().clearCache(DataCacheType.CACHE_QUICK_PRESCRIPTION_MSG
                        , userId + "_" + nameEt.getText().toString().trim());
                mConfirmDialog.dismiss();
            }

            @Override
            public void onPositiveBtnClicked(View view) {
                medicationParams = JSON.parseObject(cachePresMsg.getValue(), OrderMsgBean.class);
                //显示缓存的信息（不包括药材信息）
                showCachePresMsg();

                if (medicationParams.getPreDetailList() != null
                        && medicationParams.getPreDetailList().size() > 0) {
                    startAddMedicineActivity(medicationParams.getFormDetailMsg()
                            , medicationParams.getFormList(), medicationParams.getTakerIsPregnant()
                            , medicationParams.getLeftP(), medicationParams.getRightP()
                            , medicationParams.getPreDetailList(), true);
                    DataCacheDaoUtil.getInstance().clearCache(DataCacheType.CACHE_QUICK_PRESCRIPTION_MSG
                            , userId + "_" + nameEt.getText().toString().trim());
                }
                mConfirmDialog.dismiss();
            }
        });
    }

    /**
     * 代煎偏好popItems初始化
     */
    private void initDaiJianPopItem(){
        //初始化一个新数组
        String[] pianhaoArray = {"浓煎50~80ML", "浓煎100ML", "普通煎150ML", "普通煎200ML"};
        for (String text : pianhaoArray) {
            PopItem popItem = new PopItem();
            popItem.setName(text);
            daijianPianhaoPopItems.add(popItem);
        }
    }

    /**
     * 代煎显示的默认数据设置
     */
    private void showDaiJianDataOnView() {
        daiJianPianhaoSelectTv.setText(daijianPianhaoPopItems.get(daiJianPianhaoIndex).getName());
    }


    /**
     * 显示缓存的信息
     */
    private void showCachePresMsg() {
        // TODO: 2022/6/11
        ageEt.setText(TextUtils.isEmpty(medicationParams.getTakerAge()) ? "" : medicationParams.getTakerAge());
        if (!TextUtils.isEmpty(medicationParams.getTakerSex())) {
            if ("1".equals(medicationParams.getTakerSex())) {
                maleRb.setChecked(true);
            } else if ("0".equals(medicationParams.getTakerSex())) {
                femaleRb.setChecked(true);
            }
        }
        if (!TextUtils.isEmpty(medicationParams.getTakerIsPregnant())) {
            if ("1".equals(medicationParams.getTakerIsPregnant())) {
                pregnantRb.setChecked(true);
            } else if ("0".equals(medicationParams.getTakerIsPregnant())) {
                noPregnantRb.setChecked(true);
            }
        }
        //诊断信息
        if (!TextUtils.isEmpty(medicationParams.getDescription())) {
            diagnoseEt.setText(medicationParams.getDescription());
            diagnoseEt.setSelection(medicationParams.getDescription().length());
        }
        //服药禁忌
        if (!TextUtils.isEmpty(medicationParams.getContraindication())) {
            String tabooStr = medicationParams.getContraindication();
            cacheTaboos = tabooStr.split("，");
            selectTaboo.setText(medicationParams.getContraindication());
            for (int j = 0; j < tipsItems.size(); j++) {
                for (int i = 0; i < cacheTaboos.length; i++) {
                    if (tipsItems.get(j).getName().equalsIgnoreCase(cacheTaboos[i])) {
                        tipsItems.get(j).setChecked(true);
                        break;
                    } else {
                        tipsItems.get(j).setChecked(false);
                    }
                }
            }
        }
        //缓存中用法用量的显示
        mDrugForm = medicationParams.getDrugForm();
        showUsageAndDosageData(true);
        takingTimeTv.setText(medicationParams.getUseTime());
        if (TextUtils.isEmpty(medicationParams.getSendAfterDay())) {
            afterDayEt.setText("7");
        } else {
            afterDayEt.setText(medicationParams.getSendAfterDay());
        }
        //补充说明
        if (!TextUtils.isEmpty(medicationParams.getInstructions())) {
            directionEt.setText(medicationParams.getInstructions());
        }
        //保存为常用方
        if ("1".equals(medicationParams.getIsSaveTemplate())) {
            templateCb.setChecked(true);
            if (!TextUtils.isEmpty(medicationParams.getTemplateName())) {
                templateNameEt.setText(medicationParams.getTemplateName());
                templateNameEt.setSelection(medicationParams.getTemplateName().length());
            }
        }
        //药方是否向患者保密
        if ("1".equals(medicationParams.getIsSecrecy())) {
            privaryCb.setChecked(true);
        }
        if ("2".equals(medicationParams.getIsSecrecy())) {
            privaryDoseCb.setChecked(true);
        }
        //按语
        if (!TextUtils.isEmpty(medicationParams.getNote())) {
            noteEt.setText(medicationParams.getNote());
            noteEt.setSelection(medicationParams.getNote().length());
        }
        //诊费
        additionalChargeEt.setText(TextUtils.isEmpty(medicationParams.getConsultationfee())
                ? "0" : medicationParams.getConsultationfee());
        medicalFeeRemark = additionalChargeEt.getText().toString().trim();
        //线下诊断
        if (medicationParams.isDiagnosed()) {
            sureDiavisiblesedCb.setChecked(true);
        }
        //是否自动发送复诊单
        if (!TextUtils.isEmpty(medicationParams.getIsAutoSend())) {
            if ("1".equals(medicationParams.getIsAutoSend())) {
                switchBtn.setChecked(true);
                mIsAutoSend = true;
            } else {
                switchBtn.setChecked(false);
                mIsAutoSend = false;
            }
        }
        daiJianSwitch.setChecked(medicationParams.isUseDaiJian());
    }

    /**
     * 设置服用天数、特殊剂型发送问诊单时间随着"每次多少g"的改变及时刷新
     */
    private void addPreTimeDoseEtTextChangedListener() {
        pretimeDoseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                // 计算预计服用天数
                takeDayTv.setText(countTakeDays());
                //发送问诊单
                afterDayEt.setText(countSendWzdDays(true));

                //保存pretimeDoseEt输入的到pretimeDoseEtMap
                savePreTimeDoseEt(s.toString(), mDrugForm);
            }
        });
    }

    /**
     * 设置服用天数、特殊剂型发送问诊单时间随着"每天多少次"的改变及时刷新
     */
    private void addPreDayDoseEtTextChangedListener() {
        predayDoseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                // 计算预计服用天数
                takeDayTv.setText(countTakeDays());
                //发送问诊单时间
                afterDayEt.setText(countSendWzdDays(true));

                //保存preDayDose
                savePreDayDoseEt(s.toString(), mDrugForm);
            }
        });
    }


    //根据剂型，保存predayDoseEt输入的到predayDoseEtMap的方法,对应可以的前缀为 preDayDoseEt_ + 剂型
    private void savePreDayDoseEt(String et, String drugform) {
        predayDoseEtMap.put(preDayDoseKey + drugform, et);
    }

    private String getPreDayDoseEt(String drugform) {
        return predayDoseEtMap.get(preDayDoseKey + drugform);
    }
    //检查predayDoseEtMap是否包含某个剂型对应的值
    private boolean hasPreDayDoseEt(String drugform) {
        return predayDoseEtMap.containsKey(preDayDoseKey + drugform);
    }

    //根据剂型，保存pretimeDoseEt输入的到pretimeDoseEtMap的方法,对应可以的前缀为 preTimeDoseEt_ + 剂型
    private void savePreTimeDoseEt(String et, String drugform) {
        pretimeDoseEtMap.put(preTimeDoseKey + drugform, et);
    }

    private String getPreTimeDoseEt(String drugform) {
        return pretimeDoseEtMap.get(preTimeDoseKey + drugform);
    }
    //检查pretimeDoseEtMap是否包含某个剂型对应的值
    private boolean hasPreTimeDoseEt(String drugform) {
        return pretimeDoseEtMap.containsKey(preTimeDoseKey + drugform);
    }

    /**
     * 计算预计服用天数
     */
    private String countTakeDays() {
        String takeDays = "1";
        int takeDayInt = 1;
        //总重
        String totalWeight = "";
        if (medicationParams != null) {
            totalWeight = TextUtils.isEmpty(medicationParams.getBalanceWeight())
                    ? "0" : medicationParams.getBalanceWeight();
        }
        //每天多少次
        String predayDose = TextUtils.isEmpty(predayDoseEt.getText().toString().trim())
                ? "0" : predayDoseEt.getText().toString().trim();
        // 每次多少g
        String pretimeDose = TextUtils.isEmpty(pretimeDoseEt.getText().toString().trim())
                ? "0" : pretimeDoseEt.getText().toString().trim();
        //一天吃多少g
        String dayDose = DecimalUtils.mul(predayDose, pretimeDose);
        if (TextUtils.isEmpty(dayDose) || "0.00".equals(dayDose) || TextUtils.isEmpty(totalWeight)
                || "0".equals(totalWeight)) {
            takeDays = "1";
        } else {
            takeDays = DecimalUtils.div(totalWeight, dayDose, 0, BigDecimal.ROUND_DOWN);
        }
        takeDayInt = Integer.parseInt(takeDays);
        if (takeDayInt < 1) {
            takeDays = "1";
        }
        return takeDays;

    }

    /**
     * 普通剂型的发送问诊单时间随着"每日几剂"的改变及时刷新
     */
    private void addDayDoseEtTextChangedListener() {
        dayDoseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                //发送问诊单时间
                afterDayEt.setText(countSendWzdDays(false));
            }
        });
    }

    /**
     * 普通剂型的药费和发送问诊单时间随着"药方付数"的改变及时刷新
     */
    private void addTotalDoseEtTextChangedListener() {
        totalDoseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {//颗粒、饮片、外用中药的总计及问诊单发送时间计算
                //基础药费
                countGeneralMedicineTotalPrice(s.toString().trim());
                String num = TextUtils.isEmpty(s.toString())
                        ? "7" : s.toString();//付数
                String makeFeePre = TextUtils.isEmpty(medicationParams.getMakeCost())
                        ? "¥0.00" : "¥" + medicationParams.getMakeCost();//制作费
                //制作费,代煎费
                makeMedicinePriceTv.setText(makeFeePre + " * " + num + " = ¥" + (new BigDecimal(getMedicineMakeFee(false)).setScale(2).toString()));

                //总药费
                medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
                //总计
                olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));
                //发送问诊单时间
                afterDayEt.setText(countSendWzdDays(false));

            }
        });
    }


    /**
     * @param isSpecial 是否是特殊剂型 为true 表示是散剂、膏方、蜜丸、水丸；false 表示是颗粒、饮片、外用中药
     *                  计算发送问诊单的时间
     */
    private String countSendWzdDays(boolean isSpecial) {
        String days = "";
        int intDay = 1;
        String totalDose = TextUtils.isEmpty(totalDoseEt.getText().toString().trim())
                ? "7" : totalDoseEt.getText().toString().trim();//共几剂
        String preDayDose = TextUtils.isEmpty(dayDoseEt.getText().toString())
                ? "1" : dayDoseEt.getText().toString();//每日几剂

        if (isSpecial) {
            if (medicationParams != null) {
                String makeDays = TextUtils.isEmpty(medicationParams.getMakeDays())
                        ? "0" : medicationParams.getMakeDays();
                days = DecimalUtils.addResultIntDOWN(makeDays, countTakeDays());
            }
        } else {
            if (!TextUtils.isEmpty(totalDose) && !TextUtils.isEmpty(preDayDose) && !"0".equals(preDayDose)) {
                days = DecimalUtils.div(totalDose, preDayDose, 0, BigDecimal.ROUND_DOWN);
            } else {
                days = "0";
            }
            intDay = Integer.parseInt(days);
            if (intDay < 1) {
                days = "1";
            }

        }
        if (TextUtils.isEmpty(days)) {
            if (!TextUtils.isEmpty(totalDose)) {
                days = totalDose;
            } else {
                days = "7";
            }
        }
        return days;
    }

    /**
     * 计算颗粒、饮片、外用中药的药材费用
     *
     * @param num 付数
     */
    private void countGeneralMedicineTotalPrice(String num) {
        String price = "0";
        String t_price = "0";
        if (medicationParams != null && !TextUtils.isEmpty(medicationParams.getDrugPrice())) {
            price = DecimalUtils.format(medicationParams.getDrugPrice(), 3);
            t_price = DecimalUtils.format(countMedicinePrice(num), 2);
            baseMedicinePriceTv.setText("¥" + price
                    + " * " + num + " = ¥" + t_price);
        }
    }

    /**
     * 设置总计随着诊费的改变及时刷新
     */
    private void addAddtionalChargeEtTextChangedListener() {
        additionalChargeEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.toString().startsWith(".")) {
                    return;
                }
                //总计
                if (!TextUtils.isEmpty(mDrugForm)) {
                    if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm)
                            || PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm)
                            || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)
                            || PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
                        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));
                    } else {
                        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
                    }
                } else {//显示默认的总计
                    olderTotalpriceTv.setText("¥ " + countDefaultPrescriptionTotalPrice());
                }
            }
        });
    }

    /**
     * 获取服药时间数据
     */
    private void getTakingTimeData() {
        getTakingTimeCallBack = addHttpPostRequest(HttpUrlManager.TAKING_TIME, null
                , TakingTimeBean.class, this);
    }

    /**
     * 获取诊费
     */
    private void getMedicalFee() {
        getMedicalFeeCallBack = addHttpPostRequest(HttpUrlManager.GET_MEDICAL_FEE, null
                , ServiceSettingBean.class, this);
    }

    /**
     * 获取服药禁忌数据
     */
    private void getTabooTips() {
        getTabooTipsCallBack = addHttpPostRequest(HttpUrlManager.TABOO_TIPS, null
                , TabooTipsBean.class, this);
    }

    private void init() {
        rgSex.setOnCheckedChangeListener(this);
        pregnantRg.setOnCheckedChangeListener(this);
        mDosageFormList = new ArrayList<>();
        mMedicineList = new ArrayList<>();
        medicationParams = new OrderMsgBean();
        timePopItems = new ArrayList<>();
        daijianPianhaoPopItems = new ArrayList<>();
        tipsItems = new ArrayList<>();
        mBottomPopWindow = new BottomPopWindow(this);
        mTabooTipsPopWindow = new TabooTipsPopWindow(mContext, "选择服药禁忌", tipsItems);
        userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);

        // 移除默认的性别和怀孕状态设置
        sexStr = "";  // 性别默认为空
        isPregnant = "";  // 怀孕状态默认为空

        // 隐藏怀孕相关选项
        pregnantRg.setVisibility(View.GONE);
        tvPregnant.setVisibility(View.GONE);

        setDefaultTakerInfo();
        getDosageFormList();//获取剂型类表
        getMedicalFee();
        getTakingTimeData();
        getTabooTips();
        setDayAfterOnClickListener();//监听预计服用天数问号按钮
        setBaseFeeOnClickListener();//监听基础药费提示按钮
        setSumTipsOnClickListener();//监听总计药费提示按钮
        setAppendFeeOnClickListener();//监听附加诊费提示按钮
        setDirectionEtOnFocusChangeListener();//监听输入内容改变展示样式
        addSelectTabooTextChangedListener();//监听输入内容改变选择服药禁忌的展示样式
        setTemplateCbOnCheckedChangedListener();//监听保存模板按钮是否选中，展示不同样式
        setEditWatchListener();

        //药方保密按钮监听
        setPrivacyCbOnCheckedChangedListener();
        //药方剂量保密按钮监听
        setPrivaryDoseCbOnCheckedChangedListener();

        // 设置 都不选中
        btnInternalUse.setSelected(false);
        btnExternalUse.setSelected(false);
        setInOrOutBtnOnClickListener();

        //复诊单发送控制按钮
        controlSendFZD();
        //使用药房代煎控制按钮
        dcDaiJian();

        //默认显示 普通煎150ml
        daiJianPianhaoIndex = 2;
        //初始化代煎偏好popItem数据
        initDaiJianPopItem();
        showDaiJianDataOnView();
    }

    /**
     * 监听内服和外用按钮
     */
    private void setInOrOutBtnOnClickListener() {
        View.OnClickListener usageMethodClickListener = v -> {
            btnInternalUse.setSelected(v.getId() == R.id.btn_internal_use);
            btnExternalUse.setSelected(v.getId() == R.id.btn_external_use);
        };

        btnInternalUse.setOnClickListener(usageMethodClickListener);
        btnExternalUse.setOnClickListener(usageMethodClickListener);
    }

    private void setDefaultTakerInfo() {
        if (takerList != null && takerList.size() > 0 && takerList.get(0) != null) {
            PatientMsgBean.PatientsBean bean = takerList.get(0);
            nameEt.setText(bean.getName());
            ageEt.setText(bean.getAge());
            if ("1".equals(bean.getSex())) {
                rgSex.check(R.id.male_rb);
                RadioButton rb = (RadioButton) rgSex.findViewById(R.id.male_rb);
                if (rb != null && rb.getTag() != null) {
                    sexStr = (String) rb.getTag();
                }
                // 隐藏怀孕相关选项
                pregnantRg.setVisibility(View.GONE);
                tvPregnant.setVisibility(View.GONE);
                isPregnant = "";
            } else if ("2".equals(bean.getSex())) {
                rgSex.check(R.id.female_rb);
                RadioButton rb = (RadioButton) rgSex.findViewById(R.id.female_rb);
                if (rb != null && rb.getTag() != null) {
                    sexStr = (String) rb.getTag();
                }
                // 显示怀孕相关选项
                pregnantRg.setVisibility(View.VISIBLE);
                tvPregnant.setVisibility(View.VISIBLE);

                if ("1".equals(bean.getIsPregnant())) {
                    pregnantRg.check(R.id.pregnant_rb);
                    RadioButton rb1 = (RadioButton) pregnantRg.findViewById(R.id.pregnant_rb);
                    if (rb1 != null && rb1.getTag() != null) {
                        isPregnant = (String) rb1.getTag();
                    }
                } else {
                    pregnantRg.check(R.id.no_pregnant_rb);
                    RadioButton rb1 = (RadioButton) pregnantRg.findViewById(R.id.no_pregnant_rb);
                    if (rb1 != null && rb1.getTag() != null) {
                        isPregnant = (String) rb1.getTag();
                    }
                }
            } else {
                // 如果性别未知，不选中任何选项
                rgSex.clearCheck();
                sexStr = "";
                pregnantRg.setVisibility(View.GONE);
                tvPregnant.setVisibility(View.GONE);
                isPregnant = "";
            }
        } else {
            // 如果没有默认数据，不选中任何选项
            rgSex.clearCheck();
            sexStr = "";
            pregnantRg.setVisibility(View.GONE);
            tvPregnant.setVisibility(View.GONE);
            isPregnant = "";
        }
    }

    private void dcDaiJian() {
        daiJianSwitch.setThumbResource(R.drawable.thumb);
        daiJianSwitch.setTrackResource(R.drawable.track);
        daiJianSwitch.setChecked(false);
    }

    /**
     * 监听输入内容改变补充说明的展示样式
     */
    private void setDirectionEtOnFocusChangeListener() {
        if (directionEt == null) {
            return;
        }
        directionEt.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    directionEt.setGravity(Gravity.LEFT);
                } else {
                    if (directionEt.getText().toString().length() > 0) {
                        directionEt.setGravity(Gravity.LEFT);
                    } else {
                        directionEt.setGravity(Gravity.RIGHT);
                    }
                }
            }
        });
    }

    /**
     * 控制复诊单是否自动发送
     */
    private void controlSendFZD() {
        switchBtn.setThumbResource(R.drawable.thumb);
        switchBtn.setTrackResource(R.drawable.track);
        if (SharedPreferenceForeverUtils.getBoolean(mContext, PublicParams.IS_AUTO_SEND_FZD, false)) {
            switchBtn.setChecked(true);
            afterDayEt.setVisibility(View.VISIBLE);
            dayAfter.setVisibility(View.VISIBLE);
            mIsAutoSend = true;
        } else {
            switchBtn.setChecked(false);
            afterDayEt.setVisibility(View.GONE);
            dayAfter.setVisibility(View.GONE);
            mIsAutoSend = false;
        }

        switchBtn.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    afterDayEt.setVisibility(View.VISIBLE);
                    dayAfter.setVisibility(View.VISIBLE);
                } else {
                    afterDayEt.setVisibility(View.GONE);
                    dayAfter.setVisibility(View.GONE);
                }
            }
        });
    }

    /**
     * 监听保存模板按钮是否选中，展示不同样式
     */
    private void setTemplateCbOnCheckedChangedListener() {
        templateCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    templateNameEt.setVisibility(View.VISIBLE);
//                    mDottedLine.setVisibility(View.VISIBLE);
                    templateNameEt.requestFocus();
                } else {
                    templateNameEt.setVisibility(View.GONE);
//                    mDottedLine.setVisibility(View.GONE);
                }
            }
        });
    }

    /**
     * 监听输入内容改变选择服药禁忌的展示样式
     */
    private void addSelectTabooTextChangedListener() {
        selectTaboo.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!"选择服药禁忌".equals(selectTaboo.getText().toString())) {
                    selectTaboo.setGravity(Gravity.LEFT);
                    selectTaboo.setTextColor(getResources().getColor(R.color.br_color_theme_text));

                } else {
                    selectTaboo.setGravity(Gravity.RIGHT);
                    selectTaboo.setTextColor(getResources().getColor(R.color.br_color_et_hint));
                }

            }
        });
    }

    /**
     * 监听药方向患者保密 privaryCb
     */
    private void setPrivacyCbOnCheckedChangedListener() {
        if (privaryCb == null) {
            return;
        }
        privaryCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    privaryDoseCb.setChecked(false);
                }
            }
        });
    }

    /**
     * 监听药方剂量保密 privaryDoseCb
     */
    private void setPrivaryDoseCbOnCheckedChangedListener() {
        if (privaryDoseCb == null) {
            return;
        }
        privaryDoseCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    privaryCb.setChecked(false);
                }
            }
        });
    }

    @OnClick({R.id.clear_tv, R.id.add_medicines_ll, R.id.ll_edit, R.id.select_taking_time_img, R.id.select_daijian_preference_img, R.id.select_taboo, R.id.show_price_detail_iv, R.id.price_detail_tv, R.id.generate_basis_btn})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.clear_tv:
                clearMedicines();
                break;
            case R.id.add_medicines_ll:
                showDosageFormsPop();
                break;
            case R.id.ll_edit:
                startAddMedicineActivity(mFormDetailMsg, mFormListBean, isPregnant, leftP, rightP
                        , mMedicineList, false);
                break;
            case R.id.select_taking_time_img:
                showTakingTimePop();
                break;
            case R.id.select_daijian_preference_img:
                showDaijianPreferencePop();
                break;
            case R.id.select_taboo:
                showTabooTipsPop();
                break;
            //展开收起药费明细
            case R.id.show_price_detail_iv:
            case R.id.price_detail_tv:
                if (clickCount % 2 == 0) {
                    medicinePriceLayout.setVisibility(View.VISIBLE);
                    priceDetailTv.setText("收起");
                    showPriceDetailIv.setImageResource(R.drawable.blue_up_arrows);
                } else {
                    medicinePriceLayout.setVisibility(View.GONE);
                    priceDetailTv.setText("明细");
                    showPriceDetailIv.setImageResource(R.drawable.blue_down_arrows);
                }
                clickCount++;
                break;
            //生成医案
            case R.id.generate_basis_btn:
                doMakeMedicationCheck();
                break;
            default:
                break;
        }
    }

    private boolean checkTakerMsg() {
        String name = nameEt.getText().toString().trim();
        String age = ageEt.getText().toString().trim();
        if (TextUtils.isEmpty(name)) {
            ToastUtils.showShortMsg(this, "请输入患者的真实姓名");
            return false;
        }
        if (!isChineseName(name)) {
            String text = "根据《处方管理法》规定，开方时需要填写真实姓名，请跟患者确认真实姓名。";
            final AlertDialog dialog = AlertDialog.getInstance(this);
            dialog.setDialogTitle("")
                    .setDialogContent(text)
                    .setPositiveText("确定")
                    .setOnPositiveBtnClickedListener(view -> {
                        dialog.dismiss();
                    });
            dialog.show();

            return false;
        }
        if (TextUtils.isEmpty(age)) {
            ToastUtils.showShortMsg(this, "请输入患者的真实年龄");
            return false;
        }
        if (TextUtils.isEmpty(sexStr)) {
            ToastUtils.showShortMsg(this, "请选择患者的性别");
            return false;
        }
        if (!"1".equals(sexStr) && TextUtils.isEmpty(isPregnant)) {
            ToastUtils.showShortMsg(this, "请选择该患者是否怀孕");
            return false;
        }
        return true;
    }

    /**
     * 判断是否为合法的患者姓名
     */

    public static boolean isChineseName(String string) {
        if (string.length() <= 1) {
            return false;
        }
        String pattern = "^[\\u4e00-\\u9fa5]{1,8}(·[\\u4e00-\\u9fa5]{1,8})?$";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(string);
        return matcher.matches();
    }

    /**
     * 处理生成医案前的校验
     */
    private void doMakeMedicationCheck() {
        boolean isUsageAndDoseEmpty = false;
        boolean isDoseTooLarge = false;
        if (!checkTakerMsg()) {
            return;
        }
        //辨证
        String description = diagnoseEt.getText().toString().trim();
        //共多少剂
        String totalDose = totalDoseEt.getText().toString().trim();
        //每日多少剂
        String dayDose = dayDoseEt.getText().toString().trim();
        //每剂分多少次服用
        String preDoseTime = preDoseTimeEt.getText().toString().trim();
        //每日几次
        String preDayDose = predayDoseEt.getText().toString().trim();
        //每次多少g
        String preTimeDose = pretimeDoseEt.getText().toString().trim();
        //预计服用到少天
        String takeDay = takeDayTv.getText().toString().trim();
        //复诊单时间
        String afterDay = afterDayEt.getText().toString().trim();
        String templateName = templateNameEt.getText().toString();

        // 添加：检查颗粒剂型和必然甄选厂商的情况
        if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm) && mFormDetailMsg != null && mFormDetailMsg.getName().contains("必然甄选")) {
            // 检查剂数是否小于7剂
            int intTotalDose = 0;
            if (!TextUtils.isEmpty(totalDose)) {
                intTotalDose = Integer.parseInt(totalDose);
            }

            if (intTotalDose < 7) {
                AlertDialog alertDialog = AlertDialog.getInstance(mContext);
                alertDialog.setDialogContent("该厂商为合煎颗粒(药液浓缩后干燥)，非配方颗粒，7剂起制作(药材总量不低于800g)")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return;
            }
        }

        // 添加：代煎剂型下，青庐药局【精品选货】厂商的总剂数最小剂数判断
        if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) && mFormDetailMsg != null && mFormDetailMsg.getName().contains("青庐药局【精品选货】")) {
            int intTotalDose = 0;
            if (!TextUtils.isEmpty(totalDose)) {
                intTotalDose = Integer.parseInt(totalDose);
            }

            if (intTotalDose < 5) {
                AlertDialog alertDialog = AlertDialog.getInstance(mContext);
                alertDialog.setDialogContent(mFormDetailMsg.getName() + "5剂起煎，已自动修改为 5 剂。")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            // 重置总剂数为5
                            totalDoseEt.setText("5");
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return;
            }
        }

        // 添加：代煎剂型下，除青庐药局【精品选货】厂商之外的所有厂商的最小剂数判断
        if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) && mFormDetailMsg != null && !mFormDetailMsg.getName().contains("青庐药局【精品选货】")) {
            int intTotalDose = 0;
            if (!TextUtils.isEmpty(totalDose)) {
                intTotalDose = Integer.parseInt(totalDose);
            }

            if (intTotalDose < 3) {
                AlertDialog alertDialog = AlertDialog.getInstance(mContext);
                alertDialog.setDialogContent(mFormDetailMsg.getName() + "3剂起煎，已自动修改为 3 剂。")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            // 重置总剂数为3
                            totalDoseEt.setText("3");
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return;
            }
        }
        
        // 添加：对于颗粒、饮片、代煎、外用中药剂型，总剂数最大值为99的限制
        if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm) || 
            PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm) ||
            PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) || 
            PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
            int intTotalDose = 0;
            if (!TextUtils.isEmpty(totalDose)) {
                intTotalDose = Integer.parseInt(totalDose);
            }
            
            if (intTotalDose > 99) {
                AlertDialog alertDialog = AlertDialog.getInstance(mContext);
                alertDialog.setDialogContent("总剂数最大为99，已自动修改为99剂")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            // 重置总剂数为99
                            totalDoseEt.setText("99");
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return;
            }
        }

        int intTotalDose = 0;
        int intDayDose = 0;
        if (!TextUtils.isEmpty(totalDose)) {
            intTotalDose = Integer.parseInt(totalDose);
        }
        if (!TextUtils.isEmpty(dayDose)) {
            intDayDose = Integer.parseInt(dayDose);
        }
//普通剂型的用法用量判断
        if (normalUsageAndDosageLl.isShown()) {
            if (TextUtils.isEmpty(totalDose) || "0".equals(totalDose) || TextUtils.isEmpty(dayDose)
                    || "0".equals(dayDose) || TextUtils.isEmpty(preDoseTime) || "0".equals(preDoseTime)) {
                isUsageAndDoseEmpty = true;
            } else {
                isUsageAndDoseEmpty = false;
            }
        } else if (specialUsageAndDosageLl.isShown()) {
            //水丸蜜丸等的特殊剂型的用用量判断
            if (TextUtils.isEmpty(preDayDose) || "0".equals(preDayDose)
                    || TextUtils.isEmpty(preTimeDose) || "0".equals(preTimeDose)
                    || TextUtils.isEmpty(takeDay) || "0".equals(takeDay)) {
                isUsageAndDoseEmpty = true;
            } else {
                isUsageAndDoseEmpty = false;
            }
        }
        if (normalUsageAndDosageLl.isShown()) {
            //普通剂型的剂量超出判断
            if (intDayDose > intTotalDose) {
                isDoseTooLarge = true;
            } else {
                isDoseTooLarge = false;
            }
        } else if (specialUsageAndDosageLl.isShown()) {
            //水丸蜜丸等的剂量超出判断

        }
        if (mMedicineList.isEmpty()) {
            ToastUtils.showShortMsg(mContext, "请添加药材");
            return;
        }
        String checkResult = medicineCheck(mDrugForm, mMedicineList);

        if (checkResult != null) {
            ConfirmDialog confirmDialog = ConfirmDialog.getInstance(mContext);
            boolean finalIsUsageAndDoseEmpty = isUsageAndDoseEmpty;
            boolean finalIsDoseTooLarge = isDoseTooLarge;
            confirmDialog.setDialogTitle("用药提示")
                    .setDialogContent(checkResult)
                    .setPositiveText("确认开药")
                    .setNavigationText("返回修改")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {

                        @Override
                        public void onNavigationBtnClicked(View view) {
                            confirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            confirmDialog.dismiss();
                            checkNext(finalIsUsageAndDoseEmpty, finalIsDoseTooLarge, description, afterDay, templateName);
                        }
                    });
            confirmDialog.show();
            return;
        }
        checkNext(isUsageAndDoseEmpty, isDoseTooLarge, description, afterDay, templateName);
    }

    private void checkNext(boolean isUsageAndDoseEmpty, boolean isDoseTooLarge, String description, String afterDay, String templateName) {
        if (TextUtils.isEmpty(description)) {
            ToastUtils.showShortMsg(mContext, "请添加辨证");
            return;
        }
        if (isUsageAndDoseEmpty) {
            ToastUtils.showShortMsg(mContext, "请添加用药用量");
            return;
        }
        if (isDoseTooLarge) {
            ToastUtils.showShortMsg(mContext, "每日剂数需小于等于总剂数");
            return;
        }

        if (usageMethodLayout.getVisibility() == View.VISIBLE) {
            if (!btnInternalUse.isSelected() && !btnExternalUse.isSelected()) {
                ToastUtils.showShortMsg(mContext, "请选择用药方法");
                return;
            }
        }

        if (switchBtn.isChecked() && (TextUtils.isEmpty(afterDay) || "0".equals(afterDay))) {
            ToastUtils.showShortMsg(mContext, "请添加复诊时间");
            return;
        }

        if (!sureDiavisiblesedCb.isChecked()) {
            ToastUtils.showShortMsg(mContext, "请确认患者已进行过线下诊断");
            return;
        }
        if (templateCb.isChecked()) {
            if (TextUtils.isEmpty(templateName)) {
                ToastUtils.showShortMsg(mContext, "请添模板名称");
            } else {
                //校验模板名称是否有重复
                checkRepeatedTemplateName(jsonToStr(templateName));
            }
        } else {
            if ("weixin".equals(mPrescriptionType)) {
                PreviewMedicationActivity.launch(mContext, mDrugForm, setMedicationParams(), PreviewMedicationActivity.FROM_QUICK_PRESCRIPTION);
            } else if ("sms".equals(mPrescriptionType)) {
                PreviewMedicationActivity.launch(mContext, mDrugForm, setMedicationParams(), PreviewMedicationActivity.FROM_SMS_PRESCRIPTION);
            }

        }
    }

    private String medicineCheck(String drugForm, List<MedicineDetailMsgBean> medicineList) {
//        if (!"饮片".equals(drugForm)) {
//            return null;
//        }
        if ("饮片".equals(drugForm)) {
            Pair<String, String> pair = null;
            for (MedicineDetailMsgBean bean : medicineList) {
                if (bean.getDrugName().contains("生姜")) {
                    pair = Pair.create("生姜", "生姜属于易霉药材，建议代煎或患者自备");
                }
            }
            if (pair != null) {
                return pair.second;
            }
        }
        return null;
    }

    /**
     * @param name
     * @return 校验药方名称是否重复的参数
     */
    public String jsonToStr(String name) {
//        String userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        String jsonStr =
                " {" +
                        " \"templateName\":\"" + name + '\"' +
                        ", \"list\":" + new ArrayList<>() +
                        ", \"userId\":\"" + userId + '\"' +
                        ", \"templateId\":\"\"" +
                        '}';
        return jsonStr;

    }

    /**
     * 检查药方名字是否有重复
     */
    private void checkRepeatedTemplateName(String data) {
        HashMap map = new HashMap();
        map.put("template", data);
        saveCommonTemplateCallBack = addHttpPostRequest(HttpUrlManager.ADD_COMMON_TEMPLATE, map, ResponseResult.class, this);

    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (group.getId()) {
            case R.id.rg_sex:
                RadioButton rb = (RadioButton) group.findViewById(checkedId);
                if (rb != null && rb.getTag() != null) {
                    sexStr = (String) rb.getTag();
                }
                if (checkedId == R.id.female_rb) {
                    pregnantRg.setVisibility(View.VISIBLE);
                    tvPregnant.setVisibility(View.VISIBLE);
                    // 如果当前没有选中任何选项，则默认选中"否"
                    int id = pregnantRg.getCheckedRadioButtonId();
                    if (id == View.NO_ID) {
                        pregnantRg.check(R.id.no_pregnant_rb);
                        RadioButton rb1 = (RadioButton) pregnantRg.findViewById(R.id.no_pregnant_rb);
                        if (rb1 != null && rb1.getTag() != null) {
                            // 第一次选择女性时，直接设置状态，不触发切换提示
                            isPregnant = (String) rb1.getTag();
                        }
                    } else {
                        RadioButton rb1 = (RadioButton) pregnantRg.findViewById(id);
                        if (rb1 != null && rb1.getTag() != null) {
                            checkIsClear((String) rb1.getTag());
                        }
                    }
                } else {
                    pregnantRg.setVisibility(View.GONE);
                    tvPregnant.setVisibility(View.GONE);
                    // 选择男性时，直接清空怀孕状态，不触发切换提示
                    isPregnant = "";
                }
                break;
            case R.id.pregnant_rg:
                RadioButton rb1 = (RadioButton) group.findViewById(checkedId);
                if (rb1 != null && rb1.getTag() != null) {
                    checkIsClear((String) rb1.getTag());
                }
                break;
        }
    }

    private void checkIsClear(String tag) {
        // 只有在已经设置过怀孕状态，并且新的状态与之前不同时才提示
        if (mMedicineList.size() > 0 && !TextUtils.isEmpty(isPregnant) && !isPregnant.equals(tag)) {
            //已添加了药材且所选择患者和显示的患者是的怀孕状态不一，给出提示
            if (mConfirmDialog == null) {
                mConfirmDialog = ConfirmDialog.getInstance(mContext);
            }
            mConfirmDialog.setDialogContent("切换怀孕状态，已填用药信息将被清空")
                    .setPositiveText("确认")
                    .setNavigationText("取消")
                    .show();
            mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    mConfirmDialog.dismiss();
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    clearData();
                    mConfirmDialog.dismiss();
                }
            });
        }
        isPregnant = tag;
    }

    /**
     * 切换服药者时清空所填的信息
     */
    private void clearData() {
        try {
            setDefaultViewAndData();
            diagnoseEt.setText("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        switch (taskId) {
            case HttpUrlManager.TABOO_TIPS:
                if (result != null && result.isRequestSuccessed()) {
                    TabooTipsBean tipsBean = (TabooTipsBean) result.getBodyObject();
                    if (tipsBean != null) {
                        String tipsStr = tipsBean.getData();
                        if (!TextUtils.isEmpty(tipsStr)) {
                            String[] tips = tipsStr.split(";");
                            if (tips.length > 0) {
                                tipsItems.clear();
                                for (int i = 0; i < tips.length; i++) {
                                    TabooTipsBean.TipBean tipBean = new TabooTipsBean.TipBean();
                                    tipBean.setName(tips[i]);
                                    if (cacheTaboos != null && cacheTaboos.length > 0) {
                                        for (int j = 0; j < cacheTaboos.length; j++) {
                                            if (tipBean.getName().equalsIgnoreCase(cacheTaboos[j])) {
                                                tipBean.setChecked(true);
                                                break;
                                            } else {
                                                tipBean.setChecked(false);
                                            }
                                        }
                                    }
                                    tipsItems.add(tipBean);
                                }
                                if (showTipsPop) {
                                    showTabooTipsPop();
                                }
                            }

                        }
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.DOSAGE_FORM_LIST:
                if (result != null && result.isRequestSuccessed()) {
                    DosageFormBean dosageFormBean = (DosageFormBean) result.getBodyObject();
                    if (dosageFormBean != null) {
                        mDosageFormList = (List<DosageFormBean.FormList>) dosageFormBean.getList();
                        if (mDosageFormList != null && mDosageFormList.size() > 0) {
                            if (showDosageFormPop) {
                                showDosageFormsPop();
                            }
                        }

                    }

                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            //获取诊费
            case HttpUrlManager.GET_MEDICAL_FEE:
                if (result.isRequestSuccessed()) {
                    ServiceSettingBean feeBean = (ServiceSettingBean) result.getBodyObject();
                    if (feeBean != null) {
                        medicineFee = feeBean.getChangeSetPrice();
//                        //有缓存，显示缓存中的
//                        if (medicationParams != null && !TextUtils.isEmpty(medicationParams.getConsultationfee())) {
//                            additionalChargeEt.setText(medicationParams.getConsultationfee());
//                        } else {
                        additionalChargeEt.setText(TextUtils.isEmpty(medicineFee) ? "0" : medicineFee);
//                        }
                    } else {
                        additionalChargeEt.setText("0");
                    }
                    medicalFeeRemark = additionalChargeEt.getText().toString().trim();
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.TAKING_TIME:
                if (result != null && result.isRequestSuccessed()) {
                    TakingTimeBean timeBean = (TakingTimeBean) result.getBodyObject();
                    if (timeBean != null) {
                        //初始化用药时间pop数据
                        initTakingTimeData(timeBean.getMedicationTime1(), timeBean.getMedicationTime2());
                        if (timeBean.getMedicationTime1() != null && timeBean.getMedicationTime1().size() > 0
                                || (timeBean.getMedicationTime2() != null && timeBean.getMedicationTime2().size() > 0)) {
                            if (showTimePop) {
                                showTakingTimePop();
                            }
                        }
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.ADD_TAKING_TIME:
                if (result != null && result.isRequestSuccessed()) {
                    if (mInputDialog != null && mInputDialog.isShowing()) {
                        mInputDialog.dismiss();
                        getTakingTimeData();
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.ADD_COMMON_TEMPLATE://校验常用方名称
                if (result.isRequestSuccessed()) {
                    PreviewMedicationActivity.launch(mContext, mDrugForm, setMedicationParams(), PreviewMedicationActivity.FROM_QUICK_PRESCRIPTION);
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 设置用药参数
     */
    private OrderMsgBean setMedicationParams() {
        // TODO: 2022/6/11 缓存  应该不用关心缓存信息，界面中要录入的信息有就缓存已显示的信息
//        OrderMsgBean cacheOrderMsgBean = null;
//        DataCache cachePresMsg = DataCacheDaoUtil.getInstance()
//                .select(DataCacheType.CACHE_QUICK_PRESCRIPTION_MSG, userId + "_" + nameEt.getText().toString().trim());
//        if (cachePresMsg != null) {
//            try {
//                cacheOrderMsgBean = JSON.parseObject(cachePresMsg.getValue(), OrderMsgBean.class);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
        if (medicationParams != null) {
            medicationParams.setCode(null);
            medicationParams.setErrorMsg(null);
//            if (cacheOrderMsgBean != null && !TextUtils.isEmpty(takerId) && takerId.equalsIgnoreCase(cacheOrderMsgBean.getTakerId())) {
//                //服药者姓名
//                medicationParams.setTakerName(cacheOrderMsgBean.getTakerName());
//                //服药者年龄
//                medicationParams.setTakerAge(cacheOrderMsgBean.getTakerAge());
//                //服药者id
//                medicationParams.setTakerId(cacheOrderMsgBean.getTakerId());
//                //服药者是否怀孕
//                medicationParams.setTakerIsPregnant(cacheOrderMsgBean.getTakerIsPregnant());
//                //服药者性别
//                medicationParams.setTakerSex(cacheOrderMsgBean.getTakerSex());
//            } else {
//                //服药者姓名
//                medicationParams.setTakerName(takerName);
//                //服药者年龄
//                medicationParams.setTakerAge(takerAge);
//                //服药者id
//                medicationParams.setTakerId(takerId);
//                //服药者是否怀孕
//                medicationParams.setTakerIsPregnant(isPregnant);
//                //服药者性别
//                medicationParams.setTakerSex(takerSex);
//            }
            //患者id
//            medicationParams.setPatientId(patientId);
            medicationParams.setTakerName(nameEt.getText().toString().trim());
            medicationParams.setTakerAge(ageEt.getText().toString().trim());
            medicationParams.setTakerSex(sexStr);
            medicationParams.setTakerIsPregnant(isPregnant);
            //辨证、病情描述
            medicationParams.setDescription(TextUtils.isEmpty(diagnoseEt.getText().toString().trim())
                    ? "" : diagnoseEt.getText().toString().trim());
            //用药时间"饭后一小时",
            medicationParams.setUseTime(TextUtils.isEmpty(takingTimeTv.getText().toString().trim())
                    ? "" : takingTimeTv.getText().toString().trim());
            //发送复诊单时间
            medicationParams.setSendAfterDay(TextUtils.isEmpty(afterDayEt.getText().toString())
                    ? "" : afterDayEt.getText().toString());
            //服药禁忌
            medicationParams.setContraindication("选择服药禁忌".equals(selectTaboo.getText().toString().trim())
                    ? "" : selectTaboo.getText().toString().trim());
            //补充说明
            medicationParams.setInstructions(TextUtils.isEmpty(directionEt.getText().toString().trim())
                    ? "" : directionEt.getText().toString().trim());
            //  模板名称
            if (templateCb != null && templateCb.isChecked()) {
                medicationParams.setIsSaveTemplate("1");
                medicationParams.setTemplateName(TextUtils.isEmpty(templateNameEt.getText().toString().trim())
                        ? "" : templateNameEt.getText().toString().trim());
            } else {
                medicationParams.setIsSaveTemplate("0");
                medicationParams.setTemplateName("");
            }
            //是否对患者保密
            if (privaryCb != null && privaryCb.isChecked()) {
                medicationParams.setIsSecrecy("1");
            } else if (privaryDoseCb != null && privaryDoseCb.isChecked()) {
                medicationParams.setIsSecrecy("2");
            } else {
                medicationParams.setIsSecrecy("0");
            }
            //是否自动发送问诊单
            if (switchBtn.isChecked()) {
                medicationParams.setIsAutoSend("1");
            } else {
                medicationParams.setIsAutoSend("0");
            }

            //特殊用法用量
            //总重
            medicationParams.setBalanceWeight(specialTotalDoseTv.getText().toString()
                    .replace("g", "").trim());
            //每日几次
            medicationParams.setMrjc(predayDoseEt.getText().toString());
            //每次几g
            medicationParams.setMcjk(pretimeDoseEt.getText().toString());
            //预计可用多少天
            medicationParams.setTakeDays(takeDayTv.getText().toString());
            //饮片颗粒外用中药的用法用量
            //共几剂
            medicationParams.setTotalPreNum(totalDoseEt.getText().toString());
            //每日几剂
            medicationParams.setDayPreNum(dayDoseEt.getText().toString());
            //每剂分几次服用
            medicationParams.setPreTimes(preDoseTimeEt.getText().toString());

            //服药方式 只有显示的时候才进行赋值
            if (usageMethodLayout.getVisibility() == View.VISIBLE) {
                medicationParams.setMode(btnInternalUse.isSelected() ?
                        btnInternalUse.getText().toString() :
                        btnExternalUse.getText().toString()
                );
            }
            //按语
            medicationParams.setNote(TextUtils.isEmpty(noteEt.getText().toString().trim())
                    ? "" : noteEt.getText().toString().trim());
            //  诊费
            medicationParams.setConsultationfee(TextUtils.isEmpty(additionalChargeEt.getText().toString().trim())
                    ? "0" : additionalChargeEt.getText().toString().trim());
            //总计
            medicationParams.setTotalPrice(TextUtils.isEmpty(olderTotalpriceTv.getText().toString().trim())
                    ? "0.00" : olderTotalpriceTv.getText().toString().trim());
            //药材列表
            medicationParams.setPreDetailList(mMedicineList);
            medicationParams.setLeftP(leftP);
            medicationParams.setRightP(rightP);
            medicationParams.setFormList(mFormListBean);
            medicationParams.setFormDetailMsg(mFormDetailMsg);
            if (sureDiavisiblesedCb.isChecked()) {
                medicationParams.setDiagnosed(true);
            } else {
                medicationParams.setDiagnosed(false);
            }
            //饮片是否使用代煎
            medicationParams.setUseDaiJian(daiJianSwitch.isChecked());

            //如果是代煎
            if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)) {
                //代煎偏好
                String preference = daijianPianhaoPopItems.get(daiJianPianhaoIndex).getName();
                medicationParams.setMakeMethod(preference);

                LogUtils.i(MedicationFragment.class, "数据设置==代煎偏好:" + preference);
            }else {
                //不是代煎的时候，代煎偏好传空
                medicationParams.setMakeMethod("");
            }

            //如果是胶囊
            if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm)){
                //当前选择的厂商中获取信息
                if (mFormDetailMsg != null){
                    String packageUnit = mFormDetailMsg.getPackageUnit(); //颗
                    String dosePreUnit = mFormDetailMsg.getDosePreUnit(); //0.5

                    //获取重量 单次重量
                    String singleWeight = pretimeDoseEt.getText().toString().trim();
                    //计算胶囊颗粒数
                    int capsuleNum = (int) (Float.parseFloat(singleWeight) / Float.parseFloat(dosePreUnit));

                    //设置数量和单位
                    medicationParams.setMcDose(String.valueOf(capsuleNum));
                    medicationParams.setMcDoseUnit(packageUnit);

                    LogUtils.i(MedicationFragment.class, "数据设置==胶囊颗粒数:" + capsuleNum + "  单位:" + packageUnit);
                }

                //胶囊的话，设置 preTimes 和 totalPreNum 为空
                medicationParams.setPreTimes("");
                medicationParams.setTotalPreNum("");

            }

            if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm) ||
                    PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {

                if (makeMedicinePriceLl.getVisibility() == View.VISIBLE) {
                    // 获取显示的制作费 (格式为: "¥x * y = ¥z")
                    String makeFeeText = makeMedicinePriceTv.getText().toString();
                    if (!TextUtils.isEmpty(makeFeeText)) {
                        try {
                            // 提取总制作费 (格式: "¥x * y = ¥z" 中的z)
                            String totalMakeFee = makeFeeText.substring(makeFeeText.lastIndexOf("¥") + 1).trim();
                            // 设置总制作费到提交参数中
                            medicationParams.setMakeCost(totalMakeFee);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    medicationParams.setMakeCost("0");
                }
            }

            return medicationParams;
        } else {
            return new OrderMsgBean();
        }
    }

    /**
     * 显示服药禁忌弹框
     */
    private void showTabooTipsPop() {
        if (tipsItems != null && tipsItems.size() > 0) {
            showTipsPop = false;
            mTabooTipsPopWindow.initPopContentData(tipsItems);
            mTabooTipsPopWindow.showPopFromBottom(mLayoutRoot);
            mTabooTipsPopWindow.OnSureBtnClickListener(new TabooTipsPopWindow.OnSureBtnClickListener() {
                @Override
                public void onSureBtnClick(String str) {
                    selectTaboo.setText(str);
                    mTabooTipsPopWindow.dimissPop();
                }
            });
        } else {
            showTipsPop = true;
            getTabooTips();
        }
    }

    /**
     * 显示选择用药时间弹框
     */
    private void showTakingTimePop() {
        if (timePopItems != null && timePopItems.size() > 0) {
            showTimePop = false;
            // 重置弹窗状态，确保没有残留的底部按钮或删除按钮
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(timePopItems);
            mBottomPopWindow.setPopTitle("选择用药时间");
            mBottomPopWindow.showPop();
            mBottomPopWindow.setBottomViewVisible();
            mBottomPopWindow.setBottomText("自定义");
            mBottomPopWindow.setBottomIcon(R.drawable.icon_camera);
            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, PopItem popItem) {
                    takingTimeTv.setText(popItem.getName());
                    mBottomPopWindow.dimissPop();
                }
            });
            mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
                @Override
                public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                    //自定义特殊煎法
                    showCustomBoilWayDialog();
                }
            });
        } else {
            showTimePop = true;
            getTakingTimeData();
        }

    }

    /**
     * 显示代煎偏好弹窗
     */
    void showDaijianPreferencePop() {
        if (daijianPianhaoPopItems != null && daijianPianhaoPopItems.size() > 0) {
            // 重置弹窗状态，确保没有残留的底部按钮或删除按钮
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(daijianPianhaoPopItems);
            mBottomPopWindow.setPopTitle("选择代煎偏好");
            mBottomPopWindow.showPop();

            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, PopItem popItem) {
                    daiJianPianhaoSelectTv.setText(popItem.getName());
                    daiJianPianhaoIndex = position;
                    mBottomPopWindow.dimissPop();
                }
            });
        }
    }

    /**
     * 限制输入框只输入汉字，数字
     *
     * @param str
     * @return
     * @throws PatternSyntaxException
     */
    public boolean stringFilter(String str) throws PatternSyntaxException {
        // 只允许数字和汉字,空格
        String regEx = "[\\d\u4E00-\u9FA5\\s]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.matches();
    }

    /**
     * 自定义特殊煎法
     */
    private void showCustomBoilWayDialog() {
        mInputDialog = InputDialog.getInstance(mContext);
        mInputDialog.setInputTitle("自定义用药时间");
        mInputDialog.setInputHint("请输入用药时间");
        mInputDialog.show();
        mInputDialog.setOnButtonClickListener(new OnButtonClickListener() {
            @Override
            public void onPositiveClick(View view, CharSequence editText) {
                if (TextUtils.isEmpty(editText.toString().trim())) {
                    ToastUtils.showShortMsg(mContext, "请输入用药时间！");
                } else if (!stringFilter(editText.toString())) {
                    ToastUtils.showShortMsg(mContext, "只支持输入汉字、数字");
                } else {
                    addCustomTakingTime(editText.toString().trim());
                }
            }

            @Override
            public void onNegativeClick(View view, CharSequence editText) {
                mInputDialog.dismiss();
            }
        });
    }

    /**
     * @param name 添加自定义用药时间
     */
    private void addCustomTakingTime(String name) {
        HashMap map = new HashMap();
        map.put("title", name);
        addCustomTakingTimeCallBack = addHttpPostRequest(HttpUrlManager.ADD_TAKING_TIME, map,
                ResponseResult.class, this);

    }

    /**
     * 初始化用药时间数据
     *
     * @param takingTimes       常规用药时间
     * @param customTakingTimes 自定义用药时间
     */
    private void initTakingTimeData(List<String> takingTimes, List<String> customTakingTimes) {
        timePopItems.clear();
        if (customTakingTimes != null && customTakingTimes.size() > 0) {
            for (int i = 0; i < customTakingTimes.size(); i++) {
                PopItem popItem = new PopItem();
                popItem.setName(customTakingTimes.get(i));
                timePopItems.add(popItem);
            }
        }
        if (takingTimes != null && takingTimes.size() > 0) {
            for (int i = 0; i < takingTimes.size(); i++) {
                PopItem popItem = new PopItem();
                popItem.setName(takingTimes.get(i));
                timePopItems.add(popItem);
            }
        }
        if (timePopItems != null && timePopItems.size() > 0) {
            mBottomPopWindow.upDatePopContent(timePopItems);
        }
    }


    /**
     * 清空已添加药材
     */
    private void clearMedicines() {
        if (medicineContainer != null && medicineContainer.getChildCount() > 0) {
            if (mConfirmDialog == null) {
                mConfirmDialog = ConfirmDialog.getInstance(mContext);
            }
            mConfirmDialog.setNavigationText("取消")
                    .setPositiveText("删除")
                    .setDialogContent("是否删除已添加的药材列表？").show();
            mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    mConfirmDialog.dismiss();
                }

                @Override
                public void onPositiveBtnClicked(View view) {
//                    setDefaultViewAndData();
                    mConfirmDialog.dismiss();
                }
            });

        } else {
            ToastUtils.showShortMsg(mContext, "您还未添加药材.");
        }
    }

    /**
     * 显示剂型筛选列表
     */
    private void showDosageFormsPop() {
        // 移除对checkTakerMsg()的调用，使添加药材时不进行姓名、年龄等信息验证
        mDosageFormsPop = DosageFormsPop.getInstance(mContext);
        mDosageFormsPop.isShowShortMedication(false);
        if (mDosageFormList != null && mDosageFormList.size() > 0) {
            showDosageFormPop = false;
            mDosageFormsPop.setData(mDosageFormList);
            mDosageFormsPop.show();
            mDosageFormsPop.setOnPopItemSelectedListener(new DosageFormsPop.PopItemSelectedListener() {
                @Override
                public void onPopItemSelected(DosageFormBean.FormList dosageForm
                        , DosageFormBean.FormList.FormDetailMsg formDetailMsg
                        , int leftPosition, int rightPosition) {
                    mDosageFormsPop.setLeftSelection(leftPosition);
                    mDosageFormsPop.setRightSelection(formDetailMsg.getId());
                    mFormDetailMsg = formDetailMsg;
                    mFormListBean = dosageForm;
                    startAddMedicineActivity(formDetailMsg, dosageForm, isPregnant, leftPosition
                            , formDetailMsg.getId(), null, false);
                }
            });
        } else {
            showDosageFormPop = true;
            getDosageFormList();
        }

    }

    /**
     * 获取选则剂型列表
     */
    private void getDosageFormList() {
        Map<String, String> params = new HashMap<>();
        params.put("apiVer", "2");
        getDosageFormListCallBack = addHttpPostRequest(HttpUrlManager.DOSAGE_FORM_LIST, params
                , DosageFormBean.class, this);
    }

    private void startAddMedicineActivity(DosageFormBean.FormList.FormDetailMsg formDetailMsg
            , DosageFormBean.FormList formListBean, String isPregnant, int leftP, String rightP
            , List<MedicineDetailMsgBean> list, boolean isCache) {
        Intent intent = new Intent(mContext, AddMedicineActivity.class);
        intent.putExtra(PublicParams.DOSAGE_SUPPLIER_BEAN, formDetailMsg);
        intent.putExtra(PublicParams.DOSAGEFORMBEAN, formListBean);
        intent.putExtra(PublicParams.IS_PREGNANT, isPregnant);
        intent.putExtra("leftPosition", leftP);
        intent.putExtra("rightPosition", rightP);
        intent.putExtra("editMedicines", (Serializable) list);
        intent.putExtra(PublicParams.IS_CACHE_MEDICINE, isCache);
        intent.putExtra(PublicParams.TAKER_NAME, nameEt.getText().toString().trim());
        intent.putExtra(PublicParams.FROM, FormAndOrderMsgBean.FROM_QUICK_MEDICINE);
        startActivity(intent);
    }


    /**
     * 添加的药材显示处理
     *
     * @sender {@link AddMedicineActivity#onRequestFinished(String, ResponseResult) HttpUrlManager.GET_PRERSCRIPTION_PRICEMSG}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceiveTemplateSelectedResult(FormAndOrderMsgBean formAndOrderMsgBean) {
        if (formAndOrderMsgBean.getFrom() != FormAndOrderMsgBean.FROM_QUICK_MEDICINE) {
            return;
        }
        if (mMedicineList != null && mMedicineList.size() > 0) {
            mMedicineList.clear();
        }
        if (formAndOrderMsgBean != null) {
            OrderMsgBean orderMsgBean = formAndOrderMsgBean.getOrderMsgBean();
            if (medicationParams == null) {
                medicationParams = orderMsgBean;
            }
            if (medicationParams != null && orderMsgBean != null) {
                medicationParams.setDrugForm(orderMsgBean.getDrugForm());
                medicationParams.setProviderId(orderMsgBean.getProviderId());
                medicationParams.setProductSubType(orderMsgBean.getProductSubType());
                medicationParams.setTakerIsPregnant(orderMsgBean.getTakerIsPregnant());
                medicationParams.setPreDetailList(orderMsgBean.getPreDetailList());
                medicationParams.setMakeDesc(TextUtils.isEmpty(orderMsgBean.getMakeDesc()) ? "" : orderMsgBean.getMakeDesc());//制作描述
                medicationParams.setMakeCost(TextUtils.isEmpty(orderMsgBean.getMakeCost()) ? "" : orderMsgBean.getMakeCost());//制作费
                medicationParams.setBalanceWeight(TextUtils.isEmpty(orderMsgBean.getBalanceWeight()) ? "" : orderMsgBean.getBalanceWeight());//总重
                medicationParams.setMakeDays(TextUtils.isEmpty(orderMsgBean.getMakeDays()) ? "" : orderMsgBean.getMakeDays());
                if (!TextUtils.isEmpty(orderMsgBean.getDrugPrice())) {
                    medicationParams.setDrugPrice(orderMsgBean.getDrugPrice());
                }
                //方子原始总重量
                medicationParams.setWeight(TextUtils.isEmpty(orderMsgBean.getWeight()) ? "" : orderMsgBean.getWeight());
            }
            mDrugForm = formAndOrderMsgBean.getOrderMsgBean().getDrugForm();

            mFormDetailMsg = formAndOrderMsgBean.getFormDetailMsg();
            mFormListBean = formAndOrderMsgBean.getFormList();
            leftP = mFormListBean.getLeftPosition();
            rightP = mFormDetailMsg.getRightPosition();
            List<MedicineDetailMsgBean> medicines = formAndOrderMsgBean.getOrderMsgBean().getPreDetailList();
            if (medicines != null && medicines.size() > 0) {
                mMedicineList.addAll(medicines);
                //显示添加的药材
                showAddMedicines(medicineContainer, medicines);
                System.out.println("==============添加的药材显示处理===" + mMedicineList.size() + "========");
                //显示厂商名称跟剂型
                medicineFormSupplier.setText(mDrugForm + "(" + mFormDetailMsg.getName() + ")");
                //设置医技服务费的显示
              /*  if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
                    doctorServiceFeeEt.setText(countDoctorServiceFee(false));
                } else {
                    doctorServiceFeeEt.setText(countDoctorServiceFee(true));
                }*/
                //处理用法用量的显示
                showUsageAndDosageData(false);
            } else {
                setDefaultViewAndData();
            }
        }

    }

    /**
     *
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void finishActivity(FinishActivityEvent event) {
        if (event != null && event.isCurrentActivity(getClass().getName())) {
            finish();
        }
    }


    /**
     * 显示膏方的用法用量信息
     *
     * @param isCache 是否显示缓存中的信息
     */
    private void showDoseMsgForCreamFormula(boolean isCache) {
        showUsageAndDosageUi(true);
        // 显示预计总重及服用天数
        specialTotalDoseTv.setText(TextUtils.isEmpty(medicationParams.getBalanceWeight())
                ? "" : medicationParams.getBalanceWeight() + "g");
        String originWeightStr = "原重"+medicationParams.getWeight()+"g,";
        //制作描述
        specialMakeDescriptionTv.setText(TextUtils.isEmpty(medicationParams.getMakeDesc())
                ? "" : "(" + originWeightStr + medicationParams.getMakeDesc() + ")");
        //设置pretimeDoseEt不可编辑
        pretimeDoseEt.setFocusable(true);
        pretimeDoseEt.setFocusableInTouchMode(true);
        pretimeDoseEt.setOnClickListener(null);
        capsuleNumTv.setVisibility(View.GONE);
//        if (isCache) {
//            pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "30" : medicationParams.getMcjk());
//            predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "2" : medicationParams.getMrjc());
//        } else {
//            pretimeDoseEt.setText("30");
//            predayDoseEt.setText("2");
//        }

        if (isCache) {
            if (medicationParams != null) {
                pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "30" : medicationParams.getMcjk());
                predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "3" : medicationParams.getMrjc());
            }
        } else {
            if (hasPreTimeDoseEt(mDrugForm)) {
                pretimeDoseEt.setText(getPreTimeDoseEt(mDrugForm));
            } else {
                pretimeDoseEt.setText("30");
            }
            if (hasPreDayDoseEt(mDrugForm)) {
                predayDoseEt.setText(getPreDayDoseEt(mDrugForm));
            } else {
                predayDoseEt.setText("3");
            }
        }

        baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
                ? "¥ 0.00" : "¥ " + getMedicinePrice(true));
        //总药费
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(true));
        //制作费
        makeMedicinePriceTv.setText(TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥ 0.00" : "¥ " + medicationParams.getMakeCost());
        //总计
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
    }

    /**
     * 显示散剂的用法用量
     *
     * @param isCache 是否显示缓存中的信息
     */
    private void showDoseMsgForPowder(boolean isCache) {
        showUsageAndDosageUi(true);
        // 显示预计总重及服用天数
        specialTotalDoseTv.setText(TextUtils.isEmpty(medicationParams.getBalanceWeight())
                ? "" : medicationParams.getBalanceWeight() + "g");
        String originWeightStr = "原重"+medicationParams.getWeight()+"g,";
        //制作描述
        specialMakeDescriptionTv.setText(TextUtils.isEmpty(medicationParams.getMakeDesc())
                ? "" : "("+ originWeightStr + medicationParams.getMakeDesc() + ")");
        //设置pretimeDoseEt不可编辑
        pretimeDoseEt.setFocusable(true);
        pretimeDoseEt.setFocusableInTouchMode(true);
        pretimeDoseEt.setOnClickListener(null);

        capsuleNumTv.setVisibility(View.GONE);

//        if (isCache) {
//            predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "2" : medicationParams.getMrjc());
//            pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "10" : medicationParams.getMcjk());
//        } else {
//            predayDoseEt.setText("2");
//            pretimeDoseEt.setText("10");
//        }
        if (isCache) {
            if (medicationParams != null) {
                predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "3" : medicationParams.getMrjc());
                pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "10" : medicationParams.getMcjk());
            }
        } else {
            if (hasPreDayDoseEt(mDrugForm)) {
                predayDoseEt.setText(getPreDayDoseEt(mDrugForm));
            } else {
                predayDoseEt.setText("3");
            }
            if (hasPreTimeDoseEt(mDrugForm)) {
                pretimeDoseEt.setText(getPreTimeDoseEt(mDrugForm));
            } else {
                pretimeDoseEt.setText("10");
            }
        }

        baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
                ? "¥ 0.00" : "¥ " + getMedicinePrice(true));
        //总药费
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(true));
        //制作费
        makeMedicinePriceTv.setText(TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥ 0.00" : "¥ " + medicationParams.getMakeCost());
        //总计
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
    }

    /**
     * 显示胶囊的用法用量
     */
    @SuppressLint("SetTextI18n")
    private void showCapsuleMsgForPowder(boolean isCache) {
        showUsageAndDosageUi(true);
        // 显示预计总重及服用天数
        specialTotalDoseTv.setText(TextUtils.isEmpty(medicationParams.getBalanceWeight())
                ? "" : medicationParams.getBalanceWeight() + "g");
        String originWeightStr = "原重"+medicationParams.getWeight()+"g,";
        //制作描述
        specialMakeDescriptionTv.setText(TextUtils.isEmpty(medicationParams.getMakeDesc())
                ? "" : "("+ originWeightStr + medicationParams.getMakeDesc() + ")");
        //设置pretimeDoseEt不可编辑
        pretimeDoseEt.setFocusable(true);
        pretimeDoseEt.setFocusableInTouchMode(true);
        pretimeDoseEt.setOnClickListener(null);
//        if (isCache) {
//            predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "2" : medicationParams.getMrjc());
//            pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "3" : medicationParams.getMcjk());
//        } else {
//            predayDoseEt.setText("2");
//            pretimeDoseEt.setText("3");
//        }

        if (isCache) {
            if (medicationParams != null) {
                predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "3" : medicationParams.getMrjc());
                pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "3" : medicationParams.getMcjk());
            }
        } else {
            if (hasPreDayDoseEt(mDrugForm)) {
                predayDoseEt.setText(getPreDayDoseEt(mDrugForm));
            } else {
                predayDoseEt.setText("3");
            }
            if (hasPreTimeDoseEt(mDrugForm)) {
                pretimeDoseEt.setText(getPreTimeDoseEt(mDrugForm));
            } else {
                pretimeDoseEt.setText("3");
            }
        }

        //计算当前默认的颗粒数
//        float dosePreUnit = Float.parseFloat(mFormDetailMsg.getDosePreUnit());
        float dosePreUnit = 0.5f;
        String packageDoseUnit = "g";
        if (mFormDetailMsg != null){
            dosePreUnit = Float.parseFloat(mFormDetailMsg.getDosePreUnit());
            packageDoseUnit = mFormDetailMsg.getPackageDoseUnit();
        }
        int capsuleNum = (int) (Float.parseFloat(pretimeDoseEt.getText().toString()) / dosePreUnit);

        //设置pretimeDoseEt不可编辑
        pretimeDoseEt.setFocusable(false);
        pretimeDoseEt.setFocusableInTouchMode(false);
        capsuleNumTv.setVisibility(View.VISIBLE);

        capsuleNumTv.setText("("+ capsuleNum + "颗)");

        float finalDosePreUnit = dosePreUnit;
        String finalPackageDoseUnit = packageDoseUnit;
        float finalDosePreUnit1 = dosePreUnit;
        String finalPackageDoseUnit1 = packageDoseUnit;
        pretimeDoseEt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //选择用量(每颗约0.5g)
                String text = "选择用量(每颗约" + finalDosePreUnit + finalPackageDoseUnit +")";
                SpannableString spannableString = new SpannableString(text);

                //选择用量样式
                spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#1d2024")), 0, 4, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                //字体大小16
                spannableString.setSpan(new AbsoluteSizeSpan(16, true), 0, 4, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                //设置括号内（包括括号）的样式
                spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ef4d3d")), 4, text.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                //字体大小14
                spannableString.setSpan(new AbsoluteSizeSpan(14, true), 4, text.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

                mBottomPopWindow.setPopSnannableTitle(spannableString);
                mBottomPopWindow.setPopContentData(initCapsuleData());
                mBottomPopWindow.showPop();
                mBottomPopWindow.setBottomViewVisible();
                mBottomPopWindow.setBottomText("自定义");


                mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
                    @Override
                    public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                        mBottomPopWindow.dismiss();

                        //显示输入自定义颗粒数的弹窗
                        if (capsuleNumInputDialog == null){
                            capsuleNumInputDialog = com.doctor.br.view.InputDialog.getInstance(QuicklyPrescriptionActivity.this)
                                    .setCustomLayout(R.layout.custom_capsule_dailog_input);
                        }
                        capsuleNumInputDialog
                                .setPositiveText("保存")
                                .setNegativeText("取消")
                                .setCalculationLogic(input ->{
                                    try {
                                        int number = Integer.parseInt(input);
                                        return "总重约" + String.valueOf(finalDosePreUnit1 * number) + finalPackageDoseUnit1;
                                    }catch (NumberFormatException e){
                                        return "总重约0.0g";
                                    }
                                })
                                .setOnButtonClickListener(new OnButtonClickListener() {
                                    @Override
                                    public void onPositiveClick(View view, CharSequence editText) {
                                        if (TextUtils.isEmpty(editText.toString().trim())) {
                                            ToastUtils.showShortMsg(mContext, "请输入颗粒数");
                                            return;
                                        }

                                        //editText 为颗粒数
                                        int capsuleNum = Integer.parseInt(editText.toString().trim());
                                        capsuleNumTv.setText("("+ capsuleNum + "颗)");
                                        //根据选择的颗粒数计算胶囊的重量
                                        pretimeDoseEt.setText(String.valueOf(capsuleNum * finalDosePreUnit1));
                                        //隐藏弹窗
                                        capsuleNumInputDialog.hideKeyboard();
                                        //关闭弹窗
                                        capsuleNumInputDialog.dismiss();
                                    }

                                    @Override
                                    public void onNegativeClick(View view, CharSequence editText) {
                                        capsuleNumInputDialog.hideKeyboard();
                                        capsuleNumInputDialog.dismiss();
                                    }
                                }).show();
                    }
                });


                mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                    @Override
                    public void onPopItemClick(int position, PopItem popItem) {
                        try {
                            pretimeDoseEt.setText(popItem.getValue());
                            mBottomPopWindow.dimissPop();

                            //根据选择的颗粒数计算胶囊的数量
                            int capsuleNum = (int) (Float.parseFloat(pretimeDoseEt.getText().toString()) / finalDosePreUnit);
                            capsuleNumTv.setText("("+ capsuleNum + "颗)");

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });

        baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
                ? "¥ 0.00" : "¥ " + getMedicinePrice(true));
        //总药费
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(true));
        //制作费
        makeMedicinePriceTv.setText(TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥ 0.00" : "¥ " + medicationParams.getMakeCost());
        //总计
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
    }



    /**
     * 显示用法用量及制作费布局,根据剂型分类显示
     *
     * @param isCache 显示的是否是缓存中的信息
     */
    private void showUsageAndDosageData(boolean isCache) {
        //默认显示颗粒/饮片/外用中药的
        if (TextUtils.isEmpty(mDrugForm)) {
            showUsageAndDosageUi(false);
            showDoseMsgForKYExMedicine(isCache);
        } else {
            if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
                //显示颗粒、饮片、外用中药的用法用量信息，isCache为false说明非缓存，true说明显示缓存中信息
                showDoseMsgForKYExMedicine(isCache);

            } else if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm)) {
                //显示蜜丸、水丸的用法用量信息,isCache为false说明非缓存，true说明显示缓存中信息
                showDoseMsgForPill(isCache);

            } else if (PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm)) {
                //膏方
                //显示膏方的用法用量信息,isCache为false说明非缓存，true说明显示缓存中信息
                showDoseMsgForCreamFormula(isCache);

            } else if (PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm)) {
                //散剂
                //显示散剂的用法用量，isCache为false说明非缓存，true说明显示缓存中信息
                showDoseMsgForPowder(isCache);
            } else if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm)) {
                //胶囊
                showCapsuleMsgForPowder(isCache);
            }
        }

        //单独进行处理 饮片、代煎、散剂
        if (PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm)){
            usageMethodLayout.setVisibility(View.VISIBLE);
        }else {
            usageMethodLayout.setVisibility(View.GONE);
        }

        //添加制作费显示的控制逻辑
        if (medicationParams != null) {
            //如果是代煎,保持原有逻辑
            if (TextUtils.equals("代煎", medicationParams.getDrugForm())) {
                return;
            }

            //颗粒和外用中药的制作费显示控制
            if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm) ||
                    PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {

                String makeCost = medicationParams.getMakeCost();
                if (!TextUtils.isEmpty(makeCost) && !"0".equals(makeCost) && !"0.00".equals(makeCost)) {
                    //有制作费时显示
                    makeMedicinePriceLl.setVisibility(View.VISIBLE);
                    //更新制作费显示
                    String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                            ? "7" : totalDoseEt.getText().toString(); //付数
                    String makeFeePre = "¥" + makeCost; //单剂制作费
                    //制作费
                    makeMedicinePriceTv.setText(makeFeePre + " * " + num + " = ¥" +
                            (new BigDecimal(getMedicineMakeFee(false)).setScale(2).toString()));
                } else {
                    //无制作费时隐藏
                    makeMedicinePriceLl.setVisibility(View.GONE);
                }
            }
        } else {
            makeMedicinePriceLl.setVisibility(View.GONE);
        }


    }

    /**
     * 初始化用量数据
     */
    private List<PopItem> initUsageData() {
        List<PopItem> usageData = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            PopItem item = new PopItem();
            item.setName(i * 3 + "g");
            usageData.add(item);
        }
        return usageData;
    }

    /**
     * 初始化胶囊用量数据
     */
    private List<PopItem> initCapsuleData() {
        float dosePreUnit = 0.5f;
        if (mFormDetailMsg != null && mFormDetailMsg.getDosePreUnit() != null) {
           dosePreUnit = Float.parseFloat(mFormDetailMsg.getDosePreUnit());
        }
        List<PopItem> usageData = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            //颗粒数
            int num = i * 3;
            //对应的重量
            float weight = dosePreUnit * num;
            PopItem item = new PopItem();
            item.setName(weight + "g" + " " + "【"+ num + "颗】");
            item.setValue(Float.toString(weight));
            usageData.add(item);
        }
        return usageData;
    }

    /**
     * 显示蜜丸、水丸的用法用量信息
     *
     * @param isCache 是否显示的是缓存中的信息
     */
    private void showDoseMsgForPill(boolean isCache) {
        //蜜丸 、水丸
        showUsageAndDosageUi(true);
        // 显示预计总重及服用天数
        specialTotalDoseTv.setText(TextUtils.isEmpty(medicationParams.getBalanceWeight())
                ? "" : medicationParams.getBalanceWeight() + "g");
        String originWeightStr = "原重"+medicationParams.getWeight()+"g,";
        //制作描述
        specialMakeDescriptionTv.setText(TextUtils.isEmpty(medicationParams.getMakeDesc())
                ? "" : "("+ originWeightStr + medicationParams.getMakeDesc() + ")");
//        if (isCache) {
//            predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "2" : medicationParams.getMrjc());
//            pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "6" : medicationParams.getMcjk());
//        } else {
//            predayDoseEt.setText("2");
//            pretimeDoseEt.setText("6");
//        }
        if (isCache) {
            if (medicationParams != null) {
                predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "3" : medicationParams.getMrjc());
                pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "6" : medicationParams.getMcjk());
            }
        } else {
            if (hasPreDayDoseEt(mDrugForm)) {
                predayDoseEt.setText(getPreDayDoseEt(mDrugForm));
            } else {
                predayDoseEt.setText("3");
            }
            if (hasPreTimeDoseEt(mDrugForm)) {
                pretimeDoseEt.setText(getPreTimeDoseEt(mDrugForm));
            } else {
                pretimeDoseEt.setText("6");
            }
        }

        //设置pretimeDoseEt不可编辑
        pretimeDoseEt.setFocusable(false);
        pretimeDoseEt.setFocusableInTouchMode(false);
        capsuleNumTv.setVisibility(View.GONE);

        pretimeDoseEt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 重置弹窗状态，确保没有残留的底部按钮或删除按钮
                mBottomPopWindow.resetState();
                mBottomPopWindow.setPopTitle("选择用量");
                mBottomPopWindow.setPopContentData(initUsageData());
                mBottomPopWindow.showPop();
                mBottomPopWindow.setBottomViewGone();
                mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                    @Override
                    public void onPopItemClick(int position, PopItem popItem) {
                        try {
                            pretimeDoseEt.setText(popItem.getName().replace("g", ""));
                            mBottomPopWindow.dimissPop();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });

        baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
                ? "¥ 0.00" : "¥ " + getMedicinePrice(true));
        //总药费
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(true));
        //制作费
        makeMedicinePriceTv.setText(TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥ 0.00" : "¥ " + medicationParams.getMakeCost());
        //总计
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
    }

    /**
     * 显示颗粒、饮片、外用中药的用法用量信息
     *
     * @param isCache 是否显示的是缓存中的信息
     */
    private void showDoseMsgForKYExMedicine(boolean isCache) {
        showUsageAndDosageUi(false);
        if (isCache) {
            if (medicationParams != null) {
                totalDoseEt.setText(TextUtils.isEmpty(medicationParams.getTotalPreNum()) ? "7" : medicationParams.getTotalPreNum());
                dayDoseEt.setText(TextUtils.isEmpty(medicationParams.getDayPreNum()) ? "1" : medicationParams.getDayPreNum());
                preDoseTimeEt.setText(TextUtils.isEmpty(medicationParams.getPreTimes()) ? "2" : medicationParams.getPreTimes());
            }
        } else {
            if (TextUtils.isEmpty(totalDoseEt.getText())) {
                totalDoseEt.setText("7");
            } else {
                totalDoseEt.setText(totalDoseEt.getText());
            }
            if (TextUtils.isEmpty(dayDoseEt.getText())) {
                dayDoseEt.setText("1");
            } else {
                dayDoseEt.setText(dayDoseEt.getText());
            }
            if (TextUtils.isEmpty(preDoseTimeEt.getText())) {
                // 修改：判断是否为特定剂型，如果是则默认为3次，否则为2次
                if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm)) {
                    preDoseTimeEt.setText("3");
                } else {
                    preDoseTimeEt.setText("2");
                }
            } else {
                preDoseTimeEt.setText(preDoseTimeEt.getText());
            }
        }

        // 当剂型为代煎时，设置preDoseTimeEt（每剂分xx次服用）的特殊处理
        if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) || TextUtils.equals("代煎", medicationParams.getDrugForm())) {
            // 检查当前值是否大于4，如果是则调整为4并提示
            if (!TextUtils.isEmpty(preDoseTimeEt.getText())) {
                try {
                    int currentValue = Integer.parseInt(preDoseTimeEt.getText().toString());
                    if (currentValue > 4) {
                        preDoseTimeEt.setText("2");
//                        ToastUtils.showShortMsg(QuicklyPrescriptionActivity.this, "代煎剂型每剂最多分4次服用");
                    }
                } catch (NumberFormatException e) {
                    // 如果不是有效数字，设置为默认值2
                    preDoseTimeEt.setText("2");
                }
            }

            // 设置不可直接编辑
            preDoseTimeEt.setFocusable(false);
            preDoseTimeEt.setFocusableInTouchMode(false);

            // 添加点击事件，弹出底部选择框
            preDoseTimeEt.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    //其他地方键盘消失
                    InputMethodManager imm = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);

                    // 清除所有输入框的焦点
                    if (mLayoutRoot != null) {
                        // 方法1：将焦点转移到根布局
                        mLayoutRoot.requestFocus();

                        // 方法2：或者查找当前有焦点的视图并清除焦点
                        View currentFocus = QuicklyPrescriptionActivity.this.getCurrentFocus();
                        if (currentFocus != null) {
                            currentFocus.clearFocus();
                        }
                    }

                    // 初始化选项数据
                    List<PopItem> timesItems = new ArrayList<>();
                    String[] timesArray = {"1 次", "2 次", "3 次", "4 次"};
                    for (int i = 0; i < timesArray.length; i++) {
                        PopItem popItem = new PopItem();
                        popItem.setName(timesArray[i]);
                        popItem.setValue(String.valueOf(i + 1)); // 值为1-4
                        timesItems.add(popItem);
                    }

                    // 设置弹窗
                    if (mBottomPopWindow == null) {
                        mBottomPopWindow = new BottomPopWindow(QuicklyPrescriptionActivity.this);
                    }
                    // 重置弹窗状态，确保没有残留的底部按钮或删除按钮
                    mBottomPopWindow.resetState();
                    mBottomPopWindow.setPopTitle("选择次数");
                    mBottomPopWindow.setPopContentData(timesItems);
                    mBottomPopWindow.showPop();

                    // 设置选项点击事件
                    mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                        @Override
                        public void onPopItemClick(int position, PopItem popItem) {
                            preDoseTimeEt.setText(popItem.getValue());
                            mBottomPopWindow.dimissPop();
                        }
                    });
                }
            });
        } else {
            // 非代煎剂型，恢复正常编辑状态
            preDoseTimeEt.setFocusable(true);
            preDoseTimeEt.setFocusableInTouchMode(true);
            preDoseTimeEt.setOnClickListener(null);
        }

        countGeneralMedicineTotalPrice(totalDoseEt.getText().toString().trim());
        //药费
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
        //总计
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));

        capsuleNumTv.setVisibility(View.GONE);
    }

    /**
     * 除辨证以外都恢复默认设置
     * 显示默认设置
     */
    private void setDefaultViewAndData() {
        if (!mMedicineList.isEmpty()) {
            mMedicineList.clear();
        }
        medicineContainer.removeAllViews();
        medicationParams = new OrderMsgBean();
        medicineMessLl.setVisibility(View.GONE);
        addMedicinesLl.setVisibility(View.VISIBLE);

        //显示默认用法用量布局
        showUsageAndDosageUi(false);
        totalDoseEt.setText("7");
        dayDoseEt.setText("1");
        // 我们不修改这里的默认值为3，因为这里是针对非特定剂型的通用默认值
        // 特定剂型的默认值已经在各自的显示方法中修改
        preDoseTimeEt.setText("2");
        afterDayEt.setText("7");
        takingTimeTv.setText("饭后一小时");
        selectTaboo.setGravity(Gravity.RIGHT);
        selectTaboo.setText("选择服药禁忌");
        directionEt.setText("");
        directionEt.setGravity(Gravity.RIGHT);
        noteEt.setText("");
        templateNameEt.setText("");
        templateNameEt.setVisibility(View.GONE);
//        mDottedLine.setVisibility(View.GONE);
        templateCb.setChecked(false);
        sureDiavisiblesedCb.setChecked(false);
        privaryCb.setChecked(false);
        privaryDoseCb.setChecked(false);
        //诊费
        additionalChargeEt.setText(TextUtils.isEmpty(medicineFee) ? "0" : medicineFee);
        //订单总额
        olderTotalpriceTv.setText("¥ " + countDefaultPrescriptionTotalPrice());
        //基础药费
        baseMedicinePriceTv.setText("¥ 0.00");
        //医技服务费
//        doctorServiceFeeEt.setText("0.00");
        //药费
        medicineTotalPriceTv.setText("¥ 0.00");
        medicinePriceLayout.setVisibility(View.GONE);
        priceDetailTv.setText("明细");
        showPriceDetailIv.setImageResource(R.drawable.blue_down_arrows);
        diagnoseEt.requestFocus();
        diagnoseEt.setSelection(diagnoseEt.getText().length());
    }

    /**
     * 显示用法用量ui
     *
     * @param isSpecial false 显示颗粒、饮片、外用中药的 true 显示蜜丸水丸散剂膏方等的用法用量
     */
    private void showUsageAndDosageUi(boolean isSpecial) {
        if (isSpecial) {
            normalUsageAndDosageLl.setVisibility(View.GONE);
            specialUsageAndDosageLl.setVisibility(View.VISIBLE);
            makeMedicinePriceLl.setVisibility(View.VISIBLE);
        } else {
            normalUsageAndDosageLl.setVisibility(View.VISIBLE);
            specialUsageAndDosageLl.setVisibility(View.GONE);
            makeMedicinePriceLl.setVisibility(View.GONE);
        }
        showDoseMsgForYMedicine();
    }

    /**
     * 显示饮片代煎信息
     */
    private void showDoseMsgForYMedicine() {
        boolean isDaijian = false;
        if (medicationParams != null) {
            isDaijian = TextUtils.equals("代煎", medicationParams.getDrugForm());
        }
        daiJianSwitch.setOnCheckedChangeListener(null);
        if (isDaijian) {
//            daiJianSwitch.setChecked(medicationParams.isUseDaiJian());
            daiJianSwitch.setChecked(true);

//            if (daiJianSwitch.isChecked()) {
                makeMedicinePriceLl.setVisibility(View.VISIBLE);
//            } else {
//                makeMedicinePriceLl.setVisibility(View.GONE);
//            }
            daiJianRl.setVisibility(View.VISIBLE);
            daiJianLine.setVisibility(View.VISIBLE);
            daiJianPianhaoRl.setVisibility(View.VISIBLE);
            daijianPianhaoLine.setVisibility(View.VISIBLE);
        } else {
            daiJianRl.setVisibility(View.GONE);
            daiJianLine.setVisibility(View.GONE);
            daiJianSwitch.setChecked(false);
            daiJianPianhaoRl.setVisibility(View.GONE);
            daijianPianhaoLine.setVisibility(View.GONE);
        }

        //代煎开关
        daiJianSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                medicationParams.setUseDaiJian(isChecked);
                if (isChecked) {
                    makeMedicinePriceLl.setVisibility(View.VISIBLE);
                } else {
                    makeMedicinePriceLl.setVisibility(View.GONE);
                }
                String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                        ? "7" : totalDoseEt.getText().toString();//付数
                String makeFeePre = TextUtils.isEmpty(medicationParams.getMakeCost())
                        ? "¥0.00" : "¥" + medicationParams.getMakeCost();//制作费
                //制作费,代煎费
                makeMedicinePriceTv.setText(makeFeePre + " * " + num + " = ¥" + (new BigDecimal(getMedicineMakeFee(false)).setScale(2).toString()));

                //药费
                medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
                //总计
                olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));
            }
        });
        //药费
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
        String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                ? "7" : totalDoseEt.getText().toString();//付数
        String makeFeePre = TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥0.00" : "¥" + medicationParams.getMakeCost();//制作费
        //制作费,代煎费
        makeMedicinePriceTv.setText(makeFeePre + " * " + num + " = ¥" + (new BigDecimal(getMedicineMakeFee(false)).setScale(2).toString()));
        //总计
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));
    }

    /**
     * @return 诊费*1.064
     */
    private String getServiceFeeNew() {
        String fee = "0";
        if (additionalChargeEt != null) {
            fee = additionalChargeEt.getText().toString().trim();
        }
        if (TextUtils.isEmpty(fee)) {
            return "0";
        }
        BigDecimal feeDec = new BigDecimal(fee);
        if (feeDec.floatValue() < 0.01) {
            return "0";
        }
        return feeDec.multiply(new BigDecimal(1.064)).toString();
    }

    /**
     * @return 总基础药费+诊费*1.064+制作费（特殊剂型制作费/饮片的代煎费用）
     */
    private String getTotalPrescriptionPrice(boolean isSpecialDosageForm) {
        //总药费+诊费*1.064
        return DecimalUtils.add(getTotalMedicineFee(isSpecialDosageForm), getServiceFeeNew());
    }

    /**
     * 计算药材价格
     * 付数* 单价
     *
     * @param num 付数
     * @return
     */
    private String countMedicinePrice(String num) {
        String totalDrugPrice = "0";
        String drugPrice = "0";
        if (medicationParams != null && !TextUtils.isEmpty(medicationParams.getDrugPrice())
                && !TextUtils.isEmpty(num)) {
            drugPrice = medicationParams.getDrugPrice();
            totalDrugPrice = DecimalUtils.multiply(drugPrice, num);
        }
        return totalDrugPrice;
    }

    /**
     * @param isSpecialDosageForm 是否是特殊剂型
     * @return 基础药费
     */
    private String getMedicinePrice(boolean isSpecialDosageForm) {
        String drugPrice = "0";//药费
        String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                ? "7" : totalDoseEt.getText().toString();//付数
        if (isSpecialDosageForm) {
            if (medicationParams != null) {
                drugPrice = TextUtils.isEmpty(medicationParams.getDrugPrice())
                        ? "0" : medicationParams.getDrugPrice();
            }
        } else {
            drugPrice = DecimalUtils.format(countMedicinePrice(num), 3);
        }
        return drugPrice;
    }

    /**
     * @param isSpecialDosageForm
     * @return 总药费==总基础药费+制作费（特殊剂型制作费/饮片的代煎费用）
     */
    private String getTotalMedicineFee(boolean isSpecialDosageForm) {
        return DecimalUtils.add(getMedicinePrice(isSpecialDosageForm), getMedicineMakeFee(isSpecialDosageForm));

    }

    /**
     * @param isSpecialDosageForm 是否为特殊剂型
     * @return 获取药材制作费  普通剂型为0
     */
    private String getMedicineMakeFee(boolean isSpecialDosageForm) {
        String makeFee = "0";
        if (isSpecialDosageForm) {
            if (medicationParams != null) {
                makeFee = TextUtils.isEmpty(medicationParams.getMakeCost())
                        ? "0" : medicationParams.getMakeCost();//制作费
            }
        } else if (TextUtils.equals("代煎", medicationParams.getDrugForm()) ||
                TextUtils.equals("颗粒", medicationParams.getDrugForm()) ||
                TextUtils.equals("外用中药", medicationParams.getDrugForm())) {
            String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                    ? "7" : totalDoseEt.getText().toString();//付数
            String makeFeePre = TextUtils.isEmpty(medicationParams.getMakeCost())
                    ? "0" : medicationParams.getMakeCost();//制作费
            makeFee = new BigDecimal(makeFeePre).multiply(new BigDecimal(num)).setScale(2).toString();

        }
        return makeFee;
    }

    /**
     * @return 计算订单总金额(默认的) ：
     * 诊费+服务费（诊费*6%）=总金额
     */
    private String countDefaultPrescriptionTotalPrice() {
        String totalPrice = "0";
        String serviceFee = "0";//服务费
        String fee = TextUtils.isEmpty(additionalChargeEt.getText().toString())
                ? "0" : additionalChargeEt.getText().toString();//诊费
        if (!TextUtils.isEmpty(fee)) {
            serviceFee = DecimalUtils.mul(fee, "0.064");
        }
        totalPrice = DecimalUtils.add(fee, serviceFee);
        return totalPrice;
    }

    /**
     * 显示添加的药材
     */
    private void showAddMedicines(FlowLayout medicineContainer, List<MedicineDetailMsgBean> medicines) {
        if (medicines != null && medicines.size() > 0) {
            addMedicinesLl.setVisibility(View.GONE);
            medicineMessLl.setVisibility(View.VISIBLE);
            medicineFormSupplier.setVisibility(View.VISIBLE);

            medicineContainer.removeAllViews();
            for (MedicineDetailMsgBean medicine : medicines) {
                TextView tv = new TextView(mContext);
                tv.setLayoutParams(new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT
                        , FlowLayout.LayoutParams.WRAP_CONTENT));
                tv.setPadding(DensityUtils.dip2px(mContext, 10)
                        , DensityUtils.dip2px(mContext, 5)
                        , DensityUtils.dip2px(mContext, 15)
                        , DensityUtils.dip2px(mContext, 5));
                tv.setTextColor(getResources().getColor(R.color.br_color_theme_text));
                tv.setTextSize(15);
                String content = medicine.getDrugName() + medicine.getDose() + medicine.getUnit();
                SpannableStringBuilder ssb = new SpannableStringBuilder(content);
                if (PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm) || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)) {//饮片
                    if (!TextUtils.isEmpty(medicine.getUseMethod())) {//特殊煎药方式
                        String subContent = "(" + medicine.getUseMethod() + ")";
                        ssb.append(subContent);
                    }
                }
                ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_red_ef4d3b))
                        , content.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                ssb.setSpan(new AbsoluteSizeSpan(DensityUtils.sp2px(mContext, 12))
                        , content.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                tv.setText(ssb);
                medicineContainer.addView(tv);
            }
        } else {
            addMedicinesLl.setVisibility(View.VISIBLE);
            medicineMessLl.setVisibility(View.GONE);
        }

    }

    /**
     * 确定退出
     * true 直接退出
     * false 存值并退出
     *
     * @return
     */
    public boolean isSureExit() {

//            if (medicationParams != null && !TextUtils.isEmpty(medicationParams.getTakerId())
//                    && !patientId.equalsIgnoreCase(medicationParams.getTakerId())) {
//                return false;
//            }
        if (!TextUtils.isEmpty(nameEt.getText().toString().trim())) {
            return false;
        }
        if (!TextUtils.isEmpty(ageEt.getText().toString().trim())) {
            return false;
        }
        if (!TextUtils.isEmpty(sexStr)) {
            return false;
        }
        if (!TextUtils.isEmpty(isPregnant)) {
            return false;
        }
        if (!TextUtils.isEmpty(diagnoseEt.getText().toString().trim())) {
            return false;
        }
        if (mMedicineList.size() > 0) {
            return false;
        }
        if (!TextUtils.isEmpty(noteEt.getText().toString().trim())) {
            return false;
        }
        if (!TextUtils.isEmpty(templateNameEt.getText().toString().trim())) {
            return false;
        }
        if (!TextUtils.isEmpty(directionEt.getText().toString().trim())) {
            return false;
        }
        if (!"选择服药禁忌".equals(selectTaboo.getText().toString().trim())) {
            return false;
        }
        if (!"饭后一小时".equals(takingTimeTv.getText().toString().trim())) {
            return false;
        }
        if (!"7".equals(totalDoseEt.getText().toString().trim())) {
            return false;
        }
        if (!"1".equals(dayDoseEt.getText().toString().trim())) {
            return false;
        }
        if (!"2".equals(preDoseTimeEt.getText().toString().trim())) {
            return false;
        }
        if (!"7".equals(afterDayEt.getText().toString().trim())) {
            return false;
        }
        if (!TextUtils.isEmpty(medicalFeeRemark) &&
                !medicalFeeRemark.equals(additionalChargeEt.getText().toString().trim())) {
            return false;
        }
        if (templateCb.isChecked()) {
            return false;
        }
        if (privaryCb.isChecked()) {
            return false;
        }
        if (privaryDoseCb.isChecked()) {
            return false;
        }
        if (sureDiavisiblesedCb.isChecked()) {
            return false;
        }
        if (mIsAutoSend != switchBtn.isChecked()) {
            return false;
        }
        if (daiJianSwitch.isChecked()) {
            return false;
        }
        return true;
    }

    @Override
    protected void onStop() {
        if (!isSureExit()) {
            if (dataCacheUtil == null) {
                dataCacheUtil = DataCacheDaoUtil.getInstance();
            }
            dataCacheUtil.upDateData(setCachePresMsg());
            LogUtils.i(QuicklyPrescriptionActivity.class, "onStop======dataCacheUtil   key == " + userId + "_" + nameEt.getText().toString().trim() + ";");
        }
        super.onStop();
    }

    /**
     * 设置要缓存的药方信息
     *
     * @return
     */
    public DataCache setCachePresMsg() {
        DataCache data = new DataCache();
        data.setKey(userId + "_" + nameEt.getText().toString().trim());
        data.setType(DataCacheType.CACHE_QUICK_PRESCRIPTION_MSG);
        data.setValue(JSON.toJSONString(setMedicationParams()));
        System.out.println("快速开方设置要保存的药方数据：" + nameEt.getText().toString().trim() + "===============" + JSON.toJSONString(setMedicationParams()));
        return data;
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        if (mBottomPopWindow != null) {
            mBottomPopWindow.dimissPop();
            mBottomPopWindow = null;
        }
        if (mDosageFormsPop != null) {
            mDosageFormsPop.dismiss();
            mDosageFormsPop = null;
        }
        cancelRequest(getMedicalFeeCallBack, getDosageFormListCallBack, getTabooTipsCallBack
                , addCustomTakingTimeCallBack, getTakingTimeCallBack, saveCommonTemplateCallBack);
        super.onDestroy();
    }
}