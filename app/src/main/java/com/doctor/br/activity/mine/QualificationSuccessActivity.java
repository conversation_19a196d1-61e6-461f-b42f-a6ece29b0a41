package com.doctor.br.activity.mine;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Button;
import android.widget.TextView;

import com.doctor.br.activity.LoadDataActivity;
import com.doctor.br.activity.MainActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.event.FinishQualificationSuccessActivityEvent;
import com.doctor.br.db.entity.ServerConfig;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.greendao.gen.ServerConfigDao;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 类描述：认证成功显示的界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/2
 */

public class QualificationSuccessActivity extends ActionBarActivity {
    //上个界面传递过来的数据
    public static final String NAME = "name";
    public static final String TITLE = "title";
    public static final String HOSPITAL_NAME = "hospitalName";
    private String name, title, hospitalName;//真实姓名，职称，就职机构
    private String fromClass;//判断从哪个页面过来，判断是否为新注册用户
    //界面下的控件
    @BindView(R.id.name_tv)
    TextView nameTv;
    @BindView(R.id.title_tv)
    TextView titleTv;
    @BindView(R.id.hospital_tv)
    TextView hospitalTv;
    @BindView(R.id.commit_btn)
    com.doctor.br.view.NoDoubleClickBtn commitBtn;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_qualification_success);
        EventBusUtils.register(this);
        getIntentData(savedInstanceState);
        initView();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(NAME, name);
        outState.putString(TITLE, title);
        outState.putString(HOSPITAL_NAME, hospitalName);
        outState.putString(QualificationActivity.CLASS, fromClass);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            name = savedInstanceState.getString(NAME);
            title = savedInstanceState.getString(TITLE);
            hospitalName = savedInstanceState.getString(HOSPITAL_NAME);
            fromClass = savedInstanceState.getString(QualificationActivity.CLASS);
        } else {
            name = getIntent().getStringExtra(NAME);
            title = getIntent().getStringExtra(TITLE);
            hospitalName = getIntent().getStringExtra(HOSPITAL_NAME);
            fromClass = getIntent().getStringExtra(QualificationActivity.CLASS);
        }
        if (TextUtils.isEmpty(name) || TextUtils.isEmpty(hospitalName)) {
            ToastUtils.showShortMsg(this, "数据错误");
            onBackPressed();
        }
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("认证成功");

        nameTv.setText(name);
        if ("其他".equals(title)) {
            titleTv.setText("中医师");
        } else {
            titleTv.setText(title);
        }

        if (hospitalName.length() > 20) {
            hospitalTv.setText(hospitalName.substring(0, 20) + "…");
        } else {
            hospitalTv.setText(hospitalName);
        }
    }

    @OnClick(R.id.commit_btn)
    public void onViewClicked() {
        Intent intent = new Intent();
        intent.putExtra(QualificationActivity.HISTORY_STATE, "1");
        intent.putExtra(QualificationActivity.STATE, "1");
        intent.setClass(this, QualificationActivity.class);
        startActivity(intent);
    }

    /**
     * 关闭之前的认证成功界面
     *
     * @sender {@link QualificationActivity#closeQualificationSuccessActivity()}
     * @receiver {@link QualificationSuccessActivity#onReceive(FinishQualificationSuccessActivityEvent)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceive(FinishQualificationSuccessActivityEvent finishActivityEvent) {
        if (finishActivityEvent != null && finishActivityEvent.isClose()) {
            finish();
        }
    }

    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    @Override
    public void onBackPressed() {
        if (!TextUtils.isEmpty(fromClass) && fromClass.equals(PersonalDataActivity.class.getSimpleName())) {
            Intent intent;
            if (isLoadData()) {
                intent = new Intent(this, LoadDataActivity.class);
            } else {
                intent = new Intent(this, MainActivity.class);
            }
            startActivity(intent);
            finish();
            return;
        }
        super.onBackPressed();
    }

    /**
     * 判断是否加载数据
     *
     * @return
     */
    private boolean isLoadData() {
        ServerConfigDao serverConfigDao = AppContext.getInstances().getDaoSession().getServerConfigDao();
        List<ServerConfig> list = serverConfigDao.loadAll();
        if (list != null && list.size() > 0) {
            ServerConfig config = list.get(0);
            if (config != null) {
                if (!TextUtils.isEmpty(config.getArea_addressVersion()) && !config.getArea_addressVersion().equalsIgnoreCase(config.getArea_addressVersionLocal())) {
                    return true;
                }

                if (!TextUtils.isEmpty(config.getProduct_drugVersion()) && !config.getProduct_drugVersion().equalsIgnoreCase(config.getProduct_drugVersionLocal())) {
                    return true;
                }
            }
        }
        String updateContactsListTime = SharedPreferenceUtils.getString(this, PublicParams.UPDATE_CONTACTS_LIST_TIME);
        if (TextUtils.isEmpty(updateContactsListTime)) {
            return true;
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        super.onDestroy();
    }
}
