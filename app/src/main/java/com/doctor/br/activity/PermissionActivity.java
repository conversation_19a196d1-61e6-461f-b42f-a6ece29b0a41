package com.doctor.br.activity;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import android.text.TextUtils;
import android.view.View;

import com.doctor.br.receiver.DownloadManagerReceiver;
import com.doctor.br.utils.FileUtils;
import com.doctor.br.utils.MyPathManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.io.File;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description
 * @createTime 2017/12/22
 */

public class PermissionActivity extends NoActionBarActivity {
    private static final String EXTRA_PERMISSIONS = "extra_permissions";
    private static final String EXTRA_PERMISSION_DESC = "extra_permission_desc";
    private static final String EXTRA_PERMISSION_REQ_CODE = "extra_req_code";
    public static final int REQUEST_PERMISSIONS_INSTALL_PACKAGE = 1232;
    private static final int INSTALL_PERMISS_CODE = 2341;

    public static void start(Context context, String[] permissions, int requestCode, String permissionDesc, Bundle extras) {
        Intent intent = new Intent(context, PermissionActivity.class);
        intent.putExtra(EXTRA_PERMISSION_REQ_CODE, requestCode);
        intent.putExtra(EXTRA_PERMISSIONS, permissions);
        intent.putExtra(EXTRA_PERMISSION_DESC, permissionDesc);
        intent.putExtras(extras);
        if (!(context instanceof Activity)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        context.startActivity(intent);
    }

    private Bundle extras;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);//设置1像素
        if (getIntent() == null) {
            finish();
            return;
        }
        setContentView(R.layout.activity_permission);
        String[] permissions = getIntent().getStringArrayExtra(EXTRA_PERMISSIONS);
        String permissionDesc = getIntent().getStringExtra(EXTRA_PERMISSION_DESC);
        int requestCode = getIntent().getIntExtra(EXTRA_PERMISSION_REQ_CODE, -1);
        extras = getIntent().getExtras();
        if (permissions == null || permissions.length == 0 || requestCode < 0) {
            finish();
            return;
        }
        boolean isInstallPackagePer = false;
        for (String permission : permissions) {
            if (TextUtils.equals(permission, Manifest.permission.REQUEST_INSTALL_PACKAGES)) {
                isInstallPackagePer = true;
                break;
            }
        }
        if (isInstallPackagePer) {
            showToSettingDialog();
        } else {
            requestPermissions(permissions, requestCode, permissionDesc == null ? "" : permissionDesc);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case REQUEST_PERMISSIONS_INSTALL_PACKAGE: {
                if (grantResults != null && grantResults[0] == PackageManager.PERMISSION_GRANTED) {//获取到权限
                    if (extras != null && TextUtils.isEmpty(extras.getString("filePath"))) {
                        installApkImpl(this, extras.getString("filePath"));
                    }

                } else {
                    ToastUtils.showLongMsg(this, "安装应用需要打开未知来源权限，请去设置中开启权限!");
                }
            }
            break;
        }
        finish();
    }

    private void showToSettingDialog() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            final ConfirmDialog dialog = ConfirmDialog.getInstance(this);
            dialog.setDialogTitle("安装权限");
            dialog.setDialogContent("需要打开允许来自此来源，请去设置中开启此权限");
            dialog.setPositiveText("同意");
            dialog.setNavigationText("取消");
            dialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    try {
                        dialog.dismiss();
                    } catch (Exception e) {
                        dialog.dismiss();
                    }
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    toInstallPermissionSettingIntent();
                    dialog.dismiss();
                }
            });
            dialog.show();
        } else {
            ToastUtils.showLongMsg(this, "安装应用需要打开未知来源权限，请去设置中开启权限!");
        }

    }

    /**
     * 开启安装未知来源权限
     */
    @RequiresApi(api = Build.VERSION_CODES.O)
    private void toInstallPermissionSettingIntent() {
        Uri packageURI = Uri.parse("package:" + getPackageName());
        Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES, packageURI);
        startActivityForResult(intent, INSTALL_PERMISS_CODE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == INSTALL_PERMISS_CODE) {
            if (extras != null && TextUtils.isEmpty(extras.getString("filePath"))) {
                installApkImpl(this, extras.getString("filePath"));
            }
        }
    }


    private void installApkImpl(Context context, String filePath) {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            String fileAbosPath = "";
            if (filePath != null && filePath.startsWith("file://")) {
                fileAbosPath = filePath;
            } else {
                fileAbosPath = "file://" + filePath;
            }
            String fileName = fileAbosPath.substring(fileAbosPath.lastIndexOf("/") + 1, fileAbosPath.length());
            File apkFile = new File(MyPathManager.getSaveDir(PublicParams.CACHE_DIR_APK) + fileName);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            Uri contentUri = FileUtils.getUriForFile(context, apkFile);
            intent.setDataAndType(contentUri, "application/vnd.android.package-archive");
            context.startActivity(intent);
        } catch (Exception e) {
            LogUtils.i(DownloadManagerReceiver.class, "安装失败");
            e.printStackTrace();
        }
    }
}
