package com.doctor.br.activity.mine.agent.doctorState;

import android.content.Intent;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.AdapterView;

import com.doctor.br.activity.mine.agent.DoctorDetailsActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.AgentNoPatientDoctorRecyclerAdapter;
import com.doctor.br.bean.AgentNoPatientDoctorBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.PopUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：经纪人-大夫状态-已认证大夫界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/9/28 17:37
 * 修改人：ShiShaoPo
 * 修改时间：2018/3/12 17:37
 */

public class AgentQualificationedActivity extends ActionBarActivity implements RecyclerItemClickListener {
    private final String ALL = "0";
    private final String THIS_MONTH = "1";
    private final String LAST_MONTH = "2";
    //界面下的控件
    @BindView(R.id.doctor_recycler)
    RecyclerView doctorRecycler;
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private PopUtils bottomPop;//底部popwindow

    private int currentPage = 1;//当前显示页
    private int totalPage = -1;//总页码
    private RequestCallBack doctorListCallBack;//网络请求大夫列表回调

    private List<AgentNoPatientDoctorBean.DoctorMessagesBean> list;//大夫列表
    private AgentNoPatientDoctorRecyclerAdapter adapter;//大夫列表适配器

    private String currentType = ALL;//当前显示类型

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_my_doctor);
        initView();
        initPop();
        getDoctorListRequest(ALL, "1");
    }

    private void initView() {
        setActionBarTitle("已认证大夫");
        getBaseActionBar().setRightButton("全部");
        getBaseActionBar().setRightButtonColor(ContextCompat.getColor(this, R.color.br_color_theme));

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                getDoctorListRequest(currentType, "1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        mLoadingDialog = null;
                        getDoctorListRequest(currentType, currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        bgaRefreshLayout.setRefreshViewHolder(new BGANormalRefreshViewHolder(this, true));

        list = new ArrayList<>();
        adapter = new AgentNoPatientDoctorRecyclerAdapter(this, list, this);
        doctorRecycler.setAdapter(adapter);
    }

    private void initPop() {
        final String[] strings = {"全部", "本月", "上月"};
        bottomPop = new PopUtils(this, R.string.select_month, Arrays.asList(strings), new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                String[] types = {ALL, THIS_MONTH, LAST_MONTH};
                getDoctorListRequest(types[position], "1");
                bottomPop.dismiss();
            }
        });
    }

    @Override
    public void onRightBtnClick(View view) {
        bottomPop.showPopupWindow();
    }

    @Override
    public void itemClick(int position) {
        Intent intent = new Intent(this, DoctorDetailsActivity.class);
        intent.putExtra(DoctorDetailsActivity.DOCTOR_ID, list.get(position).getDoctorId());
        startActivity(intent);
    }

    /**
     * 网络请求获取已认证大夫列表
     *
     * @param type 请求类型 0 全部 1 本月 2 上月
     * @param page 想要请求的页码
     */
    private void getDoctorListRequest(String type, String page) {
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
        map.put("page", page);
        map.put("type", type);
        map.put("pageSize", "20");
//        map.put("userId", "2928");//测试使用
        doctorListCallBack = addHttpPostRequest(HttpUrlManager.QUALIFICATIONED, map, AgentNoPatientDoctorBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (bgaRefreshLayout == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.QUALIFICATIONED:
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    AgentNoPatientDoctorBean doctorBean = (AgentNoPatientDoctorBean) result.getBodyObject();

                    currentType = doctorBean.getType();
                    if (THIS_MONTH.equals(currentType)) {
                        getBaseActionBar().setRightButton("本月");
                    } else if (LAST_MONTH.equals(currentType)) {
                        getBaseActionBar().setRightButton("上月");
                    } else {
                        getBaseActionBar().setRightButton("全部");
                    }
                    currentPage = doctorBean.getPage();
                    totalPage = doctorBean.getTotalPage();

                    if (currentPage == 1) {
                        list.clear();
                    }
                    list.addAll(doctorBean.getDoctorMessages());
                    adapter.notifyDataSetChanged();
                    if (currentPage == 1 && list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setEmptyText("暂无大夫");
                        emptyView.setVisibility(View.VISIBLE);
                    }
                } else {
                    if (totalPage == -1) {
                        emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                        emptyView.setVisibility(View.VISIBLE);
                        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                            @Override
                            public void onReload() {
                                getDoctorListRequest(currentType, "1");
                            }
                        });
                    }
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (doctorListCallBack != null) {
            doctorListCallBack.cancel();
        }
        super.onDestroy();
    }
}
