package com.doctor.br.activity.mine.agent.framework;

import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;

import com.doctor.br.activity.mine.agent.AgentActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.mine.AgentFrameworkDispatchRecyclerAdapter;
import com.doctor.br.adapter.mine.FrameworkRecyclerAdapter;
import com.doctor.br.bean.AgentFrameworkBean;
import com.doctor.br.bean.event.FinishAgentFrameworkSerchActivityEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：组织架构调度界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/16
 */

public class FrameworkDispatchActivity extends ActionBarActivity implements RecyclerItemClickListener, RecyclerItemItemClickListener {
    //上个界面传递过来的数据
    public static List<AgentFrameworkBean.DataBean> list;
    public static final String AREA_CODE = "areaCode";
    public static final String AREA_NAME = "areaName";
    public static final String TEAM_ID = "teamId";
    public static final String TEAM_NAME = "teamName";
    public static final String TEAM_TYPE = "teamType";
    public static final String ROLE = "role";
    public static final String USER_ID = "userId";
    private String userId;//被调度人id
    private String preAreaCode;//被调度人之前的areacode
    private String preAreaName;//被调度人之前的区域名称
    private String preTeamId;//之前所属团队id
    private String preTeamName;//之前所属团队名称
    private String preTeamType;//之前所属团队类型
    private String preRole;//之前职位
    //界面下的常量
    public static final String SELECT_ROLE = "选择职位";
    public static final String HEADER_ID = "-111";
    private final String SELCT_TEAM = "选择团队";
    private final String ROLE_PROVINCE = "省负责人";
    private final String ROLE_TEAM = "团队负责人";
    private final String ROLE_NORMAL = "团队职员";
    //界面下的控件
    @BindView(R.id.left_recycler)
    RecyclerView leftRecycler;
    @BindView(R.id.right_recycler)
    RecyclerView rightRecycler;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private FrameworkRecyclerAdapter leftAdapter;//左侧适配器
    private AgentFrameworkDispatchRecyclerAdapter rightAdapter;//右侧适配器

    private List<AgentFrameworkBean.DataBean.TeamListBean> rightList;

    private RequestCallBack callBack;//网络请求组织架构回调
    private RequestCallBack dispatchCallBack;//网络请求调度回调

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_framework_dispatch);
        getIntentData(savedInstanceState);
        initView();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(USER_ID, userId);
        outState.putString(AREA_CODE, preAreaCode);
        outState.putString(AREA_NAME, preAreaName);
        outState.putString(TEAM_ID, preTeamId);
        outState.putString(TEAM_NAME, preTeamName);
        outState.putString(TEAM_TYPE, preTeamType);
        outState.putString(ROLE, preRole);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            userId = savedInstanceState.getString(USER_ID);
            preAreaCode = savedInstanceState.getString(AREA_CODE);
            preAreaName = savedInstanceState.getString(AREA_NAME);
            preTeamId = savedInstanceState.getString(TEAM_ID);
            preTeamName = savedInstanceState.getString(TEAM_NAME);
            preTeamType = savedInstanceState.getString(TEAM_TYPE);
            preRole = savedInstanceState.getString(ROLE);
        } else {
            userId = getIntent().getStringExtra(USER_ID);
            preAreaCode = getIntent().getStringExtra(AREA_CODE);
            preAreaName = getIntent().getStringExtra(AREA_NAME);
            preTeamId = getIntent().getStringExtra(TEAM_ID);
            preTeamName = getIntent().getStringExtra(TEAM_NAME);
            preTeamType = getIntent().getStringExtra(TEAM_TYPE);
            preRole = getIntent().getStringExtra(ROLE);
        }
        if (list == null || list.size() == 0 || TextUtils.isEmpty(userId)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }

    private void initView() {
        setActionBarTitle("调度");
        setActionBarRightBtnText("保存");
        setActionBarRightBtnColor(ContextCompat.getColor(this, R.color.br_color_theme));

        leftRecycler.setHasFixedSize(true);
        leftAdapter = new FrameworkRecyclerAdapter(this, list, this);
        leftRecycler.setAdapter(leftAdapter);

        final GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 2, LinearLayoutManager.VERTICAL, false);
        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                if (rightList == null) {
                    return 1;
                }
                if (HEADER_ID.equals(rightList.get(position).getTeamId())) {
                    return gridLayoutManager.getSpanCount();
                }
                return 1;
            }
        });
        rightRecycler.setLayoutManager(gridLayoutManager);

        for (int i = 0; i < list.size(); i++) {
            AgentFrameworkBean.DataBean dataBean = list.get(i);
            if (dataBean.getAreaCode().equals(preAreaCode)) {
                leftPosition = i;
                //判断之前是否网络请求过，如果请求过直接显示，如果没有请求网络请求之后在显示
                if (dataBean.isNet()) {
                    addList(dataBean.getTeamList());
                } else {
                    getFrameworkRequest(dataBean.getAreaCode());
                }
                break;
            }
        }
    }

    @Override
    public void onRightBtnClick(View view) {
        int firstTeamPos = 1;
        int lastTeamPos = rightList.indexOf(new AgentFrameworkBean.DataBean.TeamListBean(SELECT_ROLE, HEADER_ID)) - 1;
        int firstRolePos = rightList.indexOf(new AgentFrameworkBean.DataBean.TeamListBean(SELECT_ROLE, HEADER_ID)) + 1;
        int lastRolePos = rightList.size() - 1;
        int teamPosition = -1;//点击的团队位置
        int rolePosition = -1;//点击的职位位置
        //获取团队
        for (int i = firstTeamPos; i <= lastTeamPos; i++) {
            if (rightList.get(i).isSelected()) {
                teamPosition = i;
                break;
            }
        }
        //获取职位
        for (int i = firstRolePos; i <= lastRolePos; i++) {
            if (rightList.get(i).isSelected()) {
                rolePosition = i;
                break;
            }
        }
        if (teamPosition == -1 && rolePosition == -1) {
            //如果没有选择团队和职位的情况下
            ToastUtils.showShortMsg(this, "请选择团队和职位");
            return;
        }
        if (rolePosition == -1) {
            //没有选择职位
            ToastUtils.showShortMsg(this, "请选择职位");
            return;
        }
        if (teamPosition == -1 && !ROLE_PROVINCE.equals(rightList.get(rolePosition).getTeamName())) {
            //没有选择团队 并且 职位选择的不是省负责人的情况下
            ToastUtils.showShortMsg(this, "请选择团队");
            return;
        }
        //获取想要调度的teamName，teamId，teamType
        String teamName = null;
        String teamId = null;
        String teamType = null;
        if (teamPosition != -1) {
            teamName = rightList.get(teamPosition).getTeamName();
            teamId = rightList.get(teamPosition).getTeamId();
            teamType = rightList.get(teamPosition).getTeamType();
        }
        //获取想要调度的身份teamRole
        String role = AgentActivity.ROLE_PERSON;
        switch (rightList.get(rolePosition).getTeamName()) {
            case ROLE_PROVINCE://省负责人
                role = AgentActivity.ROLE_PROVINCE;
                //省负责人肯定为直营
                teamType = "1";
                break;
            case ROLE_TEAM://团队负责人
                if (AgentActivity.TEAM_TYPE_MINE.equals(rightList.get(teamPosition).getTeamType())) {
                    //直营团队
                    role = AgentActivity.ROLE_MINE_TEAM;
                } else if (AgentActivity.TEAM_TYPE_PART.equals(rightList.get(teamPosition).getTeamType())) {
                    //兼职团队
                    role = AgentActivity.ROLE_PART_TEAM;
                }
                break;
            case ROLE_NORMAL://团队职员
                if (AgentActivity.TEAM_TYPE_MINE.equals(rightList.get(teamPosition).getTeamType())) {
                    //直营团队
                    role = AgentActivity.ROLE_MINE;
                } else if (AgentActivity.TEAM_TYPE_PART.equals(rightList.get(teamPosition).getTeamType())) {
                    //兼职团队
                    role = AgentActivity.ROLE_PART;
                } else if (AgentActivity.TEAM_TYPE_PERSON.equals(rightList.get(teamPosition).getTeamType())) {
                    //个人兼职团队
                    role = AgentActivity.ROLE_PERSON;
                }
                break;
            default:
                break;
        }
        dispatchRequest(userId, list.get(leftPosition).getAreaCode(), list.get(leftPosition).getAreaName(), teamName,
                teamId, teamType, role, preAreaCode, preAreaName, preTeamId, preTeamName, preTeamType, preRole);
    }

    private int leftPosition = -1;//当前点击的左侧position

    //左侧recyclerview的item点击事件
    @Override
    public void itemClick(int position) {
        leftPosition = position;
        for (int i = 0; i < list.size(); i++) {
            AgentFrameworkBean.DataBean dataBean = list.get(i);
            if (i == position) {
                dataBean.setDispatchSelect(true);
                //判断之前是否网络请求过，如果请求过直接显示，如果没有请求网络请求之后在显示
                if (dataBean.isNet()) {
                    addList(dataBean.getTeamList());
                } else {
                    getFrameworkRequest(dataBean.getAreaCode());
                }
            } else {
                dataBean.setDispatchSelect(false);
            }
        }
        leftAdapter.notifyDataSetChanged();
    }

    //右侧recyclerview的item点击事件
    @Override
    public void itemViewClick(int position, View view) {
        //获取第一个团队的位置
        int firstTeamPos = 1;
        //获取最后一个团队的位置
        int lastTeamPos = rightList.indexOf(new AgentFrameworkBean.DataBean.TeamListBean(SELECT_ROLE, HEADER_ID)) - 1;
        //如果点击的是团队的位置的话，先把省负责人和团队负责人职位添加上去
        if (position >= firstTeamPos && position <= lastTeamPos) {
            if (!rightList.contains(new AgentFrameworkBean.DataBean.TeamListBean(ROLE_TEAM))) {
                rightList.add(rightList.size() - 1, new AgentFrameworkBean.DataBean.TeamListBean(ROLE_TEAM));
            }
            if (!rightList.contains(new AgentFrameworkBean.DataBean.TeamListBean(ROLE_PROVINCE))) {
                rightList.add(rightList.size() - 2, new AgentFrameworkBean.DataBean.TeamListBean(ROLE_PROVINCE));
            }
        }
        //获取第一个职位的位置
        int firstRolePos = rightList.indexOf(new AgentFrameworkBean.DataBean.TeamListBean(SELECT_ROLE, HEADER_ID)) + 1;
        //获取最后一个职位的位置
        int lastRolePos = rightList.size() - 1;
        if (rightList.get(position).isSelected()) {
            //如果为点击状态，则复位
            rightList.get(position).setSelected(false);
            rightAdapter.notifyDataSetChanged();
            return;
        }

        if (position >= firstTeamPos && position <= lastTeamPos) {
            int teamPosition = -1;
            //点击的为团队item
            //这里只循环团队所在位置
            for (int i = firstTeamPos; i <= lastTeamPos; i++) {
                if (i == position) {
                    teamPosition = i;
                    rightList.get(i).setSelected(true);
                } else {
                    rightList.get(i).setSelected(false);
                }
            }
            //职位清空
            for (int i = firstRolePos; i <= lastRolePos; i++) {
                rightList.get(i).setSelected(false);
            }
            //如果点击的为非直营团队，则移除省负责人
            if (teamPosition != -1 &&
                    !AgentActivity.TEAM_TYPE_MINE.equals(rightList.get(teamPosition).getTeamType())) {
                rightList.remove(new AgentFrameworkBean.DataBean.TeamListBean(ROLE_PROVINCE));
            }
            //如果点击的为个人兼职团队，则移除团队负责人
            if (teamPosition != -1 && AgentActivity.TEAM_TYPE_PERSON.equals(rightList.get(teamPosition).getTeamType())) {
                rightList.remove(new AgentFrameworkBean.DataBean.TeamListBean(ROLE_TEAM));
            }
        } else {
            //点击的为职位item
            for (int i = firstRolePos; i <= lastRolePos; i++) {
                if (i == position) {
                    rightList.get(i).setSelected(true);
                } else {
                    rightList.get(i).setSelected(false);
                }
            }
        }
        rightAdapter.notifyDataSetChanged();
    }

    /**
     * 刷新rightList数据
     *
     * @param teamList 团队列表
     */
    private void addList(List<AgentFrameworkBean.DataBean.TeamListBean> teamList) {
        rightList = new ArrayList<>();
        //为区分自建,自建header
        rightList.add(new AgentFrameworkBean.DataBean.TeamListBean(SELCT_TEAM, HEADER_ID));
        if (teamList != null) {
            rightList.addAll(teamList);
        }
        //自建header
        rightList.add(new AgentFrameworkBean.DataBean.TeamListBean(SELECT_ROLE, HEADER_ID));
        //添加职位
        rightList.add(new AgentFrameworkBean.DataBean.TeamListBean(ROLE_PROVINCE));
        rightList.add(new AgentFrameworkBean.DataBean.TeamListBean(ROLE_TEAM));
        rightList.add(new AgentFrameworkBean.DataBean.TeamListBean(ROLE_NORMAL));
        //根据团队item是否选中更新职位条数
        //获取第一个团队的位置
        int firstTeamPos = 1;
        //获取最后一个团队的位置
        int lastTeamPos = rightList.indexOf(new AgentFrameworkBean.DataBean.TeamListBean(SELECT_ROLE, HEADER_ID)) - 1;
        //这里只循环团队所在位置
        for (int i = firstTeamPos; i <= lastTeamPos; i++) {
            if (rightList.get(i).isSelected()) {
                //如果点击的为非直营团队，则移除省负责人
                if (!AgentActivity.TEAM_TYPE_MINE.equals(rightList.get(i).getTeamType())) {
                    rightList.remove(new AgentFrameworkBean.DataBean.TeamListBean(ROLE_PROVINCE));
                }
                //如果点击的为个人兼职团队，则移除团队负责人
                if (AgentActivity.TEAM_TYPE_PERSON.equals(rightList.get(i).getTeamType())) {
                    rightList.remove(new AgentFrameworkBean.DataBean.TeamListBean(ROLE_TEAM));
                }
                break;
            }
        }

        rightAdapter = new AgentFrameworkDispatchRecyclerAdapter(this, rightList, this);
        rightRecycler.setAdapter(rightAdapter);
    }

    /**
     * 网络请求获取组织架构
     *
     * @param areaCode 想要请求的省编码，非必须
     */
    private void getFrameworkRequest(String areaCode) {
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
        map.put("areaCode", areaCode);
//        map.put("userId", "2928");//测试使用
        callBack = addHttpPostRequest(HttpUrlManager.FRAMEWORK, map, AgentFrameworkBean.class, this);
    }

    /**
     * 网络请求申请调度（总助）
     *
     * @param targetUserId   要调度经纪人id
     * @param targetAreaCode 目标区域code
     * @param targetAreaName 目标区域名称
     * @param targetTeamName 目标团队名称
     * @param targetTeamId   目标团队id
     * @param targetTeamType 目标团队类型
     * @param targetRole     目标团队职位
     * @param preAreaCode    之前区域code
     * @param preAreaName    之前区域名称
     * @param preTeamId      之前团队id
     * @param preTeamName    之前团队名称
     * @param preTeamType    之前团队类型
     * @param preRole        之前团队职位
     */
    private void dispatchRequest(String targetUserId, String targetAreaCode, String targetAreaName, String targetTeamName,
                                 String targetTeamId, String targetTeamType, String targetRole,
                                 String preAreaCode, String preAreaName, String preTeamId,
                                 String preTeamName, String preTeamType, String preRole) {
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        map.put("targetUserId", targetUserId);
        map.put("targetAreaCode", targetAreaCode);
        map.put("targetAreaName", targetAreaName);
        map.put("targetTeamName", targetTeamName);
        map.put("targetTeamId", targetTeamId);
        map.put("targetTeamType", targetTeamType);
        map.put("targetRole", targetRole);
        map.put("preAreaCode", preAreaCode);
        map.put("preAreaName", preAreaName);
        map.put("preTeamId", preTeamId);
        map.put("preTeamName", preTeamName);
        map.put("preTeamType", preTeamType);
        map.put("preRole", preRole);
        dispatchCallBack = addHttpPostRequest(HttpUrlManager.DISPATCH, map, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (emptyView == null) {
            return;
        }
        switch (taskId) {
            //网络请求组织架构返回值
            case HttpUrlManager.FRAMEWORK:
                if (result.isRequestSuccessed()) {
                    AgentFrameworkBean netBean = (AgentFrameworkBean) result.getBodyObject();
                    //能走到这里说明是每个省的数据
                    for (AgentFrameworkBean.DataBean dataBean : list) {
                        if (dataBean.getAreaCode().equals(netBean.getData().get(0).getAreaCode())) {
                            dataBean.setTeamList(netBean.getData().get(0).getTeamList());
                            dataBean.setNet(true);
                            dataBean.setLeader(netBean.getData().get(0).getLeader());
                            addList(dataBean.getTeamList());
                            break;
                        }
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            getFrameworkRequest(list.get(leftPosition).getAreaCode());
                        }
                    });
                    emptyView.setVisibility(View.VISIBLE);
                }
                break;
            //网络请求申请调度返回值
            case HttpUrlManager.DISPATCH:
                if (result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "提交成功");
                    finishSearchActivity();
//                    new Handler().postDelayed(new Runnable() {
//                        @Override
//                        public void run() {
                    finish();
//                        }
//                    }, 1000);
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 关闭经纪人组织架构搜索界面并刷新组织架构界面数据
     *
     * @sender {@link FrameworkDispatchActivity#finishSearchActivity()}
     * @receive {@link FrameworkSearchActivity#finishThis(FinishAgentFrameworkSerchActivityEvent)}
     * @receive {@link FrameworkActivity#refreshData(FinishAgentFrameworkSerchActivityEvent)}
     */
    private void finishSearchActivity() {
        EventBusUtils.post(new FinishAgentFrameworkSerchActivityEvent(true, preAreaCode));
    }

    @Override
    protected void onDestroy() {
        if (callBack != null) {
            callBack.cancel();
        }
        if (dispatchCallBack != null) {
            dispatchCallBack.cancel();
        }
        super.onDestroy();
    }


}
