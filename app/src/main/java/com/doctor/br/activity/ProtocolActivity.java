package com.doctor.br.activity;

import android.app.Activity;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.doctor.br.bean.ProtocolBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.HashMap;
import java.util.Map;

/**
 * 类描述：协议内容
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/11 9:50
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/11 9:50
 */

public class ProtocolActivity extends ActionBarActivity {
    //上个界面发送过来的数据
    public static final String INVITATION_CODE = "invitationCode";
    private String invitationCode;//邀请码，非必须，默认为医生协议
    //界面下的控件
    private WebView webView;
    private EmptyView emptyView;

    private boolean isLoadError;

    private RequestCallBack agreementCallBack;//协议请求回调

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_protocol);
        getIntentData(savedInstanceState);
        initView();
        loadData();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(INVITATION_CODE, invitationCode);
        outState.putBoolean("isLoadError", isLoadError);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState == null) {
            invitationCode = getIntent().getStringExtra(INVITATION_CODE);
        } else {
            invitationCode = savedInstanceState.getString(INVITATION_CODE);
            isLoadError = savedInstanceState.getBoolean("isLoadError");
        }
    }

    private void initView() {
        setActionBarTitle(R.string.protocol);
        getBaseActionBar().setActionBarChangeListener(this);

        webView = (WebView) findViewById(R.id.web_view);
        emptyView = (EmptyView) findViewById(R.id.empty_view);

        WebSettings settings = webView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setAppCacheEnabled(true);
        settings.setBuiltInZoomControls(true);
        settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setSaveFormData(true);
        settings.setGeolocationEnabled(true);
        settings.setDomStorageEnabled(true);
        //不显示webview缩放按钮
        settings.setDisplayZoomControls(false);
        //不支持屏幕缩放
        settings.setSupportZoom(false);
        settings.setBuiltInZoomControls(false);
        webView.requestFocus();
        webView.addJavascriptInterface(this, "window");
        webView.setWebViewClient(new WebViewClient() {

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    view.loadUrl(request.getUrl().toString());
                }
                return super.shouldOverrideUrlLoading(view, request);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                isLoadError = true;
                emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                isLoadError = true;
                emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                if (isLoadError) {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                } else {
                    emptyView.hide();
                }
            }
        });

        webView.setWebChromeClient(new WebChromeClient() {


            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
                if (!TextUtils.isEmpty(title) && title.toLowerCase().contains("error")) {
                    isLoadError = true;
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                } else {
                    isLoadError = false;
                }
            }
        });

        webView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                return true;
            }
        });
    }

    private void loadData() {
        if (TextUtils.isEmpty(invitationCode)) {
            webView.loadUrl(BaseConfig.DOCTOR_AGREEMENT);
        } else {
            getAgreement(invitationCode);
        }
    }

    /**
     * 网络请求获取协议地址，根据邀请码的不同，协议不同
     *
     * @param invitationCode 邀请码
     */
    private void getAgreement(String invitationCode) {
        mLoadingDialog = null;
        Map<String, String> map = new HashMap<>();
        map.put("invitationNo", invitationCode);
        agreementCallBack = addHttpPostRequest(HttpUrlManager.REGISTER_AGREEMENT, map, ProtocolBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if(webView==null){
            return;
        }
        switch (taskId) {
            case HttpUrlManager.REGISTER_AGREEMENT://根据邀请码返回协议类型
                if (result.isRequestSuccessed()) {
                    ProtocolBean protocolBean = (ProtocolBean) result.getBodyObject();
                    if ("1".equals(protocolBean.getState())) {
                        webView.loadUrl(BaseConfig.DITUI_AGREEMENT);
                        return;
                    }
//                    webView.loadUrl(BaseConfig.DOCTOR_AGREEMENT);
                }
//                else {
//                    RequestErrorToast.showError(result.getCode(), result.getErrorMsg());
                webView.loadUrl(BaseConfig.DOCTOR_AGREEMENT);
//                }
                break;
            default:
                break;
        }
    }


    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        if (agreementCallBack != null) {
            agreementCallBack.cancel();
        }
        super.onDestroy();
    }
}
