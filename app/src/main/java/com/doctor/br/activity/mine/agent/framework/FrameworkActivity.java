package com.doctor.br.activity.mine.agent.framework;

import android.content.Intent;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.TextAppearanceSpan;
import android.view.View;
import android.widget.ExpandableListView;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.FrameworkExpandListAdapter;
import com.doctor.br.adapter.mine.FrameworkRecyclerAdapter;
import com.doctor.br.bean.AgentFrameworkBean;
import com.doctor.br.bean.event.FinishAgentFrameworkSerchActivityEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.view.AgentAddTeamDialog;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.EmptyView;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_ASSISTANT;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_INSPECTOR;

/**
 * 类描述：组织架构
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/9
 */

public class FrameworkActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //上个界面传递过来的数据
    public static final String IS_DITUI = "isDitui";
    private String isDitui;//经纪人身份
    //界面中的常量
    public static final String STATUS_DISPATCH = "0";//可以调度
    public static final String STATUS_DISPATCHING = "1";//调度中
    //界面下的控件
    @BindView(R.id.left_recycler)
    RecyclerView leftRecycler;
    @BindView(R.id.right_expand_list_view)
    ExpandableListView rightListView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;
    @BindView(R.id.add_team_btn)
    com.doctor.br.view.NoDoubleClickBtn addTeamBtn;

    public static boolean isShow = false;//是否显示调度按钮

    private FrameworkRecyclerAdapter frameworkRecyclerAdapter;//左边省列表适配器

    private RequestCallBack callBack;//网络请求组织架构回调
    private RequestCallBack addTeamCallBack;//网络请求新增团队回调

    private List<AgentFrameworkBean.DataBean> list;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_framework);
        EventBusUtils.register(this);
        getIntentData(savedInstanceState);
        initView();
        addHeader();
        getFrameworkRequest(null);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(IS_DITUI, isDitui);
    }


    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            isDitui = savedInstanceState.getString(IS_DITUI);
        } else {
            isDitui = getIntent().getStringExtra(IS_DITUI);
        }
        if (!ROLE_ASSISTANT.equals(isDitui) && !ROLE_INSPECTOR.equals(isDitui)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
        isShow = ROLE_ASSISTANT.equals(isDitui);
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("组织架构");
        getBaseActionBar().setRightButtonImg(R.drawable.search);
        getBaseActionBar().setActionBarChangeListener(this);

        emptyView.setEmptyBackgroundColor(ContextCompat.getColor(this, R.color.br_color_white));
        if (isShow) {
            //只有总助才可以新增团队
            addTeamBtn.setOnClickListener(this);
            addTeamBtn.setVisibility(View.VISIBLE);
        } else {
            addTeamBtn.setVisibility(View.GONE);
        }
        list = new ArrayList<>();
        frameworkRecyclerAdapter = new FrameworkRecyclerAdapter(this, list, this);
        leftRecycler.setAdapter(frameworkRecyclerAdapter);

        rightListView.setDividerHeight(0);
        rightListView.setOnGroupClickListener(new ExpandableListView.OnGroupClickListener() {
            @Override
            public boolean onGroupClick(ExpandableListView parent, View v, int groupPosition, long id) {
                ImageView imageView = (ImageView) v.findViewById(R.id.arrow_img);
                if (rightListView.isGroupExpanded(groupPosition)) {
                    imageView.setRotation(0);
                } else {
                    imageView.setRotation(180);
                }
                return false;
            }
        });
    }

    //右侧list headerview中的控件
    private RelativeLayout containerView;
    private ShapeImageView headImg;
    private TextView nameTv, addressTv, phoneTv;
    private com.doctor.br.view.NoDoubleClickBtn dispatchBtn;

    //为右侧listview添加headerview
    private void addHeader() {
        View headerView = View.inflate(this, R.layout.item_framework_right_list_item, null);
//        View headerView = LayoutInflater.from(this).inflate(R.layout.item_framework_right_list_item, rightListView, false);//这种方法不会使布局自适应
        containerView = (RelativeLayout) headerView.findViewById(R.id.container_linear);
        headImg = (ShapeImageView) headerView.findViewById(R.id.head_img);
        nameTv = (TextView) headerView.findViewById(R.id.name_tv);
        addressTv = (TextView) headerView.findViewById(R.id.address_tv);
        phoneTv = (TextView) headerView.findViewById(R.id.phone_tv);
        dispatchBtn = (com.doctor.br.view.NoDoubleClickBtn) headerView.findViewById(R.id.dispatch_btn);
        if (rightListView.getHeaderViewsCount() == 0) {
            rightListView.addHeaderView(headerView);
        }
        rightListView.setHeaderDividersEnabled(false);
    }

    //搜索
    @Override
    public void onRightBtnClick(View view) {
        if (list.size() == 0) {
            getFrameworkRequest(null);
            return;
        }
        FrameworkDispatchActivity.list = list;
        startActivity(new Intent(this, FrameworkSearchActivity.class));
    }

    private AgentAddTeamDialog addTeamDialog;//添加团队对话框

    @Override
    public void onClick(View view) {
        if (list.size() == 0) {
            getFrameworkRequest(null);
            return;
        }
        switch (view.getId()) {
            case R.id.add_team_btn://新增团队
                if (!isShow) {
                    //只有总助可以新增团队
                    return;
                }
                addTeamDialog = new AgentAddTeamDialog(this);
                addTeamDialog.setConfirmListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (addTeamDialog.getPos() == 0) {
                            ToastUtils.showShortMsg(FrameworkActivity.this, "请选择团队类型");
                            return;
                        }
                        if (TextUtils.isEmpty(addTeamDialog.getEditTextStr().toString().trim())) {
                            ToastUtils.showShortMsg(FrameworkActivity.this, "请输入团队名称");
                            return;
                        }
                        String areaCode = list.get(clickPosition).getAreaCode();
                        String teamName = addTeamDialog.getEditTextStr().toString().trim();
                        addTeamRequest(areaCode, teamName, addTeamDialog.getPos() + "");
                    }
                });
                addTeamDialog.show();
                break;
            default:
                break;
        }
    }

    private int clickPosition = 0;//当前点击的左侧position

    //点击左边省列表的监听
    @Override
    public void itemClick(int position) {
        clickPosition = position;
        for (int i = 0; i < list.size(); i++) {
            AgentFrameworkBean.DataBean dataBean = list.get(i);
            if (i == position) {
                dataBean.setFrameSelected(true);
                //判断之前是否网络请求过，如果请求过直接显示，如果没有请求网络请求之后在显示
                if (dataBean.isNet()) {
                    setAdapter(dataBean.getTeamList());
                    if (dataBean.getTeamList().size() > 0 || dataBean.getLeader().size() > 0) {
                        emptyView.setVisibility(View.GONE);
                        rightListView.setVisibility(View.VISIBLE);
                    } else {
                        emptyView.setVisibility(View.VISIBLE);
                        rightListView.setVisibility(View.GONE);
                    }
                } else {
                    getFrameworkRequest(dataBean.getAreaCode());
                }
            } else {
                dataBean.setFrameSelected(false);
            }
        }
        frameworkRecyclerAdapter.notifyDataSetChanged();
    }


    private void setAdapter(List<AgentFrameworkBean.DataBean.TeamListBean> teamList) {
//        LogUtils.i(FrameworkActivity.class, "= headerCount:" + rightListView.getHeaderViewsCount() + ",leader.size:" + list.get(clickPosition).getLeader().size());


        if (list.get(clickPosition).getLeader().size() > 0) {
            //如果有省负责人的话，添加headerview
            String name = list.get(clickPosition).getLeader().get(0).getUserName();
            String role = "（省负责人）";
            SpannableString spannableString = new SpannableString(name + role);
            TextAppearanceSpan textAppearanceSpan = new TextAppearanceSpan(this, R.style.FrameworkActivityTitleStyle);
            spannableString.setSpan(textAppearanceSpan, name.length(), spannableString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            nameTv.setText(spannableString);

            phoneTv.setText(list.get(clickPosition).getLeader().get(0).getUserMobile());
            GlideUtils.getInstance().loadImage(list.get(clickPosition).getLeader().get(0).getUserHeadImageUrl(), this, headImg, R.drawable.default_head_img);
            addressTv.setText(list.get(clickPosition).getLeader().get(0).getUserAreaName());
            if (isShow) {
                if (STATUS_DISPATCH.equals(list.get(clickPosition).getLeader().get(0).getUserStatus())) {
                    dispatchBtn.setBackgroundResource(R.drawable.framework_dispatch_img);
                } else if (STATUS_DISPATCHING.equals(list.get(clickPosition).getLeader().get(0).getUserStatus())) {
                    dispatchBtn.setBackgroundResource(R.drawable.framework_no_dispatch_img);
                }
                dispatchBtn.setVisibility(View.VISIBLE);
            } else {
                dispatchBtn.setVisibility(View.GONE);
            }
            dispatchBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (STATUS_DISPATCHING.equals(list.get(clickPosition).getLeader().get(0).getUserStatus())) {
                        return;
                    }
                    FrameworkDispatchActivity.list = list;
                    Intent intent = new Intent(FrameworkActivity.this, FrameworkDispatchActivity.class);

                    intent.putExtra(FrameworkDispatchActivity.AREA_NAME, list.get(clickPosition).getAreaName());
                    intent.putExtra(FrameworkDispatchActivity.TEAM_ID, list.get(clickPosition).getLeader().get(0).getTeamId());
                    intent.putExtra(FrameworkDispatchActivity.ROLE, list.get(clickPosition).getLeader().get(0).getUserRole());
                    intent.putExtra(FrameworkDispatchActivity.AREA_CODE, list.get(clickPosition).getAreaCode());
                    intent.putExtra(FrameworkDispatchActivity.USER_ID, list.get(clickPosition).getLeader().get(0).getUserId());
                    startActivity(intent);
                }
            });
            containerView.setVisibility(View.VISIBLE);
        } else {
            containerView.setVisibility(View.GONE);
        }

        FrameworkExpandListAdapter frameworkExpandListAdapter = new FrameworkExpandListAdapter(this, list, teamList);
        rightListView.setAdapter(frameworkExpandListAdapter);
//        for (int i = 0; i < frameworkExpandListAdapter.getGroupCount(); i++) {
//            rightListView.expandGroup(i);
//        }
    }

    /**
     * 网络请求获取组织架构
     *
     * @param areaCode 想要请求的省编码，非必须
     */
    private void getFrameworkRequest(String areaCode) {
        emptyView.setVisibility(View.GONE);
        rightListView.setVisibility(View.VISIBLE);
        Map<String, String> map = new HashMap<>();
        map.put("areaCode", areaCode);
//        map.put("userId", "2928");//测试使用
        callBack = addHttpPostRequest(HttpUrlManager.FRAMEWORK, map, AgentFrameworkBean.class, this);
    }

    /**
     * 网络请求新增团队
     *
     * @param areaCode   增加团队的省区域areaCode
     * @param teamName   新增团队名称
     * @param orgStrType 新增团队类型 1 直营 2 兼职
     */
    private void addTeamRequest(String areaCode, String teamName, String orgStrType) {
        Map<String, String> map = new HashMap<>();
        map.put("areaCode", areaCode);
        map.put("teamName", teamName);
        map.put("orgStrType", orgStrType);
//        map.put("userId", "2928");//测试使用
        addTeamCallBack = addHttpPostRequest(HttpUrlManager.ADD_TEAM, map, null, this);
    }
//    <<<<<<<<<测试，点击河北，搜索浙江人，调度完成后返回浙江条目

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (emptyView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.FRAMEWORK://组织架构
                if (result.isRequestSuccessed()) {
                    AgentFrameworkBean netBean = (AgentFrameworkBean) result.getBodyObject();
                    if (list.size() == 0) {
                        //如果为空说明是第一次请求
                        list.addAll(netBean.getData());
                        list.get(0).setFrameSelected(true);//默认选中第一个
                        list.get(0).setNet(true);
                        setAdapter(list.get(0).getTeamList());
                        if (list.get(0).getTeamList().size() == 0 && list.get(0).getLeader().size() == 0) {
                            //说明第一个省里面没有团队和省负责人
                            emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                            emptyView.setVisibility(View.VISIBLE);
                            rightListView.setVisibility(View.GONE);
                        }
                    } else {
                        //能走到这里说明是每个省的数据了，必须和分配页面保持一致
                        for (AgentFrameworkBean.DataBean dataBean : list) {
                            if (dataBean.getAreaCode().equals(netBean.getData().get(0).getAreaCode())) {
                                dataBean.setTeamList(netBean.getData().get(0).getTeamList());
                                dataBean.setNet(true);
                                dataBean.setLeader(netBean.getData().get(0).getLeader());
                                setAdapter(netBean.getData().get(0).getTeamList());
                                break;
                            }
                        }
                        //如果该省没有团队和省负责人，就显示空数据
                        if (netBean.getData().get(0).getTeamList().size() == 0 && netBean.getData().get(0).getLeader().size() == 0) {
                            emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                            emptyView.setVisibility(View.VISIBLE);
                            rightListView.setVisibility(View.GONE);

                        }
                    }
                    frameworkRecyclerAdapter.notifyDataSetChanged();
                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setVisibility(View.VISIBLE);
                    rightListView.setVisibility(View.GONE);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            if (list.size() == 0) {
                                getFrameworkRequest(null);
                            } else {
                                getFrameworkRequest(list.get(clickPosition).getAreaCode());
                            }
                        }
                    });
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.ADD_TEAM://添加团队返回值
                if (result.isRequestSuccessed()) {
                    if (addTeamDialog != null && addTeamDialog.isShowing()) {
                        addTeamDialog.dismiss();
                    }
                    ToastUtils.showShortMsg(this, "创建成功");
                    list.get(clickPosition).setNet(false);
                    itemClick(clickPosition);
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 关闭经纪人组织架构搜索界面并刷新组织架构界面数据
     *
     * @sender {@link FrameworkDispatchActivity#finishSearchActivity()}
     * @receive {@link FrameworkSearchActivity#finishThis(FinishAgentFrameworkSerchActivityEvent)}
     * @receive {@link FrameworkActivity#refreshData(FinishAgentFrameworkSerchActivityEvent)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshData(FinishAgentFrameworkSerchActivityEvent finishAgentFrameworkSerchActivityEvent) {
        if (finishAgentFrameworkSerchActivityEvent != null && finishAgentFrameworkSerchActivityEvent.isClose()) {
            getFrameworkRequest(finishAgentFrameworkSerchActivityEvent.getPreAreaCode());
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getAreaCode().equals(finishAgentFrameworkSerchActivityEvent.getPreAreaCode())) {
                    itemClick(i);
                    break;
                }
            }
//            list.get(clickPosition).setNet(false);
//            itemClick(clickPosition);
        }
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        FrameworkDispatchActivity.list = null;
        if (callBack != null) {
            callBack.cancel();
        }
        if (addTeamCallBack != null) {
            addTeamCallBack.cancel();
        }
        super.onDestroy();
    }


}
