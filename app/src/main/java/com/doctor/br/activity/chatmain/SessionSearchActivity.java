package com.doctor.br.activity.chatmain;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.AdapterView;
import android.widget.CheckBox;
import android.widget.ListView;
import android.widget.TextView;

import com.doctor.br.activity.SystemMsgListActivity;
import com.doctor.br.adapter.SessionAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.db.entity.Contacts;
import com.doctor.br.db.entity.Msg;
import com.doctor.br.db.entity.Session;
import com.doctor.greendao.gen.ContactsDao;
import com.doctor.greendao.gen.MsgDao;
import com.doctor.greendao.gen.SessionDao;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.greenrobot.greendao.async.AsyncOperation;
import org.greenrobot.greendao.async.AsyncOperationListener;
import org.greenrobot.greendao.async.AsyncSession;
import org.greenrobot.greendao.query.QueryBuilder;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.widgets.ActionBarView;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 搜索会话消息的页面
 * @createTime 2017/8/23
 */
public class SessionSearchActivity extends ActionBarActivity implements TextView.OnEditorActionListener, AdapterView.OnItemClickListener, TextWatcher {
    private final static int REQUEST_SYSTEM_MSG = 101;//进入系统消息页面请求码，删除系统消息时有用

    @BindView(R2.id.lv_session_search)
    ListView lvSession;
    @BindView(R2.id.wzd_question_cb)
    CheckBox wzdQuestionCb;
    @BindView(R2.id.wzd_answer_cb)
    CheckBox wzdAnswerCb;
    @BindView(R2.id.not_pay_cb)
    CheckBox notPayCb;
    @BindView(R2.id.payed_cb)
    CheckBox payedCb;
    @BindView(R2.id.fzd_question_cb)
    CheckBox fzdQuestionCb;
    @BindView(R2.id.fzd_answer_cb)
    CheckBox fzdAnswerCb;
    @BindView(R2.id.deliver_goods_cb)
    CheckBox deliverGoodsCb;
    @BindView(R2.id.tv_search_result)
    TextView tvSearchResult;
    private List<Session> sessionList;
    private SessionDao sessionDao;
    private ContactsDao contactsDao;
    private SessionAdapter sessionAdapter;
    private String userId;
    private CheckBox checkedButton;
    private MsgDao msgDao;
    private AsyncSession mAsyncSession;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarType(ActionBarView.ACTIONBAR_SEARCH);
        setActionBarContentView(R.layout.activity_session_search);
        setActionBarSearchInputHint("输入患者姓名、记录搜索");
        setOnSearchInputEditorActionListener(this);
        setActionBarSearchInputListener(this);
        sessionDao = AppContext.getInstances().getDaoSession().getSessionDao();
        msgDao = AppContext.getInstances().getDaoSession().getMsgDao();
        mAsyncSession = AppContext.getInstances().getDaoSession().startAsyncSession();
        contactsDao = AppContext.getInstances().getDaoSession().getContactsDao();
        userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        initViews();
    }

    /**
     * 初始化view
     */
    private void initViews() {
        sessionList = new ArrayList<Session>();
        sessionAdapter = new SessionAdapter(mContext, sessionList, R.layout.item_session);
        lvSession.setAdapter(sessionAdapter);
        lvSession.setOnItemClickListener(this);
    }


    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        search();
        hideKeyBoard(getActionbarSearchInput());
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        if (actionId == EditorInfo.IME_ACTION_SEARCH) {
            search();
            hideKeyBoard(getActionbarSearchInput());
            return true;
        }
        return false;
    }

    /**
     * 初始化数据
     */
    private void search() {
        String keys = getActionbarSearchInputText();
        sessionList.clear();
        if (TextUtils.isEmpty(keys) && checkedButton == null) {
            tvSearchResult.setVisibility(View.GONE);
            sessionAdapter.notifyDataSetChanged();
            return;
        }
        if (!TextUtils.isEmpty(userId)) {
            QueryBuilder queryBuilder = sessionDao.queryBuilder()
                    .where(SessionDao.Properties.To.eq(userId));

            if (checkedButton != null) {
                queryBuilder.where(SessionDao.Properties.Status.eq(checkedButton.getTag()));
            } else {
                if (!TextUtils.isEmpty(keys)) {
                    queryBuilder.whereOr(SessionDao.Properties.Name.like("%" + keys + "%"), SessionDao.Properties.Content.like("%" + keys + "%"));
                }
            }
            queryBuilder.orderDesc(SessionDao.Properties.Time);
            mAsyncSession.setListener(asyncOperation -> {
                if (isFinishing()) {
                    return;
                }
                List<Session> list = new ArrayList<>();
                if (asyncOperation.getResult() instanceof List) {
                    List<Session> asyncList = (List<Session>) asyncOperation.getResult();
                    list.addAll(asyncList);
                }
                if (checkedButton == null) {
                    List<String> sessionIdList = new ArrayList<>();
                    for (Session session : list) {
                        if (!sessionIdList.contains(session.getId())) {
                            sessionIdList.add(session.getId());
                        }
                    }

                    QueryBuilder  contactsQueryBuilder =  contactsDao.queryBuilder()
                            .where(ContactsDao.Properties.Remark.like("%" + keys + "%"));
                    List<Contacts> asyncContactsList = contactsQueryBuilder.list();
                    List<String> idList = new ArrayList<>();
                    for (Contacts contacts : asyncContactsList) {
                        if (!idList.contains(contacts.getLocalId())) {
                            idList.add(contacts.getLocalId());
                        }
                    }
                    if (!idList.isEmpty()) {
                        QueryBuilder q1 = sessionDao.queryBuilder().where(SessionDao.Properties.Id.in(idList));
                        List<Session> list1 = q1.list();
                        for (Session session : list1) {
                            if (!sessionIdList.contains(session.getId())) {
                                sessionIdList.add(session.getId());
                                list.add(session);
                            }
                        }
                    }


                    QueryBuilder msgQueryBuilder = msgDao.queryBuilder()
                            .where(MsgDao.Properties.ContentObjContent.like("%" + keys + "%"))
                            .orderAsc(MsgDao.Properties.To);
                    List<Msg> asyncMsgList = (List<Msg>) msgQueryBuilder.list();
                    idList.clear();
                    for (Msg msg : asyncMsgList) {
                        String id;
                        if (TextUtils.equals(userId, msg.getFrom())) {
                            id = msg.getTo();
                        } else {
                            id = msg.getFrom();
                        }
                        if (!idList.contains(id)) {
                            idList.add(id);
                        }
                    }
                    if (!idList.isEmpty()) {
                        QueryBuilder q2 = sessionDao.queryBuilder().where(SessionDao.Properties.From.in(idList));
                        List<Session> list2 = q2.list();
                        for (Session session : list2) {
                            if (!sessionIdList.contains(session.getId())) {
                                sessionIdList.add(session.getId());
                                list.add(session);
                            }
                        }
                    }
                }
                runOnUiThread(() -> {
                    if (list != null) {
                        sessionList.addAll(list);
                    }
                    if (sessionList.size() == 0) {
                        tvSearchResult.setText("未搜索到相关患者");
                    } else {
                        tvSearchResult.setText("搜索结果");
                    }
                    tvSearchResult.setVisibility(View.VISIBLE);
                    sessionAdapter.notifyDataSetChanged();
                });

            });
            mAsyncSession.queryList(queryBuilder.build());
        }

    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        Session session = sessionList.get(position);
        SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_ID, session.getFrom());
        SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_HEAD_URL, session.getHeadImg());
        session.setNotReadCount(0);
        sessionDao.update(session);
        Intent intent = new Intent(mContext, ChatMainActivity.class);
        if (PublicParams.CHAT_SERVICE_USER_ID.equals(session.getFrom())) {//小然
            intent = new Intent(mContext, ChatServiceActivity.class);
            intent.putExtra(PublicParams.PATIENT_USRE_NNAME, session.getName());
            startActivity(intent);
        } else if (PublicParams.SYSTEM_USER_ID.equals(session.getFrom())) {//系统消息
            intent = new Intent(mContext, SystemMsgListActivity.class);
            intent.putExtra(PublicParams.PATIENT_USRE_NNAME, session.getName());
            startActivityForResult(intent, REQUEST_SYSTEM_MSG);
        } else {
            intent = new Intent(mContext, ChatMainActivity.class);
            intent.putExtra(PublicParams.PATIENT_USRE_NNAME, session.getName());
            startActivity(intent);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_SYSTEM_MSG:
                if (resultCode == RESULT_OK) {
                    List<Session> list = sessionDao.queryBuilder()
                            .where(SessionDao.Properties.To.eq(userId))
                            .where(SessionDao.Properties.From.eq(PublicParams.SYSTEM_USER_ID))
                            .build().list();
                    sessionDao.deleteInTx(list);
                }
                break;
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        search();
    }

    /**
     * 处理患者状态的checkbox的点击事件
     *
     * @param view
     */
    @OnClick({R.id.wzd_question_cb, R.id.wzd_answer_cb, R.id.not_pay_cb, R.id.payed_cb, R.id.fzd_question_cb, R.id.fzd_answer_cb, R.id.deliver_goods_cb})
    public void onViewClicked(View view) {
        CheckBox cb = (CheckBox) view;
        if (cb.isChecked()) {
            checkedButton = cb;
        } else {
            checkedButton = null;
        }
        if (wzdQuestionCb != checkedButton) {
            wzdQuestionCb.setChecked(false);
        }
        if (wzdAnswerCb != checkedButton) {
            wzdAnswerCb.setChecked(false);
        }
        if (notPayCb != checkedButton) {
            notPayCb.setChecked(false);
        }
        if (payedCb != checkedButton) {
            payedCb.setChecked(false);
        }
        if (fzdQuestionCb != checkedButton) {
            fzdQuestionCb.setChecked(false);
        }
        if (fzdAnswerCb != checkedButton) {
            fzdAnswerCb.setChecked(false);
        }
        if (deliverGoodsCb != checkedButton) {
            deliverGoodsCb.setChecked(false);
        }
        search();
    }
}
