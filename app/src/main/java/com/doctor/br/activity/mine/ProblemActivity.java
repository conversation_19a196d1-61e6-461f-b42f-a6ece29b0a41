package com.doctor.br.activity.mine;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.AdapterView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.mine.ProblemGridAdapter;
import com.doctor.br.bean.UploadFileBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.FileIOUtils;
import com.doctor.br.utils.PopUtils;
import com.doctor.br.utils.SelectImgUtils;
import com.doctor.br.view.SelectPhotoDialog;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.HttpRequestTask;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.ImageUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.AutoHeightGridView;
import org.newapp.ones.base.widgets.NoEmojiEditText;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：问题反馈界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/12
 */

public class ProblemActivity extends ActionBarActivity implements RecyclerItemItemClickListener {
    //界面下的控件
    @BindView(R.id.problem_et)
    NoEmojiEditText problemEt;
    @BindView(R.id.word_number_tv)
    TextView wordNumberTv;
    @BindView(R.id.grid_view)
    AutoHeightGridView gridView;
    @BindView(R.id.submit_btn)
    com.doctor.br.view.NoDoubleClickBtn submitBtn;

    private int clickPos = -1;//上传完成后替换的图片位置

    private List<String> imgList;//存储图片路径
    private ProblemGridAdapter problemGridAdapter;//上传图片的grid适配器

    private PopUtils photoPop;//选择图片的popwindow

    private RequestCallBack updateImgCallBack;//上传图片回调
    private RequestCallBack submitCallBack;//提交问题回调

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_problem);
        initView();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("问题反馈");

        problemEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                wordNumberTv.setText(s.length() + "/500");
            }
        });

        imgList = new ArrayList<>();
        getImgUrlFromLocal();
        problemGridAdapter = new ProblemGridAdapter(this, imgList, this);
        gridView.setAdapter(problemGridAdapter);

        String[] strings = {"拍照", "从相册选取"};
        photoPop = new PopUtils(this, R.string.personal_select, Arrays.asList(strings), new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                switch (position) {
                    case 0://拍照
                        SelectImgUtils.gotoCarema(ProblemActivity.this);
                        break;
                    case 1://相册
                        SelectImgUtils.gotoPhoto(ProblemActivity.this);
                        break;
                    default:
                        break;
                }
                photoPop.dismiss();
            }
        });

        submitBtn.setOnClickListener(this);
    }

    /**
     * 从本地查找有没有之前没有提交的图片
     */
    private void getImgUrlFromLocal() {
        String imgUrls = SharedPreferenceUtils.getString(this, PublicParams.PROBLEM_IMGS);
        String problemText = SharedPreferenceUtils.getString(this, PublicParams.PROBLEM_TEXT);
        if (!TextUtils.isEmpty(imgUrls)) {
            String[] imgs = imgUrls.split(",");
            imgList.clear();
            imgList.addAll(Arrays.asList(imgs));
        }
        if (!TextUtils.isEmpty(problemText)) {
            problemEt.setText(problemText);
            problemEt.setSelection(problemText.length());
            wordNumberTv.setText(problemText.length() + "/500");
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.submit_btn:
                //提交反馈
                String proStr = problemEt.getText().toString().trim();
                if (TextUtils.isEmpty(proStr)) {
                    ToastUtils.showShortMsg(this, "请填写您要反馈的文字内容");
                    return;
                }
                String imgUrls = SharedPreferenceUtils.getString(this, PublicParams.PROBLEM_IMGS);
                submitProblemRequest(proStr, imgUrls);
                break;
            default:
                break;
        }
    }

    //点击上传图片
    @Override
    public void itemViewClick(int position, View view) {
        //选择上传方式
        clickPos = position;
        photoPop.showPopupWindow();
    }

    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    @Override
    public void onBackPressed() {
        String problemStr = problemEt.getText().toString().trim();
        if (!TextUtils.isEmpty(problemStr)) {
            SharedPreferenceUtils.putString(this, PublicParams.PROBLEM_TEXT, problemStr);
        }
        super.onBackPressed();
    }

    @Override
    protected void onActivityResult(final int requestCode, int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            Uri uri = null;//获取图片的真实路径
            switch (requestCode) {
                case SelectPhotoDialog.REQUEST_CAPTURE: //调用系统相机返回
                    uri = Uri.fromFile(SelectImgUtils.tempFile);
//                    filePath = SelectImgUtils.getRealFilePathFromUri(this, Uri.fromFile(SelectImgUtils.tempFile));
                    break;
                case SelectPhotoDialog.REQUEST_PICK:  //调用系统相册返回
                    uri = data.getData();
//                    filePath = SelectImgUtils.getRealFilePathFromUri(this, data.getData());
                    break;
                default:
                    break;
            }
            if (uri == null) {
                return;
            }
            if (mLoadingDialog != null && !mLoadingDialog.isShowing()) {
                mLoadingDialog.show();
            }
            //压缩图片文件
            Bitmap dimenBitmap = ImageUtils.createBitmapFromUri(this,uri, 720, 1280);//大小压缩后的图片
            SelectImgUtils.compressBitmapFile = new File(getExternalCacheDir(), "compressImg.jpg");//最后压缩完成存储的图片路径
            try {
                SelectImgUtils.compressBitmapFile.createNewFile();
            } catch (IOException e) {
                LogUtils.i(ProblemActivity.class, e.toString());
                return;
            }
            //将大小压缩后的图片进行质量压缩后，写入文件中准备上传；质量压缩大小限制上限为500k;
            FileIOUtils.writeFileFromBytesByStream(SelectImgUtils.compressBitmapFile, ImageUtils.compressByQuality(dimenBitmap, 500 * 1024L, true), false);
            File protraitFile = SelectImgUtils.compressBitmapFile;
            updateImgRequest(protraitFile);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        SelectImgUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults);
    }

    /**
     * 网络请求上传图片
     *
     * @param file 图片路径
     */
    private void updateImgRequest(File file) {
        Map<String, String> params = new HashMap<>();
        params.put("resourceType", "image");
        Map<String, File> fileParams = new HashMap<>();
        fileParams.put(file.getAbsolutePath(), file);
        updateImgCallBack = HttpRequestTask.getInstance().addHttpPostFileRequest(HttpUrlManager.UPLOAD_FILE, params, fileParams, UploadFileBean.class, this);
    }

    /**
     * 网络请求提交问题
     *
     * @param content 问题反馈的内容
     * @param imgList 图片字符串，多张图片用","分隔
     */
    private void submitProblemRequest(String content, String imgList) {
        Map<String, String> params = new HashMap<>();
        params.put("content", content);
        params.put("imgList", imgList);
        submitCallBack = addHttpPostRequest(HttpUrlManager.SUBMIT_PROBLEM, params, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (gridView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.UPLOAD_FILE://上传图片

                if (result.isRequestSuccessed()) {
                    UploadFileBean uploadFileBean = (UploadFileBean) result.getBodyObject();
                    if (imgList.size() == clickPos) {
                        imgList.add(uploadFileBean.getUrl());
                    } else {
                        //如果图片够5张的情况下，直接修改图片
                        imgList.set(clickPos, uploadFileBean.getUrl());
                    }
                    problemGridAdapter.notifyDataSetChanged();
                    //将图片保存到本地
                    StringBuilder stringBuilder = new StringBuilder();
                    for (String s : imgList) {
                        stringBuilder.append(",").append(s);
                    }
                    if (stringBuilder.length() > 0) {
                        stringBuilder.deleteCharAt(0);
                        SharedPreferenceUtils.putString(this, PublicParams.PROBLEM_IMGS, stringBuilder.toString());
                    } else {
                        SharedPreferenceUtils.removeString(this, PublicParams.PROBLEM_IMGS);
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                clickPos = -1;
                break;
            case HttpUrlManager.SUBMIT_PROBLEM://提交问题反馈
                if (result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "提交成功");
                    SharedPreferenceUtils.removeString(this, PublicParams.PROBLEM_IMGS);
                    SharedPreferenceUtils.removeString(this, PublicParams.PROBLEM_TEXT);
//                    new Handler().postDelayed(new Runnable() {
//                        @Override
//                        public void run() {
                    finish();
//                        }
//                    }, 1000);
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (updateImgCallBack != null) {
            updateImgCallBack.cancel();
        }
        if (submitCallBack != null) {
            submitCallBack.cancel();
        }
        super.onDestroy();
    }


}
