package com.doctor.br.activity;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;

import com.doctor.br.app.AppContext;
import com.doctor.br.bean.CitiesListBean;
import com.doctor.br.bean.ContactsResult;
import com.doctor.br.bean.MedicineListBean;
import com.doctor.br.db.entity.Contacts;
import com.doctor.br.db.entity.ServerConfig;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.utils.ReshipData;
import com.doctor.br.service.NettyMessageService;
import com.doctor.br.utils.DateUtils;
import com.doctor.br.utils.EncryptUtils;
import com.doctor.br.utils.FileIOUtils;
import com.doctor.br.utils.RegularUtils;
import com.doctor.br.view.NumberProgressBar2;
import com.doctor.greendao.gen.ContactsDao;
import com.doctor.greendao.gen.ServerConfigDao;
import com.doctor.greendao.gen.SessionDao;
import com.doctor.test.TestActivity;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ParseData;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.widgets.EmptyView;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 启动App时的预加载数据的等待页面
 * @createTime 2017/11/1
 */

public class LoadDataActivity extends NoActionBarActivity implements EmptyView.OnReloadListener {
    private NumberProgressBar2 pbProgress;
    private EmptyView emptyView;

    private ServerConfigDao serverConfigDao;

    private RequestCallBack getCitiesListCallBack;//网络请求城市列表回调
    private RequestCallBack medicineListCallBack;//网络请求药典列表回调

    private boolean isFinishArea, isFinishMedicine, isFinishContacts;

    private int loadDataCount = 0;//请求的个数,无需修改
    private int currentProgress = 0;//当前进度条，无需修改
    private int currentMaxProgress = 20;//当前最大进度条，无需修改
    private int currentLoadCount = 0;//当前请求数量，无需修改,至少有一个请求
    private ProgressThread progressThread;
    private ContactsDao contactsDao;
    private SessionDao sessionDao;
    private Handler mHandler = new Handler();
    private long startTimes;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_load_data);
        Intent intent = new Intent(LoadDataActivity.this, NettyMessageService.class);
        startService(intent);
        initViews();
        initData();
    }


    /**
     * 初始化View
     */
    private void initViews() {
        pbProgress = (NumberProgressBar2) findViewById(R.id.pb_progress);
        emptyView = (EmptyView) findViewById(R.id.empty_view);
        progressThread = new ProgressThread();
        updateCurrentMaxProgress();
    }


    /**
     * 初始化数据
     */
    private void initData() {
        loadDataCount = 0;//请求的个数,无需修改
        currentProgress = 0;//当前进度条，无需修改
        currentMaxProgress = 10;//当前最大进度条，无需修改
        currentLoadCount = 0;//当前请求数量，无需修改
        serverConfigDao = AppContext.getInstances().getDaoSession().getServerConfigDao();
        contactsDao = AppContext.getInstances().getDaoSession().getContactsDao();
        sessionDao = AppContext.getInstances().getDaoSession().getSessionDao();
        loadAreaData();
        loadMedicalData();
        String updateContactsListTime = SharedPreferenceUtils.getString(this, PublicParams.UPDATE_CONTACTS_LIST_TIME);
        loadContactsData(updateContactsListTime);
        updateCurrentMaxProgress();
    }


    /**
     * 加载省市区数据
     * 用户不授权的情况下获取不到IMEI信息
     * READ_PHONE_STATE
     */
    private void loadAreaData() {
        loadDataCount++;
        List<ServerConfig> list = serverConfigDao.loadAll();
        if (list != null && list.size() > 0) {
            ServerConfig config = list.get(0);
            if (config != null) {
                if (!TextUtils.isEmpty(config.getArea_addressVersion()) && !config.getArea_addressVersion().equalsIgnoreCase(config.getArea_addressVersionLocal())) {
                    LogUtils.i(LoadDataActivity.class, "加载省市区数据");
                    mLoadingDialog = null;
                    //网络请求获取城市列表
                    Map<String, String> map = new HashMap<>();
                    map.put("updateTime", "2010-01-01");
                    getCitiesListCallBack = addHttpPostRequest(HttpUrlManager.GET_CITIES_LIST, map, CitiesListBean.class, this);
                    return;
                }
            }
        }
        isFinishArea = true;
        currentLoadCount++;
    }

    /**
     * 加载药典数据
     */
    private void loadMedicalData() {
        loadDataCount++;
        List<ServerConfig> list = serverConfigDao.loadAll();
        if (list != null && list.size() > 0) {
            ServerConfig config = list.get(0);
            if (config != null) {
                if (!TextUtils.isEmpty(config.getProduct_drugVersion())
                        && !config.getProduct_drugVersion()
                        .equalsIgnoreCase(config.getProduct_drugVersionLocal())) {
                    LogUtils.i(LoadDataActivity.class, "加载药典数据");
                    mLoadingDialog = null;
                    Map<String, String> params = new HashMap<>();
                    params.put("updateTime", "");
                    params.put("fristLetter", null);
                    medicineListCallBack = addHttpPostRequest(HttpUrlManager.MEDICINE_LIST, params
                            , MedicineListBean.class, this);
                    return;
                }
            }
        }
        isFinishMedicine = true;
        currentLoadCount++;
    }

    /**
     * 加载联系人数据
     *
     * @param startTime
     */
    private void loadContactsData(String startTime) {
        loadDataCount++;
        LogUtils.i(LoadDataActivity.class, "加载联系人数据");
        startTimes = new Date().getTime();
        mLoadingDialog = null;
        Map<String, String> params = new HashMap<>();
        if (TextUtils.isEmpty(startTime)) {
            startTime = getHalfYearTime();
        }
        params.put("startTime", startTime);
        medicineListCallBack = addHttpPostRequest(HttpUrlManager.GET_CONTACTS_LIST, params, ContactsResult.class, this);
    }

    /**
     * 获取过去半年的时间
     *
     * @return
     */
    private String getHalfYearTime() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        //过去六个月
        c.setTime(new Date());
        c.add(Calendar.MONTH, -6);
        Date m = c.getTime();
        String mon = format.format(m);
        return mon;
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.GET_CITIES_LIST:
                if (result.isRequestSuccessed()) {
                    List<ServerConfig> list = serverConfigDao.loadAll();
                    if (list != null && list.size() > 0) {
                        ServerConfig config = list.get(0);
                        if (config != null) {
//                            pbProgress.setProgress(pbProgress.getProgress() + 25);
                            currentLoadCount++;
                            updateCurrentMaxProgress();
                            CitiesListBean netCities = (CitiesListBean) result.getBodyObject();
                            netCities.setUpdateTime(config.getArea_addressVersion());//设置更新时间,防止用户本地修改
                            config.setArea_addressVersionLocal(config.getArea_addressVersion());//更改配置信息中存储的时间戳
                            String string = ParseData.parseObject(netCities);//将javabean转化为json串
                            byte[] bytes = EncryptUtils.desTemplate(string.getBytes(), true);//将转化或的字符串加密
                            File file = new File(getExternalCacheDir(), FileIOUtils.CITIES_PATH);
                            FileIOUtils.writeFileFromBytesByStream(file, bytes, false);
                            //设置更新时间防止用户本地修改
                            try {
                                long time = new SimpleDateFormat("yyyyMMddHHmmss").parse(config.getArea_addressVersion()).getTime();
                                file.setLastModified(time);
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            serverConfigDao.insertOrReplace(config);
                            LogUtils.i(LoadDataActivity.class, "加载省市区数据完毕");
                            isFinishArea = true;
                        }
                    }
                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setOnReloadListener(this);
                }
                break;
            case HttpUrlManager.MEDICINE_LIST:
                if (result.isRequestSuccessed()) {
                    currentLoadCount++;
                    updateCurrentMaxProgress();
                    MedicineListBean medicineListBean = (MedicineListBean) result.getBodyObject();
                    List<ServerConfig> list = serverConfigDao.loadAll();
                    if (list != null && list.size() > 0) {
                        ServerConfig config = list.get(0);
                        if (config != null) {
//                            pbProgress.setProgress(pbProgress.getProgress() + 25);
                            saveMedicineList(medicineListBean, config);
                            isFinishMedicine = true;
                        }
                    }
                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setOnReloadListener(this);
                }
                break;
            case HttpUrlManager.GET_CONTACTS_LIST:
                if (result.isRequestSuccessed()) {
                    long endTime = new Date().getTime();
                    LogUtils.i(LoadDataActivity.class, "加载联系人数据返回耗时：" + (endTime - startTimes) + " ms");
                    ContactsResult contactsResult = (ContactsResult) result.getBodyObject();
                    if (contactsResult != null) {
                        if (contactsResult.getItems() != null) {
                            saveContactsData(contactsResult.getItems());
                        }
                        if (contactsResult.getBlacklistIds() != null) {
                            deleteBlackContacts(contactsResult.getBlacklistIds());
                        }
                        SharedPreferenceUtils.putString(this, PublicParams.CONTACT_NUM, contactsResult.getRosterCount());
                    }
                    isFinishContacts = true;
                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setOnReloadListener(this);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 删除加入黑名单的用户数据
     *
     * @param blacklistIds
     */
    private void deleteBlackContacts(List<String> blacklistIds) {
        try {
            if (blacklistIds == null) {
                return;
            }
            String userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
            List<String> idLists = new ArrayList<>();
            for (int i = 0; i < blacklistIds.size(); i++) {
                idLists.add(blacklistIds.get(i) + "_" + userId);
            }
            contactsDao.deleteByKeyInTx(idLists);
            sessionDao.deleteByKeyInTx(idLists);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 保存药典列表数据
     *
     * @param medicineListBean
     * @param config
     */
    private void saveMedicineList(final MedicineListBean medicineListBean, final ServerConfig config) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                config.setProduct_drugVersionLocal(config.getProduct_drugVersion());//更改配置信息中存储的时间戳
                serverConfigDao.insertOrReplace(config);
                AppContext.getInstances().getDaoSession().getMedicineItemDao().insertOrReplaceInTx(medicineListBean.getList());
                LogUtils.i(LoadDataActivity.class, "加载药典数据完毕");
            }
        }).start();


    }

    /**
     * 保存联系人信息
     *
     * @param list
     */
    private void saveContactsData(final List<Contacts> list) {
        new Thread(new Runnable() {
            @Override
            public void run() {

                SharedPreferenceUtils.putString(LoadDataActivity.this, PublicParams.UPDATE_CONTACTS_LIST_TIME, DateUtils.getNowDate());
                String userId = SharedPreferenceUtils.getString(LoadDataActivity.this, PublicParams.USER_ID);
                for (int i = 0; i < list.size(); i++) {
                    Contacts contacts = list.get(i);
                    if (contacts != null) {
                        if (!TextUtils.isEmpty(userId)) {
                            contacts.setUserId(userId);
                        }
                        contacts = ReshipData.reshipContacts(list.get(i));
                        if ("#".equalsIgnoreCase(RegularUtils.letterFilter(contacts.getFirstSpell()))) {
                            contacts.setFirstSpell("|");
                        }
                    }
                }
                contactsDao.insertOrReplaceInTx(list);
                currentLoadCount++;
                updateCurrentMaxProgress();
                LogUtils.i(LoadDataActivity.class, "加载联系人数据完毕");
                long endTime = new Date().getTime();
                LogUtils.i(LoadDataActivity.class, "加载联系人数据耗时：" + (endTime - startTimes) + " ms");
            }
        }).start();

    }

    @Override
    public void onReload() {
        emptyView.setVisibility(View.GONE);
        currentProgress = 0;
        initData();
        updateCurrentMaxProgress();
    }

    /**
     * 数据加载完成跳转
     */
    private void startNewActivity() {
        if (isFinishArea && isFinishMedicine && isFinishContacts) {
            startActivity(new Intent(this, MainActivity.class));
            //测试retrofit请求用
//            startActivity(new Intent(this, TestActivity.class));
            this.finish();
        }
    }

    /**
     * 更新进度条最大进度
     */
    private void updateCurrentMaxProgress() {
        if (loadDataCount == 0) {
            startThread();
            return;
        }
        int a = 100 / loadDataCount;
        currentMaxProgress = a * currentLoadCount;
        if (currentLoadCount >= loadDataCount) {
            currentMaxProgress = 100;
        }
        startThread();
    }

    /**
     * 开启线程
     */
    private void startThread() {
        try {
            if (progressThread != null) {
                progressThread.setStop();
                progressThread = null;
            }
            progressThread = new ProgressThread();
            progressThread.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新进度条的线程
     */
    class ProgressThread extends Thread {
        private int preMaxProgress = currentMaxProgress;
        private int sleepStep = 300;
        private boolean isStop = false;

        @Override
        public void run() {
            while (!isStop && currentProgress != 100 && currentProgress <= currentMaxProgress) {
                try {
                    currentProgress++;
                    updateProgressBar();
                    if (currentProgress >= 100) {
                        startNewActivity();
                    }
                    Thread.sleep(sleepStep);
                    if (preMaxProgress < currentMaxProgress || currentMaxProgress == 100) {
                        sleepStep = 66;
                        preMaxProgress = currentMaxProgress;
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        /**
         * 线程终止
         */
        public void setStop() {
            isStop = true;
        }
    }

    /**
     * 更新progressBar的进度信息
     */
    private void updateProgressBar() {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                pbProgress.setProgress(currentProgress);
            }
        });
    }

    @Override
    protected void onDestroy() {
        if (getCitiesListCallBack != null) {
            getCitiesListCallBack.cancel();
        }
        if (medicineListCallBack != null) {
            medicineListCallBack.cancel();
        }
        super.onDestroy();
    }


}
