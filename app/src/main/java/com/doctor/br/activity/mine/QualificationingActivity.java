package com.doctor.br.activity.mine;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.doctor.br.activity.LoadDataActivity;
import com.doctor.br.activity.MainActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.db.entity.ServerConfig;
import com.doctor.greendao.gen.ServerConfigDao;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;

import java.util.List;

import butterknife.BindView;

/**
 * 类描述：审核中界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/8
 */

public class QualificationingActivity extends ActionBarActivity {
    //上个界面传递过来的数据
    public static final String HISTORY_STATE = "historyState";
    private String historyState;//之前是否认证成功过 0 没有 1 成功过
    private String fromClass;//判断从哪个页面跳转过来
    //界面下的控件
    @BindView(R.id.remind_tv)
    TextView remindTv;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_qualificationing);
        getBaseActionBar().setActionBarTitle("审核中");
        getIntentData(savedInstanceState);
        initView();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(HISTORY_STATE, historyState);
        outState.putString(QualificationActivity.CLASS,fromClass);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            historyState = savedInstanceState.getString(HISTORY_STATE);
            fromClass = savedInstanceState.getString(QualificationActivity.CLASS);
        } else {
            historyState = getIntent().getStringExtra(HISTORY_STATE);
            fromClass = getIntent().getStringExtra(QualificationActivity.CLASS);
        }
        if (TextUtils.isEmpty(historyState)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }

    private void initView() {
        if ("1".equals(historyState)) {
            remindTv.setVisibility(View.VISIBLE);
        } else {
            remindTv.setVisibility(View.GONE);
        }
    }

    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    @Override
    public void onBackPressed() {
        if (!TextUtils.isEmpty(fromClass) && fromClass.equals(PersonalDataActivity.class.getSimpleName())) {
            Intent intent;
            if (isLoadData()) {
                intent = new Intent(this, LoadDataActivity.class);
            } else {
                intent = new Intent(this, MainActivity.class);
            }
            startActivity(intent);
            finish();
            return;
        }
        super.onBackPressed();
    }

    /**
     * 判断是否加载数据
     *
     * @return
     */
    private boolean isLoadData() {
        ServerConfigDao serverConfigDao = AppContext.getInstances().getDaoSession().getServerConfigDao();
        List<ServerConfig> list = serverConfigDao.loadAll();
        if (list != null && list.size() > 0) {
            ServerConfig config = list.get(0);
            if (config != null) {
                if (!TextUtils.isEmpty(config.getArea_addressVersion()) && !config.getArea_addressVersion().equalsIgnoreCase(config.getArea_addressVersionLocal())) {
                    return true;
                }

                if (!TextUtils.isEmpty(config.getProduct_drugVersion()) && !config.getProduct_drugVersion().equalsIgnoreCase(config.getProduct_drugVersionLocal())) {
                    return true;
                }
            }
        }
        String updateContactsListTime = SharedPreferenceUtils.getString(this, PublicParams.UPDATE_CONTACTS_LIST_TIME);
        if (TextUtils.isEmpty(updateContactsListTime)) {
            return true;
        }
        return false;
    }
}
