package com.doctor.br.activity.manage;

import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.base.request.common.BaseObserver2;
import com.base.request.common.BaseRxActivity;
import com.doctor.br.activity.ActionBarWebViewActivity;
import com.doctor.br.activity.chatmain.ChatMainActivity;
import com.doctor.br.apiloader.shop.OrderRecordLoader;
import com.doctor.br.bean.shop.ShopOrderDetailBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.view.LogisticsDialog;
import com.doctor.br.view.StateDialog;
import com.doctor.yy.R;

import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.EmptyView;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.disposables.Disposable;

/**
 * 类描述：养生建议 订单详情
 * 创建人： YangYajun
 */
public class OrderDetailsActivity extends BaseRxActivity implements View.OnClickListener {

    //此界面下的控件
    @BindView(R.id.amount_tv)
    TextView amountTv;
    @BindView(R.id.iv_question)
    ImageView ivQuestion;
    @BindView(R.id.yspProductImg)
    ShapeImageView yspProductImg;
    @BindView(R.id.medication_name)
    TextView medicationName;
    @BindView(R.id.medication_desc)
    TextView medicationDesc;
    @BindView(R.id.yspUnitPrice_tv)
    TextView yspUnitPriceTv;
    @BindView(R.id.orderId_tv)
    TextView orderIdTv;
    @BindView(R.id.status_tv)
    TextView statusTv;
    @BindView(R.id.nick_tv)
    TextView nickTv;
    @BindView(R.id.takerName_tv)
    TextView takerNameTv;
    @BindView(R.id.iv_message)
    ImageView ivMessage;
    @BindView(R.id.createdTime_tv)
    TextView createdTimeTv;
    @BindView(R.id.pay_time_tv)
    TextView payTimeTv;
    @BindView(R.id.sale_Product_Count)
    TextView saleProductCount;
    @BindView(R.id.logistics_status_tv)
    TextView logisticsStatusTv;
    @BindView(R.id.see_details)
    TextView seeDetails;
    @BindView(R.id.ll_container)
    LinearLayout llContainer;
    @BindView(R.id.award_price)
    TextView awardPrice;
    @BindView(R.id.empty_view)
    EmptyView emptyView;
    @BindView(R.id.rl_logistics_status)
    RelativeLayout rlLogisticsStatus;
    @BindView(R.id.rl_paytime)
    RelativeLayout rlPaytime;
    @BindView(R.id.rmb_money1)
    TextView rmbMoney1;
    @BindView(R.id.rmb_money2)
    TextView rmbMoney2;
    @BindView(R.id.rl_explain)
    RelativeLayout rlExplain;

    //  上个界面传来的值
    public static final String ORDERID = "orderId";//订单ID


    private StateDialog stateDialog;
    private ConfirmDialog mConfirmDialog;
    private LogisticsDialog logisticsDialog;
    private ShopOrderDetailBean detailBean;
    private OrderRecordLoader loader;
    private BaseObserver2<ShopOrderDetailBean> baseObserver2;

    private boolean isFromChatMain;
    private String localOrderId = "";
    private String userId;
    private String mPhone;
    private String patientId;//患者ID
    private String OrderStatus;//订单状态
    private String logistics;//物流状态
    private String takerName;//患者昵称
    private String rosterRemark;//患者备注

    private static final String WAIT_FOR_PAY = "WAIT_FOR_PAY";// 待支付
    private static final String WAIT_FOR_DELIERY = "WAIT_FOR_DELIERY";// 待发货
    private static final String ALREADY_DELIVERIED = "ALREADY_DELIVERIED";// 已发货
    private static final String FINIHSED = "FINIHSED";// 已完成

    private static final String ATTENTION = "已关注的患者";//患者类型——已关注
    private static final String NO_ATTENTION = "散户";//患者类型——未关注

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_order_details);
        isFromChatMain = getIntent().getBooleanExtra(PublicParams.IS_FROM_CHAT_MAIN_ACTIVITY, false);
        initView();
        getOrderDetails();
    }


    private void initView() {
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarTitle("订单详情");
        loader = new OrderRecordLoader();
        localOrderId = getIntent().getStringExtra(ORDERID);
        userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);

        mConfirmDialog = ConfirmDialog.getInstance(this);
        mConfirmDialog.setPositiveText("立即拨号")
                .setNavigationText("取消")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        mConfirmDialog.dismiss();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + detailBean.getPatientMobile()));
                        startActivity(intent);
                        mConfirmDialog.dismiss();
                    }
                });
    }

    /**
     * 初始化订单详情信息
     *
     * @param detailBean
     */
    private void initData(ShopOrderDetailBean detailBean) {
        this.detailBean = detailBean;
        OrderStatus = detailBean.getOrderStatus();
        logistics = detailBean.getCarrier();
        mPhone = detailBean.getPatientMobile();
        patientId = detailBean.getPatientId();
        takerName = detailBean.getPatientName();
        rosterRemark = detailBean.getRosterRemark();
        rmbMoney1.setVisibility(View.VISIBLE);
        rmbMoney2.setVisibility(View.VISIBLE);

        String amount = "该订单奖励为" + detailBean.getAwardPrice() + "元，周结将自动存入您的钱包";
        SpannableStringBuilder ssb = new SpannableStringBuilder(amount);
        ssb.setSpan(new ForegroundColorSpan(Color.RED), 6, ssb.length() - 13, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        awardPrice.setText(ssb);
        amountTv.setText(TextUtils.isEmpty(detailBean.getAmount()) ? "" : detailBean.getAmount());
        GlideUtils.getInstance().loadImage(detailBean.getYspProductImg(), this, yspProductImg, R.drawable.icon_img_load_error);
        medicationName.setText(TextUtils.isEmpty(detailBean.getYspProductName()) ? "" :
                (detailBean.getYspProductName().length() > 6 ? detailBean.getYspProductName().substring(0, 6) + "…" : detailBean.getYspProductName()));
        medicationDesc.setText(TextUtils.isEmpty(detailBean.getYspEffect()) ? "" : detailBean.getYspEffect());
        yspUnitPriceTv.setText(detailBean.getYspUnitPrice() + "");
        orderIdTv.setText(TextUtils.isEmpty(detailBean.getId()) ? "" : detailBean.getId());
        payTimeTv.setText(TextUtils.isEmpty(detailBean.getPayTime()) ? "" : detailBean.getPayTime());
        createdTimeTv.setText(TextUtils.isEmpty(detailBean.getCreatedTime()) ? "" : detailBean.getCreatedTime());
        saleProductCount.setText(TextUtils.isEmpty(detailBean.getSaleProductCount()) ? "" : detailBean.getSaleProductCount());

        //  交易状态、物流状态
        if (WAIT_FOR_PAY.equals(OrderStatus)) {
            statusTv.setText("待付款");
            rlLogisticsStatus.setVisibility(View.GONE);
            rlPaytime.setVisibility(View.GONE);
            rlExplain.setVisibility(View.GONE);
        } else if (WAIT_FOR_DELIERY.equals(OrderStatus)) {
            seeDetails.setVisibility(View.GONE);
            statusTv.setText("待发货");
            logisticsStatusTv.setText(TextUtils.isEmpty(logistics) ? "" : logistics);
        } else if (ALREADY_DELIVERIED.equals(OrderStatus)) {
            seeDetails.setVisibility(View.VISIBLE);
            statusTv.setText("已发货");
            logisticsStatusTv.setText(TextUtils.isEmpty(logistics) ? "" : logistics);
        } else if (FINIHSED.equals(OrderStatus)) {
            seeDetails.setVisibility(View.VISIBLE);
            statusTv.setText("已完成");
            logisticsStatusTv.setText(TextUtils.isEmpty(logistics) ? "" : logistics);
        }

        //  用户昵称设置
        if (ATTENTION.equals(detailBean.getPatientType())) {
            if (TextUtils.isEmpty(rosterRemark)) {
                takerNameTv.setText(takerName.length() > 8 ? takerName.substring(0, 8) + "…" : takerName);
            } else {
                takerNameTv.setText( (rosterRemark.length() > 4 ? rosterRemark.substring(0, 4) + "…" : rosterRemark)+
                        ("(" + (takerName.length() > 4 ? takerName.substring(0, 4) + "…" : takerName) + ")"));
            }
            ivMessage.setVisibility(View.VISIBLE);
        } else if (NO_ATTENTION.equals(detailBean.getPatientType())) {
            ivMessage.setVisibility(View.GONE);
            String nickPhone = ("散户(" + mPhone + ")");
            if (!TextUtils.isEmpty(mPhone)) {
                SpannableStringBuilder ssb2 = new SpannableStringBuilder(nickPhone);
                ssb2.setSpan(new ForegroundColorSpan(Color.BLUE), nickPhone.length() - 12, nickPhone.length() - 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                ssb2.setSpan(new TextClick(), nickPhone.length() - 12, nickPhone.length() - 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                takerNameTv.setText(ssb2);
                //这个一定要记得设置,不然点击拨打电话不生效
                takerNameTv.setMovementMethod(LinkMovementMethod.getInstance());
            } else {
                takerNameTv.setText("散户");
            }
        }

    }

    /**
     * 点击患者手机号弹出拨号弹框
     */
    private class TextClick extends ClickableSpan {

        @Override
        public void updateDrawState(TextPaint ds) {
            super.updateDrawState(ds);
            //所设置的点击部分不显示下划线
            ds.setUnderlineText(false);
        }

        @Override
        public void onClick(View widget) {
            if (mConfirmDialog != null) {
                mConfirmDialog.setDialogContent(mPhone);
                mConfirmDialog.show();
            }
        }
    }

    private void goToChat() {
        if (isFromChatMain) {
            finish();
        } else {
            if (detailBean != null) {
                SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_ID, patientId);
                Intent intent = new Intent(mContext, ChatMainActivity.class);
                intent.putExtra(PublicParams.PATIENT_USRE_ID, patientId);
                intent.putExtra(PublicParams.PATIENT_USRE_NNAME, detailBean.getPatientName());
                intent.putExtra(PublicParams.PATIENT_USRE_MOBILE, detailBean.getPatientMobile());
                intent.putExtra(PublicParams.PATIENT_USRE_AGE, detailBean.getPatientAge());
                intent.putExtra(PublicParams.PATIENT_USRE_SEX, detailBean.getSex());
                startActivity(intent);
            }
        }
    }

    /**
     * 网络请求订单详情
     */
    private void getOrderDetails() {
        baseObserver2 = new BaseObserver2<ShopOrderDetailBean>() {

            @Override
            public void onStart(Disposable d) {
                super.onStart(d);
                showLoading();
            }

            @Override
            public void onSuccess(ShopOrderDetailBean result) {
                if (result == null) {
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setEmptyImgResource(R.drawable.empty_no_order);
                }
                initData(result);
            }

            @Override
            public void onFinish() {
                super.onFinish();
                dismissLoading();
            }

            @Override
            public void onFailure(String code, String msg) {
                super.onFailure(code, msg);
                RequestErrorToast.showError(OrderDetailsActivity.this, HttpUrlManager.MEDICATION_DETAIL, code, msg);
                emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                emptyView.setVisibility(View.VISIBLE);
                emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                    @Override
                    public void onReload() {
                        getOrderDetails();
                    }
                });
                dismissLoading();
            }
        };

        Map<String, String> map = new HashMap();
        map.put("orderId", localOrderId);
        map.put("userId", userId);
        map.put("method_code", HttpUrlManager.MEDICATION_DETAIL);
        loader.getOrderDetailInfo(map)
                .compose(this.<ShopOrderDetailBean>bindToLifecycle())
                .subscribe(baseObserver2);

    }

    @OnClick({R.id.iv_question, R.id.see_details, R.id.ll_container, R.id.iv_message, R.id.takerName_tv})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_question://结算说明
                String temp = detailBean.getYspSettleExplain().replace("\\n", "\n");
                if (stateDialog == null) {
                    stateDialog = StateDialog.getInstance(OrderDetailsActivity.this);
                }
                stateDialog.setTitleText("结算说明")
                        .setContentText(temp).show();
                break;
            case R.id.see_details://查看物流详情
                if (logisticsDialog == null) {
                    logisticsDialog = LogisticsDialog.getInstance(OrderDetailsActivity.this);
                }
                logisticsDialog.setTitleText("物流详情")
                        .loadData(detailBean.getLogisticsUrl()).show();
                break;
            case R.id.ll_container://查看商品详情
                Intent intent = new Intent(OrderDetailsActivity.this, ActionBarWebViewActivity.class);
                intent.putExtra(PublicParams.WEBVIEW_TITLE, "商品详情");
                intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, detailBean.getOrderProductUrl());
                startActivity(intent);
                break;
            case R.id.iv_message://跳转到聊天界面
                goToChat();
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}