package com.doctor.br.activity.chatmain;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.widget.ListView;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSON;
import com.doctor.br.adapter.chatmain.ChatAdapter;
import com.doctor.br.adapter.chatmain.NewChatAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.MsgStatus;
import com.doctor.br.db.entity.Msg;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.NettyResultCode;
import com.doctor.br.netty.client.NettyClient;
import com.doctor.br.netty.client.NettyUtils;
import com.doctor.br.netty.impl.DataReceiverImpl;
import com.doctor.br.netty.model.ContentObject;
import com.doctor.br.netty.model.ContentType;
import com.doctor.br.netty.model.Message;
import com.doctor.br.netty.model.MsgResourceType;
import com.doctor.br.netty.model.ResultType;
import com.doctor.br.netty.utils.MessageFilter;
import com.doctor.br.netty.utils.ReshipData;
import com.doctor.br.utils.DateUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.view.refreshlayout.OnRefreshListener;
import com.doctor.br.view.refreshlayout.PullDownLoadMoreLayout;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.widgets.EmptyView;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 历史消息记录页面
 * @createTime 2023/03/11
 */
public class NewChatHistoryRecordsActivity extends ActionBarActivity implements OnRefreshListener {
    public static void launch(Activity context) {
        Intent intent = new Intent(context, NewChatHistoryRecordsActivity.class);
        context.startActivity(intent);
    }

    public static void launch(Fragment context) {
        Intent intent = new Intent(context.getContext(), NewChatHistoryRecordsActivity.class);
        context.startActivity(intent);
    }

    @BindView(R2.id.lv_chat_records)
    RecyclerView lvChatRecords;
    @BindView(R2.id.mPullDownLoadMoreLayout)
    PullDownLoadMoreLayout mPullDownLoadMoreLayout;
    @BindView(R2.id.empty_view)
    EmptyView emptyView;
    private List<Msg> msgList;
    private NewChatAdapter chatAdapter;
    private LinearLayoutManager linearLayoutManager;
    private String userId;
    private String toUserId = "";
    private int pageSize = 20;
    private NettyClient mNettyClient;
    private String lastMessageId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_chat_history_records_new);
        setActionBarTitle("历史记录");
        toUserId = SharedPreferenceUtils.getString(this, PublicParams.PATIENT_USRE_ID);
        userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        mNettyClient = AppContext.getInstances().getNettyClient();
        lastMessageId = getLastMessageId() + "00000000";
        EventBusUtils.register(this);
        initViews();
        initDatas();
    }


    /**
     * 初始化view
     */
    private void initViews() {
        linearLayoutManager = new LinearLayoutManager(this, RecyclerView.VERTICAL, false);
        msgList = new ArrayList<>();
        chatAdapter = new NewChatAdapter(mContext, msgList, R.layout.item_chat_record, ChatAdapter.FROM_CHAT_HISTORY_RECORDS);
        chatAdapter.setLongClick(false);
        lvChatRecords.setLayoutManager(linearLayoutManager);
        lvChatRecords.setItemAnimator(null);
        lvChatRecords.setAdapter(chatAdapter);
        mPullDownLoadMoreLayout.setOnRefreshListener(this);
        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
            @Override
            public void onReload() {
                emptyView.setEmptyType(EmptyView.TYPE_LOADING);
                initDatas();
            }
        });
    }

    /**
     * 初始化数据
     */
    private void initDatas() {
        if (mNettyClient != null && mNettyClient.isLogined()) {
            NettyUtils.getHistoryRecords(toUserId, lastMessageId, String.valueOf(pageSize));
        } else {
            emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
        }
    }

    @Override
    public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {

    }

    @Override
    public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {
        initDatas();
    }

    /**
     * 获取默认初始的messageId
     */
    private String getLastMessageId() {
        SimpleDateFormat sdFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        //通过日历获取下一天日期
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(DateUtils.getNowDate());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_YEAR, +1);
        String nextDate = sdFormat.format(cal.getTime());
        return nextDate;
    }

    /**
     * 历史消息记录返回结果
     *
     * @param result 返回结果
     * @sender {@link DataReceiverImpl#sendEventBus(NettyResult)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void nettyResult(NettyResult result) {
        if (ResultType.CHAT_RECORDS == result.getResultType()) {
            if (NettyResultCode.SUCCESS.equals(result.getResultCode())) {//获取成功
                List<Map> messages = (List<Map>) result.getResult().get("messages");
                boolean canLoadMore = true;
                if (messages == null || messages.size() < pageSize) {
                    canLoadMore = false;
                    mPullDownLoadMoreLayout.setPullDownRefreshEnable(false);
                } else {
                    canLoadMore = true;
                    mPullDownLoadMoreLayout.setPullDownRefreshEnable(true);
                }
//                int preSize = msgList.size();
                List<Msg> newList = ListFormat(messages);
                if (newList.size() == 0 && canLoadMore) {
                    NettyUtils.getHistoryRecords(toUserId, lastMessageId, String.valueOf(pageSize));
                }
                chatAdapter.addData(0, newList);
                mPullDownLoadMoreLayout.endRefreshing();
                mPullDownLoadMoreLayout.endLoadingMore();
//                lvChatRecords.setSelection();
                if (chatAdapter.getItemCount() == newList.size()) {
                    lvChatRecords.scrollToPosition(newList.size() - 1);
                }
                emptyView.hide();
            } else {
                emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
            }
        }
    }


    /**
     * 处理数据
     *
     * @param messages
     */
    private List<Msg> ListFormat(List<Map> messages) {
        if (messages == null || messages.size() < 1) {
            return Collections.emptyList();
        }
        List<Msg> newList = new ArrayList<>();
        for (int i = 0; i < messages.size(); i++) {
            try {
                Map map = messages.get(i);
                if (i == messages.size() - 1) {
                    lastMessageId = (String) map.get("id");
                }
                Object content = map.get("content");
                ContentObject object = ReshipData.contentToObject(msgList.size(), content);
                if (object == null) {
                    continue;
                } else {
                    map.put("content", object);
                }
                String jsonStr = JSON.toJSONString(map);
                Message message = JSON.parseObject(jsonStr, Message.class);
                if (MessageFilter.filter(message) && MessageFilter.isShowMsg(message)) {
                    if (userId != null && userId.equals(message.getFrom())) {
                        message.setFromUsername(toUserId);
                        message.setToUsername(userId);
                    } else {
                        message.setFromUsername(userId);
                        message.setToUsername(toUserId);
                    }
                    Msg msg = ReshipData.MessageToMsg(message);
                    msg.setSendStatus(MsgStatus.SEND_SUCCESS);
                    msg.setIsRead(MsgStatus.IS_READ);
                    newList.add(0, msg);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return newList;
    }


    private boolean isShowMsg(Msg msg) {
        String type = msg.getContentObjType();
        if (MsgResourceType.TYPE_TO == (msg.getMsgSourceType())) {
            if (ContentType.WZD_ANSWER.equalsIgnoreCase(type) || ContentType.FZD_ANSWER.equalsIgnoreCase(type) || ContentType.SUPPLEMENT_ANSWER.equalsIgnoreCase(type)) {
                return false;
            }
        } else if (MsgResourceType.TYPE_FROM == (msg.getMsgSourceType())) {
            if (ContentType.WZD_QUESTION.equalsIgnoreCase(type) || ContentType.FZD_QUESTION.equalsIgnoreCase(type) || ContentType.SUPPLEMENT_QUESTION.equalsIgnoreCase(type)) {
                return false;
            }
        }
        return true;
    }


    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        super.onDestroy();
    }


}
