package com.doctor.br.activity.mine.agent;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;

import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.activity.mine.agent.task.SelectAgentActivity;
import com.doctor.br.activity.mine.agent.task.SelectAgentSearchActivity;
import com.doctor.br.activity.mine.agent.task.TaskActivity;
import com.doctor.br.adapter.mine.PendingViewPagerAdapter;
import com.doctor.br.bean.AgentNumberBean;
import com.doctor.br.bean.event.RefreshPendingDataEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;
import com.doctor.zxing.activity.CaptureActivity;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.RequestErrorToast;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

import static com.doctor.br.view.SelectPhotoDialog.WRITE_EXTERNAL_STORAGE_AND_CAMERA_REQUEST_CODE;

/**
 * 类描述：待处理界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/9
 */

public class PendingActivity extends ActionBarActivity {
    //上个界面传递过来的数据
    public static final String SCAN_NUM = "scanNum";
    public static final String ACCEPT_NUM = "acceptNum";
    public static final String IS_DITUI = "isDitui";
    private int scanNumber, acceptNumber;//待扫描，待接收数量
    private String isDitui;//经纪人身份
    //界面下的控件
    @BindView(R.id.receive_tv)
    TextView receiveTv;
    @BindView(R.id.receive_linear)
    LinearLayout receiveLinear;
    @BindView(R.id.scan_tv)
    TextView scanTv;
    @BindView(R.id.scan_linear)
    LinearLayout scanLinear;
    @BindView(R.id.bottom_line)
    LinearLayout bottomLine;
    @BindView(R.id.view_pager)
    ViewPager viewPager;

    private List<int[]> paddingList;//bottomLine的padding

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_pending);
        EventBusUtils.register(this);
        getIntentData(savedInstanceState);
        initView();
        setNumber(acceptNumber, scanNumber);
        setList();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(ACCEPT_NUM, acceptNumber);
        outState.putInt(SCAN_NUM, scanNumber);
        outState.putString(IS_DITUI, isDitui);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            acceptNumber = savedInstanceState.getInt(ACCEPT_NUM, 0);
            scanNumber = savedInstanceState.getInt(SCAN_NUM, 0);
            isDitui = savedInstanceState.getString(IS_DITUI, isDitui);
        } else {
            acceptNumber = getIntent().getIntExtra(ACCEPT_NUM, 0);
            scanNumber = getIntent().getIntExtra(SCAN_NUM, 0);
            isDitui = getIntent().getStringExtra(IS_DITUI);
        }
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("待处理");
        getBaseActionBar().setRightButtonImg(R.drawable.pending_scan_img);
        getBaseActionBar().setActionBarChangeListener(this);

        receiveLinear.setOnClickListener(this);
        scanLinear.setOnClickListener(this);
        viewPager.setAdapter(new PendingViewPagerAdapter(getSupportFragmentManager(), isDitui));
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                setPaddingScroll(position, positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                switchColor(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }


    @Override
    public void onRightBtnClick(View view) {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED || ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {
            //申请WRITE_EXTERNAL_STORAGE权限
            requestPermissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.CAMERA},
                    WRITE_EXTERNAL_STORAGE_AND_CAMERA_REQUEST_CODE,"必然中医需要：\n1.使用相机权限用于拍照\n2.使用存储权限用于保存文件\n，是否同意使用？");
            return;
        }
        startActivity(new Intent(this, CaptureActivity.class));
//        ToastUtils.showShortMsg("二维码扫描");
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.receive_linear:
                viewPager.setCurrentItem(0);
                break;
            case R.id.scan_linear:
                viewPager.setCurrentItem(1);
                break;
            default:
                break;
        }
    }

    //获取文字左右边距，设置数据，文本修改重新调用就可以了
    private void setList() {
        paddingList = new ArrayList<>(2);
        receiveLinear.post(new Runnable() {
            @Override
            public void run() {
                int marginPx = DensityUtils.dip2px(PendingActivity.this, 5f);
                int[] leftRight1 = new int[2];//左边是到父布局左侧距离，右边是到父布局右侧距离
                leftRight1[0] = receiveTv.getLeft() - marginPx;
                leftRight1[1] = receiveLinear.getMeasuredWidth() - receiveTv.getRight() - marginPx;
                paddingList.add(leftRight1);

                int[] leftRight2 = new int[2];
                leftRight2[0] = scanTv.getLeft() - marginPx;
                leftRight2[1] = scanLinear.getMeasuredWidth() - scanTv.getRight() - marginPx;
                paddingList.add(leftRight2);
                setPaddingScroll(viewPager.getCurrentItem(), 0, 0);
            }
        });
    }

    //设置下划线滑动
    private void setPaddingScroll(int position, float positionOffset, int positionOffsetPixels) {
        if (paddingList.size() == 0 || position == paddingList.size() - 1) {
            return;
        }
        double eachWidth = bottomLine.getMeasuredWidth() / paddingList.size();
        double left, right;
        double nextLeft = paddingList.get(position + 1)[0] + (position + 1) * eachWidth;
        double nowLeft = paddingList.get(position)[0] + position * eachWidth;
        double nextRight = paddingList.get(position + 1)[1] + (paddingList.size() - 2 - position) * eachWidth;
        double nowRight = paddingList.get(position)[1] + (paddingList.size() - 1 - position) * eachWidth;
        left = positionOffset * (nextLeft - nowLeft) + nowLeft;
        right = positionOffset * (nextRight - nowRight) + nowRight;
        bottomLine.setPadding((int) left, 0, (int) right, 0);
    }

    //切换字体颜色
    private void switchColor(int position) {
        receiveTv.setTextColor(ContextCompat.getColor(this, R.color.br_color_et_hint));
        scanTv.setTextColor(ContextCompat.getColor(this, R.color.br_color_et_hint));
        switch (position) {
            case 0:
                receiveTv.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme));
                break;
            case 1:
                scanTv.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme));
                break;
            default:
                break;
        }
    }

    /**
     * 设置数量
     *
     * @param acceptNum 待接收数量
     * @param scanNum   待扫描数量
     */
    private void setNumber(int acceptNum, int scanNum) {
        if (scanNum > 0) {
            scanTv.setText("待扫描(" + scanNum + ")");
        } else {
            scanTv.setText("待扫描");
        }

        if (acceptNum > 0) {
            receiveTv.setText("待接受(" + acceptNum + ")");
        } else {
            receiveTv.setText("待接受");
        }
    }

    private RequestCallBack pendingCallBack;//请求数量回调

    /**
     * 网络请求待处理任务个数
     */
    private void getPendingNumberRequest() {
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        pendingCallBack = addHttpPostRequest(HttpUrlManager.PENDING_NUM, map, AgentNumberBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if(scanTv==null){
            return;
        }
        switch (taskId) {
            case HttpUrlManager.PENDING_NUM:
                if (result.isRequestSuccessed()) {
                    //网络请求成功刷新数字
                    AgentNumberBean agentNumberBean = (AgentNumberBean) result.getBodyObject();
                    setNumber(agentNumberBean.getWaitAccept(), agentNumberBean.getWaitScan());
                    setList();
                } else {
                    RequestErrorToast.showError(this,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 刷新待扫描、待接收、待处理、待审核、待分配数量
     *
     * @send {@link com.doctor.zxing.activity.CaptureActivity#refreshData()}
     * @send {@link com.doctor.br.fragment.mine.BeReceiveFragment#refreshData()}
     * @send {@link com.doctor.br.activity.mine.agent.DispatchActivity#refreshNumber()}
     * @send {@link com.doctor.br.fragment.mine.BeScanFragment#sendRefresh()}
     * @send {@link SelectAgentActivity#sendRefreshNumber()}
     * @send {@link SelectAgentSearchActivity#sendRefreshNumber()}
     * @receive {@link SelectAgentActivity#refreshList(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.fragment.mine.BeScanFragment#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.AgentActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.PendingActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link TaskActivity#refreshData(RefreshPendingDataEvent)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshData(RefreshPendingDataEvent refreshPendingDataEvent) {
        if (refreshPendingDataEvent != null) {
            if (refreshPendingDataEvent.isFreshScan() || refreshPendingDataEvent.isFreshReceive()) {
                getPendingNumberRequest();
            }
        }
    }


    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        if (pendingCallBack != null) {
            pendingCallBack.cancel();
        }
        super.onDestroy();
    }
}
