package com.doctor.br.activity.dialog;

import android.content.Intent;
import android.os.Bundle;
import android.widget.ImageView;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;

import com.doctor.br.activity.ActionBarWebViewActivity;
import com.doctor.br.bean.ActiveBean;
import com.doctor.br.bean.ActiveListResult;
import com.doctor.br.utils.DateUtils;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.Banner;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.widgets.BannerView;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 活动数据展示的Activity
 * @createTime 2017/12/26
 */

public class ActiveListDialogActivity extends NoActionBarActivity implements BannerView.OnItemClickedListener {
    @BindView(R2.id.ll_point)
    RadioGroup llPoint;
    @BindView(R2.id.rl_active)
    RelativeLayout rlActive;
    @BindView(R2.id.layout_active)
    RelativeLayout layoutActive;
    @BindView(R2.id.banner_view)
    BannerView bannerView;
    @BindView(R2.id.btn_close)
    ImageView btnClose;
    private ActiveListResult activeListResult;
    private ArrayList<Banner> bannersList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.zoomin, R.anim.zoomout);
        setActionBarContentView(R.layout.activity_active_list_dialog);
        activeListResult = (ActiveListResult) getIntent().getSerializableExtra(PublicParams.ACTIVE_LIST_RESULT);
        initView();
        SharedPreferenceForeverUtils.putString(this, PublicParams.SHOW_ACTIVE_TIME, DateUtils.getNowDate("yyyy-MM-dd"));
    }

    private void initView() {
        bannerView.setOnItemClickedListener(this);
        bannersList = new ArrayList<>();
        List<ActiveBean> list = activeListResult.getList();
        if (list != null) {
            for (ActiveBean activeBean : list) {
                Banner banner = new Banner();
                banner.setId(activeBean.getId());
                banner.setTitle(activeBean.getTitle());
                banner.setHttpUrl(activeBean.getWebUrl());
                banner.setImgUrl(activeBean.getImgUrl());
                bannersList.add(banner);
            }
            bannerView.setData(bannersList);
        }
    }

    @OnClick(R2.id.btn_close)
    public void onViewClicked() {
        finish();
        overridePendingTransition(R.anim.zoomin, R.anim.zoomout);
    }


    @Override
    public void onBackPressed() {
//        super.onBackPressed();
    }

    @Override
    public void onItemClicked(int position, Banner banner) {
        Intent intent = new Intent(this, ActionBarWebViewActivity.class);
        intent.putExtra(PublicParams.WEBVIEW_TITLE, banner.getTitle());
        intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, banner.getHttpUrl());
        startActivity(intent);
    }
}
