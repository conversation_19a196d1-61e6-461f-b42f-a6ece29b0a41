package com.doctor.br.activity.manage;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.doctor.br.app.AppContext;
import com.doctor.br.bean.DoctorInfoBean;
import com.doctor.br.bean.LastRecordBean;
import com.doctor.br.bean.MaxMoneyBean;
import com.doctor.br.bean.UserInfo;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.DialogHelper;
import com.doctor.br.utils.OrderAndWalletDialog;
import com.doctor.br.utils.UIHelper;
import com.doctor.br.view.BottomPopWindow;
import com.doctor.br.view.WithdrawPasswordDialog;
import com.doctor.br.bean.PopItem;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.BaseViewActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.utils.MD5Utils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 提现到银行卡界面
 */
public class GetMoneyActivity extends BaseViewActivity {

    private double maxMoney;
    @BindView(R.id.et_money)
    EditText etMoney;
    @BindView(R.id.tv_card)
    TextView tvCardType;
    @BindView(R.id.tv_number)
    TextView tvNumber;
    private String cardNumber;//完整银行卡号
    private OrderAndWalletDialog orderAndWalletDialog;//验证码

    public static final String EXTRA_MONEY = "extra_money";//获取最大可提现金额

    public static int REQUEST_BIND_CARD = 31;//绑定银行卡的返回码
    private String cardName;//卡的用户
    private String cardType;//卡的类型
    private double priceDouble;//最大可提现金额
    private ConfirmDialog confirmDialog;
    private RequestCallBack getMoneyCallBack;
    private RequestCallBack maxMoneyCallBack;
    private RequestCallBack recordCallBack;
    private RequestCallBack doctorInfoCallBack;
    
    // 密码验证相关属性
    private String checkPasswordResponseStr;
    
    // 新增：验证相关属性
    private String currentVerificationType; // "password" 或 "sms"
    private String verificationCode; // 存储密码或验证码

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_get_money);
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarTitle("提现到银行卡");
        getIntentData();
        init();
        setListener();
        //获取最大提现金额
        maxMoneyCallBack = addHttpPostRequest(HttpUrlManager.CASH_MONEY, null, MaxMoneyBean.class, this);
    }

    public void getIntentData() {
       //设置 可提现金额
      /*  maxMoney = getIntent().getDoubleExtra(EXTRA_MONEY, 0);
        if (maxMoney > 0) {//不为空
            etMoney.setHint("本次可提现" + maxMoney + "元");
        } else {
            //Todo
            etMoney.setHint("本次可提现" + "0" + "元");
        }*/
    }

    private void init() {
        doctorInfoCallBack = addHttpPostRequest(HttpUrlManager.DOCTOR_INFO, null, DoctorInfoBean.class, this);
        //获取绑定的银行卡信息
        recordCallBack = addHttpPostRequest(HttpUrlManager.LAST_BANK_RECORD, RequestParams.getLastBankRecordParams(), LastRecordBean.class, this);

        //获取验证码 的dialog
        orderAndWalletDialog = new OrderAndWalletDialog(this, false, OrderAndWalletDialog.WhichActivity.WALLET_ACTIVITY, new OrderAndWalletDialog.VisibilityListener() {
            @Override
            public void changeSuccess(boolean isShow) { //验证码校验成功
                currentVerificationType = "sms";
                verificationCode = orderAndWalletDialog.getSmsCode(); // 需要添加此方法到 OrderAndWalletDialog
                proceedToWithdraw();
            }
        });
        orderAndWalletDialog.setTvTitle("短信验证");
        orderAndWalletDialog.setBtnCancel("取消");
        orderAndWalletDialog.setBtnConfirm("确认");
        orderAndWalletDialog.isJustCheckCode(true);
    }


    private void setListener() {
        etMoney.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 如果是空字符串，直接返回
                if (s == null || s.length() == 0) return;

                String currentString = s.toString();
                String futureString = currentString;

                // 替换中文句号为英文小数点
                if (currentString.contains("。")) {
                    futureString = currentString.replace("。", ".");
                    etMoney.setText(futureString);
                    etMoney.setSelection(futureString.length());
                    return;
                }

                // 处理首位输入小数点的情况
                if (futureString.startsWith(".")) {
                    futureString = "0" + futureString;
                    etMoney.setText(futureString);
                    etMoney.setSelection(futureString.length());
                    return;
                }

                // 处理以0开头的情况
                if (futureString.length() > 1 && futureString.startsWith("0") && !futureString.startsWith("0.")) {
                    etMoney.setText(futureString.substring(0, 1));
                    etMoney.setSelection(1);
                    return;
                }

                // 限制小数点后最多两位
                if (futureString.contains(".")) {
                    int dotIndex = futureString.indexOf(".");
                    if (futureString.length() - dotIndex > 3) {
                        futureString = futureString.substring(0, dotIndex + 3);
                        etMoney.setText(futureString);
                        etMoney.setSelection(futureString.length());
                        return;
                    }
                }

                // 修改: 限制整数部分长度不超过4位，并添加Toast提示
                String integerPart = futureString.contains(".") ?
                        futureString.split("\\.")[0] : futureString;
                if (integerPart.length() > 4) {
                    // 添加: 当尝试输入超过4位数时显示Toast提示
                    ToastUtils.showShortMsg(mContext, "本次最大提现金额不能超过9999");

                    if (futureString.contains(".")) {
                        String[] parts = futureString.split("\\.");
                        futureString = parts[0].substring(0, 4) + "." + parts[1];
                    } else {
                        futureString = futureString.substring(0, 4);
                    }
                    etMoney.setText(futureString);
                    etMoney.setSelection(futureString.length());
                    return;
                }

                // 验证金额上限
                try {
                    double inputAmount = Double.parseDouble(futureString);
                    // 设置实际最大可提现金额,不允许超过9999
                    double actualMaxAmount = Math.min(maxMoney, 9999.0);

                    // 如果输入金额超过可提现金额,给出提示并修正
                    if (inputAmount > actualMaxAmount) {
                        ToastUtils.showShortMsg(mContext,
                                String.format("本次最大提现金额为%.2f", actualMaxAmount));
                        String maxString = String.format("%.2f", actualMaxAmount);
                        etMoney.setText(maxString);
                        etMoney.setSelection(maxString.length());
                        return;
                    }
                } catch (NumberFormatException e) {
                    // 处理数字格式异常
                    return;
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });
    }

    @Override
    @OnClick({R.id.rl_rule, R.id.btn_get_money, R.id.rl_bind_card})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.rl_rule://打开提现规则
                UIHelper.openGetMoneyRuleActivity(this,false);
                break;
            case R.id.btn_get_money://提现
                if (checkGetMoneyParam()) {//校验
                    if(priceDouble<999){//提现金额<999
                        confirmDialog = DialogHelper.openConfirmDialog(this, "您的提现金额未满999，需要您支付1元手续费，是否提现？", "提现", "取消", new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                confirmDialog.dismiss();
                                showVerificationTypeActionSheet();
                            }
                        });
                        confirmDialog.show();
                    }else{//提现金额>=999
                        showVerificationTypeActionSheet();
                    }

                }

                break;
            case R.id.rl_bind_card://打开绑定银行卡
                UIHelper.openBindCardActivity(this, REQUEST_BIND_CARD, cardName, cardNumber,cardType);
                break;
        }
    }

    /**
     * 校验提现前的参数
     * @return
     */
    private boolean checkGetMoneyParam() {

        cardType = tvCardType.getText().toString().trim();
        String price = etMoney.getText().toString().trim();
        if (TextUtils.isEmpty(cardNumber) | TextUtils.isEmpty(cardName) | TextUtils.isEmpty(cardType)) {
            ToastUtils.showShortMsg(this, "请绑定银行卡！");
            return false;
        }
        if (TextUtils.isEmpty(price)) {
            ToastUtils.showShortMsg(this, "请输入提现金额");
            return false;
        } else {
            String regEx = "^(([0-9]+\\.[0-9]*[0-9][0-9]*)|([0-9]*[0-9][0-9]*\\.[0-9]+)|([0-9]*[0-9][0-9]*))$";
            Pattern pattern = Pattern.compile(regEx);
            if (pattern != null) {
                Matcher matcher = pattern.matcher(price);
                if (matcher != null) {
                    boolean b = matcher.matches();
                    if (!b) {
                        ToastUtils.showShortMsg(this, "提现金额只能是数字！");
                        return false;
                    }
                }
            }
        }
        try {
            priceDouble = Double.valueOf(price);//转换为double类型
        } catch (Exception e) {
            priceDouble = 0;
        }

        if (maxMoney < priceDouble) {
            ToastUtils.showShortMsg(this, "您今日可提现的金额不得大于" + maxMoney + "元");
            return false;
        }

        if (0== priceDouble) {
            ToastUtils.showShortMsg(this, "请输入正确提现金额！");
            return false;

        }
            return true;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent intent) {
        super.onActivityResult(requestCode, resultCode, intent);
        if (requestCode == REQUEST_BIND_CARD && resultCode == RESULT_OK) {//获取绑定银行卡界面返回的数据
            String cardUserName = intent.getStringExtra(BindCardActivity.RESULT_EXTRA_CARD_USERNAME);
            String cardnumber = intent.getStringExtra(BindCardActivity.RESULT_EXTRA_CARD_NUMBER);
            String cardType = intent.getStringExtra(BindCardActivity.RESULT_EXTRA_CARD_TYPE);

            if (!TextUtils.isEmpty(cardnumber)) { //设置卡号
                this.cardNumber = cardnumber;
                String payContent = "";
                if (cardNumber.length() >= 4) {
                    payContent = "(" + cardNumber.substring(cardNumber.length() - 4, cardNumber.length()) + ")";
                } else {
                    payContent = "(" + cardNumber + ")";
                }

                tvNumber.setText(payContent);
            }
            if (!TextUtils.isEmpty(cardType)) {//设置卡类型
                this.cardType = cardType;
                tvCardType.setHint("");
                tvCardType.setText(cardType);
            }
            if (!TextUtils.isEmpty(cardUserName)) {
                this.cardName = cardUserName;
            }


        }

    }

    @Override
    public void onRequestFinished(String taskId, boolean successed, String responseStr) {
        super.onRequestFinished(taskId, successed, responseStr);
        
        if (HttpUrlManager.CHECK_WITHDRAW_PASSWORD_STATUS.equals(taskId)) {
            // 保存原始响应字符串，供另一个回调方法使用
            checkPasswordResponseStr = responseStr;
        }
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId){
            case HttpUrlManager.LAST_BANK_RECORD://获取最后一次付款记录
                resolveLastBankRecord(taskId,result);
                break;
            case HttpUrlManager.GET_MONEY://提现
                if (result.isRequestSuccessed()) {
                    if (orderAndWalletDialog != null) {
                        orderAndWalletDialog.dismissShowDialog();
                    }
                    ToastUtils.showShortMsg(this, "提交成功，等待审核！");
                    setResult(RESULT_OK);
                    finish();
                }else{//网络请求失败
                    RequestErrorToast.showError(this,taskId,result.getCode(),result.getErrorMsg());
                }
                break;
            case HttpUrlManager.CASH_MONEY://最大可提现金额
                if(result.isRequestSuccessed()){
                    MaxMoneyBean maxMoneyBean = (MaxMoneyBean) result.getBodyObject();
                    if(maxMoneyBean!=null){
                        maxMoney = Double.parseDouble(maxMoneyBean.getCanCashPrice());
                        if(maxMoney == 0){//测试让显示0.00元
                            etMoney.setHint("本次可提现0.00元");
                        }else{
                            etMoney.setHint("本次可提现" + maxMoney + "元");
                        }
                    }

                }else{ //网络请求失败
                    RequestErrorToast.showError(this,taskId,result.getCode(),result.getErrorMsg());
                    finish();
                }
                break;
            case HttpUrlManager.DOCTOR_INFO://最大可提现金额
                 if (result.isRequestSuccessed()) {
                     DoctorInfoBean bean = (DoctorInfoBean) result.getBodyObject();
                UserInfo userInfo = AppContext.getInstances().getLoginInfo();
                if (userInfo != null) {
                    userInfo.setWxOpenId(bean.getWxOpenId());
                    userInfo.setSupportWxHome(bean.getSupportWxHome());
                    userInfo.setWxOpenIdHome(bean.getWxOpenIdHome());
                    AppContext.getInstances().saveLoginInfo(userInfo);
                }
            } else {
                ToastUtils.showShortMsg(this, "请求失败，请稍候重试");
            }
                break;
            case HttpUrlManager.CHECK_WITHDRAW_PASSWORD_STATUS://检查提现密码状态
                if (result.isRequestSuccessed()) {
                    try {
                        // 直接从原始响应字符串中解析 hasCashPwd 字段
                        if (checkPasswordResponseStr != null) {
                            org.json.JSONObject jsonObject = new org.json.JSONObject(checkPasswordResponseStr);
                            boolean hasPassword = jsonObject.optBoolean("hasCashPwd", false);
                            
                            if (hasPassword) {
                                // 已设置密码，显示密码验证弹窗
                                showWithdrawPasswordVerificationDialog();
                            } else {
                                // 未设置密码，跳转到设置提现密码页面
                                Intent intent = new Intent(GetMoneyActivity.this, com.doctor.br.activity.mine.ModifyWithdrawPasswordActivity.class);
                                startActivity(intent);
                            }
                        } else {
                            ToastUtils.showShortMsg(GetMoneyActivity.this, "数据解析出错");
                        }
                    } catch (Exception e) {
                        ToastUtils.showShortMsg(GetMoneyActivity.this, "数据解析出错");
                    }
                } else {
                    RequestErrorToast.showError(GetMoneyActivity.this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            // 移除 VERIFY_WITHDRAW_PASSWORD 的处理，因为新版本不再使用单独的验证接口
        }
    }

    /**
     * 解析“最后一次付款记录”的网络请求
     * @param result
     */
    public void resolveLastBankRecord(String taskId,ResponseResult result) {
        if (result.isRequestSuccessed()) {
            LastRecordBean lastRecordBean = (LastRecordBean) result.getBodyObject();
            // 存在的有绑定的银行卡
            if (lastRecordBean.getExist() != null && lastRecordBean.getExist().equals("1")) {//有记录

                if (!TextUtils.isEmpty(lastRecordBean.getCredNo())) {//卡号
                    cardNumber = lastRecordBean.getCredNo();
                }

                if (!TextUtils.isEmpty(lastRecordBean.getEntyCredNo())) {//卡号后几位
                    tvNumber.setText("(" + lastRecordBean.getEntyCredNo() + ")");
                }
                if (!TextUtils.isEmpty(lastRecordBean.getBankName())) {//卡类型
                    tvCardType.setHint("");
                    tvCardType.setText(lastRecordBean.getBankName());
                    cardType = lastRecordBean.getBankName();
                }
                if (!TextUtils.isEmpty(lastRecordBean.getBankUserName())) {//卡用户
                    cardName = lastRecordBean.getBankUserName();
                }
            }

        } else {
            //网络请求失败
            tvCardType.setHint("请绑定银行卡");
            RequestErrorToast.showError(this,taskId,result.getCode(),result.getErrorMsg());
        }
    }

    /**
     * 显示验证方式选择ActionSheet
     */
    private void showVerificationTypeActionSheet() {
        BottomPopWindow bottomPopWindow = new BottomPopWindow(this);
        bottomPopWindow.setPopTitle("选择验证方式");
        
        // 创建选项列表
        java.util.List<PopItem> options = new java.util.ArrayList<>();
        
        PopItem passwordOption = new PopItem();
        passwordOption.setId("password");
        
        // 创建SpannableString来设置【推荐】为红色
        String passwordText = "密码验证【推荐】";
        SpannableString spannableString = new SpannableString(passwordText);
        int startIndex = passwordText.indexOf("【推荐】");
        int endIndex = startIndex + "【推荐】".length();
        spannableString.setSpan(new ForegroundColorSpan(Color.RED), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        
        passwordOption.setName(passwordText);
        passwordOption.setSpannableName(spannableString);
        passwordOption.setClickEnable(true);
        options.add(passwordOption);
        
        PopItem smsOption = new PopItem();
        smsOption.setId("sms");
        smsOption.setName("短信验证");
        smsOption.setClickEnable(true);
        options.add(smsOption);
        
        bottomPopWindow.setPopContentData(options);
        bottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
            @Override
            public void onPopItemClick(int position, PopItem popItem) {
                bottomPopWindow.dimissPop();
                
                if ("password".equals(popItem.getId())) {
                    // 密码验证
                    handlePasswordVerification();
                } else if ("sms".equals(popItem.getId())) {
                    // 短信验证码验证
                    handleSMSVerification();
                }
            }
        });
        
        bottomPopWindow.showPop();
    }

    /**
     * 处理密码验证
     */
    private void handlePasswordVerification() {
        checkWithdrawPasswordStatus();
    }

    /**
     * 检查是否设置了提现密码
     */
    private void checkWithdrawPasswordStatus() {
        Map<String, String> params = new HashMap<>();
        params.put("method_code", "000454");
        
        RequestCallBack checkPasswordCallBack = addHttpPostRequest(HttpUrlManager.CHECK_WITHDRAW_PASSWORD_STATUS, params, ResponseResult.class, this);
    }

    /**
     * 显示提现密码验证弹窗
     */
    private void showWithdrawPasswordVerificationDialog() {
        WithdrawPasswordDialog passwordDialog = WithdrawPasswordDialog.getInstance(this);
        passwordDialog.setTitle("请输入提现密码")
                .setOnPasswordVerificationListener(new WithdrawPasswordDialog.OnPasswordVerificationListener() {
                    @Override
                    public void onPasswordConfirmed(String password) {
                        // 验证密码
                        verifyWithdrawPassword(password);
                    }

                    @Override
                    public void onPasswordCanceled() {
                        // 用户取消密码验证
                        LogUtils.i(GetMoneyActivity.class, "用户取消密码验证");
                    }
                });
        passwordDialog.show();
    }

    /**
     * 验证提现密码（新版本：直接进入提现流程）
     */
    private void verifyWithdrawPassword(String password) {
        // 新版本：不再调用验证接口，直接设置验证信息并进入提现流程
        // 对密码进行MD5加密并转换为大写
        currentVerificationType = "password";
        verificationCode = MD5Utils.MD5Upper32(password);
        proceedToWithdraw();
    }

    /**
     * 处理短信验证
     */
    private void handleSMSVerification() {
        orderAndWalletDialog.show();
    }

    /**
     * 进入提现流程
     */
    private void proceedToWithdraw() {
        //提现 - 使用新版本API，包含验证参数
        String password = "password".equals(currentVerificationType) ? verificationCode : "";
        String smsCode = "sms".equals(currentVerificationType) ? verificationCode : "";
        
        Map<String, String> params = RequestParams.getGetMoneyParams(cardNumber, cardName, priceDouble + "", cardType, 
                                                                     password, smsCode, "2");
        getMoneyCallBack = addHttpPostRequest(HttpUrlManager.GET_MONEY, params, ResponseResult.class, GetMoneyActivity.this);
    }


    @Override
    protected void onDestroy() {
        cancelRequest(getMoneyCallBack);
        cancelRequest(maxMoneyCallBack);
        cancelRequest(recordCallBack);
        cancelRequest(doctorInfoCallBack);
        if (orderAndWalletDialog != null) {
            orderAndWalletDialog.cancel();
            orderAndWalletDialog = null;
        }
        if(confirmDialog!=null){
            confirmDialog.dismiss();
            confirmDialog = null;
        }
        super.onDestroy();
    }
}
