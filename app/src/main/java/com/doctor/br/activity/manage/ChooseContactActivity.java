package com.doctor.br.activity.manage;

import android.os.Bundle;

import com.doctor.yy.R;

import org.newapp.ones.base.activity.BaseViewActivity;

public class ChooseContactActivity extends BaseViewActivity{
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_choose_contact);
    }
   /* BaseRefreshActivity<ChooseContactBean.ItemsBean> implements BGAOnItemChildClickListener,CompoundButton.OnCheckedChangeListener{
    @BindView(R.id.cb_all)
    CheckBox cbAll;
    @BindView(R.id.btn_submit)
    Button btnSubmit;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_choose_contact;
    }

    @Override
    protected void init() {
        super.init();
        mRefreshAdapter.setOnItemChildClickListener(this);
        mActionBarView.setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        mActionBarView.setActionBarTitle("转发消息");
    }

    @Override
    protected void setListener() {
        super.setListener();
        cbAll.setOnCheckedChangeListener(this);
        mActionBarView.setActionBarChangeListener(new OnActionBarChangeListener() {

            @Override
            public void onShow(ActionBarView barView) {

            }

            @Override
            public void onHide(ActionBarView barView) {

            }

            @Override
            public void onBack(Activity activity) {
                ChooseContactActivity.this.finish();
            }

            @Override
            public void onRightBtnClick(View view) {

            }
        });
    }
    *//**
     * 转化成标准集合，解决javaBean中集合字段不一致的问题
     * @param object
     * @return
     *//*
    @Override
    public List<ChooseContactBean.ItemsBean> convertToStandard(BaseObject object) {
        return null;
    }

    @Override
    public BGARecyclerViewAdapter getAdapter() {
        return new ChooseContactAdapter(mRvData);
    }

    @Override
    public RequestCallBack requestData() {
        //处理数据为空的情况


        //测试使用
        mRefreshLayout.endRefreshing();
        ArrayList<ChooseContactBean.ItemsBean> visitingItems = new ArrayList<>();
        ChooseContactBean.ItemsBean itemsBean = new ChooseContactBean.ItemsBean();
        itemsBean.setType(1);
        visitingItems.add(itemsBean);
        visitingItems.add(new ChooseContactBean.ItemsBean());
        visitingItems.add(new ChooseContactBean.ItemsBean());
        mRefreshAdapter.addNewData(visitingItems);
        mRvData.smoothScrollToPosition(0);

        return null;
    }

    @Override
    @OnClick(R.id.btn_submit)
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_submit://发送
                break;


        }
    }

    @Override
    public boolean getRefreshEnable() {//不能上拉刷新
        return false;
    }//不需要下拉加载更多

    @Override
    public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {//不需要加载更多
        return false;
    }

    @Override
    public void onItemChildClick(ViewGroup parent, View childView,  int position) {
        if(childView.getId() == R.id.rl_container){  //联系人的点击事件
            final ChooseContactBean.ItemsBean itemsBean = (ChooseContactBean.ItemsBean) mRefreshAdapter.getData().get(position);
            itemsBean.setChecked(!itemsBean.isChecked());


            mRefreshAdapter.setItem(position,itemsBean);


          *//*  mRefreshAdapter.getData().set(position,itemsBean);
            System.out.println(mRefreshAdapter.getData());
            LogUtils.i(MainActivity.class,"thread........."+Thread.currentThread().getName());
           *//*

        }
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {//全选的checkbox事件
        List<ChooseContactBean.ItemsBean> itemBeanList = ( List<ChooseContactBean.ItemsBean>) mRefreshAdapter.getData();
        for(int i=0;itemBeanList!=null && i<itemBeanList.size();i++){
            itemBeanList.get(i).setChecked(cbAll.isChecked());
        }
        mRefreshAdapter.setData(itemBeanList);
        LogUtils.i(MainActivity.class,cbAll.isChecked()+"");
    }*/
}
