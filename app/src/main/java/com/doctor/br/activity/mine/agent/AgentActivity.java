package com.doctor.br.activity.mine.agent;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;

import com.doctor.br.activity.mine.agent.framework.FrameworkActivity;
import com.doctor.br.activity.mine.agent.task.SelectAgentActivity;
import com.doctor.br.activity.mine.agent.task.SelectAgentSearchActivity;
import com.doctor.br.activity.mine.agent.task.TaskActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.AgentRecyclerAdapter;
import com.doctor.br.bean.AgentNumberBean;
import com.doctor.br.bean.event.RefreshPendingDataEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：经纪人界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/9/28 17:34
 * 修改人：ShiShaoPo
 * 修改时间：2017/9/28 17:34
 */

public class AgentActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //上个界面传过来的数据
    public static final String NAME = "name";
    public static final String IS_DITUI = "isDitui";
    private String name;//经纪人姓名
    private String isDitui;//经纪人身份
    /**
     * 角色 省负责人，团队负责人，团队职员
     * 0 个人兼职经纪人
     * 1 兼职经纪人
     * 2 兼职团队负责人
     * 3 直营经纪人
     * 4 直营团队负责人
     * 5 省负责人
     * 6 总助
     * 7 总监
     * 8 人事
     */
    public static final String ROLE_PERSON = "0";
    public static final String ROLE_PART = "1";
    public static final String ROLE_PART_TEAM = "2";
    public static final String ROLE_MINE = "3";
    public static final String ROLE_MINE_TEAM = "4";
    public static final String ROLE_PROVINCE = "5";
    public static final String ROLE_ASSISTANT = "6";
    public static final String ROLE_INSPECTOR = "7";
    /**
     * 团队类型
     * 1 直营团队
     * 2 兼职团队
     * 3 个人兼职团队
     */
    public static final String TEAM_TYPE_MINE = "1";
    public static final String TEAM_TYPE_PART = "2";
    public static final String TEAM_TYPE_PERSON = "3";
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    private RequestCallBack pendingCallBack;//网络请求待处理任务个数回调

    private AgentNumberBean netBean;//网络请求返回值

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBusUtils.register(this);
        setActionBarContentView(R.layout.activity_agent);
        getIntentData(savedInstanceState);
        initView();
        getPendingNumberRequest();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(NAME, name);
        outState.putString(IS_DITUI, isDitui);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            name = savedInstanceState.getString(NAME);
            isDitui = savedInstanceState.getString(IS_DITUI);
        } else {
            name = getIntent().getStringExtra(NAME);
            isDitui = getIntent().getStringExtra(IS_DITUI);
        }
//        isDitui = AgentActivity.ROLE_ASSISTANT;//测试使用
        if (TextUtils.isEmpty(isDitui)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle(name);

        recyclerView.setAdapter(new AgentRecyclerAdapter(this, isDitui, netBean, this));
    }


    /**
     * 每个item的点击
     *
     * @param imgId 点击的图片id
     */
    @Override
    public void itemClick(int imgId) {

        Intent intent;
        switch (imgId) {
            case R.drawable.agent_dispatch_img://调度审核
                intent = new Intent(this, DispatchActivity.class);
                intent.putExtra(DispatchActivity.USER_ROLE, isDitui);
                startActivity(intent);
                break;
            case R.drawable.agent_framework_img://组织架构
                intent = new Intent(this, FrameworkActivity.class);
                intent.putExtra(FrameworkActivity.IS_DITUI, isDitui);
                startActivity(intent);
                break;
            case R.drawable.agent_pending_img://待处理
                if (netBean == null) {
                    getPendingNumberRequest();
                    return;
                }
                intent = new Intent(this, PendingActivity.class);
                intent.putExtra(PendingActivity.ACCEPT_NUM, netBean.getWaitAccept());
                intent.putExtra(PendingActivity.SCAN_NUM, netBean.getWaitScan());
                intent.putExtra(PendingActivity.IS_DITUI, isDitui);
                startActivity(intent);
                break;
            case R.drawable.agent_doctor_img://大夫状态
                startActivity(new Intent(this, DoctorStateActivity.class));
//                startActivity(new Intent(this, MyDoctorActivity.class));
                break;
            case R.drawable.agent_exception_doctor_img://异常大夫
                startActivity(new Intent(this, ExceptionDoctorActivity.class));
                break;
            case R.drawable.agent_achievement_img://业绩查询
                startActivity(new Intent(this, AchievementActivity.class));
                break;
            case R.drawable.agent_download_img://下载
                startActivity(new Intent(this, DownloadActivity.class));
                break;
            case R.drawable.agent_task_img://任务分配
                intent = new Intent(this, TaskActivity.class);
                intent.putExtra(TaskActivity.IS_DITUI, isDitui);
                startActivity(intent);
                break;
//            case R.drawable.agent_video_img://推广视频播放
//                intent = new Intent(this, VideoPlayActivity.class);
//
////                intent.putExtra(VideoPlayActivity.VIDEO_URL, "http://devapi.haoniuzhongyi.top:8082/filestore/videos/brzy-welcome.mp4");
//
//                intent.putExtra(VideoPlayActivity.VIDEO_URL, "http://img.haoniuzhongyi.top:18088/filestore/voices/brzy-welcome.mp4");
//                startActivity(intent);
//                break;
            default:
                break;
        }
    }


    /**
     * 网络请求待处理任务个数
     */
    private void getPendingNumberRequest() {
        if (AgentActivity.ROLE_ASSISTANT.equals(isDitui)) {
            return;
        }
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        pendingCallBack = addHttpPostRequest(HttpUrlManager.PENDING_NUM, map, AgentNumberBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (recyclerView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.PENDING_NUM:
                if (result.isRequestSuccessed()) {
                    //网络请求成功刷新数字
                    netBean = (AgentNumberBean) result.getBodyObject();
                    recyclerView.setAdapter(new AgentRecyclerAdapter(this, isDitui, netBean, this));
                } else {
                    netBean = null;
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 刷新待扫描、待接收、待处理、待审核、待分配数量
     *
     * @send {@link com.doctor.zxing.activity.CaptureActivity#refreshData()}
     * @send {@link com.doctor.br.fragment.mine.BeReceiveFragment#refreshData()}
     * @send {@link com.doctor.br.activity.mine.agent.DispatchActivity#refreshNumber()}
     * @send {@link com.doctor.br.fragment.mine.BeScanFragment#sendRefresh()}
     * @send {@link SelectAgentActivity#sendRefreshNumber()}
     * @send {@link SelectAgentSearchActivity#sendRefreshNumber()}
     * @receive {@link SelectAgentActivity#refreshList(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.fragment.mine.BeScanFragment#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.AgentActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.PendingActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link TaskActivity#refreshData(RefreshPendingDataEvent)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshData(RefreshPendingDataEvent refreshPendingDataEvent) {
        if (refreshPendingDataEvent != null) {
            if (refreshPendingDataEvent.isFreshScan() || refreshPendingDataEvent.isFreshReceive() ||
                    refreshPendingDataEvent.isFreshDispatch() || refreshPendingDataEvent.isFreshTask()) {
                getPendingNumberRequest();
            }
        }
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        if (pendingCallBack != null) {
            pendingCallBack.cancel();
        }
        super.onDestroy();
    }
}
