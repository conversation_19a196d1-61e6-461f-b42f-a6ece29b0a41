package com.doctor.br.activity.mine.agent.doctorState;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.doctor.br.activity.mine.agent.DoctorDetailsActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.AgentQualificationingDoctorReyclerAdapter;
import com.doctor.br.bean.AgentQualificationingDoctorBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：经纪人-大夫状态-认证中医生界面
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/7
 */

public class AgentQualificationingActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;
    //审核中大夫列表
    private List<AgentQualificationingDoctorBean.DoctorMessagesBean> doctorList;
    //列表适配器
    private AgentQualificationingDoctorReyclerAdapter adapter;
    //网络请求认证中大夫列表回调
    private RequestCallBack doctorListCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_agent_qualificationing);
        initView();
        getQualificationingDoctorList();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("认证中");

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setHasFixedSize(true);

        doctorList = new ArrayList<>();
        adapter = new AgentQualificationingDoctorReyclerAdapter(this, doctorList, this);
        recyclerView.setAdapter(adapter);
    }

    /**
     * recyclerview每条item的点击事件
     *
     * @param position 点击的item
     */
    @Override
    public void itemClick(int position) {
        Intent intent = new Intent(this, DoctorDetailsActivity.class);
        intent.putExtra(DoctorDetailsActivity.DOCTOR_ID, doctorList.get(position).getDoctorId());
        startActivity(intent);
    }

    /**
     * 网络请求获取认证中大夫列表
     */
    private void getQualificationingDoctorList() {
        emptyView.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        doctorListCallBack = addHttpPostRequest(HttpUrlManager.QUALIFICATIONING, map, AgentQualificationingDoctorBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (emptyView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.QUALIFICATIONING:
                if (result.isRequestSuccessed()) {
                    AgentQualificationingDoctorBean doctorBean = (AgentQualificationingDoctorBean) result.getBodyObject();
                    doctorList.clear();
                    doctorList.addAll(doctorBean.getDoctorMessages());
                    adapter.notifyDataSetChanged();
                    if (doctorList.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        recyclerView.setVisibility(View.GONE);
                        emptyView.setVisibility(View.VISIBLE);
                        emptyView.setEmptyText("暂无大夫");
                    }

                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    recyclerView.setVisibility(View.GONE);
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            getQualificationingDoctorList();
                        }
                    });
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (doctorListCallBack != null) {
            doctorListCallBack.cancel();
        }
        super.onDestroy();
    }


}
