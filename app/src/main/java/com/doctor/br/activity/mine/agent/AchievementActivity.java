package com.doctor.br.activity.mine.agent;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.doctor.br.activity.mine.agent.achievement.AverageUnitPriceActivity;
import com.doctor.br.activity.mine.agent.achievement.OrderPriceActivity;
import com.doctor.br.activity.mine.agent.achievement.PatientNumbersActivity;
import com.doctor.br.activity.mine.agent.achievement.PerformanceSummaryActivity;
import com.doctor.br.activity.mine.agent.achievement.PrescriptionNumbersActivity;
import com.doctor.br.activity.mine.agent.achievement.RatioIncomeActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.AchievementRecyclerAdapter;
import com.doctor.br.bean.AchievementBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.view.RecyclerDivider;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;

import java.util.HashMap;
import java.util.Map;


/**
 * 类描述：业绩查询
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/9 9:54
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/9 9:54
 */

public class AchievementActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //界面下的控件
    private RecyclerView achievementRecycler;

    private RequestCallBack achievementCallBack;//业绩查询回调

    private AchievementBean netBean;//网络请求返回值

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_performance_enquiry);
        initView();
        getAchievementRequest();
//        ToastUtils.showShortMsg("等待接口联调");
    }

    private void initView() {
        setActionBarTitle(R.string.performance_enquiry);

        achievementRecycler = (RecyclerView) findViewById(R.id.recycler_view);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 3);
        achievementRecycler.setLayoutManager(gridLayoutManager);
        achievementRecycler.setHasFixedSize(true);
        achievementRecycler.addItemDecoration(new RecyclerDivider(this, true));
        achievementRecycler.setAdapter(new AchievementRecyclerAdapter(this, netBean, this));
    }


    @Override
    public void itemClick(int position) {
        if (netBean == null) {
            getAchievementRequest();
            return;
        }
        Intent intent = new Intent();
        switch (position) {
            case 0://患者数量
            default:
                intent.setClass(this, PatientNumbersActivity.class);
                startActivity(intent);
                break;
            case 1://药方数量
                intent.setClass(this, PrescriptionNumbersActivity.class);
                startActivity(intent);
                break;
            case 2://配比收入
                intent.setClass(this, RatioIncomeActivity.class);
                startActivity(intent);
                break;
            case 3://均客单价
                intent.setClass(this, AverageUnitPriceActivity.class);
                startActivity(intent);
                break;
            case 4://订单金额
                intent.setClass(this, OrderPriceActivity.class);
                startActivity(intent);
                break;
            case 5://业绩汇总
                intent.setClass(this, PerformanceSummaryActivity.class);
                startActivity(intent);
                break;
        }
    }

    /**
     * 网络请求获取业绩查询页面数据
     */
    private void getAchievementRequest() {
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        achievementCallBack = addHttpPostRequest(HttpUrlManager.ACHIEVEMENT, map, AchievementBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (achievementRecycler == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.ACHIEVEMENT://业绩查询页面数据
                if (result.isRequestSuccessed()) {
                    netBean = (AchievementBean) result.getBodyObject();
                    achievementRecycler.setAdapter(new AchievementRecyclerAdapter(this, netBean, this));
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (achievementCallBack != null) {
            achievementCallBack.cancel();
        }
        super.onDestroy();
    }
}
