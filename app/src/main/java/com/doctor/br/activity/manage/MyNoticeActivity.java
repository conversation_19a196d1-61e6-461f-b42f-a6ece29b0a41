package com.doctor.br.activity.manage;

import android.app.Activity;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.DialogHelper;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.BaseViewActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.ConfirmDialog;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 我的公告的界面
 */
public class MyNoticeActivity extends BaseViewActivity {

    @BindView(R.id.et_content)
    EditText etContent;
    @BindView(R.id.tv_length)
    TextView tvLength;
    @BindView(R.id.btn_release)
    Button btnRelease;
    private String preNoticeContent;
    private RequestCallBack noticeCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_my_notice);
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarTitle("我的公告");
        setListener();
        preNoticeContent = SharedPreferenceUtils.getString(this, PublicParams.MY_NOTICE);//上次发布成功后的公告内容
//        if(!TextUtils.isEmpty(preNoticeContent)){
//            etContent.setText(preNoticeContent);
//            etContent.setSelection(preNoticeContent.length());
//        }

    }

    /**
     * 设置监听
     */
    private void setListener() {
        etContent.addTextChangedListener(new TextWatcher() {//设置etContent的内容监听
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                int length = etContent.getText().toString().length();
                tvLength.setText(length+"/1000");

            }
        });
    }

    @Override
    public void onBack(Activity activity) {//点击返回
        finishWithEdit();
    }



    @Override
    public void onBackPressed() {//返回
        finishWithEdit();
    }

    /**
     * 用于判断输入内容是否为空后，的关闭时间
     */
    public void finishWithEdit(){
        LogUtils.log("MyNoticeActivity......onBack");
        String content = etContent.getText().toString().trim();
        if(!preNoticeContent.equals(content)){//弹框
            ConfirmDialog confirmDialog = DialogHelper.openConfirmDialog(this, "公告内容未保存，是否退出？", "退出", "取消", new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    MyNoticeActivity.this.finish();
                }
            });
            confirmDialog.show();
        }else{
            finish();
        }
    }

    @Override
    @OnClick(R.id.btn_release)
    public void onClick(View view) {
       switch (view.getId()){
           case R.id.btn_release://发布公告
               String content = etContent.getText().toString().trim();
               if(TextUtils.isEmpty(content)){
                   ToastUtils.showShortMsg(this,"请输入公告内容...");
                   return ;
               }
                //发布公告
               noticeCallBack = addHttpPostRequest(HttpUrlManager.MY_NOTICE, RequestParams.getMyNoticeParams(content), ResponseResult.class, this);
               break;
       }
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        //判断
        switch (taskId){
            case HttpUrlManager.MY_NOTICE:
                if(result.isRequestSuccessed()){//发布成功
                    ToastUtils.showShortMsg(this,"发布成功");
                    SharedPreferenceUtils.putString(this, PublicParams.MY_NOTICE,etContent.getText().toString().trim());
                    finish();
                }else{//网络请求失败
                    RequestErrorToast.showError(mContext,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
        }

    }

    @Override
    protected void onDestroy() {
        cancelRequest(noticeCallBack);
        super.onDestroy();
    }
}
