package com.doctor.br.activity.dialog;

import android.Manifest;
import android.app.Activity;
import android.app.DownloadManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.bean.CheckUpdateResult;
import com.doctor.br.receiver.DownloadManagerReceiver;
import com.doctor.br.utils.FileUtils;
import com.doctor.br.utils.MyPathManager;
import com.doctor.br.utils.download.DownloadRequestTask;
import com.doctor.br.view.NumberProgressBar;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.io.File;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 版本更新Activity
 * @createTime 2017/12/19
 */

public class UpdateDownloadActivity extends NoActionBarActivity {
    private static final int UPDATE_PROGRESS = 1;
    private static final int DOWNLOAD_ERROR = 2;
    private static final int DOWNLOAD_COMPLETE = 3;
    private static final int REQUEST_INSTALL_PACKAGE = 1123;
    private static final int INSTALL_PERMISS_CODE = 2131;

    @BindView(R2.id.tv_update_content)
    TextView tvUpdateContent;
    @BindView(R2.id.btn_navigation)
    Button btnNavigation;
    @BindView(R2.id.btn_update_now)
    Button btnUpdateNow;
    @BindView(R2.id.update_layout)
    LinearLayout updateLayout;
    @BindView(R2.id.pb_progress)
    NumberProgressBar pbProgress;
    @BindView(R2.id.btn_reload)
    Button btnReload;
    @BindView(R2.id.download_layout)
    LinearLayout downloadLayout;
    @BindView(R2.id.btn_complete)
    Button btnComplete;
    private CheckUpdateResult checkUpdateResult;

    private boolean isForce = false;//是否强制更新
    private long downloadId;
    private volatile int progress = 0;
    private DownloadRequestTask requestTask;
    private UpdateProgressThread updateProgressThread;

    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case UPDATE_PROGRESS:
                    DownloadRequestTask.DownloadInfo info = (DownloadRequestTask.DownloadInfo) msg.obj;
                    if (info == null || info.getStatus() == DownloadManager.STATUS_FAILED) {
                        btnReload.setVisibility(View.VISIBLE);
                        if (updateProgressThread != null) {
                            updateProgressThread.setStop(true);
                            updateProgressThread = null;
                        }
                    } else {
                        btnReload.setVisibility(View.GONE);
                        progress = info.getProgress();
                        pbProgress.setProgress(info.getProgress());
                        if (progress == 100) {
                            btnComplete.setVisibility(View.VISIBLE);
                            btnComplete.setTag(info);
                        }
                    }
                    break;
                case DOWNLOAD_ERROR:
                    break;
                case DOWNLOAD_COMPLETE:
                    break;
            }
        }
    };


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.zoomin, R.anim.zoomout);
        setActionBarContentView(R.layout.activity_update_app);
        checkUpdateResult = (CheckUpdateResult) getIntent().getSerializableExtra(PublicParams.UPDATE_APP_BEAN);
        requestTask = DownloadRequestTask.getInstance(this);
        initView();
    }

    /**
     * 初始化View
     */
    private void initView() {
        if (tvUpdateContent == null || btnNavigation == null || btnUpdateNow == null) {
            return;
        }
        updateLayout.setVisibility(View.VISIBLE);
        downloadLayout.setVisibility(View.GONE);
        if (checkUpdateResult != null) {
            tvUpdateContent.setText(checkUpdateResult.getUpdateContent() + "");
            isForce = "1".equalsIgnoreCase(checkUpdateResult.getIsForce());
            if (isForce) {
                //强制更新
                btnNavigation.setVisibility(View.GONE);
            } else {
                btnNavigation.setVisibility(View.VISIBLE);
            }
        }
    }


    @OnClick({R.id.btn_navigation, R.id.btn_update_now, R.id.btn_reload, R.id.btn_complete})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_navigation:
                finishActivity();
                break;
            case R.id.btn_reload:
            case R.id.btn_update_now:
                if (checkUpdateResult != null && !TextUtils.isEmpty(checkUpdateResult.getVersionNo())) {
                    downloadId = requestTask.addTask(checkUpdateResult.getUrl(), "必然中医Android" + checkUpdateResult.getVersionNo() + "更新", !isForce);
                    DownloadRequestTask.DownloadInfo downloadInfo = requestTask.queryDownloadInfo(downloadId);
                    if (downloadInfo != null && DownloadManager.STATUS_SUCCESSFUL == downloadInfo.getStatus() && !TextUtils.isEmpty(downloadInfo.getLocalFilename())) {
                        //确保下载成功，并且存在文件路径，则进行自动安装
                        installApk(this, downloadInfo.getLocalFilename());
                    }
                    if (!isForce) {
                        finishActivity();
                    } else {
                        updateLayout.setVisibility(View.GONE);
                        downloadLayout.setVisibility(View.VISIBLE);
                        if (updateProgressThread != null) {
                            updateProgressThread.setStop(true);
                            updateProgressThread = null;
                        }
                        updateProgressThread = new UpdateProgressThread();
                        updateProgressThread.start();
                    }
                }
                btnReload.setVisibility(View.GONE);
                break;
            case R.id.btn_complete:
                if (btnComplete.getTag() instanceof DownloadRequestTask.DownloadInfo) {
                    DownloadRequestTask.DownloadInfo info = (DownloadRequestTask.DownloadInfo) btnComplete.getTag();
                    installApk(this, info.getLocalFilename());
                }
                break;
        }
    }

    /**
     * 关闭Activity
     */
    private void finishActivity() {
        finish();
        overridePendingTransition(R.anim.zoomin, R.anim.zoomout);
    }


    class UpdateProgressThread extends Thread {
        private boolean isStop = false;

        @Override
        public void run() {
            super.run();
            while (!isStop && progress < 100) {
                try {
                    DownloadRequestTask.DownloadInfo downloadInfo = requestTask.queryDownloadInfo(downloadId);
                    Message message = new Message();
                    message.what = UPDATE_PROGRESS;
                    message.obj = downloadInfo;
                    mHandler.sendMessage(message);
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        public void setStop(boolean stop) {
            isStop = stop;
        }
    }

    private String installFilePath = "";

    /**
     * 安装Apk
     *
     * @param context
     * @param filePath 文件路径
     */
    private void installApk(Context context, String filePath) {
        try {
            installFilePath = filePath;
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.REQUEST_INSTALL_PACKAGES) != PackageManager.PERMISSION_GRANTED) {
//                requestPermissions(new String[]{Manifest.permission.REQUEST_INSTALL_PACKAGES}, REQUEST_INSTALL_PACKAGE, "必然中医需要使用安装软件权限用于安装更新应用，是否同意使用？");
                showToSettingDialog();
            } else {
                installApkImpl(context, filePath);
            }
        } catch (Exception e) {
            LogUtils.i(DownloadManagerReceiver.class, "安装失败");
            e.printStackTrace();
        }
    }

    private void showToSettingDialog() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            final ConfirmDialog dialog = ConfirmDialog.getInstance(this);
            dialog.setDialogTitle("安装权限");
            dialog.setDialogContent("需要打开允许来自此来源，请去设置中开启此权限");
            dialog.setPositiveText("同意");
            dialog.setNavigationText("取消");
            dialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    try {
                        dialog.dismiss();
                    } catch (Exception e) {
                        dialog.dismiss();
                    }
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    toInstallPermissionSettingIntent();
                    dialog.dismiss();
                }
            });
            dialog.show();
        } else {
            ToastUtils.showLongMsg(this, "安装应用需要打开未知来源权限，请去设置中开启权限!");
        }

    }

    /**
     * 开启安装未知来源权限
     */
    @RequiresApi(api = Build.VERSION_CODES.O)
    private void toInstallPermissionSettingIntent() {
        Uri packageURI = Uri.parse("package:" + getPackageName());
        Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES, packageURI);
        startActivityForResult(intent, INSTALL_PERMISS_CODE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == INSTALL_PERMISS_CODE) {
            installApkImpl(this, installFilePath);
        }
    }


    /**
     * 安装Apk
     *
     * @param context
     * @param filePath 文件路径
     */
    private void installApkImpl(Context context, String filePath) {
        try {
            if (!TextUtils.isEmpty(filePath)) {
                Intent intent = new Intent(Intent.ACTION_VIEW);
                String fileAbosPath = "";
                if (filePath != null && filePath.startsWith("file://")) {
                    fileAbosPath = filePath;
                } else {
                    fileAbosPath = "file://" + filePath;
                }
                String fileName = fileAbosPath.substring(fileAbosPath.lastIndexOf("/") + 1, fileAbosPath.length());
                File apkFile = new File(MyPathManager.getSaveDir(PublicParams.CACHE_DIR_APK) + fileName);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                Uri contentUri = FileUtils.getUriForFile(context, apkFile);
                intent.setDataAndType(contentUri, "application/vnd.android.package-archive");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
            }
        } catch (Exception e) {
            LogUtils.i(DownloadManagerReceiver.class, "安装失败");
            e.printStackTrace();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_INSTALL_PACKAGE && grantResults != null && grantResults[0] == PackageManager.PERMISSION_GRANTED) {//获取到权限
            installApkImpl(this, installFilePath);
        }
    }

    @Override
    public void onBackPressed() {
//        super.onBackPressed();
    }

    @Override
    public void onBack(Activity activity) {
//        super.onBack(activity);
    }
}
