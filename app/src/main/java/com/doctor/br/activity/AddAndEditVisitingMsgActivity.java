package com.doctor.br.activity;

import android.app.Activity;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;

import com.doctor.br.bean.VisitingSettingBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.CitiesLoadUtils;
import com.doctor.br.utils.DialogHelper;
import com.doctor.br.utils.glide.StringUtil;
import com.doctor.br.view.VisitingTableView;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.HttpRequestTask;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 添加/编辑出诊信息的adapter
 */
public class AddAndEditVisitingMsgActivity extends ActionBarActivity {

    @BindView(R.id.et_institution_name)
    EditText etInstitutionName;
    @BindView(R.id.et_institution_city)
    EditText etInstitutionCity;
    @BindView(R.id.et_detail_location)
    EditText etDetailLocation;
    @BindView(R.id.iv_institution_city)
    ImageView ivInstitutionCity;
    @BindView(R.id.btn_save)
    com.doctor.br.view.NoDoubleClickBtn btnSave;
    @BindView(R.id.vtv)
    VisitingTableView vtv;
    public static final String TYPE = "type";//编辑出诊和添加出诊的判断字段
    public static final int REQUEST_CODE = 101;//startActivityForResult()的code
    public static final String VISITING_SETTING_DATABEAN = "visitingSettingDataBean";//医院id
    public static final int TYPE_ADD = 1;
    public static final int TYPE_EDIT = 2;
    public static final int TYPE_ISDELETE_YES = 1;//请求删除接口
    public static final int TYPE_ISDELETE_N0 = 0;//不是请求删除接口
    private int type;
    private String institutionName;
    private String institutionCity;
    private String detailLocation;
    private CitiesLoadUtils citiesLoadUtils;
    private String mCurrentAreaCode;
    private VisitingSettingBean.DataBean dataBean;
    public static final int MSG_WHAT_2 = 2;
    private Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == MSG_WHAT_2) {
                etInstitutionCity.setText((String) msg.obj);
            }

        }
    };
    private ConfirmDialog confirmDialog;
    private int isDelete;
    private RequestCallBack editCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_add_visiting_msg);
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);

        initIntentData();

        setListener();


    }

    private void setListener() {  //设置监听
        CitiesLoadUtils.PickerSelectListener pickerSelectListener = new CitiesLoadUtils.PickerSelectListener() {
            @Override
            public void pickerSelect(String address, String areaCode) {
                LogUtils.i(MainActivity.class, address + "......." + areaCode);
                if (address != null && address.trim().length() > 0) {
                    String newAddress = address.replace("-", "");
                    mCurrentAreaCode = areaCode;
                    etInstitutionCity.setText(newAddress);
                }
            }
        };
        citiesLoadUtils = new CitiesLoadUtils(this, pickerSelectListener);
    }

    /**
     * @param view 编辑页面，点击删除按钮
     */
    @Override
    public void onRightBtnClick(View view) {
        confirmDialog = DialogHelper.openConfirmDialog(this, "确认要删除这条出诊信息吗？", "确认", "取消"
                , new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                LogUtils.i(HttpRequestTask.class, "是否删除出诊信息？");
                requestEditVisiting(TYPE_ISDELETE_YES);

            }
        });
        confirmDialog.show();
    }

    @Override
    public void onBack(Activity activity) {//点击返回
       if(isAddParamsChange() || isEditParamsChange()) {   //添加页面,并且页面有编辑信息
            ConfirmDialog confirmDialog = DialogHelper.openConfirmDialog(this, "出诊信息未保存，是否退出？", "退出"
                    , "取消", new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    AddAndEditVisitingMsgActivity.this.finish();
                }
            });
            confirmDialog.show();
        } else {
            super.onBack(activity);
        }

    }

    /**
     * 编辑出诊信息页面，是否有参数发生了变化
     * @return
     */
    public boolean isEditParamsChange(){
        String institutionName = etInstitutionName.getText().toString().trim();
        String institutionCity = etInstitutionCity.getText().toString().trim();
        String detailLocation = etDetailLocation.getText().toString().trim();
        String workPlan = vtv.getWorkPlan();
        if(type == TYPE_EDIT && dataBean!=null){
            if(!dataBean.getHospitalName().equals(institutionName) || !dataBean.getHospitalAreaName().equals(institutionCity)
                    || !dataBean.getHospitalAddress().equals(detailLocation) || !dataBean.getWorkPlain().equals(workPlan)){
                return true;
            }
        }

        return false;
    }

    public boolean isAddParamsChange() {//添加出诊信息页面，是否有数据改动
        boolean isAddParamsChange =false;//是否有数据改动
        if (!StringUtil.isEmpty(etInstitutionName) || !StringUtil.isEmpty(etInstitutionCity)
                || !StringUtil.isEmpty(etDetailLocation) || vtv.isHasVisitingTime()) {
           isAddParamsChange = true;
        }
        if(isAddParamsChange && type == TYPE_ADD){
            return true;
        }

        return false;
    }

    /**
     * 获取Intent的数据
     */
    private void initIntentData() {
        type = getIntent().getIntExtra(TYPE, 1);
        if (type == TYPE_ADD) {
            setActionBarTitle("添加出诊信息");
        } else if (type == TYPE_EDIT) {
            dataBean = (VisitingSettingBean.DataBean) getIntent().getSerializableExtra(VISITING_SETTING_DATABEAN);
            setActionBarTitle("编辑出诊信息");
            setActionBarRightBtnImg(R.drawable.visiting_delete);
            initEditData();
        }

    }

    /**
     * 设置编辑的数据
     */
    private void initEditData() {
        if (dataBean == null) return;
        etInstitutionName.setText(dataBean.getHospitalName());
        if (!TextUtils.isEmpty(dataBean.getHospitalName())) {
            etInstitutionName.setSelection(dataBean.getHospitalName().length());
        }
        mCurrentAreaCode = dataBean.getHospitalAreaCode();
        etDetailLocation.setText(dataBean.getHospitalAddress());
        etInstitutionCity.setText(dataBean.getHospitalAreaName());
        vtv.setWorkPlan(dataBean.getWorkPlain());
    }


    @Override
    @OnClick({R.id.btn_save, R.id.rl_institution_city})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_save://保存
                requestEditVisiting(TYPE_ISDELETE_N0);
                break;
            case R.id.rl_institution_city://地址
                DensityUtils.hideSoftKeyboard(etInstitutionName);
                //地区，三级联动
                citiesLoadUtils.showPickerView();
                break;
        }
    }

    /**
     * 保存，编辑，删除的网络请求
     *
     * @param isDelete
     */
    private void requestEditVisiting(int isDelete) {
        this.isDelete = isDelete;
        if (isDelete == TYPE_ISDELETE_N0 && !checkSaveParam(isDelete)){//不是删除才会走校验参数的接口
            return;
        }

        //就诊时间信息
        LogUtils.i(HttpRequestTask.class, "开始添加出诊信息的网络请求了");
        String hospitalEditId = dataBean == null ? null : dataBean.getHospitalId();
        String hospitalId = type == TYPE_ADD ? null : hospitalEditId;//没有时为新插入,有时为更新
        Map<String, String> params = RequestParams.getAddVisitingMsgParams(hospitalId, institutionName, mCurrentAreaCode, detailLocation, vtv.getWorkPlan(), isDelete);
        editCallBack = addHttpPostRequest(HttpUrlManager.ADD_VISITING_MSG, params, ResponseResult.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId){
            case HttpUrlManager.ADD_VISITING_MSG:  //出诊信息
                if (result.isRequestSuccessed()) {  //网络请求成功
                    LogUtils.i(HttpRequestTask.class, "出诊信息成功");
                    if (type == TYPE_ADD) {//添加
                        ToastUtils.showShortMsg(mContext, "保存成功");
                    } else {
                        if (isDelete == TYPE_ISDELETE_YES) {//删除
                            ToastUtils.showShortMsg(mContext, "删除成功");
                        } else {//编辑
                            ToastUtils.showShortMsg(mContext, "保存成功");
                        }

                    }
                    setResult(RESULT_OK);
                    finish();
                }else{
                    RequestErrorToast.showError(this,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
        }
    }

    /**
     * @return 校验保存的参数，true为合法
     */
    public boolean checkSaveParam(int isDelete) {
        institutionName = etInstitutionName.getText().toString().trim();
        institutionCity = etInstitutionCity.getText().toString().trim();
        detailLocation = etDetailLocation.getText().toString().trim();
        if (institutionName == null || institutionName.length() <= 0) {
            ToastUtils.showShortMsg(this, R.string.toast_institution_name);
            etInstitutionName.requestFocus();
            return false;
        }

        if (institutionCity == null || institutionCity.length() <= 0) {
            ToastUtils.showShortMsg(this, R.string.toast_institution_position);
            return false;
        }

        if (detailLocation == null || detailLocation.length() <= 0) {
            ToastUtils.showShortMsg(this, R.string.hint_detail_location);
            etDetailLocation.requestFocus();
            return false;
        }

        if (!vtv.isHasVisitingTime()) {
            ToastUtils.showShortMsg(this, "请设置出诊时间");
            return false;
        }

        if (type == TYPE_EDIT && (isDelete == TYPE_ISDELETE_N0)) {//编辑
            if (institutionName.equals(dataBean.getHospitalName()) && dataBean.getHospitalAreaCode().equals(mCurrentAreaCode)
                    && detailLocation.equals(dataBean.getHospitalAddress()) && dataBean.getWorkPlain().equals(vtv.getWorkPlan())) {
                ToastUtils.showShortMsg(this, "请修改出诊时间后保存");
                return false;
            }
        }

        return true;
    }

    @Override
    protected void onDestroy() {
        if (citiesLoadUtils != null) {
            citiesLoadUtils.cancel();
        }
        if (confirmDialog != null) {
            confirmDialog.cancel();
            confirmDialog = null;

        }
        cancelRequest(editCallBack);
        if (handler != null) {
            handler = null;
        }
        super.onDestroy();
    }
}
