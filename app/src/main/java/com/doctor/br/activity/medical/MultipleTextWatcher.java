package com.doctor.br.activity.medical;

import android.text.Editable;
import android.text.TextWatcher;
import android.widget.EditText;

public class MultipleTextWatcher implements TextWatcher {
    private EditText editText;
    private boolean isUpdating = false;

    public MultipleTextWatcher(EditText editText) {
        this.editText = editText;
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {}

    @Override
    public void afterTextChanged(Editable s) {
        if (isUpdating) return;
        isUpdating = true;

        String text = s.toString();

        // 处理中文句号
        if (text.contains("。")) {
            text = text.replace("。", ".");
        }

        // 处理首字符为小数点的情况
        if (text.startsWith(".")) {
            text = "0" + text;
        }

        // 处理以0开头的数字
        if (text.startsWith("0") && !text.startsWith("0.") && text.length() > 1) {
            text = text.substring(1);
        }

        // 检查小数点位数
        String[] parts = text.split("\\.");
        if (parts.length > 1 && parts[1].length() > 1) {
            text = parts[0] + "." + parts[1].substring(0, 1);
        }

        // 检查数值范围（0-100）
        try {
            double value = Double.parseDouble(text);
            if (value > 100) {
                text = "100";
            }
        } catch (NumberFormatException e) {
            // 非法输入，保留原文本
        }

        editText.setText(text);
        editText.setSelection(text.length());

        isUpdating = false;
    }
}