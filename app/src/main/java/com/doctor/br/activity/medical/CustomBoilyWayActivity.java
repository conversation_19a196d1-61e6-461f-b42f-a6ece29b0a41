package com.doctor.br.activity.medical;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;

import com.doctor.br.adapter.BoilyWayAdapter;
import com.doctor.br.bean.medical.BoilWayBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.DialogHelper;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.listener.OnButtonClickListener;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.InputDialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import butterknife.BindView;

public class CustomBoilyWayActivity extends ActionBarActivity {

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    private BoilyWayAdapter adapter;
    private List<String> boilyWayList;

    private RequestCallBack getBoilWayCallBack;
    private InputDialog mDialog;
    private RequestCallBack addBoilWayCallBack;
    private String mCustomBoilWay;
    private RequestCallBack editBoilWayCallBack;
    private ConfirmDialog confirmDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        setActionBarContentView(R.layout.activity_custom_boily_way);
        setActionBarTitle("自定义煎法");

        initData();
        initViews();
        //请求自定义煎法数据
        getBoilWay();
    }

    @Override
    public void onBack(Activity activity) {
//        super.onBack(activity);
        Intent intent = new Intent();
        intent.putExtra("key", "back-custom");
        setResult(RESULT_OK, intent);
        finish();
    }


    private void initViews() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));

        findViewById(R.id.btn_add).setOnClickListener(v -> {
            if (boilyWayList.size() >= 4) { // 修改：将原来的2个改为4个
                ToastUtils.showShortMsg(mContext, "最多添加4个自定义煎法");
                return;
            }

            showCustomBoilWayDialog();
        });

        adapter = new BoilyWayAdapter(boilyWayList);

        adapter.setOnBoilyWayItemClickListener(new BoilyWayAdapter.OnBoilyWayItemClickListener() {
            @Override
            public void onEditClick(int position, String boilyWay) {
                if (mDialog == null) {
                    mDialog = InputDialog.getInstance(CustomBoilyWayActivity.this);
                }
                mDialog.setInputTitle("编辑煎法")
                        .setInputHint("请输入煎法(10字以内)")
                        .setInputMaxLength(10)
                        .setInputContent(boilyWay)
                        .show();

                mDialog.setOnButtonClickListener(new OnButtonClickListener() {
                    @Override
                    public void onPositiveClick(View view, CharSequence editText) {
                        if (TextUtils.isEmpty(editText.toString())) {
                            ToastUtils.showShortMsg(mContext, "请输入特殊煎药方式");
                        } else if (!stringFilter(editText.toString())) {
                            ToastUtils.showShortMsg(mContext, "只支持输入汉字、数字");
                        } else {
                            editBoilWay(boilyWay, editText.toString().trim());
                        }
                    }

                    @Override
                    public void onNegativeClick(View view, CharSequence editText) {
                        mDialog.dismiss();
                    }
                });
            }

            @Override
            public void onDeleteClick(int position, String boilyWay) {
                confirmDialog = DialogHelper.openConfirmDialog(
                        CustomBoilyWayActivity.this,
                        "确认要删除该煎法吗？",
                        "确认",
                        "取消",
                        new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                confirmDialog.dismiss();
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                editBoilWay(boilyWay, "");
                                confirmDialog.dismiss();
                            }
                        });

                confirmDialog.show();
            }
        });

        recyclerView.setAdapter(adapter);
    }

    private void initData() {
        boilyWayList = new ArrayList<>();
        boilyWayList.add("文火慢煎");
        boilyWayList.add("武火");
        boilyWayList.add("新增三");
        boilyWayList.add("新增四");

        LogUtils.i(CustomBoilyWayActivity.class, "boilyWayList=" + boilyWayList);

//        adapter = new BoilyWayAdapter(boilyWayList);
//        recyclerView.setAdapter(adapter);
    }

    /**
     * 自定义特殊煎法
     */
    private void showCustomBoilWayDialog() {
        if (mDialog == null) {
            mDialog = InputDialog.getInstance(CustomBoilyWayActivity.this);
        }
        mDialog.setInputTitle("自定义煎法")
                .setInputHint("请输入煎法(10字以内)")
                .setInputMaxLength(10)
                .show();
        mDialog.setOnButtonClickListener(new OnButtonClickListener() {
            @Override
            public void onPositiveClick(View view, CharSequence editText) {
                if (TextUtils.isEmpty(editText.toString())) {
                    ToastUtils.showShortMsg(mContext, "请输入特殊煎药方式");
                } else {
                    if (!stringFilter(editText.toString())) {
                        ToastUtils.showShortMsg(mContext, "只支持输入汉字、数字");
                    } else {
                        mCustomBoilWay = editText.toString().trim();
                        addBoilWay(editText.toString().trim());
                    }
                }
            }

            @Override
            public void onNegativeClick(View view, CharSequence
                    editText) {
                mDialog.dismiss();
            }
        });
    }

    private void getBoilWay() {
        getBoilWayCallBack = addHttpPostRequest(HttpUrlManager.BOIL_WAY, null, BoilWayBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);

        switch (taskId) {
            case HttpUrlManager.BOIL_WAY:
                if (result != null && result.isRequestSuccessed()) {
                    BoilWayBean boilWayBean = (BoilWayBean) result.getBodyObject();
                    if (boilWayBean != null) {
//                        List<String> boilWays = boilWayBean.getDecoction1();
                        List<String> customBoilWays = boilWayBean.getDecoction2();

                        // Clear existing list
                        boilyWayList.clear();
                        
                        // Add custom boiling ways first
                        if (customBoilWays != null && !customBoilWays.isEmpty()) {
                            boilyWayList.addAll(customBoilWays);
                        }

                        // Update adapter
                        adapter.notifyDataSetChanged();
                    }
                }
                break;
                case HttpUrlManager.ADD_BOIL_WAY:
                    if (result != null && result.isRequestSuccessed()) {
                        if (mDialog != null && mDialog.isShowing()) {
                            mDialog.dismiss();
                        }
                        //列表界面添加新的煎药方式
                        boilyWayList.add(mCustomBoilWay);
                        adapter.notifyDataSetChanged();
                    } else {
                        RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                    }
                    break;
            case HttpUrlManager.EDIT_CUSTOM_BOILYWAY:
                if (result != null && result.isRequestSuccessed()) {
                    if (mDialog != null && mDialog.isShowing()) {
                        mDialog.dismiss();
                    }
                    // Refresh the list after successful edit/delete
                    getBoilWay();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
        }
    }

    /**
     * @param title 自定义煎药方式
     */
    private void addBoilWay(String title) {
        HashMap map = new HashMap();
        map.put("title", title);
        addBoilWayCallBack = addHttpPostRequest(HttpUrlManager.ADD_BOIL_WAY, map, ResponseResult.class, this);
    }

    /**
     * 限制输入框只输入汉字，数字
     *
     * @param str
     * @return
     * @throws PatternSyntaxException
     */
    public boolean stringFilter(String str) throws PatternSyntaxException {
        // 只允许数字和汉字,空格
        String regEx = "[\\d\u4E00-\u9FA5\\s]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.matches();
    }

    /**
     * Add new method for editing boil way
     */
    private void editBoilWay(String oldTitle, String title) {
        HashMap<String, String> map = new HashMap<>();
        map.put("oldTitle", oldTitle);
        map.put("title", title);
        editBoilWayCallBack = addHttpPostRequest(HttpUrlManager.EDIT_CUSTOM_BOILYWAY, map, ResponseResult.class, this);
    }

    @Override
    protected void onDestroy() {
        cancelRequest(getBoilWayCallBack);
        cancelRequest(editBoilWayCallBack);
        super.onDestroy();
    }
}
