package com.doctor.br.activity.mine.agent.achievement;

import android.content.Intent;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.PerformanceSummaryRecyclerAdapter;
import com.doctor.br.bean.AgentPerformanaceSummaryBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：业绩汇总搜索页面
 * 创建人：ShiShaoPo
 * 创建时间：2018/5/16
 */

public class PerformanceSearchActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //界面下的控件
    @BindView(R.id.head_linear)
    LinearLayout headLienar;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private RequestCallBack getListCallBack;//网络请求获取列表回调

    private List<AgentPerformanaceSummaryBean.ListBean> list;//列表
    private PerformanceSummaryRecyclerAdapter performanceSummaryRecyclerAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_patient_numbers);
        setActionBarType(ActionBarView.ACTIONBAR_SEARCH);
        initView();
    }

    private void initView() {
        getBaseActionBar().setActionbarSearchInputHint("大夫姓名或手机号");
        setOnSearchInputEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                //兼容软键盘和硬键盘
                boolean isEvent = event != null && KeyEvent.KEYCODE_ENTER == event.getKeyCode() && KeyEvent.ACTION_UP == event.getAction();
                if (actionId == EditorInfo.IME_ACTION_SEARCH || isEvent) {
                    //处理事件
                    onRightBtnClick(null);
                    return true;
                }
                return false;
            }
        });

        headLienar.setVisibility(View.GONE);
        bgaRefreshLayout.setPullDownRefreshEnable(false);
        recyclerView = (RecyclerView) findViewById(R.id.recycler_view);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setHasFixedSize(true);

        list = new ArrayList<>();
        performanceSummaryRecyclerAdapter = new PerformanceSummaryRecyclerAdapter(this, list, this);
        recyclerView.setAdapter(performanceSummaryRecyclerAdapter);

        emptyView.setEmptyBackgroundColor(ContextCompat.getColor(this, R.color.br_color_white));
    }

    /**
     * 右上角的搜索按钮
     */
    @Override
    public void onRightBtnClick(View view) {
        if (TextUtils.isEmpty(getBaseActionBar().getActionbarSearchInputText())) {
            return;
        }
        searchPerformanceList(getBaseActionBar().getActionbarSearchInputText());
    }

    /**
     * 每个item的点击事件
     *
     * @param position 点击的item
     */
    @Override
    public void itemClick(int position) {
        Intent intent = new Intent(this, PerformanceMonthActivity.class);
        intent.putExtra(PerformanceMonthActivity.DOCTOR_NAME, list.get(position).getDoctorName());
        intent.putExtra(PerformanceMonthActivity.DOCTOR_ID, list.get(position).getDoctorId());
        startActivity(intent);
    }

    /**
     * 业绩汇总搜索
     *
     * @param doctorName 搜索的关键字，姓名或手机号
     */
    private void searchPerformanceList(String doctorName) {
        emptyView.setVisibility(View.GONE);
        bgaRefreshLayout.setVisibility(View.VISIBLE);
        Map<String,String> map = new HashMap<>();
        map.put("doctorName",doctorName);
        getListCallBack = addHttpPostRequest(HttpUrlManager.PERFORMANCE_SUMMARY, map, AgentPerformanaceSummaryBean.class, this);
    }

    @Override
    public void onRequestFinished(final String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (bgaRefreshLayout == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.PERFORMANCE_SUMMARY:
                if (result.isRequestSuccessed()) {
                    AgentPerformanaceSummaryBean agentPerformanaceSummaryBean = (AgentPerformanaceSummaryBean) result.getBodyObject();

                    list.clear();
                    list.addAll(agentPerformanaceSummaryBean.getList());
                    performanceSummaryRecyclerAdapter.notifyDataSetChanged();
                    if ( list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setVisibility(View.VISIBLE);
                        bgaRefreshLayout.setVisibility(View.GONE);
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (getListCallBack != null) {
            getListCallBack.cancel();
        }
        super.onDestroy();
    }

}
