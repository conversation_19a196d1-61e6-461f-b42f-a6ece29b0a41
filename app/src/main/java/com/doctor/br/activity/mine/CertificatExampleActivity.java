package com.doctor.br.activity.mine;

import android.app.ActivityOptions;
import android.content.Intent;
import android.os.Bundle;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.mine.CertificatExampleRecyclerAdapter;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;

import butterknife.BindView;

/**
 * 类描述：证书示例界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/01
 */
public class CertificatExampleActivity extends ActionBarActivity implements RecyclerItemItemClickListener {
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    private int[] imgs = {R.drawable.qualification_example_img1, R.drawable.qualification_example_img2, R.drawable.qualification_example_img3,
            R.drawable.qualification_example_img4, R.drawable.qualification_example_img5};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_certificat_example);
        initView();
    }

    private void initView() {
        setActionBarTitle("认证");

        recyclerView.setAdapter(new CertificatExampleRecyclerAdapter(this, imgs, this));
    }

    @Override
    public void itemViewClick(int position, View view) {
        Intent intent = new Intent(this, CertificatExampleDetailActivity.class);
        int[] intentImgs = new int[2];
        switch (position) {
            case 0:
                System.arraycopy(imgs, 0, intentImgs, 0, 2);
                break;
            case 1:
                System.arraycopy(imgs, 0, intentImgs, 0, 2);
                break;
            case 2:
                System.arraycopy(imgs, 2, intentImgs, 0, 2);
                break;
            case 3:
                System.arraycopy(imgs, 2, intentImgs, 0, 2);
                break;
            case 4:
                intentImgs = new int[1];
                System.arraycopy(imgs, 4, intentImgs, 0, 1);
                break;
            default:
                break;
        }
        intent.putExtra(CertificatExampleDetailActivity.IMGS, intentImgs);
        intent.putExtra(CertificatExampleDetailActivity.CLICK_POSITION, position % 2);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            Bundle bundle = new Bundle();
            bundle.putAll(ActivityOptions.makeSceneTransitionAnimation(this, view, "111").toBundle());
            ActivityCompat.startActivity(this, intent, bundle);
        } else {
            startActivity(intent);
        }
    }
}
