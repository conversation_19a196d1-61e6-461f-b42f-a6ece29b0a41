package com.doctor.br.activity.manage.medicineshop;

import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.base.request.common.BaseObserver;
import com.base.request.common.BaseResult;
import com.base.request.common.BaseRxActivity;
import com.base.request.common.BaseRxFragment;
import com.doctor.br.activity.MyMedicineProtocolActivity;
import com.doctor.br.activity.manage.OrderRecordActivity;
import com.doctor.br.adapter.shop.CommodityViewPagerAdapter;
import com.doctor.br.apiloader.shop.CommodityLoader;
import com.doctor.br.bean.event.RefreshCommodityListEvent;
import com.doctor.br.bean.shop.CommodityNumBean;
import com.doctor.br.fragment.medicineshop.DownShelfFragment;
import com.doctor.br.fragment.medicineshop.OnShelfFragment;
import com.doctor.br.fragment.medicineshop.ShelfingFragment;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.ShareUtils;
import com.doctor.br.view.NoScrollViewPager;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.listener.OnActionBarChangeListener;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.disposables.Disposable;

/**
 * Author:sunxiaxia
 * createdDate:2018/8/21
 * description:我的药铺首页
 */

public class MyMedicineShopActivity extends BaseRxActivity implements ViewPager.OnPageChangeListener {

    @BindView(R.id.onShelf_num)
    TextView onShelfNum;
    @BindView(R.id.shelfing_num)
    TextView shelfingNum;
    @BindView(R.id.downShelf_num)
    TextView downShelfNum;
    @BindView(R.id.view_pager)
    NoScrollViewPager viewPager;
    @BindView(R.id.view_container)
    LinearLayout viewContainer;
    @BindView(R.id.onShelf_ll)
    LinearLayout onShelfLl;
    @BindView(R.id.shelfing_ll)
    LinearLayout shelfingLl;
    @BindView(R.id.downShelf_ll)
    LinearLayout downShelfLl;
    @BindView(R.id.bottom_line)
    LinearLayout bottomLine;
    @BindView(R.id.onShelf_tv)
    TextView onShelfTv;
    @BindView(R.id.action_bar)
    ActionBarView actionBar;
    @BindView(R.id.btn_know)
    Button btnKnow;
    @BindView(R.id.guide_layout)
    FrameLayout guideLayout;
    private TextView orderRecord;
    private TextView shareShop;
    private TextView userProtocol;
    private int clickPosition;//点击哪个button进来的。从而判断首先加载哪个fragment，非必须，默认为0
    private String[] names = {"已上架", "待上架", "已下架"};
    private List<int[]> paddingList;//设置bottomLine的padding
    private CommodityViewPagerAdapter mAdapter;
    private String userId;
    private CommodityLoader loader;
    private boolean noCommodity;
    private List<BaseRxFragment> fragmentList;
    private OnShelfFragment onShelfFragment;
    private ShelfingFragment shelfFragment;
    private DownShelfFragment downShelfFragment;
    private PopupWindow mPopUpWindow;
    private boolean isShowGuide;//是否显示引导页
    private String shareUrl;
    private String shopLogoUrl;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getBaseActionBar().hide();
        setActionBarContentView(R.layout.activity_my_medicine_shop);
        setActionBarView();

        initView();
        setBottomLine();
//获取"已上架", "待上架", "已下架"商品数量
        getCommodityCount();
    }

    /**
     * 获取"已上架", "待上架", "已下架"商品数量
     */
    private void getCommodityCount() {

        BaseObserver observer = new BaseObserver<CommodityNumBean>() {
            @Override
            public void onStart(Disposable d) {
                super.onStart(d);
                showLoading();
            }

            /**
             * @param dataBean 请求成功的结果
             */
            @Override
            public void onSuccess(CommodityNumBean dataBean, List<CommodityNumBean> datas) {
                if (dataBean != null) {
                    onShelfNum.setText(String.valueOf(dataBean.getStatus1()));
                    shelfingNum.setText(String.valueOf(dataBean.getStatus0()));
                    downShelfNum.setText(String.valueOf(dataBean.getStatus2()));
                    shareUrl = dataBean.getShareUrl();
                    shopLogoUrl = dataBean.getLogoImg();
                    if (0 == dataBean.getStatus0() && 0 == dataBean.getStatus1() && 0 == dataBean.getStatus2()) {
                        noCommodity = true;
                    } else {
                        noCommodity = false;
                    }
                    SharedPreferenceUtils.putBoolean(MyMedicineShopActivity.this, PublicParams.HAS_COMMODITY, noCommodity);
                }
            }

            @Override
            public void onFinish() {
                super.onFinish();
                dismissLoading();
            }

            @Override
            public void onFailure(String code, String msg) {
                super.onFailure(code, msg);
                dismissLoading();
                ToastUtils.showShortMsg(MyMedicineShopActivity.this, msg);
            }
        };
        loader.getCommodityCount(HttpUrlManager.GET_COMMODITY_COUNT, userId)
                .compose(this.<BaseResult<CommodityNumBean>>bindToLifecycle())
                .subscribe(observer);

    }

    /**
     * 初始化控件
     */
    private void initView() {
        EventBusUtils.register(this);
        paddingList = new ArrayList<>();
        fragmentList = new ArrayList<>();
        userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        isShowGuide = SharedPreferenceForeverUtils.getBoolean(this, PublicParams.IS_SHOW_SHOP_GUIDE + userId, true);
        //初始化
        SharedPreferenceUtils.putBoolean(MyMedicineShopActivity.this, PublicParams.HAS_COMMODITY, true);
        if (isShowGuide) {
            guideLayout.setVisibility(View.VISIBLE);
        } else {
            guideLayout.setVisibility(View.GONE);
        }
        onShelfFragment = new OnShelfFragment();
        shelfFragment = new ShelfingFragment();
        downShelfFragment = new DownShelfFragment();
        fragmentList.add(onShelfFragment);
        fragmentList.add(downShelfFragment);
        fragmentList.add(shelfFragment);
        mAdapter = new CommodityViewPagerAdapter(getSupportFragmentManager(), fragmentList);
        viewPager.setOffscreenPageLimit(names.length);
        viewPager.setAdapter(mAdapter);
        viewPager.addOnPageChangeListener(this);
        viewPager.setCurrentItem(0);
        onShelfNum.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme));
        onShelfTv.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme));

        loader = new CommodityLoader();
        popUpWindow();
    }


    /**
     * 设置标题栏
     */
    private void setActionBarView() {
        actionBar.setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        actionBar.setRightButtonImg(R.drawable.three_dot_icon);
        actionBar.setActionBarTitle("养生建议");
        actionBar.setActionBarChangeListener(new OnActionBarChangeListener() {
            @Override
            public void onShow(ActionBarView barView) {

            }

            @Override
            public void onHide(ActionBarView barView) {

            }

            @Override
            public void onBack(Activity activity) {
                MyMedicineShopActivity.this.finish();
            }

            @Override
            public void onRightBtnClick(View view) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    mPopUpWindow.showAsDropDown(view, 0, 10, Gravity.BOTTOM);
                } else {
                    mPopUpWindow.showAtLocation(view, Gravity.BOTTOM, 0, 10);
                }
            }
        });

    }

    /**
     * 分享、订单记录、合作协议的弹框
     */
    private void popUpWindow() {
        mPopUpWindow = new PopupWindow(this);
        View view = LayoutInflater.from(this).inflate(R.layout.pop_mymedication, null);
        mPopUpWindow.setWidth(ViewGroup.LayoutParams.WRAP_CONTENT);
        mPopUpWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        mPopUpWindow.setContentView(view);

        orderRecord = (TextView) view.findViewById(R.id.order_record);
        shareShop = (TextView) view.findViewById(R.id.share_shop);
        userProtocol = (TextView) view.findViewById(R.id.user_protocol);
        orderRecord.setOnClickListener(this);
        shareShop.setOnClickListener(this);
        userProtocol.setOnClickListener(this);

        mPopUpWindow.setFocusable(true);
        mPopUpWindow.setOutsideTouchable(true);
        mPopUpWindow.setBackgroundDrawable(new BitmapDrawable());
        mPopUpWindow.setAnimationStyle(R.style.popupwindow_health);
    }

    /**
     * 设置底部下滑线
     */
    private void setBottomLine() {
        viewContainer.post(new Runnable() {
            @Override
            public void run() {
                int marginPx = DensityUtils.dip2px(MyMedicineShopActivity.this, 15f);
                for (int i = 0; i < names.length; i++) {
                    int[] leftRight = new int[2];//左边是到父布局左侧距离，右边是到父布局右侧距离
                    ViewGroup viewGroup = (ViewGroup) viewContainer.getChildAt(i);
                    leftRight[0] = viewGroup.getChildAt(0).getLeft() - marginPx;
                    leftRight[1] = viewGroup.getMeasuredWidth() - viewGroup.getChildAt(0).getRight() - marginPx;
                    paddingList.add(leftRight);
                }
                setSelectPosition();
            }
        });
    }

    /**
     * 根据选中位置设置字体颜色
     *
     * @param position 当前选中的位置
     */
    private void switchColor(int position) {
        for (int i = 0; i < names.length; i++) {
            LinearLayout linearLayout = (LinearLayout) viewContainer.getChildAt(i);
            TextView tvNum = (TextView) linearLayout.getChildAt(0);
            TextView tvType = (TextView) linearLayout.getChildAt(1);
            if (i == position) {
                tvNum.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme));
                tvType.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme));
            } else {
                tvNum.setTextColor(ContextCompat.getColor(this, R.color.br_color_et_hint));
                tvType.setTextColor(ContextCompat.getColor(this, R.color.br_color_et_hint));
            }
        }
    }

    /**
     * 根据当前选中的位置，设置底部bottomLine的内边距
     */
    private void setSelectPosition() {
        viewPager.setCurrentItem(0, false);
        if (paddingList.size() == 0) {
            return;
        }
        double eachWidth = bottomLine.getMeasuredWidth() / paddingList.size();
        double left = paddingList.get(clickPosition)[0] + clickPosition * eachWidth;
        double right = paddingList.get(clickPosition)[1] + (paddingList.size() - 1 - clickPosition) * eachWidth;
        bottomLine.setPadding((int) left, 0, (int) right, 0);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        setPaddingScroll(position, positionOffset);
    }

    @Override
    public void onPageSelected(int position) {
        switchColor(position);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    /**
     * @param position
     * @param positionOffset
     */
    private void setPaddingScroll(int position, float positionOffset) {
        if (paddingList == null || paddingList.size() == 0 || position == paddingList.size() - 1) {
            return;
        }
        double eachWidth = bottomLine.getMeasuredWidth() / paddingList.size();
        double left, right;
        double nextLeft = paddingList.get(position + 1)[0] + (position + 1) * eachWidth;
        double nowLeft = paddingList.get(position)[0] + position * eachWidth;
        double nextRight = paddingList.get(position + 1)[1] + (paddingList.size() - 2 - position) * eachWidth;
        double nowRight = paddingList.get(position)[1] + (paddingList.size() - 1 - position) * eachWidth;
        left = positionOffset * (nextLeft - nowLeft) + nowLeft;
        right = positionOffset * (nextRight - nowRight) + nowRight;
        bottomLine.setPadding((int) left, 0, (int) right, 0);
    }

    @OnClick({R.id.onShelf_ll, R.id.shelfing_ll, R.id.downShelf_ll, R.id.btn_know, R.id.guide_layout})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.onShelf_ll:
                viewPager.setCurrentItem(0);
                break;
            case R.id.downShelf_ll:
                viewPager.setCurrentItem(1);
                break;
            case R.id.shelfing_ll:
                viewPager.setCurrentItem(2);
                break;
            case R.id.btn_know:
                SharedPreferenceForeverUtils.putBoolean(this, PublicParams.IS_SHOW_SHOP_GUIDE + userId, false);
                guideLayout.setVisibility(View.GONE);
                break;
            case R.id.guide_layout:
                //消费点击事件
                break;
            default:
                break;

        }
    }


    /**
     * @param event
     * @sender 发送class
     * {@link ShelfingFragment#onShelfObserver}
     * {@link DownShelfFragment#onShelfObserver}
     * {@link OnShelfFragment#downOrOnShelfObserver}
     * {@link DownShelfFragment#deleteObserver}
     * {@link ShelfingFragment#deleteObserver}
     * {@link OnShelfFragment#bgaRefreshLayout}
     * {@link DownShelfFragment#bgaRefreshLayout}
     * {@link ShelfingFragment#bgaRefreshLayout}
     * @receiver 接受class
     * {@link OnShelfFragment#refreshList(RefreshCommodityListEvent)}
     * {@link DownShelfFragment#refreshList(RefreshCommodityListEvent)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshCount(RefreshCommodityListEvent event) {
        if (event != null) {
            if (event.isRefreshCount()) {
                getCommodityCount();
            }
        }

    }

    @Override
    public void onClick(View view) {
        super.onClick(view);
        switch (view.getId()) {
            case R.id.order_record://  订单记录
                Intent intent = new Intent(this, OrderRecordActivity.class);
                startActivity(intent);
                mPopUpWindow.dismiss();
                break;
            case R.id.share_shop: //  药铺分享
                String doctorName = SharedPreferenceUtils.getString(MyMedicineShopActivity.this, PublicParams.USER_NAME);
                String shareTitle = "【" + doctorName + "】" + "大夫的养生建议";
                if (!TextUtils.isEmpty(shareUrl) && !TextUtils.isEmpty(shopLogoUrl)) {
                    String url = shareUrl + "&openId=&entry=3";
                    toShareShop(shareTitle, doctorName+PublicParams.SHARE_SHOP_DESC, shopLogoUrl, url);
                    mPopUpWindow.dismiss();
                } else {
                    ToastUtils.showShortMsg(MyMedicineShopActivity.this, "商铺地址为空");
                }
                break;
            case R.id.user_protocol: //  合作协议
                Intent intent1 = new Intent(this, MyMedicineProtocolActivity.class);
                intent1.putExtra(MyMedicineProtocolActivity.IS_SHOW, false);
                startActivity(intent1);
                mPopUpWindow.dismiss();
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        super.onDestroy();
    }

    /**
     * 药铺分享
     */
    private void toShareShop(String name, String effect, String imgUrl, String shopUrl) {
        ShareUtils shareUtils = new ShareUtils(this, ShareUtils.WECHAT, ShareUtils.WECHAT_MOMENT, ShareUtils.QQ);
        //其他分享
        shareUtils.showPopupWindow(name, effect, imgUrl, shopUrl);
    }


}
