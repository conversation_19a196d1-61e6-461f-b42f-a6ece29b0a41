package com.doctor.br.activity.mine;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.TextView;

import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.NoEmojiEditText;

import butterknife.BindView;

/**
 * 类描述：个人中心输入框界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/18
 */

public class PersonalDataEditTextActivity extends ActionBarActivity {
    //上个界面传递过来的数据
    public static final String TEXT = "text";//要显示的文字，非必须，默认显示hint
    public static final String HINT = "hint";//内容为空的提示语
    public static final String TITLE = "title";//该界面标题
    public static final String MAX_LENGTH = "maxLength";//可以输入的最大长度
    public static final String INPUT_TYPE = "inputType";//输入类型
    public static final String SET_HEIGHT = "setHeight";//如果是就职机构、擅长、简介则设置最小高度
    private String text, hint, title, maxLength, inputType;
    private boolean isSetHeight;
    //界面下的控件
    @BindView(R.id.edit_text)
    NoEmojiEditText editText;
    @BindView(R.id.clear_img)
    TextView clearImg;
    @BindView(R.id.tip_tv)
    TextView tipTv;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_personal_edit);
        getIntentData(savedInstanceState);
        initView();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(TEXT, text);
        outState.putString(HINT, hint);
        outState.putString(TITLE, title);
        outState.putString(MAX_LENGTH, maxLength);
        outState.putString(INPUT_TYPE, inputType);
        outState.putBoolean(SET_HEIGHT, isSetHeight);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            text = savedInstanceState.getString(TEXT);
            hint = savedInstanceState.getString(HINT);
            title = savedInstanceState.getString(TITLE);
            maxLength = savedInstanceState.getString(MAX_LENGTH);
            inputType = savedInstanceState.getString(INPUT_TYPE);
            isSetHeight = savedInstanceState.getBoolean(SET_HEIGHT);
        } else {
            text = getIntent().getStringExtra(TEXT);
            hint = getIntent().getStringExtra(HINT);
            title = getIntent().getStringExtra(TITLE);
            maxLength = getIntent().getStringExtra(MAX_LENGTH);
            inputType = getIntent().getStringExtra(INPUT_TYPE);
            isSetHeight = getIntent().getBooleanExtra(SET_HEIGHT, false);
        }
        if (TextUtils.isEmpty(title) || TextUtils.isEmpty(hint)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }

    private String lastText = "";

    private void initView() {
        setActionBarTitle(title);
        getBaseActionBar().setRightButton("保存");
        getBaseActionBar().setActionBarChangeListener(this);

        editText.setHint(hint);
        editText.setText(text);
        setClearImgVisibility(text);

        if (PersonalDataActivity.NAME.equals(title)) {
            //姓名提示
            tipTv.setVisibility(View.VISIBLE);
        } else {
            tipTv.setVisibility(View.GONE);
        }

        if (!TextUtils.isEmpty(maxLength)) {
            //限制最大长度
            try {
                InputFilter[] filters1 = editText.getFilters();
                InputFilter[] newFilters = new InputFilter[filters1.length + 1];
                System.arraycopy(filters1, 0, newFilters, 0, filters1.length);

                int length = Integer.parseInt(maxLength);
                newFilters[newFilters.length - 1] = new InputFilter.LengthFilter(length);

                editText.setFilters(newFilters);
            } catch (NumberFormatException e) {

            }
        }


        if (isSetHeight) {
            editText.setMinHeight(DensityUtils.dip2px(this, 180));
        }

        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//                Log.e("====<<<<<<<<<", "s:" + s + ",start:" + start + ",count:" + count + ",after:" + after);
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
//                Log.e("====>>>>>>>>>", "s:" + s + ",start:" + start + ",before:" + before + ",count:" + count);
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(inputType)) {
                    //限制身份证号的输入类型
                    if (PersonalDataActivity.PAPER_NO.equals(inputType)) {
                        if (TextUtils.isEmpty(s) || isPaperNo(s.toString())) {
                            lastText = s.toString();
                            setClearImgVisibility(lastText);
                        } else {
                            editText.setText(lastText);
                            editText.setSelection(lastText.length());
                        }
                    }
                } else {
                    setClearImgVisibility(s);
                }
            }
        });
        editText.setSelection(text == null ? 0 : text.length());

        clearImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (clearImg.getVisibility() == View.VISIBLE) {
                    editText.setText("");
                }
            }
        });
    }

    public boolean isPaperNo(String str) {
        return str.matches("^[xX0-9]+$");
    }

    @Override
    public void onRightBtnClick(View view) {
        Intent intent = new Intent();
        intent.putExtra(TEXT, editText.getText().toString());
        setResult(RESULT_OK, intent);
        finish();
    }


    private void setClearImgVisibility(CharSequence s) {
        if (TextUtils.isEmpty(s) || s.length() <= 0) {
            clearImg.setVisibility(View.INVISIBLE);
        } else {
            clearImg.setVisibility(View.VISIBLE);
        }
    }

}
