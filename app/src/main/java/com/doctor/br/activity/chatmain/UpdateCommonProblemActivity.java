package com.doctor.br.activity.chatmain;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;

import com.alibaba.fastjson.JSON;
import com.doctor.br.adapter.chatmain.AddProblemAdapter;
import com.doctor.br.bean.CommonProblem;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.listener.OnButtonClickListener;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.InputDialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 添加/编辑 常见问题
 * @createTime 2017/10/26
 */

public class UpdateCommonProblemActivity extends ActionBarActivity implements AdapterView.OnItemClickListener, AdapterView.OnItemLongClickListener {
    @BindView(R2.id.problem_lv)
    ListView problemLv;
    @BindView(R2.id.layout_add)
    LinearLayout layoutAdd;
    @BindView(R2.id.save_btn)
    Button saveBtn;

    public final static String ACTION_TYPE = "actionType";
    public final static String COMMON_PROBLEM_OBJECT = "commonProblemObject";
    public final static int COMMON_PROBLEM_ADD = 101;
    public final static int COMMON_PROBLEM_UPDATE = 102;
    private final static int REQUEST_CODE = 11;

    private ConfirmDialog backDialog;
    private ConfirmDialog deleteConfirmDialog;
    private InputDialog inputDialog;
    private AddProblemAdapter problemAdapter;
    private List<CommonProblem.DataBean> problemList;
    private CommonProblem commonProblem;
    private int index = -1;//记录编辑位置
    private int actionType = COMMON_PROBLEM_ADD;
    private String flag = "add";//标识，表示是添加，更新还是删除
    private boolean isUpdated = false;//标识位，记录该常见问题是否进行过编辑
    private RequestCallBack saveCommonProblemCallBack;
    private RequestCallBack updateCommonProblemCallBack;
    private RequestCallBack deleteCommonProblemCallBack;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_add_common_problem);
        initView();
        actionType = getIntent().getIntExtra(ACTION_TYPE, COMMON_PROBLEM_ADD);
        if (actionType == COMMON_PROBLEM_UPDATE) {
            setActionBarTitle("编辑定制问诊单");
            setActionBarRightBtnText("删除");
            commonProblem = (CommonProblem) getIntent().getSerializableExtra(COMMON_PROBLEM_OBJECT);
            initData();
        } else {
            setActionBarTitle("添加定制问诊单");
        }

    }

    /**
     * 初始化编辑数据
     */
    private void initData() {
        if (commonProblem != null && commonProblem.getData() != null) {
            problemList.addAll(commonProblem.getData());
            problemAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 初始化View，设置适配器
     */
    private void initView() {
        problemList = new ArrayList<>();
        problemAdapter = new AddProblemAdapter(this, problemList, R.layout.item_add_problem);
        problemLv.setAdapter(problemAdapter);
        problemLv.setOnItemClickListener(this);
        problemLv.setOnItemLongClickListener(this);
    }


    @OnClick({R.id.layout_add, R.id.save_btn})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.layout_add: {
                Intent intent = new Intent(this, CommonProblemEditTextActivity.class);
                intent.putExtra(CommonProblemEditTextActivity.HINT, "在这里输入问题内容...");
                intent.putExtra(CommonProblemEditTextActivity.TITLE, "新增问题");
                intent.putExtra(CommonProblemEditTextActivity.TEXT, "");
                startActivityForResult(intent, REQUEST_CODE);
                index = -1;
            }
            break;
            case R.id.save_btn:
                if (problemList == null || problemList.size() == 0) {
                    ToastUtils.showShortMsg(mContext, "请先添加定制问诊单");
                    return;
                }
                if (inputDialog == null) {
                    inputDialog = InputDialog.getInstance(this);
                }
                inputDialog.setInputTitle("请输入定制问诊单标签")
                        .setInputHint("20个字以内")
                        .setInputMaxLength(20)
                        .setPositiveText("保存")
                        .setNegativeText("取消")
                        .setOnButtonClickListener(new OnButtonClickListener() {
                            @Override
                            public void onPositiveClick(View view, CharSequence editText) {
                                if (!TextUtils.isEmpty(editText)) {
                                    if (commonProblem != null) {
                                        commonProblem.setLabel(editText.toString());
                                    }
                                    if (actionType == COMMON_PROBLEM_UPDATE) {
                                        updateCommonProblem(editText.toString());
                                    } else {
                                        saveCommonProblem(editText.toString());
                                    }
                                    inputDialog.dismiss();
                                } else {
                                    ToastUtils.showShortMsg(UpdateCommonProblemActivity.this, "请输入标签内容！");
                                }
                            }

                            @Override
                            public void onNegativeClick(View view, CharSequence editText) {
                                inputDialog.dismiss();
                            }
                        });
                if (commonProblem != null && !TextUtils.isEmpty(commonProblem.getLabel())) {
                    inputDialog.setInputContent(commonProblem.getLabel());
                }
                inputDialog.show();
                break;
        }
    }

    /**
     * 保存新增的常见问题
     *
     * @param label 标签文字
     */
    private void saveCommonProblem(String label) {
        flag = "add";
        HashMap<String, String> params = new HashMap<>();
        params.put("label", label);
        params.put("content", JSON.toJSONString(new CommonProblem(problemList)));
        params.put("flag", flag);
        saveCommonProblemCallBack = addHttpPostRequest(HttpUrlManager.UPDATE_COMMON_PROBLEM, params, ResponseResult.class, this);
    }


    /**
     * 保存编辑的常见问题
     *
     * @param label 标签文字
     */
    private void updateCommonProblem(String label) {
        flag = "update";
        HashMap<String, String> params = new HashMap<>();
        if (commonProblem != null) {
            params.put("labelCode", commonProblem.getLabelCode());
        }
        params.put("label", label);
        params.put("content", JSON.toJSONString(new CommonProblem(problemList)));
        params.put("flag", "update");
        updateCommonProblemCallBack = addHttpPostRequest(HttpUrlManager.UPDATE_COMMON_PROBLEM, params, ResponseResult.class, this);
    }

    /**
     * 删除常见问题
     *
     * @param label 标签文字
     */
    private void deleteCommonProblem(String label) {
        flag = "del";
        HashMap<String, String> params = new HashMap<>();
        if (commonProblem != null) {
            params.put("labelCode", commonProblem.getLabelCode());
        }
        params.put("label", label);
        params.put("flag", "del");
        deleteCommonProblemCallBack = addHttpPostRequest(HttpUrlManager.UPDATE_COMMON_PROBLEM, params, ResponseResult.class, this);
    }

    /**
     * 请求返回结果
     *
     * @param taskId
     * @param result
     */
    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.UPDATE_COMMON_PROBLEM:
                if (result.isRequestSuccessed()) {
                    if ("del".equalsIgnoreCase(flag)) {
                        ToastUtils.showShortMsg(this, "删除成功！");
                    }
                    setResult(RESULT_OK);
                    finish();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
        }
    }

    /**
     * 问题列表每一项点击事件，进入编辑页面
     *
     * @param parent
     * @param view
     * @param position
     * @param id
     */
    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        CommonProblem.DataBean dataBean = problemList.get(position);
        if (dataBean != null) {
            index = position;
            Intent intent = new Intent(this, CommonProblemEditTextActivity.class);
            intent.putExtra(CommonProblemEditTextActivity.HINT, "在这里输入问题内容...");
            intent.putExtra(CommonProblemEditTextActivity.TITLE, "编辑问题");
            intent.putExtra(CommonProblemEditTextActivity.TEXT, dataBean.getContent() + "");
            startActivityForResult(intent, REQUEST_CODE);
        }
    }


    /**
     * 添加问题或者编辑问题返回的方法
     *
     * @param requestCode
     * @param resultCode
     * @param data
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_CODE:
                if (resultCode == RESULT_OK) {
                    isUpdated = true;
                    String text = data.getStringExtra(CommonProblemEditTextActivity.TEXT);
                    if (index >= 0) {
                        problemList.get(index).setContent(text);
                    } else {
                        CommonProblem.DataBean dataBean = new CommonProblem.DataBean();
                        dataBean.setContent(text);
                        problemList.add(dataBean);
                    }
                    problemAdapter.notifyDataSetChanged();
                }
                break;
        }
    }


    /**
     * 右上角删除按钮点击事件
     *
     * @param view
     */
    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        if (deleteConfirmDialog == null) {
            deleteConfirmDialog = ConfirmDialog.getInstance(this);
        }
        deleteConfirmDialog.setDialogContent("是否删除所有定制问诊单？")
                .setPositiveText("删除")
                .setNavigationText("取消")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        deleteConfirmDialog.dismiss();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        if (commonProblem != null) {
                            deleteCommonProblem(commonProblem.getLabel());
                        }
                        deleteConfirmDialog.dismiss();
                    }
                }).show();
    }

    /**
     * 长按问题，进行删除操作
     *
     * @param parent
     * @param view
     * @param position
     * @param id
     * @return
     */
    @Override
    public boolean onItemLongClick(AdapterView<?> parent, View view, final int position, long id) {
        if (position >= 0 && position < problemList.size()) {

            if (deleteConfirmDialog == null) {
                deleteConfirmDialog = ConfirmDialog.getInstance(this);
            }
            deleteConfirmDialog.setDialogContent("是否删除此定制问诊单？")
                    .setNavigationText("取消")
                    .setPositiveText("删除")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
                            deleteConfirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            isUpdated = true;
                            problemList.remove(position);
                            problemAdapter.notifyDataSetChanged();
                            deleteConfirmDialog.dismiss();

                        }
                    }).show();

        }
        return true;
    }

    /**
     * 系统返回键
     */
    @Override
    public void onBackPressed() {
        closeActivity();
    }


    /**
     * 导航栏返回箭头
     *
     * @param activity
     */
    @Override
    public void onBack(Activity activity) {
        closeActivity();
    }

    /**
     * 处理关闭页面的逻辑
     */
    private void closeActivity() {
        if (isUpdated) {
            if (actionType == COMMON_PROBLEM_ADD && problemList.size() == 0) {
                finish();
            } else {
                if (backDialog == null) {
                    backDialog = ConfirmDialog.getInstance(this);
                }
                backDialog.setDialogContent("新增问题未保存，是否退出？")
                        .setNavigationText("取消")
                        .setPositiveText("退出")
                        .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                backDialog.dismiss();
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                backDialog.dismiss();
                                UpdateCommonProblemActivity.this.finish();
                            }
                        }).show();
            }
        } else {
            finish();
        }
    }


    @Override
    protected void onDestroy() {
        cancelRequest(saveCommonProblemCallBack,
                updateCommonProblemCallBack,
                deleteCommonProblemCallBack);
        super.onDestroy();
    }
}
