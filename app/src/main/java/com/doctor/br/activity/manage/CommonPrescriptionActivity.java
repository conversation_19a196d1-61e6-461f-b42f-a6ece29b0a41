package com.doctor.br.activity.manage;

import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.doctor.br.activity.BaseRefreshActivity;
import com.doctor.br.adapter.manage.CommonPrescriptionAdapter;
import com.doctor.br.bean.CommonPrescriptionBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.DialogHelper;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.UIHelper;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bingoogolapple.androidcommon.adapter.BGAOnItemChildClickListener;
import cn.bingoogolapple.androidcommon.adapter.BGAOnItemChildLongClickListener;
import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;

/**
 * 常用方界面
 */
public class CommonPrescriptionActivity extends BaseRefreshActivity<CommonPrescriptionBean.DatalistBean> implements BGAOnItemChildClickListener
        ,BGAOnItemChildLongClickListener {
    @BindView(R.id.ll_bottom)
    LinearLayout llBottom;
    private ConfirmDialog confirmDialog;
    private int currentLongClickPosition = -1;//当前的长点击位置
    private static final int CODE_REQUEST_COMMONPRESCRIPTION = 11;//常用方的request_code
    private RequestCallBack prescriptionCallBack;
    private RequestCallBack deleteCallBack;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_common_prescription;
    }

    @Override
    protected void init() {//初始化
        super.init();
//        EventBusUtils.register(this);
        mRefreshAdapter.setOnItemChildClickListener(this);
        mRefreshAdapter.setOnItemChildLongClickListener(this);
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarTitle("常用方");
        setActionBarRightBtnImg(R.drawable.search);

        EventBusUtils.register(this);

        requestData(1);//请求数据

    }

    @Override
    protected void setListener() {//设置监听,在init()之后执行
        super.setListener();
        if (emptyView != null) {
            emptyView.setEmptyText("暂无数据");
            emptyView.setEmptyImgResource(R.drawable.no_commonprescription_data);
        }

    }

    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        UIHelper.openSearchPrescriptionActivity(CommonPrescriptionActivity.this);
    }
    @Override
    public BGARecyclerViewAdapter<CommonPrescriptionBean.DatalistBean> getAdapter() {//返回adapter
        return new CommonPrescriptionAdapter(mRvData);
    }


    public void requestData(int pageNum) {//网络请求 addHttp......
        currentPage = pageNum;
        if(currentPage == 1){
            //上啦没数据了之后，再次刷新，在上啦，没办法加载更多
            setCanLoadMore(true);
        }
        Map<String, String> params = RequestParams.getCommonPrescriptionParams(null, currentPage, getPageSize());
        prescriptionCallBack = addHttpPostRequest(HttpUrlManager.COMMON_PRESCRIPTION, params, CommonPrescriptionBean.class, this);
    }

    /**
     * 发送常用方数据到编辑常用方界面
     *
     * @reciove {@link AddCommonPrescriptionActivity#}
     */
    @Override
    public void onItemChildClick(ViewGroup parent, View childView, int position) {  //条目的点击事件
        if (childView.getId() == R.id.rl_container) {
            //传递数据到编辑常用方界面
            List<CommonPrescriptionBean.DatalistBean> list =mRefreshAdapter.getData();
            UIHelper.openAddCommonPrescriptionActivity(this,CODE_REQUEST_COMMONPRESCRIPTION,"编辑常用方",list.get(position));
        }
    }

    @Override
    public boolean onItemChildLongClick(ViewGroup parent, View childView, int position) {//条目的long点击事件
        if (childView.getId() == R.id.rl_container) {
            this.currentLongClickPosition = position;
            confirmDialog = DialogHelper.openConfirmDialog(this, "是否删除此常用方？", "确认", "取消",
                    new View.OnClickListener() {

                @Override
                public void onClick(View v) {
                    if (currentLongClickPosition >= 0 && currentLongClickPosition < mRefreshAdapter.getData().size()) {
                        String templateId = mRefreshAdapter.getData().get(currentLongClickPosition).getTemplateId();
                        deleteCallBack = addHttpPostRequest(HttpUrlManager.DELETE_COMMON_PRESCRIPTION, RequestParams.getDeleteCommonPrescription(templateId),
                                ResponseResult.class, CommonPrescriptionActivity.this);
                    }

                }
            });
            confirmDialog.show();
            return true;
        }
        return false;
    }

    @Override
    @OnClick(R.id.ll_bottom)
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ll_bottom://点击新增常用方
                UIHelper.openAddCommonPrescriptionActivity(this,CODE_REQUEST_COMMONPRESCRIPTION,"新增常用方",null);
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
       /* if(requestCode == CODE_REQUEST_COMMONPRESCRIPTION && resultCode == RESULT_OK){//编辑或添加常用方成功
            requestData(1);
        }*/
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceiveUpdateCommonPrescriptionNotify(CommonPrescriptionBean.DatalistBean datalistBean){//更新列表的通知，在AddCommonPrescriptionActivity中发布通知
        if(datalistBean!=null && "0000".equals(datalistBean.getCode())){
            requestData(1);
        }
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.COMMON_PRESCRIPTION://列表数据
                if (result.isRequestSuccessed()) {  //请求成功
                    CommonPrescriptionBean commonPrescriptionBean = (CommonPrescriptionBean) result.getBodyObject();
                    if(currentPage == 1){//pageNum为1，是下拉刷新

                    }else{

                    }
                    pageCount = commonPrescriptionBean.getTotalPageSize();
                    onRequestListSuccess(taskId,commonPrescriptionBean.getDatalist());
                } else {//网络请求失败
                    onRequestListError();
                    RequestErrorToast.showError(this,taskId,result.getCode(),result.getErrorMsg());
                }
                break;
            case HttpUrlManager.DELETE_COMMON_PRESCRIPTION://删除常用方
                if (result != null && result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "常用方删除成功");
                    if (confirmDialog != null) {
                        confirmDialog.dismiss();
                    }
                    //刷新数据
                    requestData(1);
                    currentLongClickPosition = -1;//重置position
                }else{
                    RequestErrorToast.showError(this,taskId,result.getCode(),result.getErrorMsg());
                }
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (confirmDialog != null) {
            confirmDialog.cancel();
            confirmDialog = null;
        }
        cancelRequest(deleteCallBack);
        cancelRequest(prescriptionCallBack);
        EventBusUtils.unRegister(this);
        super.onDestroy();
    }

    @Override
    public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {
        requestData(1);
    }

    @Override
    public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {
        if(hasMoreData()){
            requestData(currentPage);
        }
    }

    @Override
    public void onReload() {//网络异常
        requestData(1);
    }


    @Override
    public void addNewData() {//重写刷新请求成功后，数据处理
        //添加含有字符的空Bean
        java.util.List<CommonPrescriptionBean.DatalistBean> newList = new ArrayList<>();
        setFirstSpell();
        Collections.sort(dataList);//排序
        createNewListBean(newList, dataList);

        mRefreshLayout.endRefreshing();
        mRefreshAdapter.clear();
        mRefreshAdapter.getData().addAll(newList);
        mRefreshAdapter.notifyDataSetChangedWrapper();
        mRvData.smoothScrollToPosition(0);

    }

    @Override
    public void addMoreData() {//重写加载更多后，数据的处理

        List<CommonPrescriptionBean.DatalistBean> newList = createMoreData();
        mRefreshLayout.endLoadingMore();
        mRefreshAdapter.getData().clear();
        mRefreshAdapter.getData().addAll(newList);
        mRefreshAdapter.notifyDataSetChangedWrapper();

    }


    /**
     * 先获取已有的数据，去除含有字符的空Bean,合并为一个新的集合，然后按字符排序，再添加含字符的空bean
     */
    private List<CommonPrescriptionBean.DatalistBean> createMoreData() {
        List<CommonPrescriptionBean.DatalistBean> centerList = new ArrayList<>();
        List<CommonPrescriptionBean.DatalistBean> newList = new ArrayList<>();
        List<CommonPrescriptionBean.DatalistBean> beforeList = mRefreshAdapter.getData();
        int beforeSize = beforeList.size();
        for (int i = 0; i < beforeList.size(); i++) {
            CommonPrescriptionBean.DatalistBean listBean = beforeList.get(i);
            if (listBean.getType() != CommonPrescriptionAdapter.TYPE_CHARACTER) {//不是字符bean
                centerList.add(listBean);

            }
        }
        setFirstSpell();
        centerList.addAll(dataList);//先获取已有的数据，去除含有字符的空Bean,合并为一个新的集合
        Collections.sort(centerList);//排序
        createNewListBean(newList, centerList);//添加含字符的空bean
        return newList;
    }

    /**
     * 添加含字符的空bean
     *
     * @param newList
     * @param oldList
     */
    private void createNewListBean(List<CommonPrescriptionBean.DatalistBean> newList, List<CommonPrescriptionBean.DatalistBean> oldList) {
        for (int i = 0; i < oldList.size(); i++) {
            if (i == 0) {
                addCharacterData(newList, oldList, i);
            } else {
                if (oldList.get(i).getFirstSpell().equals(oldList.get(i - 1).getFirstSpell())) {
                    newList.add(oldList.get(i));
                } else {
                    addCharacterData(newList, oldList, i);
                }
            }
        }

    }

    /**
     * 添加含空字符的bean
     *
     * @param newList
     * @param oldList
     * @param i
     */
    public void addCharacterData(List<CommonPrescriptionBean.DatalistBean> newList, List<CommonPrescriptionBean.DatalistBean> oldList, int i) {
        CommonPrescriptionBean.DatalistBean dataBean = new CommonPrescriptionBean.DatalistBean();
        dataBean.setFirstSpell(oldList.get(i).getFirstSpell());
        dataBean.setType(CommonPrescriptionAdapter.TYPE_CHARACTER);
        newList.add(dataBean);
        newList.add(oldList.get(i));
    }

    public void setFirstSpell(){
        for(int i=0;dataList!=null && i<dataList.size();i++){
            char itemChar = dataList.get(i).getFirstSpell().charAt(0);
            if((itemChar>='a' && itemChar<='z') || (itemChar>='A' && itemChar<='Z')){//字符在a--->z,,,,,A-->Z之间
                dataList.get(i).setFirstSpell(dataList.get(i).getFirstSpell().toUpperCase());
            }else{
                dataList.get(i).setFirstSpell("|");
            }
        }
    }

}

