package com.doctor.br.activity.manage;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MotionEvent;
import android.view.View;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.TextView;

import com.doctor.br.adapter.manage.MedicineListAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.db.entity.MedicineItem;
import com.doctor.br.utils.RegularUtils;
import com.doctor.br.view.MyLetterSortView;
import com.doctor.br.view.stickyListView.StickyListHeadersListView;
import com.doctor.greendao.gen.MedicineItemDao;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;

import java.util.ArrayList;
import java.util.List;

/**
 * 类描述：药典查询界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/15
 */

public class PharmacopoeiaQueryActivity extends ActionBarActivity implements AdapterView.OnItemClickListener {
    //界面下的控件
    private EditText editText;
    private TextView cancelTv;
    private StickyListHeadersListView stickyListHeadersListView;
    private MyLetterSortView letterView;
    private TextView midTv;

    private List<MedicineItem> medicineList;//药典列表
    private List<String> medicineLetterList;//保存字母

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_medicine_list);
        initView();
        getMedicineList();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("药典");

        editText = (EditText) findViewById(R.id.edit_text);
        cancelTv = (TextView) findViewById(R.id.cancel_tv);
        stickyListHeadersListView = (StickyListHeadersListView) findViewById(R.id.sticky_list_view);
        letterView = (MyLetterSortView) findViewById(R.id.letter_view);
        midTv = (TextView) findViewById(R.id.mid_tv);

        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
//                if(s.length()>0){
//                    cancelTv.setTextColor(ContextCompat.getColor(PharmacopoeiaQueryActivity.this,R.color.br_color_theme));
//                }
                searchList(s.toString());
            }
        });

        cancelTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(editText.getText())) {
                    //如果没有输入的话隐藏软键盘
//                    cancelTv.setTextColor(ContextCompat.getColor(PharmacopoeiaQueryActivity.this,R.color.br_color_et_hint));
                    hideKeyBoard(editText);
                    return;
                }
                editText.setText("");
            }
        });

        stickyListHeadersListView.setDividerHeight(0);
        stickyListHeadersListView.setOnTouchListener(new View.OnTouchListener() {

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                hideKeyBoard(v);
                return false;
            }
        });
        stickyListHeadersListView.setOnItemClickListener(this);

        letterView.setOnTouchingLetterChangedListener(new MyLetterSortView.OnTouchingLetterChangedListener() {
            @Override
            public void onTouchingLetterChanged(String s) {
                int position = 0;
                if (medicineList != null) {
                    for (int i = 0; i < medicineList.size(); i++) {
                        MedicineItem medicineItem = medicineList.get(i);
                        if (s.toUpperCase().equals(medicineItem.getFristLeter().toUpperCase())) {
                            position = i;
                            break;
                        }
                    }
                }
                stickyListHeadersListView.setSelection(position);
            }
        });
    }


    //数据库获取药典列表
    private void getMedicineList() {
//        medicineList = AppContext.getInstances().getDaoSession().getMedicineItemDao().loadAll();
        medicineList = AppContext.getInstances().getDaoSession().getMedicineItemDao()
                .queryBuilder()
                .orderAsc(MedicineItemDao.Properties.FristLeter)
                .list();
        setAdapter();
    }

    //将存在的字母添加进去
    private void addLetters() {
        if (medicineList == null) {
            return;
        }
        medicineLetterList = new ArrayList<>();
        for (int i = 0; i < medicineList.size(); i++) {
            MedicineItem medicineItem = medicineList.get(i);
            String firstLetter = RegularUtils.letterFilter(medicineItem.getFristLeter().toUpperCase());
            if (i == 0) {
                medicineLetterList.add(firstLetter);
            } else if (!medicineItem.getFristLeter().toUpperCase().equals(medicineList.get(i - 1).getFristLeter().toUpperCase())) {
                medicineLetterList.add(firstLetter);
            }
            medicineItem.setFristLeter(firstLetter);
        }
    }

    //根据输入的字符搜索药材
    private void searchList(String s) {
        if (s.length() > 0) {
            String dbString = " WHERE " + MedicineItemDao.Properties.DrugName.columnName + " LIKE '%" + s
                    + "%' OR " + MedicineItemDao.Properties.SecondName.columnName + " LIKE '%" + s
                    + "%' OR " + MedicineItemDao.Properties.Spell.columnName + " LIKE '%" + s + "%'";
            medicineList = AppContext.getInstances().getDaoSession().getMedicineItemDao().queryRaw(dbString);
        } else {
            medicineList = AppContext.getInstances().getDaoSession().getMedicineItemDao().loadAll();
        }
        setAdapter();
    }

    private void setAdapter() {
        addLetters();
        MedicineListAdapter medicineListAdapter = new MedicineListAdapter(this, medicineList);
        stickyListHeadersListView.setAdapter(medicineListAdapter);

        letterView.setData(midTv, medicineLetterList);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        Intent intent = new Intent(this, MedicineDetailsActivity.class);
        intent.putExtra(MedicineDetailsActivity.DRUG_ID, medicineList.get(position).getDetailId());
        startActivity(intent);
    }
}
