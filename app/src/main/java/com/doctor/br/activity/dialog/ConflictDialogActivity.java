package com.doctor.br.activity.dialog;

import android.os.Bundle;
import android.view.Display;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.app.AppContext;
import com.doctor.br.utils.ReLoginCallBack;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.utils.SharedPreferenceUtils;

public class ConflictDialogActivity extends NoActionBarActivity {

    private LinearLayout layoutTitle;
    private TextView tvTitle;
    private TextView tvContent;
    private Button btnPositive;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.zoomin, R.anim.zoomout);
        setActionBarContentView(R.layout.activity_conflict_dialog);
        //关闭activity动画显示
        this.overridePendingTransition(R.anim.push_center_in, 0);
        WindowManager m = getWindowManager();
        Display d = m.getDefaultDisplay(); // 为获取屏幕宽、高
        android.view.WindowManager.LayoutParams p = getWindow().getAttributes();
        p.width = (int) (d.getWidth() * 0.7); // 宽度设置为屏幕的0.7
        getWindow().setAttributes(p);
        initViews();
    }

    private void initViews() {
        layoutTitle = (LinearLayout) findViewById(R.id.layout_title);
        tvTitle = (TextView) findViewById(R.id.dialog_title);
        tvContent = (TextView) findViewById(R.id.dialog_content);
        btnPositive = (Button) findViewById(R.id.btn_dialog_positive);
        btnPositive.setOnClickListener(this);
        btnPositive.post(new Runnable() {
            @Override
            public void run() {
                ReLoginCallBack.currentState = 0;//不需要弹窗
            }
        });
    }

    @Override
    public void onClick(View view) {
        super.onClick(view);
        switch (view.getId()) {
            case R.id.btn_dialog_positive:
                SharedPreferenceUtils.clearString(this);
                AppContext.getInstances().reboot();
                this.finish();
                //关闭activity动画显示
                overridePendingTransition(R.anim.zoomin, R.anim.zoomout);
                break;
        }
    }

    @Override
    public void onBackPressed() {
//        super.onBackPressed();
    }
}
