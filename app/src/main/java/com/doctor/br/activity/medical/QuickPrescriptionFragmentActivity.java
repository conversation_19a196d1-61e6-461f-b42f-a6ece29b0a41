package com.doctor.br.activity.medical;

import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.Fragment;

import com.doctor.br.fragment.chatmain.MedicationFragment;
import com.doctor.br.utils.AuthCheckHelper;
import com.doctor.br.bean.AuthStateResult;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.widgets.AlertDialog;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 快速开方Activity，使用MedicationFragment实现快速开方功能
 */
public class QuickPrescriptionFragmentActivity extends ActionBarActivity {
    
    /**
     * 启动快速开方Activity，带权限检查
     * @param context Fragment上下文
     * @param prescriptionType 开方类型："weixin" 或 "sms"
     */
    public static void launchPreDialog(Fragment context, String prescriptionType) {
        String text = "";
        if ("weixin".equals(prescriptionType)) {
            text = "微信开方提示";
        } else if ("sms".equals(prescriptionType)) {
            text = "短信开方提示";
        }

        final AlertDialog dialog = AlertDialog.getInstance(context.getActivity());
        dialog.setDialogTitle(text)
                .setDialogContent("该功能仅适用于一次性诊断或年老患者。患者未扫码关注医生，不享有在线交流、管理医案、调用历史药方、自动提醒支付、管理患者等丰富功能。长期复诊患者建议【微信扫码开方】")
                .setPositiveText("确认使用")
                .setOnPositiveBtnClickedListener(v -> {
                    dialog.dismiss();
                    
                    // 检查用户是否是邀请用户
                    if (context.getActivity() != null) {
                        String userId = org.newapp.ones.base.utils.SharedPreferenceUtils.getString(context.getActivity(), org.newapp.ones.base.base.PublicParams.USER_ID, "");
                        boolean isInviteUser = org.newapp.ones.base.utils.SharedPreferenceUtils.getIsInviteUser(context.getActivity(), userId);
                        
                        if (isInviteUser) {
                            // 邀请用户直接打开页面
                            Intent intent = new Intent(context.getActivity(), QuickPrescriptionFragmentActivity.class);
                            intent.putExtra("prescriptionType", prescriptionType);
                            context.startActivity(intent);
                        } else {
                            // 非邀请用户，需要检查权限
                            if (context instanceof org.newapp.ones.base.fragment.BaseFragment) {
                                org.newapp.ones.base.fragment.BaseFragment baseFragment = (org.newapp.ones.base.fragment.BaseFragment) context;
                                AuthCheckHelper.checkAuthFeatureWithLatestState(baseFragment, new AuthCheckHelper.AuthCheckCallback() {
                                    @Override
                                    public void onAuthCheckResult(boolean canUse, AuthStateResult result) {
                                        if (canUse) {
                                            Intent intent = new Intent(context.getActivity(), QuickPrescriptionFragmentActivity.class);
                                            intent.putExtra("prescriptionType", prescriptionType);
                                            context.startActivity(intent);
                                        }
                                    }
                                });
                            }
                        }
                    }
                });
        dialog.show();
    }
    
    /**
     * 直接启动快速开方Activity
     * @param context Fragment上下文
     * @param prescriptionType 开方类型："weixin" 或 "sms"
     */
    public static void launch(Fragment context, String prescriptionType) {
        Intent intent = new Intent(context.getContext(), QuickPrescriptionFragmentActivity.class);
        intent.putExtra("prescriptionType", prescriptionType);
        context.startActivity(intent);
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 获取传入的开方类型
        String prescriptionType = getIntent().getStringExtra("prescriptionType");
        
        // 设置标题
        if ("weixin".equals(prescriptionType)) {
            setActionBarTitle("微信开方");
        } else if ("sms".equals(prescriptionType)) {
            setActionBarTitle("短信开方");
        } else {
            setActionBarTitle("快速开方");
        }
        
        // 设置帮助按钮
        setActionBarRightBtnImg(R.drawable.icon_help);
        
        // 设置内容视图
        setActionBarContentView(R.layout.activity_quick_prescription_fragment);
        
        // 创建并添加MedicationFragment
        if (savedInstanceState == null) {
            MedicationFragment fragment = MedicationFragment.newInstanceForQuickPrescription(prescriptionType);
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.fragment_container, fragment)
                    .commit();
        }
    }
    
    @Override
    public void onRightBtnClick(android.view.View view) {
        String prescriptionType = getIntent().getStringExtra("prescriptionType");
        String text = "提示";
        if ("weixin".equals(prescriptionType)) {
            text = "微信开方提示";
        } else if ("sms".equals(prescriptionType)) {
            text = "短信开方提示";
        }

        super.onRightBtnClick(view);
        final AlertDialog dialog = AlertDialog.getInstance(this);
        dialog.setDialogTitle(text)
                .setDialogContent("该功能仅适用于一次性诊断或年老患者。患者未扫码关注医生，不享有在线交流、管理医案、调用历史药方、自动提醒支付、管理患者等丰富功能。长期复诊患者建议【微信扫码开方】")
                .setPositiveText("知道了")
                .setOnPositiveBtnClickedListener(v -> {
                    dialog.dismiss();
                });
        dialog.show();
    }
    
    /**
     * 判断是否为合法的患者姓名
     */
    public static boolean isChineseName(String string) {
        if (string.length() <= 1) {
            return false;
        }
        String pattern = "^[\\u4e00-\\u9fa5]{1,8}(·[\\u4e00-\\u9fa5]{1,8})?$";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(string);
        return matcher.matches();
    }
}