package com.doctor.br.activity.manage;

import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.base.request.common.BaseRxActivity;
import com.doctor.br.adapter.manage.OrderRecordViewPagerAdapter;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.widgets.ActionBarView;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

public class OrderRecordActivity extends BaseRxActivity implements ViewPager.OnPageChangeListener, View.OnClickListener {

    private int clickPosition;//点击哪个title进来的。从而判断首先加载哪个fragment，非必须，默认为0

    @BindView(R.id.linear_container)
    LinearLayout linearContainer;
    @BindView(R.id.bottom_line)
    LinearLayout bottomLine;
    @BindView(R.id.view1)
    RelativeLayout view1;
    @BindView(R.id.view_pager)
    ViewPager viewPager;
    @BindView(R.id.gone_tv)
    TextView goneTv;

    private List<int[]> paddingList;//设置bottomLine的padding

    private OrderRecordViewPagerAdapter orderRecordViewPagerAdapter;//下面viewpager的适配器

    private String[] names = {"待付款", "待发货", "已发货","已完成"};

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_order_record);
        initView();
        setList();
    }

    private void initView() {
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarTitle("订单记录");
        getBaseActionBar().setActionBarChangeListener(this);

        orderRecordViewPagerAdapter = new OrderRecordViewPagerAdapter(getSupportFragmentManager(), names.length);
        viewPager.setOffscreenPageLimit(names.length);
        viewPager.setAdapter(orderRecordViewPagerAdapter);
        viewPager.addOnPageChangeListener(this);
    }

    /**
     * 根据names设置文字，并获取左右边距
     */
    private void setList() {
        paddingList = new ArrayList<>(names.length);
        for (int i = 0; i < names.length; i++) {
            LinearLayout linearLayout = new LinearLayout(this);

            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT);
            layoutParams.weight = 1;
            linearLayout.setLayoutParams(layoutParams);
            linearLayout.setGravity(Gravity.CENTER);
            linearLayout.setTag(R.id.img_url, i);
            linearLayout.setOnClickListener(this);

            TextView textView = new TextView(this);
            LinearLayout.LayoutParams tvLp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            textView.setLayoutParams(tvLp);
            textView.setTextSize(16);
            textView.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme_text));
            textView.setText(names[i]);

            linearLayout.addView(textView);
            linearContainer.addView(linearLayout);

        }
        linearContainer.post(new Runnable() {
            @Override
            public void run() {
                int marginPx = DensityUtils.dip2px(OrderRecordActivity.this, 5f);
                for (int i = 0; i < names.length; i++) {
                    int[] leftRight = new int[2];//左边是到父布局左侧距离，右边是到父布局右侧距离
                    ViewGroup viewGroup = (ViewGroup) linearContainer.getChildAt(i);
                    leftRight[0] = viewGroup.getChildAt(0).getLeft() - marginPx;
                    leftRight[1] = viewGroup.getMeasuredWidth() - viewGroup.getChildAt(0).getRight() - marginPx;
                    paddingList.add(leftRight);
                }
                setSelectPosition();
            }
        });
    }

    /**
     * 根据当前选中的位置，设置底部bottomLine的内边距
     */
    private void setSelectPosition() {
        viewPager.setCurrentItem(clickPosition, false);
        if (paddingList.size() == 0) {
            return;
        }
        double eachWidth = bottomLine.getMeasuredWidth() / paddingList.size();
        double left = paddingList.get(clickPosition)[0] + clickPosition * eachWidth;
        double right = paddingList.get(clickPosition)[1] + (paddingList.size() - 1 - clickPosition) * eachWidth;
        bottomLine.setPadding((int) left, 0, (int) right, 0);
    }

    /**
     * 根据选中位置设置字体颜色
     *
     * @param position 当前选中的位置
     */
    private void switchColor(int position) {
        for (int i = 0; i < names.length; i++) {
            LinearLayout linearLayout = (LinearLayout) linearContainer.getChildAt(i);
            TextView textView = (TextView) linearLayout.getChildAt(0);
            if (i == position) {
                textView.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme));
            } else {
                textView.setTextColor(ContextCompat.getColor(this, R.color.br_color_theme_text));
            }
        }
    }

    @Override
    public void onClick(View view) {
        if (view.getTag(R.id.img_url) instanceof Integer) {
            int clickPos = (int) view.getTag(R.id.img_url);
            viewPager.setCurrentItem(clickPos);
        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        setPaddingScroll(position, positionOffset);
    }

    @Override
    public void onPageSelected(int position) {
        switchColor(position);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    private void setPaddingScroll(int position, float positionOffset) {
        if (paddingList.size() == 0 || position == paddingList.size() - 1) {
            return;
        }
        double eachWidth = bottomLine.getMeasuredWidth() / paddingList.size();
        double left, right;
        double nextLeft = paddingList.get(position + 1)[0] + (position + 1) * eachWidth;
        double nowLeft = paddingList.get(position)[0] + position * eachWidth;
        double nextRight = paddingList.get(position + 1)[1] + (paddingList.size() - 2 - position) * eachWidth;
        double nowRight = paddingList.get(position)[1] + (paddingList.size() - 1 - position) * eachWidth;
        left = positionOffset * (nextLeft - nowLeft) + nowLeft;
        right = positionOffset * (nextRight - nowRight) + nowRight;
        bottomLine.setPadding((int) left, 0, (int) right, 0);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
