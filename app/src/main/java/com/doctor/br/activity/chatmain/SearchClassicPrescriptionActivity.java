package com.doctor.br.activity.chatmain;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.activity.BaseRefreshActivity;
import com.doctor.br.adapter.medical.SearchClassicBean;
import com.doctor.br.adapter.medical.SearchClassicPrescriptionAdapter;
import com.doctor.br.bean.medical.CommonTemplateMsg;
import com.doctor.br.bean.medical.ReplaceDrugBean;
import com.doctor.br.bean.medical.TemplateDrugBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.UIHelper;
import com.doctor.br.view.CommentTemplateListPopWindow;
import com.doctor.br.view.ReplaceDrugPop;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.yy.R;

import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.ActivityManager;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import butterknife.BindView;
import cn.bingoogolapple.androidcommon.adapter.BGAOnRVItemClickListener;
import cn.bingoogolapple.androidcommon.adapter.BGAOnRVItemLongClickListener;
import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;
/**
 * <AUTHOR>
 * @version 4.0.3
 * @project BrZhongYiAndroid
 * @description  搜索经典方界面
 * @createTime 2018/3/14
 */
public class SearchClassicPrescriptionActivity extends BaseRefreshActivity<SearchClassicBean.DataBean> implements BGAOnRVItemClickListener,BGAOnRVItemLongClickListener{
    private static final String ISCLICK = "isClick";
    private static final String ISLONGCLICKk = "isLongClic";

    @BindView(R.id.rl_container)
    RelativeLayout rlContainer;

    private List<SearchClassicBean.DataBean> medicationList;
    private CommentTemplateListPopWindow commentTemplateListPop;//详情的popwindow
    private ReplaceDrugPop mReplaceDrugPop;//替换药的popwindow
    private RequestCallBack searchClassicCallBack;
    private RequestCallBack detailCallBack;
    private String drugType;
    private String drugProviderId;
    private String drugForm;
    private String isSelfSupport;
    private String taskType;
    private boolean showReplacePop;//是否显示替换要的popwindow



    @Override
    protected int getLayoutId() {
        return R.layout.activity_base_refresh_okhttp;
    }

    @Override
    public BGARecyclerViewAdapter<SearchClassicBean.DataBean> getAdapter() {//返回adapter
        return new SearchClassicPrescriptionAdapter(mRvData);
    }

    /**
     * 初始化
     */
    @Override
    protected void init() {
        super.init();
        setActionBarType(ActionBarView.ACTIONBAR_SEARCH);
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarSearchInputHint("方名、分类（如清热、安神等）");
        //替换药的popwindow
        mReplaceDrugPop = new ReplaceDrugPop(mContext);
        //获取开药界面传递过来的参数
        drugType = SharedPreferenceUtils.getString(this, PublicParams.DRUG_TYPE);
        drugProviderId = SharedPreferenceUtils.getString(this, PublicParams.DRUG_PROVIDERID);
        drugForm = SharedPreferenceUtils.getString(this, PublicParams.DRUG_FROM);
        isSelfSupport = SharedPreferenceUtils.getString(this, PublicParams.SELFSUPPORT);

        setRefreshEnable(false);//不需要上啦刷新
        mRefreshLayout.setCanLoadMore(false);//不需要加载更多
        //设置点击事件
        mRefreshAdapter.setOnRVItemClickListener(this);
        mRefreshAdapter.setOnRVItemLongClickListener(this);

    }

    public void requestData(int pageNum) {//网络请求 addHttp......
        if(TextUtils.isEmpty(getActionbarSearchInputText().trim())){
            ToastUtils.showShortMsg(this,"请输入搜索内容");
            return;
        }
        currentPage = pageNum;
        String content = getActionbarSearchInputText().trim();
       LinkedHashMap<String,String> params = new LinkedHashMap();
       params.put("keyword",content);
        searchClassicCallBack = addHttpPostRequest(HttpUrlManager.CLASSIC_SEARCH, params, SearchClassicBean.class, this);

    }



    @Override
    protected void setListener() {//设置监听,在init()之后执行
        super.setListener();
        if (emptyView != null) {
            emptyView.setEmptyText("暂无数据");
            emptyView.setEmptyImgResource(R.drawable.no_commonprescription_data);
        }
        setOnSearchInputEditorActionListener(new TextView.OnEditorActionListener() {//点击键盘上的search的监听
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if(actionId == EditorInfo.IME_ACTION_SEARCH){
                  requestData(1);
                    return true;
                }
                return false;
            }
        });
        setActionBarSearchInputListener(new TextWatcher() {//添加数据框的实时监听
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String content = getActionbarSearchInputText().trim();
                if(TextUtils.isEmpty(content)){//清除之前的列表数据
                    if(mRefreshAdapter.getData()!=null && mRefreshAdapter.getData().size()>0)
                    mRefreshAdapter.clear();
                }
            }
        });

    }

    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        requestData(1);
    }

    @Override
    public void onRVItemClick(ViewGroup parent, View itemView, int position){ //条目点击事件
        SearchClassicBean.DataBean dataBean = mRefreshAdapter.getData().get(position);
        if(SearchClassicBean.DataBean.TYPE_CATEGORY ==dataBean.getDataType()){//说明是分类的item
            UIHelper.openSecondClassicPrescription(this,dataBean.getTypeId(),dataBean.getName());
        }else{//经典方的item
            showReplacePop = false;//重新选择一副药的时候至为false
            taskType = ISCLICK;
            //ToastUtils.showShortMsg(getActivity(),"点击事件");
            getTemplateDetail(drugType, dataBean.getId(), drugProviderId, drugForm);
        }
    }

    @Override
    public boolean onRVItemLongClick(ViewGroup parent, View itemView, int position) {//常点击事件
        SearchClassicBean.DataBean dataBean = mRefreshAdapter.getData().get(position);
        if(SearchClassicBean.DataBean.TYPE_PRESCRIPTION ==dataBean.getDataType()){ //经典方的item
            showReplacePop = false; //重新选择一副药的时候至为false
            taskType = ISLONGCLICKk;
            getTemplateDetail(drugType, dataBean.getId(), drugProviderId, drugForm);
        }
        return true;
    }

    /**
     * @param drugType       K/Y
     * @param recipeId  经典方id
     *                       请求经典方详情数据
     * @param drugProviderId 厂商id
     * @param drugForm       剂型（外层大剂型:蜜丸水丸等）
     */
    public void getTemplateDetail(String drugType, String recipeId, String drugProviderId, String drugForm) {
        HashMap map = new HashMap();
        map.put("drugType", drugType);
        map.put("recipeId", recipeId);
        map.put("drugProviderId", drugProviderId);
        map.put("drugForm", drugForm);
        detailCallBack = addHttpPostRequest(HttpUrlManager.CLASSIC_DETAIL, map, CommonTemplateMsg.class, this);
    }



    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId){
            case HttpUrlManager.CLASSIC_SEARCH://搜索经典方
                if(result.isRequestSuccessed()){
                    SearchClassicBean searchClassicBean =(SearchClassicBean)result.getBodyObject();
                    medicationList = searchClassicBean.getData();
                    if(searchClassicBean!=null){
                        onRequestListSuccess(taskId,searchClassicBean.getData());
                    }
                }else{
                    onRequestListError();
                    RequestErrorToast.showError(this,taskId,result.getCode(),result.getErrorMsg());
                }
                break;
            case HttpUrlManager.CLASSIC_DETAIL://经典方详情
                if (result != null && result.isRequestSuccessed()) {
                    CommonTemplateMsg commonTemplate = (CommonTemplateMsg) result.getBodyObject();
                    if (commonTemplate != null && commonTemplate.getData() != null && commonTemplate.getData().size() > 0) {
                        if (ISCLICK.equals(taskType)) {  //点击操作

                            resolveSingleClick(commonTemplate);

                        } else if (ISLONGCLICKk.equals(taskType)) {  //长点击事件
                            resolveLongClick(commonTemplate);
                        }
                    } else {
                        // 药方中药材为空处理
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
        }
    }

    public void resolveSingleClick( CommonTemplateMsg commonTemplate){//处理单击的逻辑
        //颗粒或自营饮片及其他剂型
        if (drugType.equals(PublicParams.DOSAGEFORM_GRANULE_K)
                || (drugType.equals(PublicParams.DOSAGEFORM_SLICES_Y) && isSelfSupport.equals(PublicParams.IS_SELFSUPPORT))) {
            for (int i = 0; i < commonTemplate.getData().size(); i++) {
                List<ReplaceDrugBean> replaceDrugs = commonTemplate.getData().get(i).getReplaceData();
                if (replaceDrugs != null && replaceDrugs.size() > 0) {
                    showReplacePop = true;
                }
            }
        }
        isShowReplacePop(commonTemplate.getData());
    }

    public void resolveLongClick(CommonTemplateMsg commonTemplate){//处理长按的逻辑
        if (commentTemplateListPop == null) {
            commentTemplateListPop = CommentTemplateListPopWindow.getInstance(mContext);
        }
        commentTemplateListPop.setPopTitle(commonTemplate.getPreTemplateName());
        commentTemplateListPop.initPopContentData(commonTemplate.getData());
        commentTemplateListPop.showPopFromBottom(rlContainer);
        commentTemplateListPop.OnSureBtnClickListener(new CommentTemplateListPopWindow.OnSureBtnClickListener() {
            @Override
            public void onSureBtnClick(List<TemplateDrugBean> list) {
                //颗粒或自营饮片及其他剂型
                if (drugType.equals(PublicParams.DOSAGEFORM_GRANULE_K) || (drugType.equals(PublicParams.DOSAGEFORM_SLICES_Y)
                        && isSelfSupport.equals(PublicParams.IS_SELFSUPPORT))) {
                    if (list != null && list.size() > 0) {
                        for (int i = 0; i < list.size(); i++) {
                            List<ReplaceDrugBean> replaceDrugs = list.get(i).getReplaceData();
                            if (replaceDrugs != null && replaceDrugs.size() > 0) {
                                showReplacePop = true;
                            }
                        }
                    }
                }

                commentTemplateListPop.dimissPop();
                isShowReplacePop(list);//详情显示完，执行替换药的逻辑
            }
        });
    }

    /**
     * 显示替换药弹框
     *
     * @param templateDrug
     */
    private void isShowReplacePop(List<TemplateDrugBean> templateDrug) {
        if (showReplacePop) {//显示替换药弹框
            mReplaceDrugPop.setPopTitle("替换药提示");
            mReplaceDrugPop.setPopContentData(templateDrug);
            mReplaceDrugPop.showPop();
            mReplaceDrugPop.setBottomViewVisible();
            mReplaceDrugPop.setBottomIcon(null);
            mReplaceDrugPop.setBottomText("确认");
            mReplaceDrugPop.setOnBottomViewClickListener(new ReplaceDrugPop.OnBottomViewClickListener() {
                @Override
                public void onBottomViewClick(List<TemplateDrugBean> data) {
                    // 回传数据到添加药材界面
                    EventBusUtils.post(data);
                    mReplaceDrugPop.dimissPop();
                    ActivityManager.getInstance().removeActivity(ChooseCommonPrescriptionActivity.class);
                   SearchClassicPrescriptionActivity.this.finish();

                }
            });
        } else {// 回传数据到添加药材界面
            EventBusUtils.post(templateDrug);
            ActivityManager.getInstance().removeActivity(ChooseCommonPrescriptionActivity.class);
           finish();
        }
    }


    @Override
    public void onClick(View v) {

    }
    @Override
    public void onReload() {//点击重新加载数据
        requestData(1);
    }


    @Override
    public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {//空实现，没有刷新功能

    }

    @Override
    public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {//空实现，没有加载更多的功能

    }


    @Override
    public void onDestroy() {
        if (commentTemplateListPop != null && commentTemplateListPop.isShowing()) {
            commentTemplateListPop.dimissPop();
        }
        cancelRequest(searchClassicCallBack);
        cancelRequest(detailCallBack);

        super.onDestroy();
    }



}
