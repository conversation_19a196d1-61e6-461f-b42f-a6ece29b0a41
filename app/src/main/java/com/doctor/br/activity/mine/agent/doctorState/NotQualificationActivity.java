package com.doctor.br.activity.mine.agent.doctorState;

import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.doctor.br.adapter.mine.AgentNotQualificationDoctorRecyclerAdapter;
import com.doctor.br.bean.NotQualificationDoctorBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：大夫状态-已注册未认证界面
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/7
 */

public class NotQualificationActivity extends ActionBarActivity {
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    //已注册未认证大夫列表
    private List<NotQualificationDoctorBean.DoctorMessagesBean> list;
    //列表适配器
    private AgentNotQualificationDoctorRecyclerAdapter adapter;
    //网络请求未认证大夫列表回调
    private RequestCallBack doctorListCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_not_qualification);
        initView();
        getNotQualificationDoctorList();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("已注册未认证");

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setHasFixedSize(true);

        list = new ArrayList<>();
        adapter = new AgentNotQualificationDoctorRecyclerAdapter(this, list);
        recyclerView.setAdapter(adapter);
    }

    /**
     * 网络请求获取未认证大夫列表
     */
    private void getNotQualificationDoctorList() {
        emptyView.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        doctorListCallBack = addHttpPostRequest(HttpUrlManager.NOT_QUALIFICATION, map, NotQualificationDoctorBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (recyclerView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.NOT_QUALIFICATION:
                if (result.isRequestSuccessed()) {
                    NotQualificationDoctorBean notQualificationDoctorBean = (NotQualificationDoctorBean) result.getBodyObject();
                    list.clear();
                    list.addAll(notQualificationDoctorBean.getDoctorMessages());
                    adapter.notifyDataSetChanged();
                    if (list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setVisibility(View.VISIBLE);
                        emptyView.setEmptyText("暂无大夫");
                        recyclerView.setVisibility(View.GONE);
                    }

                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    recyclerView.setVisibility(View.GONE);
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            getNotQualificationDoctorList();
                        }
                    });
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (doctorListCallBack != null) {
            doctorListCallBack.cancel();
        }
        super.onDestroy();
    }
}
