//package com.doctor.br.activity.mine;
//
//import android.media.MediaPlayer;
//import android.os.Bundle;
//import android.os.Environment;
//import android.support.annotation.NonNull;
//import android.support.v4.view.ViewCompat;
//import android.support.v7.widget.AppCompatSeekBar;
//import android.text.TextUtils;
//import android.view.View;
//import android.view.WindowManager;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.ProgressBar;
//import android.widget.RelativeLayout;
//import android.widget.SeekBar;
//import android.widget.TextView;
//import android.widget.VideoView;
//
//import com.doctor.yy.R;
//
//import org.newapp.ones.base.activity.NoActionBarActivity;
//import org.newapp.ones.base.base.PublicParams;
//import org.newapp.ones.base.utils.LogUtils;
//import org.newapp.ones.base.utils.NetUtils;
//import org.newapp.ones.base.utils.SharedPreferenceUtils;
//import org.newapp.ones.base.utils.toast.ToastUtils;
//import org.newapp.ones.base.widgets.ConfirmDialog;
//
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.io.InputStream;
//import java.util.concurrent.ScheduledThreadPoolExecutor;
//import java.util.concurrent.TimeUnit;
//
//import butterknife.BindView;
//import okhttp3.Call;
//import okhttp3.Callback;
//import okhttp3.OkHttpClient;
//import okhttp3.Request;
//import okhttp3.Response;
//import okio.ByteString;
//
///**
// * 类描述：VideoView测试界面
// * 创建人：ShiShaoPo
// * 创建时间：2018/4/23
// */
//
//public class VideoActivity extends NoActionBarActivity {
//    //上个界面传递过来的数据
//    public static final String VIDEO_URL = "url";
//    private String url;//视频地址
//
//    @BindView(R.id.container)
//    RelativeLayout container;
//    @BindView(R.id.videoView)
//    VideoView videoView;
//    @BindView(R.id.topLinear)
//    LinearLayout topLinear;
//    @BindView(R.id.backImg)
//    ImageView backImg;//返回按钮
//    @BindView(R.id.bottomLinear)
//    LinearLayout bottomLinear;
//    @BindView(R.id.centerPlayImg)
//    ImageView centerPlayImg;//屏幕中央的播放按钮
//    @BindView(R.id.playImg)
//    ImageView playImg;//播放按钮
//    @BindView(R.id.currentTime)
//    TextView currentTimeTv;//当前播放时间
//    @BindView(R.id.mSeekBar)
//    AppCompatSeekBar sb;//播放进度
//    @BindView(R.id.totalTime)
//    TextView totalTimeTv;//总时间
//    @BindView(R.id.progressBar)
//    ProgressBar progressBar;//缓冲等待框
//
//    private boolean isShowing;//mediaController是否显示
//    private boolean isPlaying = true;//由用户控制的是否处于播放状态
//    private int currentPosition = 0;//当前播放位置
//    private boolean isReady = true;//是否处于等待状态
//    private final int CACHE_SIZE = 2 * 1024 * 1024;//缓冲区大小
//    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;//周期性线程
//    private File file;//本地保存地址
//    private final Object lock = new Object();//锁
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setActionBarContentView(R.layout.activity_video);
//        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);//屏幕保持常亮
//        getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);//全屏显示
//
//        getIntentData(savedInstanceState);
//
//        try {
//            //如果有外部存储使用外部存储
//            file = new File(getExternalFilesDir(Environment.DIRECTORY_MOVIES).getAbsolutePath(), url.substring(url.lastIndexOf(File.separator) + 1));
//        } catch (Exception e) {
//            //没有的话使用内部存储
//            file = new File(getFilesDir().getAbsolutePath(), url.substring(url.lastIndexOf(File.separator) + 1, url.length()));
//        }
//
//        videoView.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
//            @Override
//            public void onPrepared(MediaPlayer mp) {
//                LogUtils.i(VideoActivity.class, "<<<<<videoViewOnprepared");
//                videoView.seekTo(currentPosition);
//                dismissLoading();
//            }
//        });
//        videoView.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
//            @Override
//            public void onCompletion(MediaPlayer mp) {
//                LogUtils.i(VideoActivity.class, "<<<<<<<<<<videoViewOnCompletion");
//                currentPosition = 0;
//                videoView.pause();
//                finish();
//            }
//        });
//        videoView.setOnErrorListener(new MediaPlayer.OnErrorListener() {
//            @Override
//            public boolean onError(MediaPlayer mp, int what, int extra) {
//                LogUtils.i(VideoActivity.class, "<<<<videoViewOnError,what=" + what + ",extra = " + extra);
//                isReady = true;
//                videoView.pause();
//                showLoading();
//                return true;
//            }
//        });
//
//        //用户拖动进度条监听
//        sb.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
//            @Override
//            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
//                if (fromUser) {
//                    if (videoView.isPlaying()) {
//                        videoView.pause();
//                    }
//                    currentPosition = videoView.getDuration() * progress / 100;
//                    videoView.seekTo(videoView.getDuration() * progress / 100);
//
//                    long totalSeconds = videoView.getDuration() / 1000;//总秒数
//                    long totalMinute = totalSeconds / 60;//转化获得分钟
//                    totalSeconds = totalSeconds % 60;//获取剩余的秒数
//                    totalTimeTv.setText(totalMinute + ":" + totalSeconds);
//
//                    long currentSeconds = currentPosition / 1000;//当前已播放秒数
//                    long currentMinute = currentSeconds / 60;//转化分钟
//                    currentSeconds = currentSeconds % 60;//剩余秒数
//                    currentTimeTv.setText(currentMinute + ":" + currentSeconds);
//                    showController();
//                }
//            }
//
//            @Override
//            public void onStartTrackingTouch(SeekBar seekBar) {
//
//            }
//
//            @Override
//            public void onStopTrackingTouch(SeekBar seekBar) {
//                if (isPlaying) {
//                    videoView.start();
//                }
//            }
//        });
//
//        scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(1);
//        scheduledThreadPoolExecutor.scheduleAtFixedRate(new Runnable() {
//            @Override
//            public void run() {
//                if (videoView != null && videoView.isPlaying()) {
//                    currentPosition = videoView.getCurrentPosition();//当前播放时间，单位：毫秒
//                    int totalPosition = videoView.getDuration();//总播放时间，单位：毫秒
//                    totalPosition = (totalPosition <= 0 ? 1 : totalPosition);
//                    sb.setProgress(currentPosition * 100 / totalPosition);
//
//                    long totalSeconds = videoView.getDuration() / 1000;//总秒数
//                    final long totalMinute = totalSeconds / 60;//转化获得分钟
//                    totalSeconds = totalSeconds % 60;//获取剩余的秒数
//
//
//                    long currentSeconds = currentPosition / 1000;//当前已播放秒数
//                    final long currentMinute = currentSeconds / 60;//转化分钟
//                    currentSeconds = currentSeconds % 60;//剩余秒数
//
//                    final String finalTotalSeconds = (totalSeconds < 10) ? ("0" + totalSeconds) : (totalSeconds + "");
//                    final String finalCurrentSeconds = (currentSeconds < 10) ? ("0" + currentSeconds) : (currentSeconds + "");
//                    runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            if (totalTimeTv == null || currentTimeTv == null) {
//                                return;
//                            }
//                            totalTimeTv.setText(totalMinute + ":" + finalTotalSeconds);
//                            currentTimeTv.setText(currentMinute + ":" + finalCurrentSeconds);
//                        }
//                    });
//                }
//            }
//        }, 0, 500, TimeUnit.MILLISECONDS);
//
////        container.setActivity(this);
//        container.setOnClickListener(this);
//        backImg.setOnClickListener(this);
//        playImg.setOnClickListener(this);
//        centerPlayImg.setOnClickListener(this);
//
//        dialog = ConfirmDialog.getInstance(VideoActivity.this)
//                .setDialogContent("播放文件过大，建议在WIFI环境下观看。")
//                .setPositiveText("继续观看")
//                .setNavigationText("下次观看")
//                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
//                    @Override
//                    public void onNavigationBtnClicked(View view) {
//                        dialog.dismiss();
//                        VideoActivity.this.finish();
//
//                    }
//
//                    @Override
//                    public void onPositiveBtnClicked(View view) {
//                        synchronized (lock) {
//                            lock.notify();
//                        }
//                        dialog.dismiss();
//                    }
//                });
//
//        String videoLength = SharedPreferenceUtils.getString(this, PublicParams.VIDEO_LENGTH);
//        if (!TextUtils.isEmpty(videoLength) &&
//                file.exists() &&
//                file.isFile() &&
//                file.getName().equals(url.substring(url.lastIndexOf(File.separator) + 1)) &&
//                videoLength.equals(file.length() + "")) {
//            //如果文件存在，并且名字相同，大小和视频大小一样的情况下，直接开始本地播放
//            sb.setSecondaryProgress(100);
//            videoView.setVideoPath(file.getAbsolutePath());
//            if (isPlaying) {
//                videoView.start();
//            }
//            dismissLoading();
//            showController();
//        } else {
//            downLoadFile();
//        }
//    }
//
//    private void getIntentData(Bundle bundle) {
//        if (bundle == null) {
//            url = getIntent().getStringExtra(VIDEO_URL);
//        } else {
//            url = bundle.getString(VIDEO_URL);
//        }
//        if (TextUtils.isEmpty(url)) {
//            ToastUtils.showShortMsg(this, "数据错误");
//            finish();
//        }
//    }
//
//    @Override
//    protected void onSaveInstanceState(Bundle outState) {
//        super.onSaveInstanceState(outState);
//        outState.putString(VIDEO_URL, url);
//    }
//
//    @Override
//    public void onClick(View view) {
//        switch (view.getId()) {
//            case R.id.container:
//                if (!isShowing) {
//                    showController();
//                } else {
//                    hideController();
//                }
//                LogUtils.i(VideoActivity.class, "<<<<点击container");
//                break;
//            case R.id.backImg:
//                finish();
//                break;
//            case R.id.playImg:
//            case R.id.centerPlayImg:
//                LogUtils.i(VideoActivity.class, "<<<<点击img");
//                if (centerPlayImg.getAlpha() < 0.5) {
//                    //在透明情况下拦截点击事件，相当于点击外边框
//                    onClick(container);
//                    return;
//                }
//
//                if (isPlaying) {
//                    if (videoView != null && videoView.isPlaying()) {
//                        videoView.pause();
//                    }
//                    playImg.setImageResource(R.drawable.video_play);
//                    centerPlayImg.setImageResource(R.drawable.video_play);
//
//                    isPlaying = false;
//                } else {
//                    if (videoView != null) {
//                        videoView.start();
//                    }
//                    playImg.setImageResource(R.drawable.video_pause);
//                    centerPlayImg.setImageResource(R.drawable.video_pause);
//
//                    isPlaying = true;
//                }
//                showController();
//                break;
//            default:
//                break;
//        }
//
//    }
//
//
//    /**
//     * 显示controller，默认在播放状态下3秒后隐藏controller
//     */
//    private void showController() {
//        if (topLinear == null || bottomLinear == null || centerPlayImg == null) {
//            return;
//        }
//        ViewCompat.animate(topLinear)
//                .translationY(0)
//                .start();
//        ViewCompat.animate(bottomLinear)
//                .translationY(0)
//                .start();
//        ViewCompat.animate(centerPlayImg)
//                .alpha(1)
//                .start();
////        centerPlayImg.setVisibility(View.VISIBLE);
//        isShowing = true;
//
//        bottomLinear.removeCallbacks(hideRunnable);
//        bottomLinear.postDelayed(hideRunnable, 3000);
//    }
//
//    private Runnable hideRunnable = new Runnable() {
//        @Override
//        public void run() {
//            if (isPlaying) {
//                hideController();
//            }
//        }
//    };
//
//    /**
//     * 隐藏controller
//     */
//    private void hideController() {
//        if (topLinear == null || bottomLinear == null || centerPlayImg == null) {
//            return;
//        }
//        ViewCompat.animate(topLinear)
//                .translationY(-topLinear.getHeight())
//                .start();
//        ViewCompat.animate(bottomLinear)
//                .translationY(bottomLinear.getHeight())
//                .start();
//        ViewCompat.animate(centerPlayImg)
//                .alpha(0)
//                .start();
////        centerPlayImg.setVisibility(View.GONE);
//        isShowing = false;
//    }
//
//    /**
//     * 显示正在加载视频的等待框
//     */
//    private void showLoading() {
//        if (progressBar == null || progressBar.getVisibility() == View.VISIBLE) {
//            return;
//        }
//        progressBar.setVisibility(View.VISIBLE);
//    }
//
//    /**
//     * 隐藏加载视频等待狂
//     */
//    private void dismissLoading() {
//        if (progressBar == null || progressBar.getVisibility() == View.GONE) {
//            return;
//        }
//        progressBar.setVisibility(View.GONE);
//    }
//
//    private ConfirmDialog dialog;//网络环境提示框
//
//    /**
//     * 下载文件
//     */
//    private void downLoadFile() {
//        mLoadingDialog.show();
//        OkHttpClient okHttpClient = new OkHttpClient().newBuilder().connectTimeout(15, TimeUnit.SECONDS)
//                .writeTimeout(20, TimeUnit.SECONDS)
//                .readTimeout(20, TimeUnit.SECONDS)
//                .build();
//        Request request = new Request.Builder().url(url).build();
//        Call call = okHttpClient.newCall(request);
//        call.enqueue(new Callback() {
//            @Override
//            public void onFailure(@NonNull Call call, @NonNull IOException e) {
//                runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
//                            mLoadingDialog.dismiss();
//                        }
//                    }
//                });
//
//                LogUtils.i(VideoActivity.class, e.toString());
////                RequestErrorToast.showError(mContext,taskId, result.getCode(), result.getErrorMsg());
//            }
//
//            @Override
//            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
//                runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
//                            mLoadingDialog.dismiss();
//                        }
//                    }
//                });
//                if (!response.isSuccessful()) {
//                    return;
//                }
//                if (response.body() == null) {
//                    return;
//                }
//                runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        showLoading();
//                    }
//                });
//                InputStream is = null;
//                byte[] buf = new byte[2 * 1024];
//                int len = 0;
//                FileOutputStream fos = null;
//                try {
//                    final long total = response.body().contentLength();
//                    SharedPreferenceUtils.putString(VideoActivity.this, PublicParams.VIDEO_LENGTH, "" + total);
//                    //如果相同名字的存在并且是文件夹的话，直接删除
//                    if (file.isDirectory()) {
//                        for (File file : file.listFiles()) {
//                            file.delete();
//                        }
//                        file.delete();
//                    }
//
//                    //本地没有视频，判断网络情况是否需要继续观看
//                    if ("0".equals(NetUtils.isWifi(VideoActivity.this))) {
//                        runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                if (dialog == null) {
//                                    return;
//                                }
//                                dialog.show();
//                            }
//                        });
//                        synchronized (lock) {
//                            lock.wait();
//                        }
//                    }
//                    //mdat:36 moov:25929161 ftyp:0
//                    //判断编码方式,MP4有两种编码方式，一种是尾部在视频内容前的，可以直接缓冲播放
//                    //一种是尾部在视频内容结尾，需要先解码尾部信息才可以缓冲播放
////                    final long ftypIndex = response.body().source().indexOf(ByteString.decodeHex("66747970")) - 4;//头部信息
////                    final long mDatIndex = response.body().source().indexOf(ByteString.decodeHex("6d646174")) - 4;//视频内容
////                    final long moovIndex = response.body().source().indexOf(ByteString.decodeHex("6d6f6f76")) - 4;//尾部信息
////                    LogUtils.i(VideoActivity.class, "mDatIndex=" + mDatIndex + ",ftypIndex=" + ftypIndex + ",moovIndex=" + moovIndex);
////                    if (moovIndex > mDatIndex) {
//                    //尾部信息在视频内容后面，必须先加载尾部信息
////                    is = response.body().byteStream();
////                    fos = new FileOutputStream(file);
////                    byte[] buffer = new byte[8];
////                    int readLength;
////                    is.mark(9);
////                    while ((readLength = is.read(buffer)) != -1) {
////                        //1,-117,-91,-91,109,100,97,116
////                        if (buffer[0] == 109 &&
////                                buffer[1] == 100 &&
////                                buffer[2] == 97 &&
////                                buffer[3] == 116) {
////                            //恢复到标记地点获取视频长度
////                            is.reset();
////                            is.read(buffer);
////                            int videoLength = (buffer[4] << 24) | (buffer[5] << 16) | (buffer[6] << 8) | buffer[7];
////                            LogUtils.i(VideoActivity.class, "videoLength=" + videoLength);
////                            readLength = is.read(buffer);
////                        }
////                        fos.write(buffer, 0, readLength);
////                        is.mark(9);
////                    }
////
////
//                    long current = 0;
//                    is = response.body().byteStream();
//                    fos = new FileOutputStream(file);
//                    final long[] lastKaTime = {0L};//第一次缓冲标志
//                    while ((len = is.read(buf)) != -1) {
//                        current += len;
//                        fos.write(buf, 0, len);
//                        final long finalCurrent = current;
//                        if (sb == null) {
//                            return;
//                        }
//                        runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                if (sb == null) {
//                                    return;
//                                }
//
//                                sb.setSecondaryProgress((int) (finalCurrent * 100 / total));//progressbar可以异步更新
//                                //如果是第一次缓冲，判断是否达到缓冲容量
//                                if (lastKaTime[0] == 0 && isReady && finalCurrent - lastKaTime[0] > CACHE_SIZE) {
//                                    lastKaTime[0] = -1L;
//                                    isReady = false;
//                                    videoView.setVideoPath(file.getAbsolutePath());
//                                    if (isPlaying) {
//                                        videoView.start();
//                                    }
//                                    showController();
//                                    dismissLoading();
//                                } else if (isReady && sb.getSecondaryProgress() - sb.getProgress() > 10) {
//                                    //如果不是第一次缓冲，需要判断缓冲度超过播放进度10%开始播放
//                                    isReady = false;
//                                    videoView.setVideoPath(file.getAbsolutePath());
//                                    if (isPlaying) {
//                                        videoView.start();
//                                    }
//                                    dismissLoading();
//                                }
//
//                            }
//                        });
//
//                    }
//                    fos.flush();
//                    runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            if (sb == null) {
//                                return;
//                            }
//                            ToastUtils.showShortMsg(VideoActivity.this, "开始0流量本地播放");
//                        }
//                    });
//                } catch (IOException e) {
//                    LogUtils.i(VideoActivity.class, ">>>>>>>" + e.toString());
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                } finally {
//                    try {
//                        response.close();
//                        if (is != null) {
//                            is.close();
//                        }
//                        if (fos != null) {
//                            fos.close();
//                        }
//                    } catch (IOException e) {
//                        LogUtils.i(VideoActivity.class, e.toString());
//                    }
//                }
//            }
//        });
//    }
//
//    @Override
//    protected void onDestroy() {
//        scheduledThreadPoolExecutor.shutdownNow();
//        videoView.stopPlayback();
//        super.onDestroy();
//    }
//}
