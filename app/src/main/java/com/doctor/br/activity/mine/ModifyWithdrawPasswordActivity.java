package com.doctor.br.activity.mine;

import android.os.Bundle;
import android.text.TextUtils;
import com.google.android.material.tabs.TabLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.doctor.br.bean.event.ModifyWithdrawPasswordRefreshStateEvent;
import com.doctor.br.fragment.mine.FirstSetWithdrawPasswordFragment;
import com.doctor.br.fragment.mine.PasswordModifyWithdrawPasswordFragment;
import com.doctor.br.fragment.mine.SmsModifyWithdrawPasswordFragment;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.network.RequestCallBack;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：修改提现密码
 * 创建人：Assistant
 * 创建时间：2024/12/15
 */

public class ModifyWithdrawPasswordActivity extends ActionBarActivity {
    //界面下的控件
    @BindView(R.id.tab_layout)
    TabLayout tabLayout;
    @BindView(R.id.view_pager)
    ViewPager viewPager;

    private boolean isPasswordSet = false; // 是否已设置过提现密码
    private RequestCallBack checkPasswordStatusCallBack; // 检查密码状态的请求回调
    private String checkPasswordResponseStr; // 保存原始响应字符串

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_modify_withdraw_password);
        // 先设置一个默认标题，等检查完密码状态后再更新
        getBaseActionBar().setActionBarTitle("提现密码");



        // 首先检查是否设置过提现密码
       checkWithdrawPasswordStatus();
    }

    /**
     * 检查是否设置过提现密码
     */
    private void checkWithdrawPasswordStatus() {
//        Map<String, String> map = new HashMap<>();
//        map.put("userId", SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID, ""));
        Map<String, String> map = new HashMap<>();
        map.put("userId", SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID));

        LogUtils.i(ModifyWithdrawPasswordActivity.class, "检查是否设置过提现密码===");
        checkPasswordStatusCallBack = addHttpPostRequest(HttpUrlManager.CHECK_WITHDRAW_PASSWORD_STATUS, map, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, boolean successed, String responseStr) {
        super.onRequestFinished(taskId, successed, responseStr);
        
        if (HttpUrlManager.CHECK_WITHDRAW_PASSWORD_STATUS.equals(taskId)) {
            // 保存原始响应字符串，供另一个回调方法使用
            checkPasswordResponseStr = responseStr;
            LogUtils.i(ModifyWithdrawPasswordActivity.class, "收到原始响应: " + responseStr);
        }
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        
        if (HttpUrlManager.CHECK_WITHDRAW_PASSWORD_STATUS.equals(taskId)) {
            // 检查密码状态响应
            if (result.isRequestSuccessed()) {
                try {
                    // 直接从原始响应字符串中解析 hasCashPwd 字段
                    // 因为 result.getBodyObject() 在解析时会丢失 hasCashPwd 字段
                    if (checkPasswordResponseStr != null) {
                        org.json.JSONObject jsonObject = new org.json.JSONObject(checkPasswordResponseStr);
                        isPasswordSet = jsonObject.optBoolean("hasCashPwd", false);
                        LogUtils.i(ModifyWithdrawPasswordActivity.class, "从原始响应解析 hasCashPwd: " + isPasswordSet);
                        LogUtils.i(ModifyWithdrawPasswordActivity.class, "原始响应内容: " + checkPasswordResponseStr);
                    } else {
                        LogUtils.e(ModifyWithdrawPasswordActivity.class, "原始响应字符串为空，使用默认值");
                        isPasswordSet = true;
                    }
                    initView();
                } catch (Exception e) {
                    e.printStackTrace();
                    LogUtils.e(ModifyWithdrawPasswordActivity.class, "解析响应数据失败: " + e.getMessage());
                    ToastUtils.showShortMsg(this, "解析响应数据失败");
                    // 解析失败时，默认认为已设置过密码，显示修改界面
                    isPasswordSet = true;
                    initView();
                }
            } else {
                RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                finish();
            }
        }
    }

    private void initView() {
        LogUtils.i(ModifyWithdrawPasswordActivity.class, "initView: isPasswordSet = " + isPasswordSet);
        if (isPasswordSet) {
            // 已设置过密码，显示"修改提现密码"标题和密码方式、验证码方式两个选项卡
            LogUtils.i(ModifyWithdrawPasswordActivity.class, "显示修改提现密码界面，包含两个选项卡");
            getBaseActionBar().setActionBarTitle("修改提现密码");
            tabLayout.setVisibility(TabLayout.VISIBLE); // 确保TabLayout可见
            tabLayout.setupWithViewPager(viewPager);
            viewPager.setAdapter(new FragmentPagerAdapter(getSupportFragmentManager()) {
                @Override
                public Fragment getItem(int position) {
                    if (position == 0) {
                        return new PasswordModifyWithdrawPasswordFragment();
                    }
                    return new SmsModifyWithdrawPasswordFragment();
                }

                @Override
                public int getCount() {
                    return 2;
                }

                @Override
                public CharSequence getPageTitle(int position) {
                    if (position == 0) {
                        return "密码方式";
                    }
                    return "验证码方式";
                }
            });
            
            // 添加页面切换监听器，实现跟更换手机号界面一样的切换方式
            viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                }

                @Override
                public void onPageSelected(int position) {
                    // 当切换页面时，刷新对应的Fragment状态
                    refreshFragment(position);
                }

                @Override
                public void onPageScrollStateChanged(int state) {
                }
            });
        } else {
            // 未设置过密码，显示"设置提现密码"标题，隐藏选项卡，直接显示设置界面
            LogUtils.i(ModifyWithdrawPasswordActivity.class, "显示设置提现密码界面，隐藏选项卡");
            getBaseActionBar().setActionBarTitle("设置提现密码");
            tabLayout.setVisibility(TabLayout.GONE);
            viewPager.setAdapter(new FragmentPagerAdapter(getSupportFragmentManager()) {
                @Override
                public Fragment getItem(int position) {
                    return new FirstSetWithdrawPasswordFragment();
                }

                @Override
                public int getCount() {
                    return 1;
                }

                @Override
                public CharSequence getPageTitle(int position) {
                    return "设置提现密码";
                }
            });
        }
    }

    /**
     * 重置 "密码方式修改提现密码界面" 和 "验证码方式修改提现密码界面"
     * 实现跟更换手机号界面一样的切换方式
     */
    private void refreshFragment(int position) {
        if (0 == position) {
            // 切换到密码方式时，刷新密码方式Fragment
            EventBusUtils.post(new ModifyWithdrawPasswordRefreshStateEvent(true, false));
        } else if (1 == position) {
            // 切换到验证码方式时，刷新验证码方式Fragment
            EventBusUtils.post(new ModifyWithdrawPasswordRefreshStateEvent(false, true));
        }
    }

    @Override
    protected void onDestroy() {
        if (checkPasswordStatusCallBack != null) {
            checkPasswordStatusCallBack.cancel();
        }
        super.onDestroy();
    }
}