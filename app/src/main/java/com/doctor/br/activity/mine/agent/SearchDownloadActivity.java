package com.doctor.br.activity.mine.agent;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.provider.MediaStore;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import android.text.Editable;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;

import com.doctor.br.activity.BaseRefreshActivity;
import com.doctor.br.adapter.mine.DownloadRecyclerAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.AgentDownloadCardUrlBean;
import com.doctor.br.bean.TableListBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.yy.R;

import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.bingoogolapple.androidcommon.adapter.BGAOnItemChildClickListener;
import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;

/**
 * 搜索摆台的界面
 */
public class SearchDownloadActivity extends BaseRefreshActivity<TableListBean.DataBean> implements BGAOnItemChildClickListener {
    public static final int DOWNLOAD_SUCCESS = 1;
    public static final int DOWNLOAD_FAIL = 2;

    public static final int TYPE_TABLE = 1;//摆台下载，2二维码下载 DataBean中的type字段
    public static final int TYPE_QR = 2;//摆台下载，2二维码下载 DataBean中的type字段

    public static final int CODE_PERMISSION = 11;
    @SuppressLint("HandlerLeak")
    private Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                mLoadingDialog.dismiss();
            }
            switch (msg.what) {
                case DOWNLOAD_SUCCESS:
                    ToastUtils.showShortMsg(AppContext.getContext(), "下载成功");
                    break;
                case DOWNLOAD_FAIL:
                    ToastUtils.showShortMsg(AppContext.getContext(), "下载失败");
                    break;
                default:
                    break;
            }
        }
    };
    //    private AsyncTask<TableListBean.DataBean, String, Bitmap> downloadTask;
    private View clicChildView;//当前点击的view
    private TableListBean.DataBean clickDataBean;
    private RequestCallBack tableListCallBack;

    /**
     * 初始化
     */
    @Override
    protected void init() {
        super.init();
        setActionBarType(ActionBarView.ACTIONBAR_SEARCH);
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarSearchInputHint("请输入姓名或手机号");

        //设置EmptyView
        emptyView.setEmptyText("暂无数据");

        setRefreshEnable(false);//不需要上啦刷新
        mRefreshLayout.setCanLoadMore(false);//不需要加载更多
        mRefreshAdapter.setOnItemChildClickListener(this);//设置条目点击事件

    }


    public void requestData(int pageNum) {//网络请求 addHttp......
        if (TextUtils.isEmpty(getActionbarSearchInputText().trim())) {
            ToastUtils.showShortMsg(this, "请输入姓名或手机号");
            return;
        }
        currentPage = pageNum;
        String content = getActionbarSearchInputText().trim();
        Map<String, String> params = RequestParams.getTableListParams(content, currentPage, getPageSize());
        tableListCallBack = addHttpPostRequest(HttpUrlManager.TABLE_LIST, params, TableListBean.class, this);

    }

    @Override
    public BGARecyclerViewAdapter<TableListBean.DataBean> getAdapter() {//返回adapter
        return new DownloadRecyclerAdapter(mRvData);
    }

    @Override
    protected void setListener() {//设置监听,在init()之后执行
        super.setListener();

        setOnSearchInputEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {//点击搜索
                    requestData(1);
                    return true;
                }
                return false;
            }
        });

        setActionBarSearchInputListener(new TextWatcher() {//添加数据框的实时监听
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String content = getActionbarSearchInputText().trim();
                if (TextUtils.isEmpty(content)) {//清除之前的列表数据
                    if (mRefreshAdapter.getData() != null && mRefreshAdapter.getData().size() > 0)
                        mRefreshAdapter.clear();
                }
            }
        });
    }

    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        requestData(1);
    }

    @Override
    public void onItemChildClick(ViewGroup parent, View childView, int position) {//条目点击事件
        this.clicChildView = childView;
        clickDataBean = mRefreshAdapter.getItem(position);
        if (clickDataBean == null) {
            ToastUtils.showShortMsg(this, "下载失败");
            return;
        }
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {//有权限
            downloadClick(childView, clickDataBean);//下载

        } else {
            //申请权限
            requestPermissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, CODE_PERMISSION,"必然中医需要使用存储权限用于保存文件，是否同意使用？");

        }

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == CODE_PERMISSION && grantResults != null && grantResults[0] == PackageManager.PERMISSION_GRANTED) {//获取到权限
            if (clickDataBean != null && clicChildView != null) {
                downloadClick(clicChildView, clickDataBean);
            }
        }
    }

    /**
     * 开始下载
     *
     * @param childView
     * @param dataBean
     */
    private void downloadClick(View childView, TableListBean.DataBean dataBean) {
        switch (childView.getId()) {
            case R.id.btn_table://摆台下载
                if (TextUtils.isEmpty(dataBean.getUserCardImgUrl())) {
                    ToastUtils.showShortMsg(this, "下载失败");
                    return;
                }
                dataBean.setType(TYPE_TABLE);
                executorService.execute(new DownloadRunnable(dataBean.getUserCardImgUrl(), TYPE_TABLE + "", dataBean.getUserName()));
//                downloadTask = new TableAsyncTask().execute(dataBean.getUserCardImgUrl(), TYPE_TABLE + "", dataBean.getUserName());
                break;
            case R.id.btn_code://二维码下载
                if (TextUtils.isEmpty(dataBean.getUserWeiImgUrl())) {
                    ToastUtils.showShortMsg(this, "下载失败");
                    return;
                }
                dataBean.setType(TYPE_QR);
//                downloadTask = new TableAsyncTask().execute(dataBean.getUserWeiImgUrl(), TYPE_QR + "", dataBean.getUserName());
                executorService.execute(new DownloadRunnable(dataBean.getUserWeiImgUrl(), TYPE_QR + "", dataBean.getUserName()));
                break;
            case R.id.btn_card://名片下载
                requestCardUrl(dataBean.getUserId());
                break;
            default:
                break;
        }
    }

    private RequestCallBack cardUrlCallBack;//网络请求名片地址回调

    /**
     * 网络请求获取名片地址
     *
     * @param doctorId 医生id
     */
    private void requestCardUrl(String doctorId) {
        Map<String, String> map = new HashMap<>();
        map.put("doctorId", doctorId);
        cardUrlCallBack = addHttpPostRequest(HttpUrlManager.CARD_URL, map, AgentDownloadCardUrlBean.class, this);
    }

    public static final int TYPE_CARD = 3;//名片下载 DataBean中的type字段
    //下载图片线程池
    @SuppressWarnings("AlibabaThreadPoolCreation")
    private ExecutorService executorService = Executors.newCachedThreadPool();

    private class DownloadRunnable implements Runnable {
        private String url;//要下载的图片url
        private String type;//图片类型
        private String name;//医生姓名

        DownloadRunnable(String url, String type, String name) {
            this.url = url;
            this.type = type;
            this.name = name;
        }

        @Override
        public void run() {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (mLoadingDialog != null && !mLoadingDialog.isShowing()) {
                        mLoadingDialog.show();
                    }
                }
            });
            final Message msg = Message.obtain();
            if (TextUtils.isEmpty(url) ||
                    TextUtils.isEmpty(type)) {
                msg.what = DOWNLOAD_FAIL;
                handler.sendMessage(msg);
                return;
            }
            Bitmap bitmap = null;
            if ((TYPE_TABLE + "").equals(type)) {
                //摆台下载
                bitmap = createBitmaByUrl(url);
            } else if ((TYPE_QR + "").equals(type)) {
                //二维码下载
                try {
                    bitmap = createQrBitmap(url, name);
                } catch (Exception e) {
                    bitmap = null;
                }
            } else if ((TYPE_CARD + "").equals(type)) {
                bitmap = createBitmaByUrl(url);
            }
            if (bitmap != null) {
                try {
                    // 其次把文件插入到系统图库
                    String uri = MediaStore.Images.Media.insertImage(getContentResolver(),
                            bitmap, url.substring(url.lastIndexOf("/") + 1), null);
                    LogUtils.e(DownloadActivity.class, "保存图片返回值:" + uri);
                    //给系统相册发送通知
                    Intent intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                    intent.setData(Uri.parse(uri));
                    sendBroadcast(intent);
                    //toast  "下载成功"
                    msg.what = DOWNLOAD_SUCCESS;
                    handler.sendMessage(msg);
                } catch (Exception e) {
                    //toast  "下载失败"
                    msg.what = DOWNLOAD_FAIL;
                    handler.sendMessage(msg);
                }
                bitmap.recycle();
            } else {//bitmap =null
                //toast  "下载失败"
                msg.what = DOWNLOAD_FAIL;
                handler.sendMessage(msg);
            }
        }
    }

    @Override
    public void onClick(View v) {

    }


    @Override
    public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {//不要刷新

    }

    @Override
    public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {//不要加载更多

    }

    @Override
    public void onReload() {
        requestData(1);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        switch (taskId) {
            case HttpUrlManager.TABLE_LIST://摆台列表
                super.onRequestFinished(taskId, result);
                if (result.isRequestSuccessed()) {//网络请求成功
                    TableListBean tableListBean = (TableListBean) result.getBodyObject();
                    pageCount = tableListBean.getTotalPage();
                    onRequestListSuccess(taskId, tableListBean.getData());

                } else {//网络请求失败
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                    onRequestListError();
                }
                break;
            case HttpUrlManager.CARD_URL://名片地址
                if (result.isRequestSuccessed()) {//网络请求成功
                    AgentDownloadCardUrlBean cardUrlBean = (AgentDownloadCardUrlBean) result.getBodyObject();
//                    downloadTask = new TableAsyncTask().execute(cardUrlBean.getBusinessCardFoward(), TYPE_CARD + "", cardUrlBean.getDoctorName());
//                    downloadTask2 = new TableAsyncTask().execute(cardUrlBean.getBusinessCardBack(), TYPE_CARD + "", cardUrlBean.getDoctorName());
                    executorService.execute(new DownloadRunnable(cardUrlBean.getBusinessCardFoward(), TYPE_CARD + "", cardUrlBean.getDoctorName()));
                    executorService.execute(new DownloadRunnable(cardUrlBean.getBusinessCardBack(), TYPE_CARD + "", cardUrlBean.getDoctorName()));
                } else {//网络请求失败
                    super.onRequestFinished(taskId, result);
                    ToastUtils.showShortMsg(this, "下载失败");
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onRequestFinished(String taskId, boolean successed, String responseStr) {
        //防止loading框消失
    }

    /**
     * 根据图片url,生成bitmap
     *
     * @param url
     * @return
     */
    public Bitmap createBitmaByUrl(String url) {
        URL imageurl = null;
        Bitmap bitmap = null;

        try {
            imageurl = new URL(url);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        try {
            HttpURLConnection conn = (HttpURLConnection) imageurl.openConnection();
            conn.setDoInput(true);
            conn.connect();
            InputStream is = conn.getInputStream();
            bitmap = BitmapFactory.decodeStream(is);
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return bitmap;
    }

    /**
     * 绘制需要的二维码的样式
     *
     * @param url
     * @return
     */
    public Bitmap createQrBitmap(String url, String userName) throws Exception {
        Bitmap bitmap = createBitmaByUrl(url);//获取二维码bitmap

        Bitmap backBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.back_download).copy(Bitmap.Config.ARGB_8888, true);//背景图片
        LogUtils.log(backBitmap.getWidth() + "....." + backBitmap.getHeight());
        Canvas canvas = new Canvas(backBitmap);//以背景图片为图层
        float srcTop = AppContext.getContext().getResources().getDisplayMetrics().density * 35;
        float srcLeft = (backBitmap.getWidth() - bitmap.getWidth()) * 1.0f / 2;
        canvas.drawBitmap(bitmap, srcLeft, srcTop, null);//绘制二维码

        TextPaint textPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        textPaint.setStyle(Paint.Style.FILL);
        textPaint.setTextSize(30 * AppContext.getContext().getResources().getDisplayMetrics().scaledDensity);
        textPaint.setColor(AppContext.getContext().getResources().getColor(R.color.br_color_theme_text));
        textPaint.setTextAlign(Paint.Align.CENTER);
        Paint.FontMetrics fontMetrics = textPaint.getFontMetrics();
        float baseline = (backBitmap.getHeight() + srcTop + bitmap.getHeight() - fontMetrics.bottom + fontMetrics.top) / 2 - fontMetrics.top;

        canvas.drawText(userName, backBitmap.getWidth() * 1.0f / 2, baseline, textPaint);//绘制文字

        return backBitmap;
    }

    @Override
    protected void onDestroy() {
        cancelRequest(tableListCallBack, cardUrlCallBack);
        executorService.shutdownNow();
        handler.removeCallbacksAndMessages(null);
//        if (downloadTask != null && !downloadTask.isCancelled()) {
//            downloadTask.cancel(true);
//        }
//        downloadTask = null;
        super.onDestroy();

    }
}