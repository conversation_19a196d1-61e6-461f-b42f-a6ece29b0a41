package com.doctor.br.activity.medical;

import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.doctor.br.activity.ShowImageActivity;
import com.doctor.br.activity.UserProtocolActivity;
import com.doctor.br.activity.mine.QualificationActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.PatientMsgBean;
import com.doctor.br.bean.PictureMedicineBean;
import com.doctor.br.bean.UploadFileBean;
import com.doctor.br.bean.medical.DosageFormBean;
import com.doctor.br.db.entity.ShowProtolItem;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.FileIOUtils;
import com.doctor.br.utils.SelectImgUtils;
import com.doctor.br.view.DosageFormsPop;
import com.doctor.br.view.SelectPhotoDialog;
import com.doctor.yy.R;
import com.google.common.collect.Lists;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.HttpRequestTask;
import org.newapp.ones.base.network.OnRequestListener;
import org.newapp.ones.base.utils.ImageUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import me.grantland.widget.AutofitTextView;

/**
 * 类描述：拍照开方主页
 * 创建人：ShiShaoPo
 * 创建时间：2018/7/10
 */

public class PictureMedicineActivity extends ActionBarActivity {
    //界面下的控件
    @BindView(R.id.send_btn)
    com.doctor.br.view.NoDoubleClickBtn sendBtn;
    @BindView(R.id.supplier_tv)
    AutofitTextView supplierTv;
    @BindView(R.id.select_supplier_linear)
    ConstraintLayout selectSupplierLinear;
    @BindView(R.id.photo_tv)
    TextView photoTv;
    @BindView(R.id.demo_img)
    ImageView demoImg;
    @BindView(R.id.camera_img)
    ImageView cameraImg;
    @BindView(R.id.camera_tv)
    TextView cameraTv;
    @BindView(R.id.del_pic_tv)
    TextView delPicTv;

    //判断是否已经拍方
    private String imgUrl;
    //选择剂型的弹窗
    private DosageFormsPop supplierPop;
    //选择剂型、供货商列表
    private List<DosageFormBean.FormList> supplierList;
    //删除图片对话框
    private ConfirmDialog delDialog;
    //当前选择的剂型、供货商id
    private String drugForm, supplierId;
    private String drugType;//药材类型
    //患者姓名
    private String patientName;
    //患者性别
    private String patientSex;
    //患者id
    private String patientId;
    //患者年龄
    private String patientAge;
    //是否怀孕
    private String isPregnant;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_picture_medicine);
        initView();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("拍照开方");
        setActionBarRightBtnText("协议");

        sendBtn.setOnClickListener(this);
        selectSupplierLinear.setOnClickListener(this);
        photoTv.setOnClickListener(this);
        demoImg.setOnClickListener(this);
        cameraImg.setOnClickListener(this);
        cameraTv.setOnClickListener(this);
        delPicTv.setOnClickListener(this);

        patientId = SharedPreferenceUtils.getString(this, PublicParams.PATIENT_USRE_ID);

        String userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        ShowProtolItem showProtolItem = new ShowProtolItem(userId,"1");
        AppContext.getInstances().getDaoSession().getShowProtolItemDao().insertOrReplace(showProtolItem);

        getPatients();
        getDosageFormList();
    }

    @Override
    public void onRightBtnClick(View view) {
        Intent intent = new Intent(this, UserProtocolActivity.class);
        intent.putExtra(UserProtocolActivity.SHOW_OR_HIDE, false);
        startActivity(intent);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.send_btn://发送电子方
                if (supplierId == null) {
                    ToastUtils.showShortMsg(this, "请选择剂型、供货商");
                    return;
                }
                if (imgUrl == null) {
                    ToastUtils.showShortMsg(this, "请上传药方照片");
                    return;
                }
                if (patientSex == null) {
                    getPatients();
                    return;
                }
                sendMedicine(drugForm, supplierId);
                break;
            case R.id.select_supplier_linear://选择供货商、剂型
                if (supplierList == null || supplierList.size() == 0) {
                    getDosageFormList();
                    return;
                }
                showDosageFormsPop();
                break;
            case R.id.photo_tv://跳转相册
                SelectImgUtils.gotoPhoto(this);
                break;
            case R.id.demo_img://拍方示例，查看大图
                ShowImageActivity.showImage(this, Lists.<Object>newArrayList(R.drawable.picture_order_demo), R.drawable.picture_order_demo);
                break;
            case R.id.camera_img://点击图片调用相机
                if (imgUrl != null) {
                    ShowImageActivity.showImage(this, Lists.<String>newArrayList(imgUrl), imgUrl);
                    break;
                }
            case R.id.camera_tv://点击重新拍方，调用相机
                SelectImgUtils.gotoCarema(this);
                break;
            case R.id.del_pic_tv://删除照片
                if (delDialog == null) {
                    delDialog = ConfirmDialog.getInstance(this)
                            .setDialogContent("是否删除照片？")
                            .setPositiveText("确认")
                            .setNavigationText("取消")
                            .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                                @Override
                                public void onNavigationBtnClicked(View view) {
                                    delDialog.dismiss();
                                }

                                @Override
                                public void onPositiveBtnClicked(View view) {
                                    imgUrl = null;
                                    mGlideUtils.loadImage(R.drawable.picture_medice_take, PictureMedicineActivity.this, cameraImg, R.drawable.picture_medice_take);
                                    cameraTv.setVisibility(View.GONE);
                                    delPicTv.setVisibility(View.GONE);
                                    delDialog.dismiss();
                                }
                            });
                }
                delDialog.show();
                break;
            default:
                break;
        }

    }


    /**
     * 显示选择剂型弹窗
     */
    private void showDosageFormsPop() {
        if (supplierPop == null) {
            supplierPop = DosageFormsPop.getInstance(this);
            supplierPop.isShowShortMedication(false);
            supplierPop.setData(supplierList);
            supplierPop.setOnPopItemSelectedListener(new DosageFormsPop.PopItemSelectedListener() {
                @Override
                public void onPopItemSelected(DosageFormBean.FormList dosageForm
                        , DosageFormBean.FormList.FormDetailMsg formDetailMsg
                        , int leftPosition, int rightPosition) {
                    drugForm = dosageForm.getDrugFormName();
                    supplierId = formDetailMsg.getId();
                    drugType = formDetailMsg.getProductSubType();

                    supplierTv.setText(dosageForm.getDrugFormName() + "(" + formDetailMsg.getName() + ")");
                }
            });
        } else {
            supplierPop.setLeftSelection(0);
        }
        supplierPop.show();
    }

    @Override
    protected void onActivityResult(final int requestCode, int resultCode, final Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (mLoadingDialog != null && !mLoadingDialog.isShowing()) {
                mLoadingDialog.show();
            }
            new Thread() {
                @Override
                public void run() {
                    if (!compressImg(requestCode, data)) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (isFinishing()) {
                                    return;
                                }
                                if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                                    mLoadingDialog.dismiss();
                                }
                            }
                        });
                    }
                }
            }.start();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == SelectPhotoDialog.READ_EXTERNAL_STORAGE_REQUEST_CODE ||
                requestCode == SelectPhotoDialog.WRITE_EXTERNAL_STORAGE_AND_CAMERA_REQUEST_CODE) {
            SelectImgUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults);
        }
    }

    /**
     * 获取患者信息
     */
    private void getPatients() {
        HashMap<String, String> map = new HashMap<>();
        map.put("patientId", patientId);
        addHttpPostRequest(HttpUrlManager.SELECT_PATIENT, map, PatientMsgBean.class, this);
    }

    /**
     * 网络请求获取剂型、供货商列表
     */
    private void getDosageFormList() {
        addHttpPostRequest(HttpUrlManager.DOSAGE_FORM_LIST, null
                , DosageFormBean.class, this);
    }

    /**
     * 发送药方图片到客服
     *
     * @param drugForm   剂型
     * @param supplierId 供货商id
     */
    private void sendMedicine(String drugForm, String supplierId) {
        PictureMedicineBean pictureMedicineBean = new PictureMedicineBean();
        pictureMedicineBean.setDrugForm(drugForm);
        pictureMedicineBean.setDrugType(drugType);
        pictureMedicineBean.setProductSubType(drugType);
        pictureMedicineBean.setTakerSex(patientSex);
        pictureMedicineBean.setTakerName(patientName);
        pictureMedicineBean.setTakerIsPregnant(isPregnant);
        pictureMedicineBean.setTakerId(patientId);
        pictureMedicineBean.setTakerAge(patientAge);
        pictureMedicineBean.setProviderId(supplierId);
        pictureMedicineBean.setPatientId(patientId);
        pictureMedicineBean.setPrescriptionUrl(imgUrl);
        Map<String, String> map = new HashMap<>();
        map.put("order", JSON.toJSONString(pictureMedicineBean));
        addHttpPostRequest(HttpUrlManager.SEND_PICTURE_MEDICINE, map, null, this);
    }


    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        if (isFinishing()) {
            return;
        }
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.SEND_PICTURE_MEDICINE:
                if (result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "发送成功");
                    finish();
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.SELECT_PATIENT:
                if (result != null && result.isRequestSuccessed()) {
                    PatientMsgBean patientMsgBean = (PatientMsgBean) result.getBodyObject();
                    if (patientMsgBean != null) {
                        for (PatientMsgBean.PatientsBean patientsBean : patientMsgBean.getPatients()) {
                            if ("1".equals(patientsBean.getIsSelf())) {
                                patientSex = patientsBean.getSex();
                                patientName = patientsBean.getName();
                                isPregnant = patientsBean.getIsPregnant();
                                patientAge = patientsBean.getAge();
                                break;
                            }
                        }
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.DOSAGE_FORM_LIST://网络请求获取剂型、供货商列表
                if (result.isRequestSuccessed()) {
                    DosageFormBean dosageFormBean = (DosageFormBean) result.getBodyObject();
                    if (dosageFormBean != null) {
                        supplierList = dosageFormBean.getList();
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 压缩图片并上传
     *
     * @param requestCode 请求码
     * @param data        图片数据
     * @return 是否成功
     */
    private boolean compressImg(int requestCode, Intent data) {
        Uri uri = null;//获取图片的真实路径
        switch (requestCode) {
            case SelectPhotoDialog.REQUEST_CAPTURE: //调用系统相机返回
                uri = Uri.fromFile(SelectImgUtils.tempFile);
//                filePath = SelectImgUtils.getRealFilePathFromUri(PictureMedicineActivity.this, Uri.fromFile(SelectImgUtils.tempFile));
                break;
            case SelectPhotoDialog.REQUEST_PICK:  //调用系统相册返回
                uri = data.getData();
//                filePath = SelectImgUtils.getRealFilePathFromUri(PictureMedicineActivity.this, data.getData());
                break;
            default:
                break;
        }
        if (uri == null) {
            return false;
        }
        //压缩图片文件
        Bitmap dimenBitmap = ImageUtils.createBitmapFromUri(PictureMedicineActivity.this,uri, 720, 1280);//大小压缩后的图片
        SelectImgUtils.compressBitmapFile = new File(getExternalCacheDir(), "compressImg.jpg");//最后压缩完成存储的图片路径
        try {
            SelectImgUtils.compressBitmapFile.createNewFile();
        } catch (IOException e) {
            LogUtils.i(QualificationActivity.class, e.toString());
            return false;
        }
        //将大小压缩后的图片进行质量压缩后，写入文件中准备上传；质量压缩大小限制上限为500k;
        FileIOUtils.writeFileFromBytesByStream(SelectImgUtils.compressBitmapFile, ImageUtils.compressByQuality(dimenBitmap, 500 * 1024L, true), false);
        File protraitFile = SelectImgUtils.compressBitmapFile;

        Map<String, String> params = new HashMap<>();
        params.put("resourceType", "image");
        Map<String, File> fileParams = new HashMap<>();
        fileParams.put(protraitFile.getAbsolutePath(), protraitFile);
        //先上传后显示
        HttpRequestTask.getInstance().addHttpPostFileRequest(HttpUrlManager.UPLOAD_FILE, params, fileParams, UploadFileBean.class, new OnRequestListener() {
            @Override
            public void onRequestFinished(String taskId, boolean successed, String responseStr) {

            }

            @Override
            public void onRequestFinished(String taskId, ResponseResult result) {
                if (isFinishing()) {
                    return;
                }
                if (result.isRequestSuccessed()) {
                    UploadFileBean uploadFileBean = (UploadFileBean) result.getBodyObject();
                    imgUrl = uploadFileBean.getUrl();
                    mGlideUtils.loadImage(imgUrl, PictureMedicineActivity.this, cameraImg, R.drawable.picture_medice_take);
                    cameraTv.setVisibility(View.VISIBLE);
                    delPicTv.setVisibility(View.VISIBLE);
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                    mLoadingDialog.dismiss();
                }
            }
        });
        return true;
    }
}
