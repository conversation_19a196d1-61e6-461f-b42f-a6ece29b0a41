package com.doctor.br.activity.manage;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ImageSpan;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.manage.MyWalletRecyclerAdapter;
import com.doctor.br.bean.CashType;
import com.doctor.br.bean.MyWalletBean;
import com.doctor.br.bean.event.WalletVisibleEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.AuthDialogHelper;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.OrderAndWalletDialog;
import com.doctor.br.utils.PopUtils;
import com.doctor.br.utils.UIHelper;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.widgets.EmptyView;
import org.newapp.ones.base.widgets.LoadingDialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_ASSISTANT;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_INSPECTOR;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_MINE;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_MINE_TEAM;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PART;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PART_TEAM;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PERSON;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PROVINCE;

/**
 * 类描述：我的钱包页面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/7
 */

public class MyWalletActivity extends ActionBarActivity implements RecyclerItemItemClickListener {
    //上个界面传递过来的数据
    public static final String IS_SHOW = "is_show";
    private boolean isShow;//钱包页面是否显示，非必须，默认为隐藏
    private static int REQUEST_GET_MONEY = 12;
    //界面下的控件
    @BindView(R.id.top_view)
    View topView;
    @BindView(R.id.current_view)
    LinearLayout currentView;
    @BindView(R.id.current_tv)
    TextView currentTv;
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;
    @BindView(R.id.gone_tv)
    TextView goneTv;
    private AuthDialogHelper authDialogHelper;//认证信息的dialog

    private PopUtils bottomPop;//底部弹出全部收入支出的popwindow
    private OrderAndWalletDialog orderAndWalletDialog;//控制显示与隐藏的对话框

    private int currentPage = 1;//当前页码
    private int totalPage = -1;//总页码

    private String currentType = "0";//当前账单明细的类型，默认为0，全部，1，收入，2，支出

    private RequestCallBack billCallBack;//网络请求获取我的钱包信息回调
    private List<MyWalletBean.BillsBean> billList;//账单列表
    private List<CashType> cashTypeList = new ArrayList<>();//提现类型

    private MyWalletRecyclerAdapter myWalletRecyclerAdapter;//我的钱包recyclerview适配器

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_my_wallet);
        getIntentData(savedInstanceState);
        initView();
        setEye();
        CashType defaultCashType = new CashType();
        defaultCashType.setType("yinhangka");
        defaultCashType.setStatus(1);
        cashTypeList.add(defaultCashType);
        setPopList();
        getBillRequest("0", "1");

        initAuthMsg();


    }

    private void initAuthMsg() {  //初始化认证信息
        authDialogHelper = new AuthDialogHelper();
        authDialogHelper.isRequestOnCreate(false);
        authDialogHelper.init(this, null, this);
        //添加更新认证信息的eventBus
        //EventBusUtils.register(this);
        authDialogHelper.setOnAuthListener(new AuthDialogHelper.OnAuthListener() {
            @Override
            public void stateNTCheckFail() {//点击暂不认证

            }

            @Override
            public void btnAuthClick(String authState, String isAuthentication) {//点击前往认证
                UIHelper.openQualificationActivity(MyWalletActivity.this, authState, isAuthentication);

            }

            @Override
            public void doYourBusiness() {//请求认证信息，认证信息通过后
                UIHelper.openGetMoneyActivity(MyWalletActivity.this,cashTypeList, REQUEST_GET_MONEY);
            }
        });
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putBoolean(IS_SHOW, isShow);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState == null) {
            isShow = getIntent().getBooleanExtra(IS_SHOW, false);
        } else {
            isShow = savedInstanceState.getBoolean(IS_SHOW, false);
        }
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("我的钱包");
        getBaseActionBar().setActionBarChangeListener(this);

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                getBillRequest(currentType + "", "1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        getBillRequest(currentType + "", currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        BGANormalRefreshViewHolder bgaNormalRefreshViewHolder = new BGANormalRefreshViewHolder(this, true);
        bgaNormalRefreshViewHolder.setLoadingMoreText("努力加载中...");
        bgaRefreshLayout.setRefreshViewHolder(bgaNormalRefreshViewHolder);

        final LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        billList = new ArrayList<>();

        String isDitui = SharedPreferenceUtils.getString(this, PublicParams.USER_TYPE);
        boolean isDoctor = true;
        if (ROLE_PERSON.equals(isDitui) || ROLE_PART.equals(isDitui)
                || ROLE_PART_TEAM.equals(isDitui) || ROLE_MINE.equals(isDitui)
                || ROLE_MINE_TEAM.equals(isDitui) || ROLE_PROVINCE.equals(isDitui)
                || ROLE_ASSISTANT.equals(isDitui) || ROLE_INSPECTOR.equals(isDitui)) {
            //判断当前用户是经纪人还是医生
            isDoctor = false;
        }

        myWalletRecyclerAdapter = new MyWalletRecyclerAdapter(this,isDoctor, null, currentType, billList, this);
        recyclerView.setAdapter(myWalletRecyclerAdapter);
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                int firstVisiblePosition = linearLayoutManager.findFirstVisibleItemPosition();
                if (firstVisiblePosition >= 1 && isShow) {
                    topView.setVisibility(View.VISIBLE);
                } else {
                    topView.setVisibility(View.GONE);
                }
            }
        });

        currentView.setOnClickListener(this);

        orderAndWalletDialog = new OrderAndWalletDialog(this, isShow, OrderAndWalletDialog.WhichActivity.WALLET_ACTIVITY, new OrderAndWalletDialog.VisibilityListener() {
            @Override
            public void changeSuccess(boolean isShow) {
                MyWalletActivity.this.isShow = isShow;
                refreshManageFragmentWalletState();
                setEye();
                getBillRequest(currentType + "", "1");
            }
        });
    }

    /**
     * 设置眼睛状态
     */
    private void setEye() {
        View view = View.inflate(this, R.layout.view_eye_state, null);
        ImageView stateImg = (ImageView) view.findViewById(R.id.state_img);
        TextView stateTv = (TextView) view.findViewById(R.id.state_tv);
        if (isShow) {
            stateImg.setImageResource(R.drawable.eye_close);
            stateTv.setText("隐藏");
        } else {

            stateImg.setImageResource(R.drawable.eye_open);
            stateTv.setText("显示");
        }
        getBaseActionBar().setRightView(view);
    }

    /**
     * 初始化底部选择账单明细类型的popwindow
     */
    private void setPopList() {
        final String[] strings = {"全部", "收入", "支出"};
        bottomPop = new PopUtils(this, R.string.bill, Arrays.asList(strings), new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                bottomPop.dismiss();
                if (mLoadingDialog == null) {
                    mLoadingDialog = LoadingDialog.getInstance(MyWalletActivity.this);
                }
                getBillRequest(position + "", "1");
            }
        });
    }

    @Override
    public void onRightBtnClick(View view) {
        orderAndWalletDialog.show();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.current_view:
                bottomPop.showPopupWindow();
                break;
            default:
                break;
        }
    }


    @Override
    public void itemViewClick(int position, View view) {
        if (position == 0 && view.getId() == R.id.get_money_btn) {  //点击提现
           /* if (authDialogHelper != null) {//判断是否认证成功
                Dialog authDialog = authDialogHelper.showAuthDialog();
                if (authDialog != null) {
                    return;
                }
            }*/
            if (authDialogHelper != null) {//请求认证信息
                authDialogHelper.requestData(true);
            }
        }
        if (position == 1) {
            onClick(currentView);
        }
    }

    /**
     * 网络请求获取账单明细
     *
     * @param accountType 账单明细的类型；1 收入 2 支出 0 全部
     * @param page        当前页码
     */
    private void getBillRequest(String accountType, String page) {
        emptyView.setVisibility(View.GONE);
        if (!isShow) {
            SpannableString spannableString = new SpannableString("点击右上角的眼睛标识，显示我的钱包");
            Drawable drawable = ContextCompat.getDrawable(this, R.drawable.eye_open);
            drawable.setBounds(0, 0, DensityUtils.dip2px(this, 22), DensityUtils.dip2px(this, 22));
            ImageSpan imageSpan = new ImageSpan(drawable);
            spannableString.setSpan(imageSpan, spannableString.toString().indexOf("眼睛"), spannableString.toString().indexOf("眼睛") + 2, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            goneTv.setText(spannableString);
            goneTv.setVisibility(View.VISIBLE);
            return;
        }
        goneTv.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
        map.put("accountType", accountType);
        map.put("page", page);
        map.put("pageSize", "20");
//        map.put("userId", "5568");//测试使用
        billCallBack = addHttpPostRequest(HttpUrlManager.BILL, map, MyWalletBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (bgaRefreshLayout == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.BILL://我的钱包详情
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    MyWalletBean myWalletBean = (MyWalletBean) result.getBodyObject();
                    //设置悬浮view的数据
                    if ("0".equals(myWalletBean.getType())) {
                        currentTv.setText("全部");
                    } else if ("1".equals(myWalletBean.getType())) {
                        currentTv.setText("收入");
                    } else if ("2".equals(myWalletBean.getType())) {
                        currentTv.setText("支出");
                    }
                    if(myWalletBean.getCashTypeList()!=null&&cashTypeList.size()>0) {
                        cashTypeList.clear();
                        cashTypeList.addAll(myWalletBean.getCashTypeList());
                    }
                    currentType = myWalletBean.getType();
                    myWalletRecyclerAdapter.setAccountType(myWalletBean.getType());
                    totalPage = myWalletBean.getTotalPage();
                    currentPage = myWalletBean.getPageNumber();
                    myWalletRecyclerAdapter.setAmount(myWalletBean.getAmount());
                    if (currentPage == 1) {
                        billList.clear();
                    }
                    billList.addAll(myWalletBean.getBills());
                    if (currentPage >= totalPage && billList.size() > 0) {
                        billList.add(new MyWalletBean.BillsBean(true));
                    }
                    //设置emptyview的高度
                    bgaRefreshLayout.post(new Runnable() {
                        @Override
                        public void run() {
                            float topView2Height = getResources().getDimension(R.dimen.my_wallet_top2_height);
                            if ("0".equals(currentType) && billList.size() == 0) {
                                topView2Height = 0f;
                            }
                            myWalletRecyclerAdapter.setEmptyViewHeight((int) (bgaRefreshLayout.getMeasuredHeight() - getResources().getDimension(R.dimen.my_wallet_top1_height) - topView2Height));
                        }
                    });
                    myWalletRecyclerAdapter.notifyDataSetChanged();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                    myWalletRecyclerAdapter.setAmount(null);
                    myWalletRecyclerAdapter.notifyDataSetChanged();
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            if (mLoadingDialog == null) {
                                mLoadingDialog = LoadingDialog.getInstance(MyWalletActivity.this);
                            }
                            getBillRequest(currentType + "", "1");
                        }
                    });
                    emptyView.setVisibility(View.VISIBLE);
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_GET_MONEY && resultCode == RESULT_OK) {//刷新
            getBillRequest(currentType + "", "1");
        }
    }

    /**
     * 刷新管理界面我的钱包显示隐藏的状态，避免网络延迟问题
     *
     * @sender {@link MyWalletActivity#refreshManageFragmentWalletState()}
     * @recevier {@link com.doctor.br.fragment.main.ManageFragment#refreshWalletVisible(WalletVisibleEvent)}
     */
    private void refreshManageFragmentWalletState() {
        EventBusUtils.post(new WalletVisibleEvent(isShow));
    }

    @Override
    protected void onDestroy() {
        //认证信息销毁
        if (authDialogHelper != null) {
            authDialogHelper.onDestoryView();
            authDialogHelper = null;
        }
        //EventBusUtils.unRegister(this);

        if (billCallBack != null) {
            billCallBack.cancel();
        }
        if (orderAndWalletDialog != null) {
            orderAndWalletDialog.cancel();
        }
        super.onDestroy();
    }

    /**
     * @param refreshDoctorDataEvent 接收认证的跟新信息
     */
   /* @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshDoctorInfo(RefreshDoctorDataEvent refreshDoctorDataEvent) {
        LogUtils.log("我收到跟新认证的消息了....." + refreshDoctorDataEvent.toString());
        if (null != refreshDoctorDataEvent && !TextUtils.isEmpty(refreshDoctorDataEvent.getIsAuthentication())) {
            if (authDialogHelper != null) {
                LogUtils.log("我收到跟新认证的消息了...里面.." + refreshDoctorDataEvent.toString());
                authDialogHelper.setAuthState(refreshDoctorDataEvent.getIsAuthentication());

            }
        }
    }*/

}
