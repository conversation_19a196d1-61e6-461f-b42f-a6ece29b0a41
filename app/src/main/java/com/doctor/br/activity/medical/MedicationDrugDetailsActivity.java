package com.doctor.br.activity.medical;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.doctor.br.bean.medical.MedicineDetailMsgBean;
import com.doctor.br.bean.medical.OrderMsgBean;
import com.doctor.br.utils.DecimalUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.adapter.BaseRecyclerAdapter;

import java.util.List;

import butterknife.BindView;

/**
 * Author:sunxiaxia
 * createdDate:2022/11/27
 * description:用药明细
 */

public class MedicationDrugDetailsActivity extends ActionBarActivity {
    public static String FROM_QUICK_PRESCRIPTION = "from_quick_prescription";
    private static String EXTRA_FROM = "extra_from";

    public static void launch(Context context,  OrderMsgBean orderMsgBean) {
        Intent intent = new Intent(context, MedicationDrugDetailsActivity.class);
        intent.putExtra("orderMsgBean", orderMsgBean);
        context.startActivity(intent);
    }

    @BindView(R.id.drug_total_price)
    TextView tvDrugTotalPrice;
    @BindView(R.id.drug_list_rv)
    RecyclerView rvDrugList;


    private OrderMsgBean mOrderMsgBean;
    private String mDrugForm;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_medication_list_details);
        setActionBarTitle("药品明细");
        OrderMsgBean orderMsgBean = (OrderMsgBean) getIntent().getSerializableExtra("orderMsgBean");
        if(orderMsgBean.getPreDetailList()==null){
            finish();
            return;
        }
        setViewData(orderMsgBean);
    }

    /**
     * @param orderMsgBean 显示药品明细
     */
    private void setViewData(OrderMsgBean orderMsgBean) {
        MedicationDetailsAdapter adapter = new MedicationDetailsAdapter(this,orderMsgBean.getPreDetailList());
        LinearLayoutManager manager = new LinearLayoutManager(this,LinearLayoutManager.VERTICAL,false);
        rvDrugList.setLayoutManager(manager);
        rvDrugList.setAdapter(adapter);

        tvDrugTotalPrice.setText(orderMsgBean.getDrugPrice()+" 元");

    }



   private static class MedicationDetailsAdapter extends BaseRecyclerAdapter<MedicineDetailMsgBean,MedicationDetailsVH>{

       public MedicationDetailsAdapter(Context context,List<MedicineDetailMsgBean> drugList) {
           super(context, drugList, R.layout.item_medication_list);
       }


       @Override
       public MedicationDetailsVH onCreateVH(ViewGroup viewGroup, View view, int viewType) {
           return new MedicationDetailsVH(view);
       }

       @Override
       public void onBindVH(MedicationDetailsVH holder, MedicineDetailMsgBean data, int position) {
           holder.bindData(data);
       }




   }

    private static class MedicationDetailsVH extends BaseRecyclerAdapter.BaseRecyclerViewHolder{
        private TextView drugNameTv;
        private TextView drugPriceTv;
        private TextView drugCountTv;
        private TextView drugUnitTv;
        public MedicationDetailsVH(View itemView) {
            super(itemView);
            drugNameTv = itemView.findViewById(R.id.drug_name_title);
            drugPriceTv = itemView.findViewById(R.id.drug_price_title);
            drugCountTv = itemView.findViewById(R.id.drug_count_title);
            drugUnitTv = itemView.findViewById(R.id.drug_unit_title);
        }

        public void bindData(MedicineDetailMsgBean data){
            drugNameTv.setText(data.getDrugName());
            drugPriceTv.setText(DecimalUtils.format(data.getPrice(),3));
            drugCountTv.setText(data.getDose());
            drugUnitTv.setText(data.getUnit());
        }
    }

}
