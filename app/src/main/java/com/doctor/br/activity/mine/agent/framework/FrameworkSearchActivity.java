package com.doctor.br.activity.mine.agent.framework;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.mine.AgentFrameworkSearchRecyclerAdapter;
import com.doctor.br.bean.AgentFrameworkSearchBean;
import com.doctor.br.bean.event.FinishAgentFrameworkSerchActivityEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：组织架构搜索界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/15
 */

public class FrameworkSearchActivity extends ActionBarActivity implements RecyclerItemItemClickListener {
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private RequestCallBack searchCallBack;//网络请求搜索经纪人回调

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarType(ActionBarView.ACTIONBAR_SEARCH);
        setActionBarContentView(R.layout.activity_framework_search);
        EventBusUtils.register(this);
        initView();
        setKeyBoardListenr();
    }

    private void initView() {
        getBaseActionBar().setActionbarSearchInputHint("请输入经纪人姓名或手机号");

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
//                mLoadingDialog = null;
//                getDaifukuanOrderListRequest("1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
//                if (totalPage != -1) {
//                    if (currentPage < totalPage) {
//                        mLoadingDialog = null;
//                        getDaifukuanOrderListRequest(currentPage + 1 + "");
//                        return true;
//                    }
//                }
                return false;
            }
        });
        //暂时关闭下拉刷新和上拉加载
        bgaRefreshLayout.setRefreshViewHolder(new BGANormalRefreshViewHolder(this, false));
        bgaRefreshLayout.setPullDownRefreshEnable(false);

        recyclerView.setHasFixedSize(true);
    }


    @Override
    public void onRightBtnClick(View view) {
        if (TextUtils.isEmpty(getActionbarSearchInputText())) {
            return;
        }
        searchAgent(getActionbarSearchInputText());
    }

    @Override
    public void itemViewClick(int position, View view) {
        if (list == null) {
            return;
        }
        if (FrameworkActivity.STATUS_DISPATCHING.equals(list.get(position).getUserStatus())) {
            //调度中
            return;
        }
        Intent intent = new Intent(this, FrameworkDispatchActivity.class);
        intent.putExtra(FrameworkDispatchActivity.AREA_NAME, list.get(position).getProvinceName());
        intent.putExtra(FrameworkDispatchActivity.TEAM_TYPE, list.get(position).getTeamType());
        intent.putExtra(FrameworkDispatchActivity.TEAM_ID, list.get(position).getTeamId());
        intent.putExtra(FrameworkDispatchActivity.ROLE, list.get(position).getUserRole());
        intent.putExtra(FrameworkDispatchActivity.AREA_CODE, list.get(position).getProvinceCode());
        intent.putExtra(FrameworkDispatchActivity.TEAM_NAME, list.get(position).getTeamName());
        intent.putExtra(FrameworkDispatchActivity.USER_ID, list.get(position).getUserId());
        startActivity(intent);
    }

    /**
     * 关闭经纪人组织架构搜索界面并刷新组织架构界面数据
     *
     * @sender {@link FrameworkDispatchActivity#finishSearchActivity()}
     * @receive {@link FrameworkSearchActivity#finishThis(FinishAgentFrameworkSerchActivityEvent)}
     * @receive {@link FrameworkActivity#refreshData(FinishAgentFrameworkSerchActivityEvent)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void finishThis(FinishAgentFrameworkSerchActivityEvent finishAgentFrameworkSerchActivityEvent) {
        if (finishAgentFrameworkSerchActivityEvent != null && finishAgentFrameworkSerchActivityEvent.isClose()) {
            finish();
        }
    }

    //为软键盘设置监听
    private void setKeyBoardListenr() {

        setOnSearchInputEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                //兼容软键盘和硬键盘
                boolean isEvent = event != null && KeyEvent.KEYCODE_ENTER == event.getKeyCode() && KeyEvent.ACTION_UP == event.getAction();
                if (actionId == EditorInfo.IME_ACTION_SEARCH || isEvent) {
                    //处理事件
                    onRightBtnClick(null);
                    return true;
                }
                return false;
            }
        });
    }

    /**
     * 网络请求搜索地推
     *
     * @param keyword 搜索关键字
     */
    private void searchAgent(String keyword) {
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
        map.put("keyword", keyword);
//        map.put("userId", "2928");//测试使用
        searchCallBack = addHttpPostRequest(HttpUrlManager.SEARCH_AGENT, map, AgentFrameworkSearchBean.class, this);
    }

    private List<AgentFrameworkSearchBean.DataBean> list;

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (emptyView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.SEARCH_AGENT://搜索经纪人结果
                if (result.isRequestSuccessed()) {
                    AgentFrameworkSearchBean agentFrameworkSearchBean = (AgentFrameworkSearchBean) result.getBodyObject();
                    list = agentFrameworkSearchBean.getData();
                    recyclerView.setAdapter(new AgentFrameworkSearchRecyclerAdapter(this, agentFrameworkSearchBean.getData(), this));
                    if (agentFrameworkSearchBean.getData().size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setVisibility(View.VISIBLE);
                    }
                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            onRightBtnClick(null);
                        }
                    });
                    RequestErrorToast.showError(this,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        if (searchCallBack != null) {
            searchCallBack.cancel();
        }
        super.onDestroy();
    }


}
