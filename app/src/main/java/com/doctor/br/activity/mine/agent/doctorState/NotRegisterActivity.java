package com.doctor.br.activity.mine.agent.doctorState;

import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.doctor.br.adapter.mine.AgentNotRegisterDoctorRecyclerAdapter;
import com.doctor.br.bean.NotRegisterDoctorBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：大夫状态列表-已邀请未注册的医生列表界面
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/7
 */

public class NotRegisterActivity extends ActionBarActivity {
    //界面中的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    //列表适配器
    private AgentNotRegisterDoctorRecyclerAdapter adapter;
    //邀请未注册大夫列表
    private List<NotRegisterDoctorBean.DoctorMessagesBean> doctorList;
    //网络请求未注册大夫列表回调
    private RequestCallBack doctorListCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_not_register);
        initView();
        getNotRegisterDoctorList();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("已邀请未注册");

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setHasFixedSize(true);

        doctorList = new ArrayList<>();
        adapter = new AgentNotRegisterDoctorRecyclerAdapter(this, doctorList);
        recyclerView.setAdapter(adapter);
    }

    /**
     * 网络请求获取未注册大夫列表
     */
    private void getNotRegisterDoctorList() {
        emptyView.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2928");//测试使用
        doctorListCallBack = addHttpPostRequest(HttpUrlManager.NOT_REGISTER, map, NotRegisterDoctorBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (recyclerView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.NOT_REGISTER:
                if (result.isRequestSuccessed()) {
                    NotRegisterDoctorBean notRegisterDoctorBean = (NotRegisterDoctorBean) result.getBodyObject();
                    doctorList.clear();
                    doctorList.addAll(notRegisterDoctorBean.getDoctorMessages());
                    adapter.notifyDataSetChanged();
                    if (doctorList.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setVisibility(View.VISIBLE);
                        emptyView.setEmptyText("暂无大夫");
                        recyclerView.setVisibility(View.GONE);
                    }
                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    recyclerView.setVisibility(View.GONE);
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            getNotRegisterDoctorList();
                        }
                    });
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (doctorListCallBack != null) {
            doctorListCallBack.cancel();
        }
        super.onDestroy();
    }
}
