package com.doctor.br.activity.mine;

import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;

import com.danikula.videocache.HttpProxyCacheServer;
import com.doctor.yy.R;
import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.MediaMetadata;
import com.google.android.exoplayer2.PlaybackParameters;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.Timeline;
import com.google.android.exoplayer2.audio.AudioAttributes;
import com.google.android.exoplayer2.device.DeviceInfo;
import com.google.android.exoplayer2.metadata.Metadata;
import com.google.android.exoplayer2.source.DefaultMediaSourceFactory;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.TrackGroupArray;
import com.google.android.exoplayer2.text.Cue;
import com.google.android.exoplayer2.trackselection.AdaptiveTrackSelection;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.trackselection.ExoTrackSelection;
import com.google.android.exoplayer2.trackselection.TrackSelectionArray;
import com.google.android.exoplayer2.trackselection.TrackSelector;
import com.google.android.exoplayer2.ui.PlayerControlView;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.BandwidthMeter;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultBandwidthMeter;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.google.android.exoplayer2.util.Util;
import com.google.android.exoplayer2.video.VideoSize;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.NetUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.List;

import butterknife.BindView;

/**
 * 类描述：视频播放页面
 * 创建人：ShiShaoPo
 * 创建时间：2018/5/8
 */

public class VideoPlayActivity extends NoActionBarActivity {
    //上个界面传递过来的数据
    public static String VIDEO_URL = "videoUrl";
    private String videoUrl;
    //界面下的控件
    @BindView(R.id.backImg)
    ImageView backImg;
    @BindView(R.id.topLinear)
    LinearLayout topLinear;
    @BindView(R.id.centerPlayImg)
    ImageView centerPlayImg;
    @BindView(R.id.progressBar)
    ProgressBar progressBar;
    @BindView(R.id.container)
    RelativeLayout container;
    @BindView(R.id.videoView)
    com.google.android.exoplayer2.ui.PlayerView videoView;

    private SimpleExoPlayer player;//视频播放controller
    private ConfirmDialog dialog;//视频播放提示框
    private String myUrl;//转化后的url，可能是本地uri或网络url

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_video);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);//屏幕保持常亮
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);//全屏显示
        getIntentData(savedInstanceState);

        backImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });


        myUrl = new HttpProxyCacheServer(this).getProxyUrl(videoUrl);
        if (myUrl.startsWith("file")) {
            //如果本地已经缓存完毕直接播放
            initVideo();
            return;
        }
        if (!NetUtils.isNetworkAvailable(VideoPlayActivity.this)) {
            //无网返回
            ToastUtils.showShortMsg(VideoPlayActivity.this, "请检查网络后重试！");
            finish();
            return;
        }
        if ("0".equals(NetUtils.isWifi(this))) {
            //有网但不是wifi环境，弹出提示语
            if (dialog == null) {
                dialog = ConfirmDialog.getInstance(this)
                        .setDialogContent("播放文件过大，建议在WIFI环境下观看。")
                        .setPositiveText("继续观看")
                        .setNavigationText("下次观看")
                        .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                dialog.dismiss();
                                finish();
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                dialog.dismiss();
                                initVideo();
                            }
                        });
            }
            dialog.show();
            return;
        }
        initVideo();
    }


    private void getIntentData(Bundle bundle) {
        if (bundle == null) {
            videoUrl = getIntent().getStringExtra(VIDEO_URL);
        } else {
            videoUrl = bundle.getString(VIDEO_URL);
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(VIDEO_URL, videoUrl);
    }

    /**
     * 初始化视频播放器
     */
    private void initVideo() {
        centerPlayImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (player.getPlayWhenReady()) {
                    player.setPlayWhenReady(false);
                } else {
                    player.setPlayWhenReady(true);
                }
            }
        });
        // 1. Create a default TrackSelector
        BandwidthMeter bandwidthMeter = new DefaultBandwidthMeter();
        ExoTrackSelection.Factory videoTrackSelectionFactory = new AdaptiveTrackSelection.Factory();
        TrackSelector trackSelector = new DefaultTrackSelector(this,videoTrackSelectionFactory);
// 2. Create the player
        player = new SimpleExoPlayer.Builder(this).build();
        videoView.setPlayer(player);
// Produces DataSource instances through which media data is loaded.
//        DataSource.Factory dataSourceFactory = new VideoCacheUtils(this, cacheFile);
        DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(this, Util.getUserAgent(this, this.getString(R.string.app_name)));
// This is the MediaSource representing the media to be played.
        MediaSource videoSource = new DefaultMediaSourceFactory(this).createMediaSource(MediaItem.fromUri(Uri.parse(myUrl)));
// Prepare the player with the source.
        player.prepare(videoSource);
        player.setPlayWhenReady(true);//缓冲完毕后开始播放

        videoView.setControllerVisibilityListener(new PlayerControlView.VisibilityListener() {
            @Override
            public void onVisibilityChange(int i) {
                //控制器的显示隐藏监听
                centerPlayImg.setVisibility(i);
                topLinear.setVisibility(i);
            }
        });
        player.addListener(new Player.Listener() {
            @Override
            public void onCues(List<Cue> cues) {
            }

            @Override
            public void onMetadata(Metadata metadata) {
            }

            @Override
            public void onTimelineChanged(Timeline timeline, int reason) {
            }

            @Override
            public void onTimelineChanged(Timeline timeline, @Nullable Object manifest, int reason) {
            }

            @Override
            public void onMediaItemTransition(@Nullable MediaItem mediaItem, int reason) {
            }

            @Override
            public void onTracksChanged(TrackGroupArray trackGroups, TrackSelectionArray trackSelections) {
            }

            @Override
            public void onStaticMetadataChanged(List<Metadata> metadataList) {
            }

            @Override
            public void onMediaMetadataChanged(MediaMetadata mediaMetadata) {
            }

            @Override
            public void onIsLoadingChanged(boolean isLoading) {
            }

            @Override
            public void onLoadingChanged(boolean isLoading) {
            }

            @Override
            public void onAvailableCommandsChanged(Player.Commands availableCommands) {
            }


            @Override
            public void onPlaybackStateChanged(int state) {
            }

            @Override
            public void onPlayWhenReadyChanged(boolean playWhenReady, int reason) {
            }

            @Override
            public void onPlaybackSuppressionReasonChanged(int playbackSuppressionReason) {
            }

            @Override
            public void onIsPlayingChanged(boolean isPlaying) {
            }

            @Override
            public void onRepeatModeChanged(int repeatMode) {
            }

            @Override
            public void onShuffleModeEnabledChanged(boolean shuffleModeEnabled) {
            }


            @Override
            public void onPositionDiscontinuity(int reason) {
            }

            @Override
            public void onPositionDiscontinuity(Player.PositionInfo oldPosition, Player.PositionInfo newPosition, int reason) {
            }

            @Override
            public void onPlaybackParametersChanged(PlaybackParameters playbackParameters) {
            }

            @Override
            public void onSeekProcessed() {
            }

            @Override
            public void onEvents(Player player, Player.Events events) {
            }

            @Override
            public void onAudioSessionIdChanged(int audioSessionId) {
            }

            @Override
            public void onAudioAttributesChanged(AudioAttributes audioAttributes) {
            }

            @Override
            public void onVolumeChanged(float volume) {
            }

            @Override
            public void onSkipSilenceEnabledChanged(boolean skipSilenceEnabled) {
            }

            @Override
            public void onDeviceInfoChanged(DeviceInfo deviceInfo) {
            }

            @Override
            public void onDeviceVolumeChanged(int volume, boolean muted) {
            }

            @Override
            public void onVideoSizeChanged(VideoSize videoSize) {
            }

            @Override
            public void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio) {
            }

            @Override
            public void onSurfaceSizeChanged(int width, int height) {
            }

            @Override
            public void onRenderedFirstFrame() {
            }

            @Override
            public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {

                switch (playbackState) {
                    case Player.STATE_READY:
                        LogUtils.e(VideoPlayActivity.class, "准备播放:" + playWhenReady);
                        //准备播放，缓冲完毕会进入这里，不管播放状态还是暂停状态
                        progressBar.setVisibility(View.GONE);
                        //移除弱网提示语
                        progressBar.removeCallbacks(leakNetRunnable);
                        if (playWhenReady) {
                            centerPlayImg.setImageResource(R.drawable.video_pause);
                        } else {
                            centerPlayImg.setImageResource(R.drawable.video_play);
                            centerPlayImg.setVisibility(View.VISIBLE);
                        }
                        break;
                    case Player.STATE_ENDED:
                        LogUtils.e(VideoPlayActivity.class, "播放完毕");

                        //播放完毕
                        finish();
                        break;
                    case Player.STATE_BUFFERING:
                        LogUtils.e(VideoPlayActivity.class, "开始缓冲");
                        //开始缓冲
                        progressBar.setVisibility(View.VISIBLE);
                        //10秒后提示弱网
                        progressBar.postDelayed(leakNetRunnable, 10 * 1000);
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onPlayerError(ExoPlaybackException error) {
                //播放错误
                if (!NetUtils.isNetworkAvailable(VideoPlayActivity.this)) {
                    ToastUtils.showShortMsg(VideoPlayActivity.this, "请检查网络后重试！");
                    finish();
                }
            }
        });
    }

    /**
     * 弱网环境下的提示
     */
    private Runnable leakNetRunnable = new Runnable() {
        @Override
        public void run() {
            if (progressBar != null) {
                ToastUtils.showShortMsg(VideoPlayActivity.this, "请检查网络后重试！");
            }
        }
    };

    @Override
    protected void onStop() {
        if (player != null) {
            player.setPlayWhenReady(false);
        }
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        if(progressBar!=null){
            progressBar.removeCallbacks(leakNetRunnable);
        }
        if (player != null) {
            player.release();
        }
        super.onDestroy();
    }
}
