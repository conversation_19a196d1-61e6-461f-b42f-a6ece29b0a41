package com.doctor.br.activity;

import android.app.Activity;
import android.os.Bundle;
import android.os.CountDownTimer;
import androidx.core.content.ContextCompat;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.doctor.br.activity.mine.SafeSettingActivity;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.MD5Utils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：忘记密码
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/11 14:44
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/11 14:44
 */

public class ForgetPasswordActivity extends ActionBarActivity {
    //上个界面传递过来的数据
    public static final String WITCH_ACTIVITY = "witch_activity";

    private String whereFrom;//判断从哪个界面跳转过来，从登陆界面过来为忘记密码；安全设置界面过来为修改密码；
    //界面下的控件
    @BindView(R.id.invitation_code_linear)
    LinearLayout invitationCodeLinear;//邀请码整行
    @BindView(R.id.top_line)
    View topLine;//邀请码下面的分割线
    @BindView(R.id.phone_et)
    EditText phoneEt;//手机号输入框
    @BindView(R.id.clear_img)
    ImageView clearImg;//清空手机号图片
    @BindView(R.id.verification_code_et)
    EditText verificationCodeEt;//验证码输入框
    @BindView(R.id.get_code_btn)
    com.doctor.br.view.NoDoubleClickBtn getCodeBtn;//获取验证码按钮
    @BindView(R.id.password_et)
    EditText passwordEt;//密码输入框
    @BindView(R.id.confirm_password_et)
    EditText confirmPasswordEt;//确认密码输入框
    @BindView(R.id.bottom_linear)
    LinearLayout bottomLinear;//底部的整行
    @BindView(R.id.commit_btn)
    com.doctor.br.view.NoDoubleClickBtn commitBtn;//提交按钮

    private CountDownTimer countDownTimer;//倒计时
    private RequestCallBack getCodeCallBack;//获取验证码网络请求回调
    private RequestCallBack updatePasswordCallBack;//修改密码回调

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_register);
        getIntentData(savedInstanceState);
        initView();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(WITCH_ACTIVITY, whereFrom);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            whereFrom = savedInstanceState.getString(WITCH_ACTIVITY);
        } else {
            whereFrom = getIntent().getStringExtra(WITCH_ACTIVITY);
        }
        if (TextUtils.isEmpty(whereFrom)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }

    private void initView() {
        //根据不同界面进入，修改标题，默认为找回密码，从安全设置过来为修改密码
        if (whereFrom.equals(SafeSettingActivity.class.getSimpleName())) {
            setActionBarTitle("修改登录密码");
        } else if (whereFrom.equals(LoginActivity.class.getSimpleName())) {
            setActionBarTitle("找回密码");
        }
        getBaseActionBar().setActionBarChangeListener(this);

        if (whereFrom.equals(SafeSettingActivity.class.getSimpleName())) {
            //如果为修改密码，直接将手机号赋值，从本地取出登录的账号
            String spTel = SharedPreferenceUtils.getString(this, PublicParams.USER_TEL);
            if (!TextUtils.isEmpty(spTel) && spTel.length() == 11) {
                phoneEt.setText(spTel);
                phoneEt.setEnabled(false);
                switchCodeState(true);
                clearImg.setVisibility(View.GONE);
            }
        }

        invitationCodeLinear.setVisibility(View.GONE);
        topLine.setVisibility(View.GONE);
        bottomLinear.setVisibility(View.GONE);

        phoneEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.toString().length() == 0) {
                    clearImg.setVisibility(View.GONE);
                } else {
                    clearImg.setVisibility(View.VISIBLE);
                }
                switchCodeState(s.length() == 11 && getCodeBtn.getText().toString().contains("获取验证码"));
            }
        });

        clearImg.setOnClickListener(this);
        getCodeBtn.setOnClickListener(this);
        commitBtn.setOnClickListener(this);

        countDownTimer = new CountDownTimer(60 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                switchCodeState(false);
                getCodeBtn.setText("重新发送(" + millisUntilFinished / 1000 + "s" + ")");
            }

            @Override
            public void onFinish() {
                switchCodeState(true);
                getCodeBtn.setText("获取验证码");
            }
        };
    }

    /**
     * 切换获取验证码按钮的状态
     *
     * @param clickable 是否可以点击
     */
    private void switchCodeState(boolean clickable) {
        if (clickable) {
            getCodeBtn.setBackgroundResource(R.drawable.btn_circle_theme);
            getCodeBtn.setTextColor(ContextCompat.getColor(this, R.color.br_color_white));
        } else {
            getCodeBtn.setBackgroundResource(R.drawable.btn_circle_gray_line);
            getCodeBtn.setTextColor(ContextCompat.getColor(this, R.color.br_color_black_999));
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.clear_img://清空输入框
                phoneEt.setText("");
                break;
            case R.id.get_code_btn://获取验证码
                String tel1 = phoneEt.getText().toString();
                if (tel1.length() != 11 || getCodeBtn.getText().toString().contains("重新发送")) {
                    return;
                }
                getCodeRequest(tel1, "1", "1");
                break;
            case R.id.commit_btn://提交
                String tel = phoneEt.getText().toString();
                if (TextUtils.isEmpty(tel)) {
                    phoneEt.requestFocus();
                    ToastUtils.showShortMsg(this, "请输入您的手机号");
                    return;
                }
                if (tel.length() != 11) {
                    phoneEt.requestFocus();
                    ToastUtils.showShortMsg(this, "请输入11位手机号");
                    return;
                }

                String code = verificationCodeEt.getText().toString();
                if (TextUtils.isEmpty(code)) {
                    verificationCodeEt.requestFocus();
                    ToastUtils.showShortMsg(this, "请输入短信验证码");
                    return;
                }

                String password = passwordEt.getText().toString();
                if (TextUtils.isEmpty(password)) {
                    passwordEt.requestFocus();
                    ToastUtils.showShortMsg(this, "请输入新密码");
                    return;
                }
                if (password.length() < 6) {
                    passwordEt.requestFocus();
                    ToastUtils.showShortMsg(this, "请输入6-20位密码");
                    return;
                }

                String confirmPassword = confirmPasswordEt.getText().toString();
                if (TextUtils.isEmpty(confirmPassword)) {
                    confirmPasswordEt.requestFocus();
                    ToastUtils.showShortMsg(this, "请再次输入密码");
                    return;
                }
                if (!confirmPassword.equals(password)) {
                    confirmPasswordEt.requestFocus();
                    ToastUtils.showShortMsg(this, "两次密码不一致");
                    return;
                }

                updatePasswordRequest(tel, password, confirmPassword, code, "1");
                break;
            default:
                break;
        }

    }

    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    private ConfirmDialog confirmDialog;

    @Override
    public void onBackPressed() {
        //如果为忘记密码的话，返回要提示
        if (whereFrom.equals(LoginActivity.class.getSimpleName())) {
            if (!TextUtils.isEmpty(phoneEt.getText()) || !TextUtils.isEmpty(verificationCodeEt.getText()) || !TextUtils.isEmpty(passwordEt.getText())
                    || !TextUtils.isEmpty(confirmPasswordEt.getText())) {
                confirmDialog = ConfirmDialog.getInstance(this)
                        .setDialogContent("已填信息未保存，是否退出？")
                        .setPositiveText("退出")
                        .setNavigationText("取消")
                        .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                confirmDialog.dismiss();
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                ForgetPasswordActivity.super.onBackPressed();
                            }
                        });
                confirmDialog.show();
                return;
            }
        }
        super.onBackPressed();

    }

    /**
     * 网络请求获取验证码
     *
     * @param tel     获取验证码的手机
     * @param type    不知道为啥，反正就是1
     * @param optType 1=包括登陆、忘记密码、安全设置(修改手机号)2=包括注册
     */
    private void getCodeRequest(String tel, String type, String optType) {
        Map<String, String> map = new HashMap<>();
        map.put("tel", tel);
        map.put("type", type);
        map.put("optType", optType);
        getCodeCallBack = addHttpPostRequest(HttpUrlManager.GET_CODE, map, null, this);
    }

    /**
     * 网络请求修改密码
     *
     * @param tel             要修改密码的手机号
     * @param password        新密码
     * @param confirmPassword 确认密码
     * @param code            验证码
     * @param type            类型，1 是登录密码 2 是隐私密码（目前版本已经取消）
     */
    private void updatePasswordRequest(String tel, String password, String confirmPassword, String code, String type) {
        Map<String, String> map = new HashMap<>();
        map.put("tel", tel);
        map.put("newPassword", MD5Utils.MD5Upper32(password));
        map.put("confirmPassword", MD5Utils.MD5Upper32(confirmPassword));
        map.put("code", code);
        map.put("type", type);
        updatePasswordCallBack = addHttpPostRequest(HttpUrlManager.UPDATE_PASSWORD, map, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (passwordEt == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.GET_CODE://获取验证码
                if (result.isRequestSuccessed()) {
                    countDownTimer.start();
                    ToastUtils.showShortMsg(this, "验证码已发送");
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.UPDATE_PASSWORD://更新密码
                if (result.isRequestSuccessed()) {
                    String toast;
                    if (whereFrom.equals(LoginActivity.class.getSimpleName())) {
                        //忘记密码设置新密码成功提示语
                        toast = "设置成功";
                    } else {
                        //修改密码成功提示语
                        toast = "新密码设置成功";
                    }
                    ToastUtils.showShortMsg(this, toast);
                    if (!TextUtils.isEmpty(passwordEt.getText().toString())) {
//                        SharedPreferenceUtils.putString(this, PublicParams.LOGIN_PASSWORD, passwordEt.getText().toString());
//                        new Handler().postDelayed(new Runnable() {
//                            @Override
//                            public void run() {
                        ForgetPasswordActivity.this.finish();
//                            }
//                        }, 1000);
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (getCodeCallBack != null) {
            getCodeCallBack.cancel();
        }
        if (updatePasswordCallBack != null) {
            updatePasswordCallBack.cancel();
        }
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        super.onDestroy();
    }
}
