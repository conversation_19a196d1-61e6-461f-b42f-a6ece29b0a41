package com.doctor.br.activity.chatmain;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;

import com.doctor.br.adapter.MsgRepeatAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.MsgStatus;
import com.doctor.br.db.entity.Contacts;
import com.doctor.br.db.entity.Msg;
import com.doctor.br.netty.NettyMsgNotify;
import com.doctor.br.netty.NotifyType;
import com.doctor.br.netty.client.NettyUtils;
import com.doctor.br.netty.model.MsgResourceType;
import com.doctor.br.utils.DateUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.SessionUtils;
import com.doctor.br.view.stickyListView.StickyListHeadersListView;
import com.doctor.greendao.gen.ContactsDao;
import com.doctor.greendao.gen.MsgDao;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 消息转发页面
 * @createTime 2017/12/16
 */

public class MsgRepeatActivity extends ActionBarActivity implements MsgRepeatAdapter.OnItemCheckedListener {
    @BindView(R2.id.sticky_list_view)
    StickyListHeadersListView stickyListView;
    @BindView(R2.id.select_all_cb)
    CheckBox selectAllCb;
    @BindView(R2.id.send_btn)
    Button sendBtn;

    private MsgRepeatAdapter msgRepeatAdapter;
    private ContactsDao contactsDao;
    private MsgDao msgDao;
    private List<Contacts> mList;
    private List<Contacts> selectList;
    private String userId;
    private Msg repeatMsg;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_msg_repeat);
        setActionBarTitle("转发消息");
        contactsDao = AppContext.getInstances().getDaoSession().getContactsDao();
        msgDao = AppContext.getInstances().getDaoSession().getMsgDao();
        userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        repeatMsg = (Msg) getIntent().getSerializableExtra(PublicParams.REPEAT_MSG);
        initView();
        initContactsDatas();
    }

    /**
     * 初始化View
     */
    private void initView() {
        mList = new ArrayList<>();
        selectList = new ArrayList<>();
        msgRepeatAdapter = new MsgRepeatAdapter(mContext, mList, R.layout.item_layout_msg_repeat);
        msgRepeatAdapter.setOnItemCheckedListener(this);
        stickyListView.setAdapter(msgRepeatAdapter);
/*        letterView.setOnTouchingLetterChangedListener(new MyLetterSortView.OnTouchingLetterChangedListener() {
            @Override
            public void onTouchingLetterChanged(String s) {
                if ("#".equalsIgnoreCase(s)) {
                    s = "|";
                }
                int position = 0;
                if (mList != null) {
                    for (int i = 0; i < mList.size(); i++) {
                        Contacts contacts = mList.get(i);
                        if (s.toUpperCase().equals(contacts.getFirstSpell().toUpperCase())) {
                            position = i;
                            break;
                        }
                    }
                }
                stickyListView.setSelection(position);
            }
        });*/


    }

    /**
     * 初始化联系人数据
     */
    private void initContactsDatas() {
        mList.clear();
        List<Contacts> contactsList = contactsDao.queryBuilder()
                .where(ContactsDao.Properties.UserId.eq(userId))
                .where(ContactsDao.Properties.RosterUserId.notEq(PublicParams.CHAT_SERVICE_USER_ID))
                .orderAsc(ContactsDao.Properties.FirstSpell)
                .list();
        if (contactsList != null) {
            for (Contacts contacts : contactsList) {
                contacts.setChecked(false);
                mList.add(contacts);
            }

        }
        msgRepeatAdapter.notifyDataSetChanged();
    }

    @OnClick({R.id.select_all_cb, R.id.send_btn})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.select_all_cb:
                CheckBox checkBox = (CheckBox) view;
                selectAllContacts(checkBox.isChecked());
                break;
            case R.id.send_btn:
                if (selectList.size() > 0) {
                    sendMsg();
                } else {
                    ToastUtils.showShortMsg(this, "请选择患者");
                }
                break;
        }
    }

    /**
     * 发送消息
     */
    private void sendMsg() {

        for (Contacts contacts : selectList) {
            Msg msg = new Msg();
            msg.setContentObjType(repeatMsg.getContentObjType());
            msg.setContentObjTitle(repeatMsg.getContentObjTitle());
            msg.setContentObjContent(repeatMsg.getContentObjContent());
            msg.setContentObjWzdType(repeatMsg.getContentObjWzdType());
            msg.setContentObjUrl(repeatMsg.getContentObjUrl());
            msg.setContentObjOpenType(repeatMsg.getContentObjOpenType());
            msg.setContentObjVersion(repeatMsg.getContentObjVersion());
            msg.setContentObjExtra1(repeatMsg.getContentObjExtra1());
            msg.setContentObjExtra2(repeatMsg.getContentObjExtra2());
            msg.setContentObjExtra3(repeatMsg.getContentObjExtra3());
            msg.setIsRead(MsgStatus.IS_READ);
            msg.setSendStatus(MsgStatus.SENDING);
            msg.setFrom(contacts.getRosterUserId());
            msg.setCreatedTime(DateUtils.getNowDate());
            msg.setLocalTime(DateUtils.getNowDate());
            msg.setTo(contacts.getUserId());
            msg.setMsgSourceType(MsgResourceType.TYPE_TO);
            msgDao.insertOrReplace(msg);
            sendEventBus(msg);//更新交流界面消息
            NettyUtils.enQueueSendMsg(msg);
            SessionUtils.getInstance().updateSessionList(msg);
        }
        ToastUtils.showShortMsg(this, "发送成功");
        finish();
    }


    /**
     * 更新交流界面的消息
     *
     * @param msg
     * @receiver {@link com.doctor.br.fragment.chatmain.ChatFragment#onMsgNotifyEvent(NettyMsgNotify)}
     * @receiver {@link ChatServiceActivity#onMsgNotifyEvent(NettyMsgNotify)}
     */
    private void sendEventBus(Msg msg) {
        NettyMsgNotify notify = new NettyMsgNotify();
        notify.setNotifyType(NotifyType.MSG_LIST);
        notify.setNotifyMsg(msg);
        EventBusUtils.post(notify);
    }


    /**
     * 全选和取消全选
     *
     * @param isChecked
     */
    private void selectAllContacts(boolean isChecked) {
        selectList.clear();
        for (Contacts contacts : mList) {
            contacts.setChecked(isChecked);
            if (isChecked) {
                selectList.add(contacts);
            } else {
                selectList.remove(contacts);
            }
        }
        if (selectList.size() > 0) {
            sendBtn.setClickable(true);
            sendBtn.setBackgroundColor(getResources().getColor(R.color.br_color_theme));
        } else {
            sendBtn.setClickable(false);
            sendBtn.setBackgroundColor(getResources().getColor(R.color.br_color_split_line));
        }

        msgRepeatAdapter.notifyDataSetChanged();
    }

    @Override
    public void onItemChecked(int position, View view, boolean isChecked, Contacts contacts) {
        if (isChecked) {
            selectList.add(contacts);
        } else {
            selectList.remove(contacts);
        }
        if (selectList.size() == mList.size()) {
            selectAllCb.setChecked(true);
        } else {
            selectAllCb.setChecked(false);
        }

        if (selectList.size() > 0) {
            sendBtn.setClickable(true);
            sendBtn.setBackgroundColor(getResources().getColor(R.color.br_color_theme));
        } else {
            sendBtn.setClickable(false);
            sendBtn.setBackgroundColor(getResources().getColor(R.color.br_color_split_line));
        }
    }
}
