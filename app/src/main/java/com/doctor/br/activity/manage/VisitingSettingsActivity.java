package com.doctor.br.activity.manage;

import android.content.Intent;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.doctor.br.activity.AddAndEditVisitingMsgActivity;
import com.doctor.br.activity.BaseRefreshActivity;
import com.doctor.br.adapter.manage.VisitingSettingAdapter;
import com.doctor.br.bean.DoctorInfoBean;
import com.doctor.br.bean.VisitingSettingBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.ShareUtils;
import com.doctor.br.utils.UIHelper;
import com.doctor.br.view.SpaceItemDecoration;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.yy.R;

import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.HttpRequestTask;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.widgets.ActionBarView;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bingoogolapple.androidcommon.adapter.BGAOnItemChildClickListener;
import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;

/**
 * 类描述：出诊设置
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/12 17:50
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/12 17:50
 */

public class VisitingSettingsActivity extends BaseRefreshActivity<VisitingSettingBean.DataBean> {
    @BindView(R.id.btn_share)
    com.doctor.br.view.NoDoubleClickBtn btnShare;
    private RequestCallBack visitingCallBack;
    private RequestCallBack doctorInfoCallBack;

    /**
     * 初始化
     */
    @Override
    protected void init() {
        super.init();
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarTitle("出诊安排");
        setActionBarRightBtnImg(R.drawable.iv_add_visiting);

        //设置EmptyView
        emptyView.setEmptyText("暂无数据");

        setRefreshEnable(false);//不需要上啦刷新
        mRefreshLayout.setCanLoadMore(false);//不需要加载更多

        mRefreshLayout.setBackgroundColor(getResources().getColor(R.color.transparent));
        //设置距离下边10*density
        mRvData.addItemDecoration(new SpaceItemDecoration(0, DensityUtils.dip2px(this, 20)));//设置间隔

        requestList();

    }

    /**
     * 网络请求获取出诊列表
     */
    private void requestList() {
        Map<String, String> map = new HashMap<>();
        map.put("isNm", "1");
        visitingCallBack = addHttpPostRequest(HttpUrlManager.VISITING_LIST, map, VisitingSettingBean.class, this);//网络请求
    }


    @Override
    protected void setListener() {//设置监听,在init()之后执行
        super.setListener();

        mRefreshAdapter.setOnItemChildClickListener(new BGAOnItemChildClickListener() {
            @Override
            public void onItemChildClick(ViewGroup parent, View childView, int position) {
                if (childView.getId() == R.id.rl_edit) {//编辑出诊信息的点击事件
                    VisitingSettingBean.DataBean itemBean = (VisitingSettingBean.DataBean) mRefreshAdapter.getData().get(position);
                    UIHelper.openAddVisitingMsgActivity(VisitingSettingsActivity.this, AddAndEditVisitingMsgActivity.TYPE_EDIT, itemBean);
                }
            }
        });
    }

    @Override
    public void onRightBtnClick(View view) {//添加出诊信息
        super.onRightBtnClick(view);
        UIHelper.openAddVisitingMsgActivity(VisitingSettingsActivity.this, AddAndEditVisitingMsgActivity.TYPE_ADD, null);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_visiting_setting;
    }


    @Override
    public BGARecyclerViewAdapter<VisitingSettingBean.DataBean> getAdapter() {//返回adapter
        mRvData.addItemDecoration(new RecyclerView.ItemDecoration() {
        });
        return new VisitingSettingAdapter(mRvData);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == AddAndEditVisitingMsgActivity.REQUEST_CODE && resultCode == RESULT_OK) {
            LogUtils.i(HttpRequestTask.class, "接收到resultcode");
            requestList();

        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.VISITING_LIST://列表的网络请求
                if (result.isRequestSuccessed()) {  //成功
                    VisitingSettingBean visitingSettingBean = (VisitingSettingBean) result.getBodyObject();
                    if (visitingSettingBean != null) {
                        onRequestListSuccess(taskId, visitingSettingBean.getData());
                        btnShare.setVisibility(visitingSettingBean.getData().size() > 0 ? View.VISIBLE : View.GONE);
                    }
                } else { //失败
                    onRequestListError();
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                    btnShare.setVisibility(View.GONE);
                }
                break;
            case HttpUrlManager.DOCTOR_INFO://医生信息
                if (result != null && result.isRequestSuccessed()) {
                    DoctorInfoBean doctorInfoBean = (DoctorInfoBean) result.getBodyObject();
                    if (doctorInfoBean == null) {
                        return;
                    }

                    SharedPreferenceUtils.putString(this, PublicParams.USER_NAME, doctorInfoBean.getName());
                    SharedPreferenceUtils.putString(this, PublicParams.USER_WEIXIN_URL, doctorInfoBean.getWeiXinUrl());
                    SharedPreferenceUtils.putString(this, PublicParams.USER_HEAD_URL, doctorInfoBean.getHandleUrl());
                    SharedPreferenceUtils.putString(this, PublicParams.USER_TYPE, doctorInfoBean.getIsDiTui());
                    share();

                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    @OnClick({R.id.btn_share})
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_share://分享
                requestDoctorDetails();
                break;
            default:
                break;
        }
    }

    private void share() {//分享
        String name = SharedPreferenceUtils.getString(this, PublicParams.USER_NAME);
        String headUrl = SharedPreferenceUtils.getString(this, PublicParams.USER_HEAD_URL);
        String doctorId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        String targetUrl = BaseConfig.getShareInvitePatientUrl() + doctorId;
        new ShareUtils(this, ShareUtils.WECHAT, ShareUtils.WECHAT_MOMENT).showPopupWindow("我是" + name + "大夫，欢迎关注我的中医工作室!"
                , "通过微信关注我的工作室,直接向我发起在线咨询。",
                headUrl, targetUrl);
    }

    //判断是否有分享需要的信息，如果没有网络请求获取大夫详情
    private void requestDoctorDetails() {
        String name = SharedPreferenceUtils.getString(this, PublicParams.USER_NAME);
        String headUrl = SharedPreferenceUtils.getString(this, PublicParams.USER_HEAD_URL);
        String weixinUrl = SharedPreferenceUtils.getString(this, PublicParams.USER_WEIXIN_URL);
        if (TextUtils.isEmpty(name) || TextUtils.isEmpty(headUrl) || TextUtils.isEmpty(weixinUrl)) {
            Map<String, String> map = new HashMap<>();
            doctorInfoCallBack = addHttpPostRequest(HttpUrlManager.DOCTOR_INFO, map, DoctorInfoBean.class, this);
        } else {
            share();
        }

    }


    @Override
    public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {//下拉刷新

    }

    @Override
    public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {//上啦加载更多

    }

    @Override
    public void onReload() {
        requestList();
    }

}
