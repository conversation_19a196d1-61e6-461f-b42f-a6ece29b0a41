package com.doctor.br.activity.mine.agent.task;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.ListView;
import android.widget.TextView;

import com.doctor.br.activity.mine.agent.AgentActivity;
import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.mine.SelectAgentListAdapter;
import com.doctor.br.bean.AgentSelectBean;
import com.doctor.br.bean.event.RefreshPendingDataEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.view.stickyListView.ExpandableStickyListHeadersListView;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：选择经纪人搜索界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/26
 */

public class SelectAgentSearchActivity extends ActionBarActivity implements RecyclerItemItemClickListener {
    //上个界面传递过来的数据
    public static final String TASK_ID = "taskId";
    public static final String IS_DITUI = "isDitui";
    public static final String NAME = "name";
    public static final String NODE_ID = "nodeId";
    private String taskId;//任务id
    private String isDitui;//经纪人身份
    private String name;//所分配的医生姓名
    private String nodeId;//任务节点id
    //界面下的控件
    @BindView(R.id.sticky_list_view)
    ExpandableStickyListHeadersListView stickyListView;
    @BindView(R.id.list_view)
    ListView listView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private RequestCallBack agentListCallBack;//网络请求经纪人列表回调
    private RequestCallBack assignmentTaskCallBack;//网络请求分配任务回调
    private List<AgentSelectBean.RowsBean> list;//经纪人列表

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarType(ActionBarView.ACTIONBAR_SEARCH);
        setActionBarContentView(R.layout.activity_select_agent);
        getIntentData(savedInstanceState);
        initView();
        setKeyBoardListenr();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(TASK_ID, taskId);
        outState.putString(IS_DITUI, isDitui);
        outState.putString(NAME, name);
        outState.putString(NODE_ID, nodeId);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            taskId = savedInstanceState.getString(TASK_ID);
            isDitui = savedInstanceState.getString(IS_DITUI);
            name = savedInstanceState.getString(NAME);
            nodeId = savedInstanceState.getString(NODE_ID);
        } else {
            taskId = getIntent().getStringExtra(TASK_ID);
            isDitui = getIntent().getStringExtra(IS_DITUI);
            name = getIntent().getStringExtra(NAME);
            nodeId = getIntent().getStringExtra(NODE_ID);
        }
        if (TextUtils.isEmpty(taskId) || TextUtils.isEmpty(isDitui) || TextUtils.isEmpty(nodeId)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }

    }

    private void initView() {
        getBaseActionBar().setActionbarSearchInputHint("输入经纪人姓名或手机号");

        stickyListView.setVisibility(View.GONE);
        listView.setVisibility(View.GONE);
        if (AgentActivity.ROLE_MINE_TEAM.equals(isDitui) || AgentActivity.ROLE_PART_TEAM.equals(isDitui)) {
            //团队负责人
            listView.setVisibility(View.VISIBLE);
        } else if (AgentActivity.ROLE_PROVINCE.equals(isDitui)) {
            //省负责人
            stickyListView.setVisibility(View.VISIBLE);
        }
        stickyListView.setDividerHeight(0);
        listView.setDividerHeight(0);
    }

    //为软键盘设置监听
    private void setKeyBoardListenr() {

        setOnSearchInputEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                //兼容软键盘和硬键盘
                boolean isEvent = event != null && KeyEvent.KEYCODE_ENTER == event.getKeyCode() && KeyEvent.ACTION_UP == event.getAction();
                if (actionId == EditorInfo.IME_ACTION_SEARCH || isEvent) {
                    //处理事件
                    onRightBtnClick(null);
                    return true;
                }
                return false;
            }
        });
    }

    @Override
    public void onRightBtnClick(View view) {
        if (TextUtils.isEmpty(getActionbarSearchInputText())) {
            return;
        }
        getAgentListRequest(isDitui, getActionbarSearchInputText());
    }

    private ConfirmDialog confirmDialog;//任务分配确认框

    //任务分配点击
    @Override
    public void itemViewClick(final int position, View view) {
        if (list == null || list.size() == 0) {
            return;
        }
        confirmDialog = ConfirmDialog.getInstance(this)
                .setDialogContent("是否将“" + name + "”大夫分配给“" + list.get(position).getAgentName() + "”经纪人")
                .setPositiveText("确认")
                .setNavigationText("取消")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        confirmDialog.dismiss();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        confirmDialog.dismiss();
                        assignmentTaskRequest(taskId, list.get(position).getAgentId(), nodeId);
                    }
                });
        confirmDialog.show();
    }

    /**
     * 网络请求获取经纪人列表
     *
     * @param role 当前经纪人身份
     * @param name 如果为搜索的话，需要传入
     */
    private void getAgentListRequest(String role, String name) {
        if (stickyListView.getVisibility() != View.VISIBLE &&
                listView.getVisibility() != View.VISIBLE) {
            emptyView.setVisibility(View.VISIBLE);
            return;
        }
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2766");//测试使用
        map.put("role", role);
        map.put("name", name);
        agentListCallBack = addHttpPostRequest(HttpUrlManager.SELECT_AGENT, map, AgentSelectBean.class, this);
    }

    /**
     * 网络请求分配任务给经纪人
     *
     * @param taskId 任务id
     * @param owner  经纪人id
     * @param nodeId 任务节点
     */
    private void assignmentTaskRequest(String taskId, String owner, String nodeId) {
        Map<String, String> map = new HashMap<>();
//        map.put("userId", "2766");//测试使用
        map.put("taskId", taskId);
        map.put("owner", owner);
        map.put("nodeId", nodeId);
        assignmentTaskCallBack = addHttpPostRequest(HttpUrlManager.ASSIGNMENT_TASK, map, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (emptyView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.SELECT_AGENT://选择经纪人列表
                if (result.isRequestSuccessed()) {
                    AgentSelectBean agentSelectBean = (AgentSelectBean) result.getBodyObject();
                    list = agentSelectBean.getRows();
//                    Collections.sort(list);
                    stickyListView.setAdapter(new SelectAgentListAdapter(this, agentSelectBean.getRows(), this));
                    listView.setAdapter(new SelectAgentListAdapter(this, agentSelectBean.getRows(), this));
                    if (list == null || list.size() == 0) {
                        emptyView.setVisibility(View.VISIBLE);
                    }
                } else {
//                    if (totalPage == -1) {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setVisibility(View.VISIBLE);
//                        bgaRefreshLayout.setVisibility(View.GONE);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            getAgentListRequest(isDitui, getActionbarSearchInputText());
                        }
                    });
//                    }
                    RequestErrorToast.showError(this,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.ASSIGNMENT_TASK://分配任务
                if (result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "分配成功");
                    sendRefreshNumber();
//                    new Handler().postDelayed(new Runnable() {
//                        @Override
//                        public void run() {
                            finish();
//                        }
//                    }, 1000);
                } else {
                    RequestErrorToast.showError(this,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 刷新待扫描、待接收、待处理、待审核、待分配数量
     *
     * @send {@link com.doctor.zxing.activity.CaptureActivity#refreshData()}
     * @send {@link com.doctor.br.fragment.mine.BeReceiveFragment#refreshData()}
     * @send {@link com.doctor.br.activity.mine.agent.DispatchActivity#refreshNumber()}
     * @send {@link com.doctor.br.fragment.mine.BeScanFragment#sendRefresh()}
     * @send {@link SelectAgentActivity#sendRefreshNumber()}
     * @send {@link SelectAgentSearchActivity#sendRefreshNumber()}
     * @receive {@link SelectAgentActivity#refreshList(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.fragment.mine.BeScanFragment#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.AgentActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link com.doctor.br.activity.mine.agent.PendingActivity#refreshData(RefreshPendingDataEvent)}
     * @receive {@link TaskActivity#refreshData(RefreshPendingDataEvent)}
     */
    private void sendRefreshNumber() {
        RefreshPendingDataEvent refreshPendingDataEvent = new RefreshPendingDataEvent();
        refreshPendingDataEvent.setFreshTask(true);
        refreshPendingDataEvent.setFinishTask(true);
        EventBusUtils.post(refreshPendingDataEvent);
    }


    @Override
    protected void onDestroy() {
        if (agentListCallBack != null) {
            agentListCallBack.cancel();
        }
        if (assignmentTaskCallBack != null) {
            assignmentTaskCallBack.cancel();
        }
        super.onDestroy();
    }
}
