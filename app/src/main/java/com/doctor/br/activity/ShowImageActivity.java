package com.doctor.br.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.view.photoview.MutipleTouchViewPager;
import com.doctor.br.view.photoview.PhotoView;
import com.doctor.br.view.photoview.PhotoViewAttacher;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.utils.glide.GlideUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 显示大图的ImageView，传入需要显示的图片的List和当前图片的Url
 * @createTime 2017/8/23
 */

public class ShowImageActivity extends NoActionBarActivity implements ViewPager.OnPageChangeListener {
    private MutipleTouchViewPager mViewPager;
    private CommentImagePreAdapter mAdapter;
    private List<String> picList = new ArrayList<>();
    private int id;
    private LinearLayout dotLayout;
    private int porPosition = 0;
    private Handler mHandler = new Handler();
    private TextView tvIndex;

    private static List imgList;//要显示的图片集合

    public static void showImage(Context context, @NonNull ArrayList<String> list, @NonNull String IndexUrl) {
        if (list != null && !list.contains(IndexUrl)) {
            list.add(IndexUrl);
        }
        Intent intent = new Intent(context, ShowImageActivity.class);
        intent.putStringArrayListExtra("list", list);
        intent.putExtra("position", list.indexOf(IndexUrl));
        context.startActivity(intent);
    }

    public static void showImage(Context context, List<Object> imgList, @NonNull Object currentImg) {
        if (imgList == null) {
            imgList = new ArrayList<>();
            imgList.add(currentImg);
        }
        if (imgList.contains(currentImg)) {
            Intent intent = new Intent(context, ShowImageActivity.class);
            ShowImageActivity.imgList = imgList;
            intent.putExtra("position", imgList.indexOf(currentImg));
            context.startActivity(intent);
        }
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.zoomin, 0);
        setActionBarContentView(R.layout.activity_show_image);
        mViewPager = (MutipleTouchViewPager) findViewById(R.id.viewpager);
        dotLayout = (LinearLayout) findViewById(R.id.dot_layout);
        tvIndex = (TextView) findViewById(R.id.tv_index);
        if (imgList == null) {
            imgList = getIntent().getStringArrayListExtra("list");
        }
        id = getIntent().getIntExtra("position", 0);
        for (int i = 0; i < imgList.size(); i++) {
            initDot();
        }
        tvIndex.setText((id + 1) + "/" + imgList.size());
        dotLayout.getChildAt(id).setBackgroundResource(R.drawable.select_shape);
        porPosition = id;
        mAdapter = new CommentImagePreAdapter(imgList);

        mViewPager.setAdapter(mAdapter);
        mAdapter.notifyDataSetChanged();
        mViewPager.setCurrentItem(id);
        mViewPager.addOnPageChangeListener(this);
        //让虚拟键盘一直不显示
        Window window = getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE;
        window.setAttributes(params);

    }

    // 初始化小圆点
    private void initDot() {
        View view = new View(this);
        view.setBackgroundResource(R.drawable.unselect_shape);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(15, 15);
        params.leftMargin = 10;
        view.setLayoutParams(params);
        dotLayout.addView(view);
    }


    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        dotLayout.getChildAt(position).setBackgroundResource(R.drawable.select_shape);
        dotLayout.getChildAt(porPosition).setBackgroundResource(R.drawable.unselect_shape);
        porPosition = position;
        tvIndex.setText((position + 1) + "/" + imgList.size());
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    protected void onDestroy() {
        imgList = null;
        super.onDestroy();
    }

    class CommentImagePreAdapter extends PagerAdapter {
        private List<Object> picList;

        public CommentImagePreAdapter(List<Object> picList) {
            super();
            this.picList = picList;
        }

        @Override
        public int getCount() {
            // TODO Auto-generated method stub
            return picList == null ? 0 : picList.size();
        }

        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }

        @Override
        public Object instantiateItem(ViewGroup container, final int position) {
            // TODO Auto-generated method stub
            final Bitmap[] bitmap = {null};
            View view = LayoutInflater.from(container.getContext()).inflate(R.layout.show_big_image, null);
            final PhotoView photoView = (PhotoView) view.findViewById(R.id.image);
//			photoView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            GlideUtils.getInstance().loadImageLarge(picList.get(position), ShowImageActivity.this, photoView, R.drawable.icon_img_load_error, R.drawable.image_default);
            photoView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    // TODO Auto-generated method stub
                    photoView.setDrawingCacheEnabled(true);
//                    showPop(bitmap[0]);
                    photoView.setDrawingCacheEnabled(false);
                    return false;
                }
            });
            photoView.setOnViewTapListener(new PhotoViewAttacher.OnViewTapListener() {
                @Override
                public void onViewTap(View view, float x, float y) {
                    finish();
                    overridePendingTransition(0, R.anim.zoomout);
                }
            });
            container.addView(view, ViewPager.LayoutParams.MATCH_PARENT,
                    ViewPager.LayoutParams.WRAP_CONTENT);

            return view;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            // TODO Auto-generated method stub
            container.removeView((View) object);
        }

        @Override
        public boolean isViewFromObject(View arg0, Object arg1) {
            // TODO Auto-generated method stub
            return arg0 == arg1;
        }
    }

}
