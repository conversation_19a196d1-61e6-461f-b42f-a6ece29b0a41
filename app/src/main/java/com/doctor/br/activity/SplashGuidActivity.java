package com.doctor.br.activity;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.CountDownTimer;

import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioGroup;

import com.doctor.br.activity.mine.PersonalDataActivity;
import com.doctor.br.adapter.ImgPagerAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.db.entity.ServerConfig;
import com.doctor.br.netty.NettyRequestCode;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.client.NettyClient;
import com.doctor.br.netty.impl.DataReceiverImpl;
import com.doctor.br.netty.model.ResultType;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.greendao.gen.ServerConfigDao;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class SplashGuidActivity extends NoActionBarActivity implements ViewPager.OnPageChangeListener {
    public static final int READ_EXTERNAL_STORAGE_REQUEST_CODE = 303;       //请求访问外部存储
    public static final String SHARED_PREFERENCE_NAME = "splashVersion";   //SharedPreference操作的文件
    @BindView(R2.id.vp_splash)
    ViewPager vpSplash;
    @BindView(R2.id.ll_splash_point)
    LinearLayout llSplashPoint;
    @BindView(R2.id.iv_enter)
    ImageView ivEnter;
    private int mLoginType;
    private String mLoginUserName;
    private String mLoginPassword;
    private boolean isReturn = false;//是否跳转页面
    private CountDownTimer countDownTimer;
    private int[] imageIds = new int[]{R.drawable.splash1, R.drawable.splash2, R.drawable.splash3,R.drawable.splash4};
    private ImageView prePoint;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_splash_guid);
        EventBusUtils.register(this);
//        checkSelfPermission();
        initViews();
    }

    private void initViews() {
        for (int i = 0; i < imageIds.length; i++) {
            ImageView iv = new ImageView(this);
            iv.setId(i);
            RadioGroup.LayoutParams layoutParams = new RadioGroup.LayoutParams(DensityUtils.dip2px(this, 17), DensityUtils.dip2px(this, 6));
            layoutParams.setMargins(DensityUtils.dip2px(this, 1), 0, DensityUtils.dip2px(this, 1), 0);
            iv.setLayoutParams(layoutParams);
            iv.setImageResource(R.drawable.point);
            iv.setScaleType(ImageView.ScaleType.CENTER);
            llSplashPoint.addView(iv);
        }
        vpSplash.addOnPageChangeListener(this);
        ImgPagerAdapter imgPagerAdapter = new ImgPagerAdapter(this, imageIds);
        vpSplash.setAdapter(imgPagerAdapter);
        vpSplash.setCurrentItem(0);
        if (llSplashPoint.getChildCount() > 0 && llSplashPoint.getChildAt(0) instanceof ImageView) {
            ImageView iv = (ImageView) llSplashPoint.getChildAt(0);
            iv.setImageResource(R.drawable.point_check);
            prePoint = iv;
        }
    }

    public void putInt(Context context, String key, int value) {
        SharedPreferences.Editor editor = context.getSharedPreferences(
                SHARED_PREFERENCE_NAME, Context.MODE_PRIVATE).edit();
        editor.putInt(key, value);
        editor.commit();
    }

    /**
     * 检测内存卡存储权限
     */
    private void checkSelfPermission() {
        if (ContextCompat.checkSelfPermission(mContext, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            //申请READ_EXTERNAL_STORAGE权限
            requestPermissions(new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                    READ_EXTERNAL_STORAGE_REQUEST_CODE,"必然中医需要使用存储权限来初始化使用数据，是否同意使用？");
        }
    }

    /**
     * 初始化数据，判断跳转到哪个界面
     */
    private void nextPage() {
        mLoginType = SharedPreferenceUtils.getInt(this, PublicParams.LOGIN_TYPE);
        mLoginUserName = SharedPreferenceUtils.getString(this, PublicParams.LOGIN_USERNAME);
        mLoginPassword = SharedPreferenceUtils.getString(this, PublicParams.LOGIN_PASSWORD);

        if (TextUtils.isEmpty(mLoginUserName) || mLoginType == 0 /*|| (mLoginType == 1 && TextUtils.isEmpty(mLoginPassword))*/) {
            //如果从引导页面，调用的initData()方法，并且登录信息不全，应立即跳转到登陆页面；其他登录信息不全，3s后跳转到登陆页面
            Intent intent = new Intent(this, LoginActivity.class);
            startActivity(intent);
            finish();
        } else {
            //登录信息完整，进行自动登录
            autoLogin();
        }
    }

    /**
     * 实现app自动登录
     */
    private void autoLogin() {
        NettyClient mNettyClient = AppContext.getInstances().getNettyClient();
        if (mNettyClient == null) {
            mNettyClient = NettyClient.getInstance(mLoginUserName, mLoginPassword);
            AppContext.getInstances().setNettyClient(mNettyClient);
            countDownTimer = new TimeCount(10000, 1000, false);
            countDownTimer.start();
        } else {
            countDownTimer = new TimeCount(5000, 1000, false).start();
            mNettyClient.stop();
        }
        mNettyClient.start();
    }

    /**
     * 自动登录的返回结果
     *
     * @param result 登录结果
     * @sender {@link DataReceiverImpl#sendEventBus(NettyResult)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN) //在ui线程执行
    public void autoLoginEvent(NettyResult result) {
        if (NettyRequestCode.LOGIN.equalsIgnoreCase(result.getCode())) {//登录返回
            if (ResultType.LOGIN_SUCCESS == result.getResultType()) {
                //登录成功，跳转到MainActivity
                if (!isReturn) {
                    boolean isPerfected = SharedPreferenceUtils.getBoolean(this, PublicParams.USER_IS_PERFECTED);
                    Intent intent;
                    if (isPerfected) {
                        if (isLoadData()) {
                            intent = new Intent(this, LoadDataActivity.class);
                        } else {
                            intent = new Intent(this, MainActivity.class);
                        }
                    } else {
                        intent = new Intent(this, PersonalDataActivity.class);
                    }
                    startActivity(intent);
                    finish();
                    isReturn = true;
                }
            } else {
                String desc = "";
                if (TextUtils.isEmpty(result.getResultDesc())) {
                    desc = "登录失败，请检查您的网络是否正常以及用户名和验证码是否正确";
                }
                if (desc.contains("user not exists")) {
                    desc = "该手机号尚未注册！";
                }
                //登录信息不全，跳转到登陆页面
                if (!isReturn) {
                    Intent intent = new Intent(this, LoginActivity.class);
                    startActivity(intent);
                    finish();
                    isReturn = true;
                }
            }
        }
    }

    /**
     * 判断是否加载数据
     *
     * @return
     */
    private boolean isLoadData() {
        ServerConfigDao serverConfigDao = AppContext.getInstances().getDaoSession().getServerConfigDao();
        List<ServerConfig> list = serverConfigDao.loadAll();
        if (list != null && list.size() > 0) {
            ServerConfig config = list.get(0);
            if (config != null) {
                if (!TextUtils.isEmpty(config.getArea_addressVersion()) && !config.getArea_addressVersion().equalsIgnoreCase(config.getArea_addressVersionLocal())) {
                    return true;
                }

                if (!TextUtils.isEmpty(config.getProduct_drugVersion()) && !config.getProduct_drugVersion().equalsIgnoreCase(config.getProduct_drugVersionLocal())) {
                    return true;
                }
            }
        }
        return false;
    }

    @OnClick(R.id.iv_enter)
    public void onViewClicked() {
        //更新版本号
        putInt(SplashGuidActivity.this, "splashVersion", BaseConfig.SPLASH_VERSION);
        nextPage();
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        llSplashPoint.setVisibility(View.VISIBLE);
        ivEnter.setVisibility(View.GONE);
        if (position >= 0 && position < llSplashPoint.getChildCount()) {
            if (llSplashPoint.getChildAt(position) instanceof ImageView) {
                if (prePoint != null) {
                    prePoint.setImageResource(R.drawable.point);
                }
                ImageView iv = (ImageView) llSplashPoint.getChildAt(position);
                iv.setImageResource(R.drawable.point_check);
                prePoint = iv;
            }
            if(position == llSplashPoint.getChildCount()-1){
                llSplashPoint.setVisibility(View.GONE);
                ivEnter.setVisibility(View.VISIBLE);
            }
        }

    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    /**
     * 倒计时
     */
    class TimeCount extends CountDownTimer {

        private final boolean isLogined;

        /**
         * 倒计时
         *
         * @param millisInFuture    总共倒计时的毫秒数
         * @param countDownInterval 多少毫秒数更新一次
         */
        public TimeCount(long millisInFuture, long countDownInterval, boolean isLogined) {
            super(millisInFuture, countDownInterval);
            this.isLogined = isLogined;
        }

        @Override
        public void onTick(long millisUntilFinished) {

        }

        @Override
        public void onFinish() {
            if (!isReturn) {//没有返回登录结果
                Intent intent;
                if (isLogined) {
                    intent = new Intent(SplashGuidActivity.this, MainActivity.class);
                } else {
                    intent = new Intent(SplashGuidActivity.this, LoginActivity.class);
                }
                startActivity(intent);
//                AppContext.getInstances().getNettyClient().setLoginStatusListener(null);
                finish();
                isReturn = true;
            }
        }
    }

    /**
     * 根据版本号判断，判断是否显示引导页面
     */
  /*  private void showSplashGuid() {
        //上一次的版本号
        int beforeVersionCode = SharedPreferenceUtils.getInt(this, PublicParams.BEFORE_VERSION_CODE);
        final int currentVersionCode = AppUtils.getVersionCode(this);//当前版本

        if(beforeVersionCode == 0 || beforeVersionCode<currentVersionCode){ //需要引导页面
            isNeedSplashGuid = true;
            vpSplash.setVisibility(View.VISIBLE);

            ImgPagerAdapter imgPagerAdapter = new ImgPagerAdapter(this);
            imgPagerAdapter.setOnEnterButtonClickListener(new ImgPagerAdapter.OnEnterButtonClickListener() {
                @Override
                public void onEnterButtonClick() {//点击进入主界面或登录页面
                    //更新版本号
                    SharedPreferenceUtils.putInt(LauncherActivity.this,PublicParams.BEFORE_VERSION_CODE,currentVersionCode);
                    initData();
                }
            });
            vpSplash.setAdapter(imgPagerAdapter);
            vpSplash.setCurrentItem(0);


        }else{//不需要引导页面
            vpSplash.setVisibility(View.GONE);

            initData();

        }
    }*/
    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        super.onDestroy();
    }
}
