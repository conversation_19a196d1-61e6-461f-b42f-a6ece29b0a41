package com.doctor.br.activity.photo;

import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.view.clipphoto.ClipViewLayout;
import com.doctor.yy.R;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

/**
 * 头像裁剪Activity
 */
public class ClipImageActivity extends AppCompatActivity implements View.OnClickListener {
    private static final String TAG = "ClipImageActivity";
    private ClipViewLayout clipViewLayout;
    private TextView btnCancel;
    private TextView btnOk;
    //类别 1: qq, 2: weixin
    // private int type;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_clip_image);
//        setActionBarContentView(R.layout.activity_clip_image);
//        setActionBarTitle(getResources().getString(R.string.clip_image));
        // type = getIntent().getIntExtra("type", 1);
        initView();
    }

    /**
     * 初始化组件
     */
    public void initView() {
        clipViewLayout = (ClipViewLayout) findViewById(R.id.clipViewLayout);
        btnCancel = (TextView) findViewById(R.id.btn_cancel);
        ImageView back= (ImageView) findViewById(R.id.iv_back);
        back.setOnClickListener(this);
        btnOk = (TextView) findViewById(R.id.bt_ok);
        btnCancel.setOnClickListener(this);
        btnOk.setOnClickListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.i(TAG, "image uri: "+getIntent().getData());
//        Log.e(">>>>>>>>>>>>>剪切接收","uri==>"+getIntent().getData().toString());
        //if (type == 1) {
        clipViewLayout.setVisibility(View.VISIBLE);

        //设置图片资源
        clipViewLayout.setImageSrc(getIntent().getData());
//        } else {
//            clipViewLayout2.setVisibility(View.VISIBLE);
//            clipViewLayout1.setVisibility(View.GONE);
//            //设置图片资源
//            clipViewLayout2.setImageSrc(getIntent().getData());
//        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.iv_back:
            case R.id.btn_cancel:
                finish();
                break;
            case R.id.bt_ok:
                generateUriAndReturn();
                break;
        }
    }


    /**
     * 生成Uri并且通过setResult返回给打开的activity
     */
    private void generateUriAndReturn() {
        //调用返回剪切图
        //Bitmap zoomedCropBitmap;
        //if (type == 1) {
        Bitmap  zoomedCropBitmap = clipViewLayout.clip();
//        } else {
//            zoomedCropBitmap = clipViewLayout2.clip();
//        }
        if (zoomedCropBitmap == null) {
            Log.e("android", "zoomedCropBitmap == null");
            return;
        }
        Uri mSaveUri = Uri.fromFile(new File(getExternalCacheDir(), "cropped_" + System.currentTimeMillis() + ".jpg"));
        if (mSaveUri != null) {
            OutputStream outputStream = null;
            try {
                outputStream = getContentResolver().openOutputStream(mSaveUri);
                if (outputStream != null) {
                    zoomedCropBitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
                }
            } catch (IOException ex) {
                Log.e("android", "Cannot open file: " + mSaveUri, ex);
            } finally {
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            Intent intent = new Intent();
            intent.setData(mSaveUri);
            setResult(RESULT_OK, intent);
            finish();
        }
    }
}
