package com.doctor.br.activity.manage;

import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;

import com.doctor.br.activity.BaseRefreshActivity;
import com.doctor.br.adapter.manage.BankCardListAdapter;
import com.doctor.br.bean.BankCardBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.view.refreshlayout.RefreshLayout;

import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.ActionBarView;

import cn.bingoogolapple.androidcommon.adapter.BGAOnRVItemClickListener;
import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;

/**
 * 选择银行卡的界面
 */
public class BankCardListActivity extends BaseRefreshActivity<BankCardBean.BankCardItemBean> implements BGAOnRVItemClickListener {

    private RequestCallBack bankCallBack;

    /**
     * 初始化
     */
    @Override
    protected void init() {
        super.init();
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarTitle("选择银行");

        setRefreshEnable(false);//不需要上啦刷新
        mRefreshLayout.setCanLoadMore(false);//不需要加载更多

        //请求数据
        bankCallBack = addHttpPostRequest(HttpUrlManager.BANK_CARD_LIST, RequestParams.getBankCardListParams(), BankCardBean.class, this);

    }

    @Override
    protected void setListener() {//设置监听,在init()之后执行
        super.setListener();
        mRefreshAdapter.setOnRVItemClickListener(this);

    }

    @Override
    public BGARecyclerViewAdapter<BankCardBean.BankCardItemBean> getAdapter() {//返回adapter
        return new BankCardListAdapter(mRvData);
    }


    @Override
    public void onClick(View v) {

    }


    @Override
    public void onRVItemClick(ViewGroup parent, View itemView, int position) {//条目的点击事件
        BankCardBean.BankCardItemBean bankCardItemBean = mRefreshAdapter.getData().get(position);
        Intent intent = new Intent();
        intent.putExtra(BindCardActivity.TYPE_BANK_NAME,bankCardItemBean.getBankName());
        setResult(RESULT_OK,intent);
        finish();
    }

    @Override
    public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {//下拉刷新

    }

    @Override
    public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {//上啦加载更多

    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId){
            case HttpUrlManager.BANK_CARD_LIST:
                if(result.isRequestSuccessed()){
                    BankCardBean bankCardBean =(BankCardBean)result.getBodyObject();
                    if(bankCardBean!=null){
                        onRequestListSuccess(taskId,bankCardBean.getResultList());
                    }
                }else{
                    onRequestListError();
                    RequestErrorToast.showError(this,taskId,result.getCode(),result.getErrorMsg());
                }
                break;
        }
    }

    @Override
    public void onReload() {//网络异常
       bankCallBack =  addHttpPostRequest(HttpUrlManager.BANK_CARD_LIST, RequestParams.getBankCardListParams(),BankCardBean.class,this);//请求数据
    }

    @Override
    protected void onDestroy() {
        cancelRequest(bankCallBack);
        super.onDestroy();
    }
}
