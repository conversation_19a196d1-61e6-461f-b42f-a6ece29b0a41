package com.doctor.br.activity.mine.agent.achievement;

import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.LinearLayout;

import com.doctor.br.adapter.mine.PerformanceEveryMonthAdapter;
import com.doctor.br.bean.AgentPerformanceEveryMonthBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：业绩汇总每个月的展示
 * 创建人：ShiShaoPo
 * 创建时间：2018/5/16
 */

public class PerformanceEveryMonthActivity extends ActionBarActivity  {
    //上个界面传递过来的数据
    public static final String DOCTOR_ID = "doctorId";
    public static final String YEAR_MONTH = "yearMonth";//格式为:yyyy年MM月
    public static final String YEAR_MONTH1 = "yearMonth1";//格式为:yyyy-MM
    private String doctorId, yearMonth, yearMonth1;//医生id和年月
    //界面下的控件
    @BindView(R.id.head_linear)
    LinearLayout headLienar;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private int totalPage = -1;//总页码
    private int currentPage = 1;//当前页码

    private RequestCallBack getListCallBack;//网络请求获取列表回调

    private List<AgentPerformanceEveryMonthBean.ListBean> list;//列表
    private PerformanceEveryMonthAdapter everyMonthAdapter;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_patient_numbers);
        getIntentData(savedInstanceState);
        initView();
        getPerformanceListRequest("1");
    }

    private void getIntentData(Bundle bundle) {
        if (bundle == null) {
            doctorId = getIntent().getStringExtra(DOCTOR_ID);
            yearMonth = getIntent().getStringExtra(YEAR_MONTH);
            yearMonth1 = getIntent().getStringExtra(YEAR_MONTH1);
        } else {
            doctorId = bundle.getString(DOCTOR_ID);
            yearMonth = bundle.getString(YEAR_MONTH);
            yearMonth1 = bundle.getString(YEAR_MONTH1);
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(DOCTOR_ID, doctorId);
        outState.putString(YEAR_MONTH, yearMonth);
        outState.putString(YEAR_MONTH1, yearMonth1);
    }

    private void initView() {
        setActionBarTitle(yearMonth);

        headLienar.setVisibility(View.GONE);

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                getPerformanceListRequest("1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        mLoadingDialog = null;
                        getPerformanceListRequest(currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        bgaRefreshLayout.setRefreshViewHolder(new BGANormalRefreshViewHolder(this, true));

        recyclerView = (RecyclerView) findViewById(R.id.recycler_view);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setHasFixedSize(true);

        list = new ArrayList<>();
        everyMonthAdapter = new PerformanceEveryMonthAdapter(this, list, null);
        recyclerView.setAdapter(everyMonthAdapter);

        emptyView.setEmptyBackgroundColor(ContextCompat.getColor(this, R.color.br_color_white));
    }


    /**
     * 网络请求获取业绩汇总按月份列表
     * <p>
     *
     * @param page 想要请求的页码
     */
    private void getPerformanceListRequest(String page) {
        emptyView.setVisibility(View.GONE);
        bgaRefreshLayout.setVisibility(View.VISIBLE);
        Map<String, String> map = new HashMap<>();
        map.put("doctorId", doctorId);
        map.put("page", page);
        map.put("pageSize", "20");
        map.put("yearMonth", yearMonth1);
        getListCallBack = addHttpPostRequest(HttpUrlManager.PERFORMANCE_EVERY_MONTH, map, AgentPerformanceEveryMonthBean.class, this);
    }

    @Override
    public void onRequestFinished(final String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (bgaRefreshLayout == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.PERFORMANCE_EVERY_MONTH:
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    AgentPerformanceEveryMonthBean agentPerformanaceSummaryBean = (AgentPerformanceEveryMonthBean) result.getBodyObject();
                    totalPage = agentPerformanaceSummaryBean.getTotalPageSize();
                    currentPage = agentPerformanaceSummaryBean.getCurrentPage();
                    if (currentPage == 1) {
                        list.clear();
                    }
                    list.addAll(agentPerformanaceSummaryBean.getList());
                    everyMonthAdapter.notifyDataSetChanged();
                    if (currentPage == 1 && list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setVisibility(View.VISIBLE);
                        bgaRefreshLayout.setVisibility(View.GONE);
                    }
                } else {
                    if (totalPage == -1) {
                        emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                        emptyView.setVisibility(View.VISIBLE);
                        bgaRefreshLayout.setVisibility(View.GONE);
                        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                            @Override
                            public void onReload() {
                                getPerformanceListRequest("1");
                            }
                        });
                    }
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (getListCallBack != null) {
            getListCallBack.cancel();
        }
        super.onDestroy();
    }

}
