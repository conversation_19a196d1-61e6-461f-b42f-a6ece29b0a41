package com.doctor.br.activity.manage;

import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.manage.BlackListRecyclerAdapter;
import com.doctor.br.bean.BlackListBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.NettyMsgNotify;
import com.doctor.br.netty.NotifyType;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：黑名单界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/6
 */

public class BlackListActivity extends ActionBarActivity implements RecyclerItemItemClickListener {
    //界面下的控件
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;
    @BindView(R.id.all_check_linear)
    LinearLayout allCheckLinear;//全选点击框
    @BindView(R.id.all_check_img)
    ImageView allCheckImg;//全选图片
    @BindView(R.id.commit_btn)
    com.doctor.br.view.NoDoubleClickBtn commitBtn;//确认按钮

    private List<BlackListBean.DataBean> list;//黑名单列表
    private BlackListRecyclerAdapter blackListRecyclerAdapter;//黑名单adapter
    private int currentPage = 1;//当前页码
    private int totalPage = -1;//总页码

    private RequestCallBack getBlackListCallBack;//网络请求黑名单回调
    private RequestCallBack resumeCallBack;//网络请求移除黑名单回调

    private boolean isAllSelect;//判断是否全选的标记

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_black_list);
        initView();
        getListRequest("1");
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("黑名单");

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                getListRequest("1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        getListRequest(currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        BGANormalRefreshViewHolder bgaNormalRefreshViewHolder = new BGANormalRefreshViewHolder(this, true);
        bgaNormalRefreshViewHolder.setLoadingMoreText("努力加载中...");
        bgaRefreshLayout.setRefreshViewHolder(bgaNormalRefreshViewHolder);

        list = new ArrayList<>();
        blackListRecyclerAdapter = new BlackListRecyclerAdapter(this, list, this);
        recyclerView.setItemAnimator(null);
        recyclerView.setAdapter(blackListRecyclerAdapter);

        allCheckLinear.setOnClickListener(this);
        commitBtn.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.all_check_linear://更改是否全选的状态
                if (isAllSelect) {
                    //如果是全选的状态下，全部改为未选中状态
                    for (BlackListBean.DataBean dataBean : list) {
                        dataBean.setSelected(false);
                    }
                    switchBackground(false);
                } else {
                    //如果是未全选状态，全部改为选中状态
                    for (BlackListBean.DataBean dataBean : list) {
                        dataBean.setSelected(true);
                    }
                    switchBackground(true);
                }
                blackListRecyclerAdapter.notifyDataSetChanged();
                break;
            case R.id.commit_btn://确认移除黑名单
                //拼接id
                final StringBuilder stringBuilder = new StringBuilder();
                for (BlackListBean.DataBean dataBean : list) {
                    if (dataBean.isSelected()) {
                        stringBuilder.append(",").append(dataBean.getPatientId());
                    }
                }
                if (stringBuilder.length() > 0) {
                    stringBuilder.deleteCharAt(0);
                    if (confirmDialog == null) {
                        confirmDialog = ConfirmDialog.getInstance(this)
                                .setDialogContent("是否移除黑名单？")
                                .setPositiveText("确认")
                                .setNavigationText("取消")
                                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                                    @Override
                                    public void onNavigationBtnClicked(View view) {
                                        confirmDialog.dismiss();
                                    }

                                    @Override
                                    public void onPositiveBtnClicked(View view) {
                                        confirmDialog.dismiss();
                                        resumeRequest(stringBuilder.toString());
                                    }
                                });
                    }
                    confirmDialog.show();
                }
                break;
            default:
                break;
        }
    }

    private ConfirmDialog confirmDialog;//确认移除黑名单对话框

    @Override
    public void itemViewClick(int position, View view) {
        boolean isSelected = list.get(position).isSelected();
        list.get(position).setSelected(!isSelected);
        blackListRecyclerAdapter.notifyItemChanged(position);
        //根据每个item的状态判断是否已经全选
        for (BlackListBean.DataBean dataBean : list) {
            if (!dataBean.isSelected()) {
                switchBackground(false);
                return;
            }
        }
        switchBackground(true);
    }

    /**
     * 切换全选按钮的状态和提交按钮的背景色
     *
     * @param isSelect 全选按钮是否选中
     */
    private void switchBackground(boolean isSelect) {
        //切换全选按钮的状态
        this.isAllSelect = isSelect;
        if (isAllSelect) {
            allCheckImg.setImageResource(R.drawable.black_list_all_check_img);
        } else {
            allCheckImg.setImageResource(R.drawable.black_list_all_uncheck_img);
        }
        //切换提交按钮的背景色
        for (BlackListBean.DataBean dataBean : list) {
            if (dataBean.isSelected()) {
                commitBtn.setBackgroundResource(R.color.br_color_theme);
                return;
            }
        }
        commitBtn.setBackgroundResource(R.color.br_color_split_line);
    }


    /**
     * 网络请求获取黑名单列表
     *
     * @param page 想要请求的页码
     */
    private void getListRequest(String page) {
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
        map.put("page", page);
        map.put("pageSize", "20");
        getBlackListCallBack = addHttpPostRequest(HttpUrlManager.BLACK_LIST, map, BlackListBean.class, this);
    }

    /**
     * 网络请求恢复黑名单
     *
     * @param patientId 移除黑名单的患者id，多个id用逗号分隔
     */
    private void resumeRequest(String patientId) {
        Map<String, String> map = new HashMap<>();
        map.put("patientId", patientId);
        //1 加入黑名单 2 移除黑名单
        map.put("optType", "2");
        resumeCallBack = addHttpPostRequest(HttpUrlManager.REMOVE_BLACK_LIST, map, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (bgaRefreshLayout == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.BLACK_LIST://黑名单列表
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    BlackListBean blackListBean = (BlackListBean) result.getBodyObject();
                    totalPage = blackListBean.getTotalPageSize();
                    currentPage = blackListBean.getCurrentPage();
                    if (currentPage == 1) {
                        list.clear();
                        switchBackground(false);
                    }
                    if (blackListBean.getData() != null) {
                        list.addAll(blackListBean.getData());
                    }
                    blackListRecyclerAdapter.notifyDataSetChanged();
                    if (currentPage == 1 && list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setEmptyText("暂无数据");
                        emptyView.setVisibility(View.VISIBLE);
                    }
                } else {
                    if (totalPage == -1) {
                        emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                        emptyView.setVisibility(View.VISIBLE);
                        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                            @Override
                            public void onReload() {
                                getListRequest("1");
                            }
                        });
                    }
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.REMOVE_BLACK_LIST://移除黑名单列表
                if (result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "移除成功");
                    EventBusUtils.post(new NettyMsgNotify(NotifyType.LOAD_CONTACTS_LIST));
//                    new Handler().postDelayed(new Runnable() {
//                        @Override
//                        public void run() {
                    finish();
//                        }
//                    }, 1000);
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (getBlackListCallBack != null) {
            getBlackListCallBack.cancel();
        }
        if (resumeCallBack != null) {
            resumeCallBack.cancel();
        }
        super.onDestroy();
    }
}
