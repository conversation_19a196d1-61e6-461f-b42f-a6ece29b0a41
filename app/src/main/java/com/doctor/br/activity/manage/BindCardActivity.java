package com.doctor.br.activity.manage;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.app.AppContext;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.UIHelper;
import com.doctor.br.utils.glide.StringUtil;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.BaseViewActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 绑定到银行卡界面
 */
public class BindCardActivity extends BaseViewActivity {

    @BindView(R.id.tv_name)
    TextView tv_name;
    @BindView(R.id.et_number)
    EditText etNumber;
    @BindView(R.id.tv_card_type)
    TextView tvCardType;
    @BindView(R.id.rl_card_type)
    RelativeLayout rlCardType;
    @BindView(R.id.btn_bind_card)
    Button btnBindCard;
    @BindView(R.id.et_clear)
    EditText etClear;

    public static final int REQUEST_BANK_CARD = 15;
    public static final String TYPE_BANK_NAME = "type_bank_name";//卡的类型

    public static final String RESULT_EXTRA_CARD_USERNAME = "cardName";//持卡人名字
    public static final String RESULT_EXTRA_CARD_NUMBER = "cardNumber";//卡的账号
    public static final String RESULT_EXTRA_CARD_TYPE = "cardType";//卡类型
    private String cardType;
    private String accountName;
    private String accountNumber;
    private RequestCallBack bindCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_bind_card);
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarTitle("绑定银行卡");
        initIntentData();
    }

    private void initIntentData() {//初始化数据
        String cardName = getIntent().getStringExtra(RESULT_EXTRA_CARD_USERNAME);//卡的用户
        if ("1".equals(AppContext.getInstances().getLoginInfo().getSupportWxHome())) {
            if (!TextUtils.isEmpty(cardName)) {
                tv_name.setText(cardName);
            } else {
                //要显示当前用户的用户名
                String userName = SharedPreferenceUtils.getString(this, PublicParams.USER_NAME);
                tv_name.setText(userName);
            }
            tv_name.setEnabled(true);
        } else {
            String userName = SharedPreferenceUtils.getString(this, PublicParams.USER_NAME);
            tv_name.setText(userName);
            tv_name.setEnabled(false);
        }

        String cardNumber = getIntent().getStringExtra(RESULT_EXTRA_CARD_NUMBER);//卡号
        if (!TextUtils.isEmpty(cardNumber)) {
            etNumber.setText(cardNumber);
            etNumber.setSelection(cardNumber.length());
        }
        String cardType = getIntent().getStringExtra(RESULT_EXTRA_CARD_TYPE);//卡的类型
        if (!TextUtils.isEmpty(cardType)) {
            tvCardType.setText(cardType);
        }
    }

    @Override
    @OnClick({R.id.rl_card_type, R.id.btn_bind_card/*,R.id.tv_name*/})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.rl_card_type://银行卡类型
                UIHelper.openBankCardListActivity(this, REQUEST_BANK_CARD);
                break;
            case R.id.btn_bind_card://绑定
                /*if(StringUtil.isEmpty(this,tv_name,"请输入持卡人姓名！")){
                    tv_name.requestFocus();
                    return;
                }*/

                if (StringUtil.isEmpty(this, etNumber, "请输入卡号！")) {
                    etNumber.requestFocus();
                    return;
                }
                if (StringUtil.isEmpty(this, tvCardType, "请选择银行！")) {
                    etClear.requestFocus();
                    return;
                }
                cardType = tvCardType.getText().toString().trim();
                accountName = tv_name.getText().toString().trim();
                accountNumber = etNumber.getText().toString().trim();
                //绑定银行卡
                bindCallBack = addHttpPostRequest(HttpUrlManager.BIND_CARD, RequestParams.getBindCardParams(cardType, accountName, accountNumber), ResponseResult.class, this);
                break;
//            case R.id.tv_name:
//                ToastUtils.showShortMsg(this,"请联系客服修改");
//                break;
        }
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.BIND_CARD://绑定银行卡
                if (result.isRequestSuccessed()) {  //请求成功
                    Intent intent = new Intent();
                    intent.putExtra(RESULT_EXTRA_CARD_USERNAME, accountName);
                    intent.putExtra(RESULT_EXTRA_CARD_NUMBER, accountNumber);
                    intent.putExtra(RESULT_EXTRA_CARD_TYPE, cardType);
                    ToastUtils.showShortMsg(this, "绑定成功");
                    setResult(RESULT_OK, intent);
                    finish();

                } else {//网络请求失败
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
        }

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent intent) {
        super.onActivityResult(requestCode, resultCode, intent);
        if (requestCode == REQUEST_BANK_CARD && resultCode == RESULT_OK) {//银行卡类型的返回结果
            String bankCardName = intent.getStringExtra(TYPE_BANK_NAME);
            tvCardType.setText(bankCardName);
        }
    }

    @Override
    protected void onDestroy() {
        cancelRequest(bindCallBack);
        super.onDestroy();
    }
}
