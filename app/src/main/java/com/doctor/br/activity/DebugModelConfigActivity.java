package com.doctor.br.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.yy.R;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.MD5Utils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description debug模式配置参数页面
 * @createTime 2017/12/22
 */

public class DebugModelConfigActivity extends NoActionBarActivity {
    private static final String COUNTERSIGN_KEY = "********************************";
    @BindView(R.id.tvApiHost)
    TextView tvApiHost;
    @BindView(R.id.tvSocketHost)
    TextView tvSocketHost;
    @BindView(R.id.et_countersign)
    EditText etCountersign;
    @BindView(R.id.et_debugParams)
    EditText etDebugParams;
    @BindView(R.id.btn_clear_config)
    Button btnClearConfig;
    @BindView(R.id.btn_apply_config)
    Button btnApplyConfig;
    @BindView(R.id.iv_close)
    ImageView ivClose;
    @BindView(R.id.cb_show_error_code)
    CheckBox cbShowErrorCode;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_debug_model_config);
        initView();
    }

    private void initView() {
        tvApiHost.setText("ApiHost：" + BaseConfig.getHttpBaseUrl());
        tvSocketHost.setText("Socket：" + BaseConfig.getSocketIp());
    }


    @OnClick({R.id.btn_clear_config, R.id.btn_apply_config, R.id.iv_close})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_clear_config:
                SharedPreferenceUtils.putString(this, PublicParams.APP_DEBUG_MODEL_CONFIG, null);
                ToastUtils.showShortMsg(this, "参数配置已清除！");
                this.finish();
                break;
            case R.id.btn_apply_config:
                applyConfig();
                break;
            case R.id.iv_close:
                finish();
                break;
        }
    }

    /**
     * 应用debug模式配置
     */
    private void applyConfig() {
        String countersign = etCountersign.getText().toString().trim();
        String debugParams = etDebugParams.getText().toString().trim();
        if (COUNTERSIGN_KEY.equalsIgnoreCase(MD5Utils.MD5Upper32(countersign))) {
            SharedPreferenceUtils.putBoolean(this, PublicParams.APP_DEBUG_IS_SHOW_ERROR_CODE, cbShowErrorCode.isChecked());
            SharedPreferenceUtils.putString(this, PublicParams.APP_DEBUG_MODEL_CONFIG, debugParams);
            this.finish();
        } else {
            ToastUtils.showShortMsg(this, "请输入正确口令！");
        }
    }

}
