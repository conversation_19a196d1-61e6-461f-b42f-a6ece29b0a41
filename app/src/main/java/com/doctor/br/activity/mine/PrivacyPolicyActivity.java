package com.doctor.br.activity.mine;

import android.content.Context;
import android.content.Intent;

import com.doctor.br.activity.ActionBarWebViewActivity;
import com.doctor.yy.R;

import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;

/**
 * <AUTHOR>
 * @date 4/17/21
 */
public class PrivacyPolicyActivity extends ActionBarWebViewActivity {

    public static void startPrivacyPolicy(Context context){
        Intent intent = new Intent(context,PrivacyPolicyActivity.class);
        intent.putExtra(PublicParams.WEBVIEW_LOAD_URL,BaseConfig.PRIVACY_AGREEMENT);
        intent.putExtra(PublicParams.WEBVIEW_TITLE,context.getString(R.string.privacy_policy_title));
        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE,"");
        context.startActivity(intent);
    }


    public static void startUserAgreement(Context context){
        Intent intent = new Intent(context,PrivacyPolicyActivity.class);
        intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.DOCTOR_AGREEMENT);
        intent.putExtra(PublicParams.WEBVIEW_TITLE,context.getString(R.string.user_agreement_title));
        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE,"");
        context.startActivity(intent);
    }
}
