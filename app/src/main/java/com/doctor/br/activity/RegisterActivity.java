package com.doctor.br.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import androidx.core.content.ContextCompat;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.activity.mine.PersonalDataActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.app.Common;
import com.doctor.br.bean.event.FinishLoginActivityEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.NettyRequestCode;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.NettyResultCode;
import com.doctor.br.netty.client.NettyClient;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.MD5Utils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.HashMap;
import java.util.Map;

import static com.doctor.yy.R.id.agreen_img;

import me.iwf.photopicker.BuildConfig;

/**
 * 类描述：快速注册
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/10 16:50
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/10 16:50
 */

public class RegisterActivity extends ActionBarActivity {

    //界面下的控件
    private EditText invitationCodeEt;//邀请码  22832957
    private EditText phoneEt;//手机号输入框
    private ImageView clearImg;//清空手机号
    private EditText verificationCodeEt;//验证码
    private com.doctor.br.view.NoDoubleClickBtn getCodeBtn;//获取验证码
    private EditText passwordEt;//密码输入框
    private EditText confirmPasswordEt;//确认密码输入框
    private ImageView agreeImg;//是否同意的imgview
    private TextView protocolTv;//协议
    private com.doctor.br.view.NoDoubleClickBtn submitBtn;//确认按钮

    private boolean isAgree;//是否同意协议
    private RequestCallBack getCodeCallBack;//网络请求获取验证码回调
    private RequestCallBack registerCallBack;//网络请求注册回调
    private CountDownTimer countDownTimer;//倒计时

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_register);
        EventBusUtils.register(this);
        if (savedInstanceState != null) {
            isAgree = savedInstanceState.getBoolean("isAgree");
        }
        initView();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putBoolean("isAgree", isAgree);
    }

    private void initView() {
        setActionBarTitle(R.string.register);
        getBaseActionBar().setActionBarChangeListener(this);

        invitationCodeEt = (EditText) findViewById(R.id.invitation_code_et);
        phoneEt = (EditText) findViewById(R.id.phone_et);
        clearImg = (ImageView) findViewById(R.id.clear_img);
        verificationCodeEt = (EditText) findViewById(R.id.verification_code_et);
        getCodeBtn = (com.doctor.br.view.NoDoubleClickBtn) findViewById(R.id.get_code_btn);
        passwordEt = (EditText) findViewById(R.id.password_et);
        confirmPasswordEt = (EditText) findViewById(R.id.confirm_password_et);
        agreeImg = (ImageView) findViewById(agreen_img);
        protocolTv = (TextView) findViewById(R.id.agreement_tv);
        submitBtn = (com.doctor.br.view.NoDoubleClickBtn) findViewById(R.id.commit_btn);

        if (Common.INSTANCE.isHuawei()){
            invitationCodeEt.setHint("请输入邀请码（可选）");
        }

        phoneEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.toString().length() == 0) {
                    clearImg.setVisibility(View.GONE);
                } else {
                    clearImg.setVisibility(View.VISIBLE);
                }
                switchCodeState(s.length() == 11 && getCodeBtn.getText().toString().contains("获取验证码"));
            }
        });

        clearImg.setOnClickListener(this);
        getCodeBtn.setOnClickListener(this);
        agreeImg.setOnClickListener(this);
        protocolTv.setOnClickListener(this);
        submitBtn.setOnClickListener(this);

        countDownTimer = new CountDownTimer(60 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                switchCodeState(false);
                getCodeBtn.setText("重新发送(" + millisUntilFinished / 1000 + "s" + ")");
            }

            @Override
            public void onFinish() {
                switchCodeState(true);
                getCodeBtn.setText(R.string.get_validation_code);
            }
        };
    }


    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.clear_img://清空手机号输入框
                phoneEt.setText("");
                break;
            case R.id.get_code_btn://获取验证码
                String tel = phoneEt.getText().toString();
                if (tel.length() != 11 || getCodeBtn.getText().toString().contains("重新发送")) {
                    return;
                }
                getCodeRequest(tel, "1","2");
                break;
            case agreen_img://是否同意协议
                if (isAgree) {
                    agreeImg.setImageResource(R.drawable.register_not_agree);
                    isAgree = false;
                } else {
                    agreeImg.setImageResource(R.drawable.register_agree);
                    isAgree = true;
                }
                break;
            case R.id.agreement_tv://必然中医协议
                Intent intent1 = new Intent(this, ProtocolActivity.class);
                intent1.putExtra(ProtocolActivity.INVITATION_CODE, invitationCodeEt.getText().toString());//根据邀请码显示协议内容
                startActivity(intent1);
                break;
            case R.id.commit_btn://提交
                String invitationCode = invitationCodeEt.getText().toString();
                if (Common.INSTANCE.isHuawei() && TextUtils.isEmpty(invitationCode)) {
                    invitationCode = "56789";
                } else if (TextUtils.isEmpty(invitationCode)) {
                    ToastUtils.showShortMsg(this, "请输入邀请码");
                    invitationCodeEt.requestFocus();
                    return;
                }
                if (TextUtils.isEmpty(phoneEt.getText())) {
                    ToastUtils.showShortMsg(this, "请输入11位手机号");
                    phoneEt.requestFocus();
                    return;
                }
                if (phoneEt.getText().length() != 11) {
                    ToastUtils.showShortMsg(this, "请输入正确的手机号");
                    phoneEt.requestFocus();
                    return;
                }
                if (TextUtils.isEmpty(verificationCodeEt.getText())) {
                    ToastUtils.showShortMsg(this, "请输入短信验证码");
                    verificationCodeEt.requestFocus();
                    return;
                }
                if (passwordEt.getText().length() < 6) {
                    if (passwordEt.getText().length() == 0) {
                        ToastUtils.showShortMsg(this, "请输入新密码");
                    } else {
                        ToastUtils.showShortMsg(this, "请输入6-20位密码");
                    }
                    passwordEt.requestFocus();
                    return;
                }
                if (TextUtils.isEmpty(confirmPasswordEt.getText())) {
                    ToastUtils.showShortMsg(this, "请再次输入密码");
                    confirmPasswordEt.requestFocus();
                    return;
                }
                if (!confirmPasswordEt.getText().toString().equals(passwordEt.getText().toString())) {
                    ToastUtils.showShortMsg(this, "两次密码不一致");
                    confirmPasswordEt.requestFocus();
                    return;
                }
                if (!isAgree) {
                    ToastUtils.showShortMsg(this, "请阅读服务协议并勾选");
                    return;
                }
                registerRequest(phoneEt.getText().toString(), invitationCode,
                        verificationCodeEt.getText().toString(), passwordEt.getText().toString(),
                        confirmPasswordEt.getText().toString());
                break;
            default:
                break;
        }

    }


    /**
     * 切换获取验证码按钮的状态
     *
     * @param clickable 是否可以点击
     */
    private void switchCodeState(boolean clickable) {
        //根据是否可以点击的状态设置背景和文字颜色
        if (clickable) {
            getCodeBtn.setBackgroundResource(R.drawable.btn_circle_theme);
            getCodeBtn.setTextColor(ContextCompat.getColor(this, R.color.br_color_white));
        } else {
            getCodeBtn.setBackgroundResource(R.drawable.btn_circle_gray_line);
            getCodeBtn.setTextColor(ContextCompat.getColor(this, R.color.br_color_black_999));
        }
    }


    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    private ConfirmDialog confirmDialog;

    @Override
    public void onBackPressed() {
        //如果页面内有输入内容，则退出前弹出提示
        if (!TextUtils.isEmpty(invitationCodeEt.getText()) || !TextUtils.isEmpty(phoneEt.getText()) ||
                !TextUtils.isEmpty(verificationCodeEt.getText()) || !TextUtils.isEmpty(passwordEt.getText())
                || !TextUtils.isEmpty(confirmPasswordEt.getText())) {
            confirmDialog = ConfirmDialog.getInstance(this)
                    .setDialogContent("已填信息未保存，是否退出？")
                    .setPositiveText("退出")
                    .setNavigationText("取消")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
                            confirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            confirmDialog.dismiss();
                            RegisterActivity.super.onBackPressed();
                        }
                    });
            confirmDialog.show();
            return;
        }
        super.onBackPressed();

    }

    /**
     * 网络请求获取验证码
     *
     * @param tel     获取验证码的手机
     * @param type    不知道为啥，反正就是1
     * @param optType 1=包括登陆、忘记密码、安全设置(修改手机号)2=包括注册
     */
    private void getCodeRequest(String tel, String type, String optType) {
        Map<String, String> map = new HashMap<>();
        map.put("tel", tel);
        map.put("type", type);
        map.put("optType", optType);
        getCodeCallBack = addHttpPostRequest(HttpUrlManager.GET_CODE, map, null, this);
    }

    /**
     * 网络请求注册
     *
     * @param tel              注册手机号
     * @param invitationCode   邀请码
     * @param verificationCode 验证码
     * @param password         密码
     * @param confirmPassword  确认密码
     */
    private void registerRequest(String tel, String invitationCode, String verificationCode, String password, String confirmPassword) {
        Map<String, String> map = new HashMap<>();
        map.put("tel", tel);
        map.put("platformCode", invitationCode);
        map.put("code", verificationCode);
        map.put("password", MD5Utils.MD5Upper32(password));
        map.put("confirmPassword", MD5Utils.MD5Upper32(confirmPassword));
        registerCallBack = addHttpPostRequest(HttpUrlManager.REGISTER_CODE, map, null, this);
    }

    /**
     * 注册完成直接登录
     *
     * @param userName 登录账号
     * @param password 登录密码
     */
    private void login(String userName, String password) {
        NettyClient mNettyClient = AppContext.getInstances().getNettyClient();
        if (mNettyClient == null) {
            mNettyClient = NettyClient.getInstance(userName, password);
            AppContext.getInstances().setNettyClient(mNettyClient);
        } else {
            mNettyClient.setUsername(userName);
            mNettyClient.setPassword(password);
            AppContext.getInstances().setNettyClient(mNettyClient);
        }
        mNettyClient.stop();
        mNettyClient.start();
        if (mLoadingDialog != null && !mLoadingDialog.isShowing()) {
            mLoadingDialog.show();
        }
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        if(phoneEt==null){
            return;
        }
        switch (taskId) {
            case HttpUrlManager.GET_CODE://获取验证码
                if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                    mLoadingDialog.dismiss();
                }
                if (result.isRequestSuccessed()) {
                    countDownTimer.start();
                    ToastUtils.showShortMsg(this, "验证码已发送");
                } else {
                    RequestErrorToast.showError(mContext,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.REGISTER_CODE://注册回调
                if (result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "注册成功");
                    login(phoneEt.getText().toString(), passwordEt.getText().toString());
                } else {
                    RequestErrorToast.showError(mContext,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 消息登录返回结果处理方法
     *
     * @param result 返回结果
     * @sender {@link com.doctor.br.netty.impl.DataReceiverImpl#sendEventBus(NettyResult)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN) //在ui线程执行
    public void onLoginEvent(NettyResult result) {
        if (NettyRequestCode.LOGIN.equalsIgnoreCase(result.getCode())) {
            if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                mLoadingDialog.dismiss();
            }
            if (NettyResultCode.SUCCESS.equalsIgnoreCase(result.getResultCode())) {
                AppContext.getInstances().getNettyClient().setPassword(null);
                AppContext.getInstances().saveLoginAccount(1, phoneEt.getText().toString(), null, true);
                startActivity(new Intent(this, PersonalDataActivity.class));
                closeLoginActivity();
                finish();
            } else {
                NettyClient mNettyClient = AppContext.getInstances().getNettyClient();
                if (mNettyClient != null) {
                    mNettyClient.stop();
                }
                String desc = result.getResultDesc();
                if (TextUtils.isEmpty(result.getResultDesc())) {
                    desc = "登录失败，请检查您的网络是否正常以及用户名和验证码是否正确";
                }
                if (desc.contains("user not exists")) {
                    desc = "该手机号尚未注册！";
                }
                ToastUtils.showShortMsg(this, desc + "");
                finish();
            }
        }

    }

    /**
     * 自动登录成功后，关闭登录界面
     *
     * @send {@link RegisterActivity#closeLoginActivity()}
     * @receive {@link LoginActivity#finishThisActivity(FinishLoginActivityEvent)}
     */
    private void closeLoginActivity() {
        EventBusUtils.post(new FinishLoginActivityEvent(true));
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        if (getCodeCallBack != null) {
            getCodeCallBack.cancel();
        }
        if (registerCallBack != null) {
            registerCallBack.cancel();
        }
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        if (confirmDialog != null) {
            confirmDialog.dismiss();
        }
        super.onDestroy();
    }


}
