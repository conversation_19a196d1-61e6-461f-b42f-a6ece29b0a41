package com.doctor.br.activity.manage;

import android.os.Bundle;
import android.text.TextUtils;
import android.widget.TextView;

import com.doctor.br.bean.MedicineDetailsBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：药品详情
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/4
 */

public class MedicineDetailsActivity extends ActionBarActivity {
    //上个界面传递过来的数据
    public static final String DRUG_ID = "drug_id";
    private String id;//药品id
    //界面下的控件
    @BindView(R.id.drug_name_tv)
    TextView drugNameTv;
    @BindView(R.id.component_tv)
    TextView componentTv;
    @BindView(R.id.taste_tv)
    TextView tasteTv;
    @BindView(R.id.function_tv)
    TextView functionTv;
    @BindView(R.id.using_method_tv)
    TextView usingMethodTv;
    @BindView(R.id.disable_drug_tv)
    TextView disableDrugTv;
    @BindView(R.id.attention_tv)
    TextView attentionTv;
    @BindView(R.id.application_site_tv)
    TextView applicationSiteTv;
    @BindView(R.id.preparation_pieces_tv)
    TextView preparationPiecesTv;
    @BindView(R.id.storage_tv)
    TextView storageTv;

    private RequestCallBack detailCallBack;//网络请求药品详情回调

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_medicine_details);
        getIntentData(savedInstanceState);
        getBaseActionBar().setActionBarTitle("药品详情");
        getMedicineDetailRequest(id);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(DRUG_ID, id);
    }

    private void getIntentData(Bundle savedInstanceState) {
        if (savedInstanceState == null) {
            id = getIntent().getStringExtra(DRUG_ID);
        } else {
            id = savedInstanceState.getString(DRUG_ID);
        }
        if (TextUtils.isEmpty(id)) {
            ToastUtils.showShortMsg(this, "数据错误");
            finish();
        }
    }


    /**
     * 网络请求获取药品详情，根据药品id
     *
     * @param id 药品id
     */
    private void getMedicineDetailRequest(String id) {
        Map<String, String> map = new HashMap<>();
        map.put("detailId", id);
        detailCallBack = addHttpPostRequest(HttpUrlManager.MEDICINE_DETAILS, map, MedicineDetailsBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if(drugNameTv==null){
            return;
        }
        switch (taskId) {
            case HttpUrlManager.MEDICINE_DETAILS://药品详情
                if (result.isRequestSuccessed()) {
                    MedicineDetailsBean medicineDetailsBean = (MedicineDetailsBean) result.getBodyObject();
                    drugNameTv.setText(setData(medicineDetailsBean.getDrugName()));
                    componentTv.setText(setData(medicineDetailsBean.getComponent()));
                    tasteTv.setText(setData(medicineDetailsBean.getTaste()));
                    functionTv.setText(setData(medicineDetailsBean.getFunction()));
                    usingMethodTv.setText(setData(medicineDetailsBean.getUseingMethod()));
                    disableDrugTv.setText(setData(medicineDetailsBean.getDisableDurg()));
                    attentionTv.setText(setData(medicineDetailsBean.getAttention()));
                    applicationSiteTv.setText(setData(medicineDetailsBean.getApplicationSite()));
                    preparationPiecesTv.setText(setData(medicineDetailsBean.getPreparationPieces()));
                    storageTv.setText(setData(medicineDetailsBean.getStorage()));
                } else {
                    RequestErrorToast.showError(this,taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    //处理当没有数据时用无来替代
    private String setData(String s) {
        if (TextUtils.isEmpty(s)) {
            return "无";
        }
        return s;
    }

    @Override
    protected void onDestroy() {
        if (detailCallBack != null) {
            detailCallBack.cancel();
        }
        super.onDestroy();
    }
}
