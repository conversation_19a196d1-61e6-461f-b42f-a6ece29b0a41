package com.doctor.br.activity.manage;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.doctor.br.activity.medical.AddMedicineActivity;
import com.doctor.br.activity.medical.CustomBoilyWayActivity;
import com.doctor.br.adapter.medical.MedicineHLvAdapter;
import com.doctor.br.bean.CommonPrescriptionBean;
import com.doctor.br.bean.PopItem;
import com.doctor.br.bean.medical.BoilWayBean;
import com.doctor.br.bean.medical.MedicineDetailMsgBean;
import com.doctor.br.bean.medical.MedicineOrTempleBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.NettyResultCode;
import com.doctor.br.netty.client.NettyUtils;
import com.doctor.br.netty.impl.DataReceiverImpl;
import com.doctor.br.netty.model.ResultType;
import com.doctor.br.utils.DecimalUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.view.BRGradientView;
import com.doctor.br.view.BottomPopWindow;
import com.doctor.br.view.CustomShowPopup;
import com.doctor.br.view.FlowLayout;
import com.doctor.br.view.HorizontalListView;
import com.doctor.br.view.NumberKeyboardView;
import com.doctor.br.view.TableLayout;
import com.doctor.br.view.TemplateListPopWindow;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.listener.OnButtonClickListener;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.ActivityManager;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.InputDialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Author:sunxiaxia
 * createdDate:2017/12/14
 * description:新增常用方
 */

public class AddCommonPrescriptionActivity extends ActionBarActivity implements NumberKeyboardView.OnViewTouchListener, NumberKeyboardView.OnKeyDownListener {
    @BindView(R2.id.medicines_table)
    TableLayout mMedicinesTable;
    @BindView(R2.id.medicine_scrollview)
    ScrollView mMedicineScrollview;
    @BindView(R2.id.medicine_container_ll)
    LinearLayout mMedicineContainerLl;
    @BindView(R2.id.down_arrow)
    ImageView mDownArrow;
    @BindView(R2.id.up_slide_medicines)
    FlowLayout mUpSlideMedicines;
    @BindView(R2.id.up_slide_medicines_sl)
    ScrollView mUpSlideMedicinesSl;
    @BindView(R2.id.up_slide_medicines_rl)
    RelativeLayout rlUpSlideMedicines;
    @BindView(R2.id.up_arrow)
    ImageView mUpArrow;
    @BindView(R2.id.horizontal_lv)
    HorizontalListView mHorizontalLv;
    @BindView(R2.id.input_et)
    EditText mInputEt;
    @BindView(R2.id.num_keyboard)
    NumberKeyboardView numKeyboard;
    @BindView(R2.id.bottom_views)
    LinearLayout mBottomViews;
    @BindView(R2.id.layout_root)
    RelativeLayout mLayoutRoot;
    @BindView(R2.id.rl_horizontal_layout)
    RelativeLayout rlHorizontalLayout;

    private static final String isClick = "ISCLICK";
    private static final String isLongClick = "ISLONGCLICK";
    private static final String ADDMEDICINETYPE_ADD = "1";//添加药材
    //    private static final String ADDMEDICINETYPE_CHANGE = "2";//修改剂型
    private static final String ADDMEDICINETYPE_TEMPLATE = "3";//调模板
    private String mTitle;
    private List<PopItem> boilPopItems;
    private ArrayList<MedicineDetailMsgBean> mMedicineList;
    private List<MedicineOrTempleBean> mMedicineOrTemples;
    private List<String> mMedicineIdList;
    private ConfirmDialog mConfirmDialog;
    private BottomPopWindow mBottomPopWindow;
    private InputDialog mDialog;
    private String mCustomBoilWay;
    private MedicineHLvAdapter mMedicineHLvAdapter;
    private String templateName;
    private TemplateListPopWindow templateListPop;
    private String medicineId;
    private String minSaleUnit;
    private String addMedicineType = "";//1添加药材，2是修改剂型,3调模板
    private InputDialog inputDialog;
    private String mTemplateId = "";
    private String mTemplateName;
    private RequestCallBack saveCommonTemplateCallBack, getBoilWayCallBack, addBoilWayCallBack;

    // 在类的开头添加编辑模式枚举
    public enum EditMode {
        NORMAL,     // 正常模式
        FORWARD,    // 向前插入
        BACKWARD,   // 向后插入
        REPLACE     // 替换模式
    }

    // 添加编辑模式相关的成员变量
    private EditMode mEditMode = EditMode.NORMAL;
    private int mEditIndex = -1;
    private BRGradientView mEditGradientView;

    // 其他需要的全局变量
    private CustomShowPopup customShowPopup;

    private ActivityResultLauncher<Intent> launcher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == Activity.RESULT_OK) {
                    LogUtils.i(AddMedicineActivity.class, "result.getData()====" + result.getData());
                    Intent data = result.getData();
                    if (mBottomPopWindow != null && mBottomPopWindow.isShowing()){
                        refreshBoilWayData();
                    }
                }
            }
    );


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_add_common_prescription);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mTitle = getIntent().getStringExtra("title");
        setActionBarTitle(mTitle);
        setActionBarRightBtnText("保存");
        EventBusUtils.register(this);
        initViewData();

        //设置为双列显示
        mMedicinesTable.setColumnCount(2, false);

        numKeyboard.setOnKeyDownListener(this);
        numKeyboard.setOnViewTouchListener(this);

        try {
            if ((CommonPrescriptionBean.DatalistBean) getIntent().getSerializableExtra("templateMedicine") != null) {
                CommonPrescriptionBean.DatalistBean templateMsg = (CommonPrescriptionBean.DatalistBean) getIntent().getSerializableExtra("templateMedicine");
                mTemplateId = templateMsg.getTemplateId();
                mTemplateName = templateMsg.getName();
                ArrayList<MedicineDetailMsgBean> templateMedicine = (ArrayList<MedicineDetailMsgBean>) templateMsg.getList();
                addMedicineType = ADDMEDICINETYPE_TEMPLATE;
                addMedicineView(templateMedicine, mMedicinesTable, addMedicineType);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 显示编辑模式修改的弹窗方式
     */
    private void showEditModePop(int position, BRGradientView gradientView) {
        hideKeyBoard(mInputEt);

        //popItem数组
        List<PopItem> items = new ArrayList<>();
        PopItem popItem = new PopItem();
        popItem.setName("向前插入");
        popItem.setPosition(0);
        items.add(popItem);

        popItem = new PopItem();
        popItem.setName("向后插入");
        popItem.setPosition(1);
        items.add(popItem);

        popItem = new PopItem();
        popItem.setName("替换");
        popItem.setPosition(2);
        items.add(popItem);

        mBottomPopWindow.setPopTitle("编辑模式");
        mBottomPopWindow.setPopContentData(items);
        mBottomPopWindow.setBottomViewVisible();
        mBottomPopWindow.setBottomText("正常");
        mBottomPopWindow.showPop();
        mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
            @Override
            public void onPopItemClick(int tag, PopItem popItem) {
                int index = popItem.getPosition();

                mBottomPopWindow.dimissPop();

                //修改编辑模式
                if (index == 0) {
                    mEditMode = EditMode.FORWARD;
                } else if (index == 1) {
                    mEditMode = EditMode.BACKWARD;
                } else if (index == 2) {
                    mEditMode = EditMode.REPLACE;
                }

                //当前编辑的位置
                mEditIndex = position;

                if (mEditGradientView != null) {
                    mEditGradientView.setVisibility(View.GONE);
                }
                mEditGradientView = gradientView;
                gradientView.setVisibility(View.VISIBLE);
            }
        });

        mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
            @Override
            public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                //恢复正常
                mEditMode = EditMode.NORMAL;
                mEditIndex = -1;
                mBottomPopWindow.dimissPop();

                if (mEditGradientView != null) {
                    mEditGradientView.setVisibility(View.GONE);
                    mEditGradientView = null;
                }
                gradientView.setVisibility(View.GONE);
            }
        });
    }

    /**
     * 初始化数据
     */
    private void initViewData() {
        mBottomPopWindow = new BottomPopWindow(this);
        boilPopItems = new ArrayList<>();
        mMedicineList = new ArrayList<>();
        mMedicineOrTemples = new ArrayList<>();
        mMedicineIdList = new ArrayList<>();//存放已添加的药材的id，用于去重操作

        getBoilWay();

        mMedicineHLvAdapter = new MedicineHLvAdapter(this, mMedicineOrTemples, R.layout.medicine_hlv_item_layout,MedicineHLvAdapter.TYPE_ADD_TEMPLATE_MEDICINE);
        mHorizontalLv.setAdapter(mMedicineHLvAdapter);
        mHorizontalLv.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int i, long id) {
                String type = mMedicineOrTemples.get(i).getType();
                String ids = mMedicineOrTemples.get(i).getId();
                requestMedicineOrTemplateMsg(ids, type, isClick);

            }
        });
        mHorizontalLv.setOnItemLongClickListener(new AdapterView.OnItemLongClickListener() {
            @Override
            public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
                // 显示药方列表
                String type = mMedicineOrTemples.get(position).getType();
                if (("2").equals(type) || ("3").equals(type)) {
                    templateName = mMedicineOrTemples.get(position).getName();
                    NettyUtils.getCommTemplateDetailMsg(mMedicineOrTemples.get(position).getId(), type, isLongClick/*, PublicParams.DOSAGEFORM_SLICES_Y, PublicParams.DRUG_PROVIDERID_10*/);
                }
                return true;
            }
        });
//添加输入框输入监听事件并实时检索药材
        addInputEtTextChangedListener();
    }

    /**
     * 添加输入框输入监听事件并实时检索药材
     */
    private void addInputEtTextChangedListener() {
        mInputEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    NettyUtils.searchMedicines(s.toString().trim(), "", "", "");
                } else {
                    mMedicineOrTemples.clear();
                    mMedicineHLvAdapter.updateData(mMedicineOrTemples);
                    mUpSlideMedicines.removeAllViews();
                }

            }
        });
    }

    /**
     * @param scroll
     * @param inner
     */
    public void scrollToBottom(final View scroll, final View inner) {
        Handler mHandler = new Handler();
        mHandler.postDelayed(new Runnable() {
            public void run() {
                if (scroll == null || inner == null) {
                    return;
                }
                int offset = inner.getMeasuredHeight() - scroll.getHeight();
                if (offset < 0) {
                    offset = 0;
                }

                scroll.scrollTo(0, offset);
            }
        }, 600);
    }

    @Override
    public void onRightBtnClick(View view) {
        boolean isSave = true;
        if (mMedicineList.isEmpty()) {
            ToastUtils.showShortMsg(mContext, "请添加药材");
        } else {
            for (int i = 0; i < mMedicineList.size(); i++) {
                if (TextUtils.isEmpty(mMedicineList.get(i).getDose()) ||
                        "0".equals(mMedicineList.get(i).getDose()) ||
                        "0.00".equals(mMedicineList.get(i).getDose()) ||
                        "0.0".equals(mMedicineList.get(i).getDose())) {
                    isSave = false;
                    ToastUtils.showShortMsg(mContext, "请输入“" + mMedicineList.get(i).getDrugName() + "”剂量");
                    break;
                }
            }
            if (isSave) {
                showTemplateNameInputPop();
            }
        }
    }

    /**
     * 显示模板名称输入框
     */
    private void showTemplateNameInputPop() {
        if (inputDialog == null) {
            inputDialog = InputDialog.getInstance(this);
        }
        inputDialog.setInputTitle("常用方名称");
        if (!TextUtils.isEmpty(mTemplateName)) {
            inputDialog.setInputContent(mTemplateName);
        }
        inputDialog.setInputHint("请输入常用方名称（10字内）");
        inputDialog.setInputMaxLength(10);
        inputDialog.show();
        inputDialog.setOnButtonClickListener(new OnButtonClickListener() {
            @Override
            public void onPositiveClick(View view, CharSequence editText) {
                String name = editText.toString().trim();
                if (TextUtils.isEmpty(name)) {
                    ToastUtils.showShortMsg(mContext, "请输入常用方名称！");
                } else {
                    saveCommonTemplateData(jsonToStr(mMedicineList, name, mTemplateId));
                }

            }

            @Override
            public void onNegativeClick(View view, CharSequence editText) {
                inputDialog.dismiss();
            }
        });
    }

    /**
     * @param list
     * @param name
     * @return 生成保存药方参数
     */
    public String jsonToStr(List<MedicineDetailMsgBean> list, String name, String templateId) {
        String userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        String jsonStr =
                " {" +
                        " \"templateName\":\"" + name + '\"' +
                        ", \"list\":" + list +
                        ", \"userId\":\"" + userId + '\"' +
                        ", \"templateId\":\"" + templateId + '\"' +
                        '}';
        return jsonStr;

    }

    /**
     * @param data 保存药方
     */
    public void saveCommonTemplateData(String data) {
        HashMap map = new HashMap();
        map.put("template", data);
        saveCommonTemplateCallBack = addHttpPostRequest(HttpUrlManager.ADD_COMMON_TEMPLATE, map, ResponseResult.class, this);
    }

    /**
     * @param id   药材或者药方的id
     * @param type 药材或药方 1.药材，2.常用方，3.经典方
     */
    private void requestMedicineOrTemplateMsg(String id, String type, String taskId) {
        if (!TextUtils.isEmpty(type) && "1".equals(type)) {//药材
            NettyUtils.getCommMedicineDetailMsg(/*PublicParams.DOSAGEFORM_SLICES_Y,*/ id/*, PublicParams.DRUG_PROVIDERID_10*/);
        } else if (!TextUtils.isEmpty(type) && ("2".equals(type) || "3".equals(type))) {//2.常用方 3、经典方
            NettyUtils.getCommTemplateDetailMsg(id, type, taskId/*, PublicParams.DOSAGEFORM_SLICES_Y, PublicParams.DRUG_PROVIDERID_10*/);
        }
    }

    /**
     * 获取药材煎法列表数据
     */
    private void getBoilWay() {
        getBoilWayCallBack = addHttpPostRequest(HttpUrlManager.BOIL_WAY, null, BoilWayBean.class, this);
    }

    /**
     * 获取特殊煎法数据
     */
    private List<PopItem> initBoilWays(List<String> boilWays, List<String> customBoilWays) {
        boilPopItems.clear();
        if (customBoilWays != null && boilWays.size() > 0) {
            for (int i = 0; i < customBoilWays.size(); i++) {
                PopItem item = new PopItem();
                item.setPosition(i);
                item.setName(customBoilWays.get(i));
                boilPopItems.add(item);
            }
        }
        if (boilWays != null && boilWays.size() > 0) {
            for (int i = 0; i < boilWays.size(); i++) {
                PopItem item = new PopItem();
                item.setPosition(i);
                item.setName(boilWays.get(i));
                boilPopItems.add(item);
            }
        }

        return boilPopItems;
    }

    /**
     * @param title 自定义煎药方式
     */
    private void addBoilWay(String title) {
        HashMap map = new HashMap();
        map.put("title", title);
        addBoilWayCallBack = addHttpPostRequest(HttpUrlManager.ADD_BOIL_WAY, map, ResponseResult.class, this);
    }

    /**
     * 点击药材名称选择特殊煎法
     *
     * @param name_tv  显示药材名称view
     * @param boil_tv
     * @param medicine
     */
    private void onNameClick(View name_tv, final View boil_tv, final MedicineDetailMsgBean medicine) {
        if (name_tv != null) {
            // 先移除已有的点击监听器，避免重复添加
            name_tv.setOnClickListener(null);

            name_tv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (boilPopItems != null && boilPopItems.size() > 0) {
                        mBottomPopWindow.setPopTitle(medicine.getDrugName());
                        mBottomPopWindow.setPopContentData(boilPopItems);
                        mBottomPopWindow.showPop();
                        mBottomPopWindow.setBottomViewVisible();
                        mBottomPopWindow.setBottomText("自定义");
                        mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                            @Override
                            public void onPopItemClick(int tag, PopItem popItem) {
                                upDataUseMethod(popItem.getName(), medicine, boil_tv);
                                mBottomPopWindow.dimissPop();
                            }
                        });

                        mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
                            @Override
                            public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                                Intent intent = new Intent(mContext, CustomBoilyWayActivity.class);
                                launcher.launch(intent);
                            }
                        });
                    } else {
                        getBoilWay();
                    }
                }
            });
        }
    }
    /**
     * 显示添加自定义特殊煎药方式弹框
     */
    private void showCustomBoilDialog() {
        //自定义特殊煎法
        if (mDialog == null) {
            mDialog = InputDialog.getInstance(AddCommonPrescriptionActivity.this);
        }
        mDialog.setInputTitle("自定义煎法");
        mDialog.setInputHint("请输入煎法");
        mDialog.show();
        mDialog.setOnButtonClickListener(new OnButtonClickListener() {
            @Override
            public void onPositiveClick(View view, CharSequence editText) {
                if (TextUtils.isEmpty(editText.toString())) {
                    ToastUtils.showShortMsg(mContext, "请输入特殊煎药方式");
                } else {
                    if (!stringFilter(editText.toString())) {
                        ToastUtils.showShortMsg(mContext, "只支持输入汉字、数字");
                    } else {
                        mCustomBoilWay = editText.toString().trim();
                        addBoilWay(editText.toString().trim());
                    }
                }
            }

            @Override
            public void onNegativeClick(View view, CharSequence editText) {
                mDialog.dismiss();
            }
        });
    }

    /**
     * 限制输入框只输入汉字，数字
     *
     * @param str
     * @return
     * @throws PatternSyntaxException
     */
    public boolean stringFilter(String str) throws PatternSyntaxException {
        // 只允许数字和汉字,空格
        String regEx = "[\\d\u4E00-\u9FA5\\s]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.matches();

    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.BOIL_WAY:
                if (result.isRequestSuccessed()) {
                    BoilWayBean boilWayBean = (BoilWayBean) result.getBodyObject();
                    if (boilWayBean != null) {
                        List<String> boilWays = boilWayBean.getDecoction1();
                        List<String> customBoilWays = boilWayBean.getDecoction2();

                        // 重新初始化煎法列表
                        boilPopItems.clear();

                        // 添加自定义煎法
                        if (customBoilWays != null && customBoilWays.size() > 0) {
                            for (String customWay : customBoilWays) {
                                PopItem item = new PopItem();
                                item.setName(customWay);
                                item.setCustom(true);
                                boilPopItems.add(item);
                            }
                        }

                        // 添加普通煎法
                        if (boilWays != null && boilWays.size() > 0) {
                            for (String way : boilWays) {
                                PopItem item = new PopItem();
                                item.setName(way);
                                item.setCustom(false);
                                boilPopItems.add(item);
                            }
                        }

                        // 如果弹窗还在显示，则更新弹窗内容
                        if (mBottomPopWindow != null && mBottomPopWindow.isShowing()) {
                            mBottomPopWindow.upDatePopContent(boilPopItems);
                        }
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.ADD_BOIL_WAY:
                if (result.isRequestSuccessed()) {
                    if (mDialog != null && mDialog.isShowing()) {
                        mDialog.dismiss();
                    }
                    //更新煎药方式数据
                    PopItem item = new PopItem();
                    item.setName(mCustomBoilWay);
                    if (boilPopItems.size() > 1) {
                        boilPopItems.remove(1);
                    }
                    boilPopItems.add(0, item);
                    mBottomPopWindow.upDatePopContent(boilPopItems);
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.ADD_COMMON_TEMPLATE://添加或编辑常用方成功
                if (result != null && result.isRequestSuccessed()) {
                    if (inputDialog.isShowing()) {
                        inputDialog.dismiss();
                    }
                    ToastUtils.showShortMsg(mContext, "保存成功");
                    //setResult(RESULT_OK);
                    //发送更新常用方的EventBus
                    CommonPrescriptionBean.DatalistBean datalistBean = new CommonPrescriptionBean.DatalistBean();
                    datalistBean.setCode("0000");
                    EventBusUtils.post(datalistBean);
                    ActivityManager.getInstance().removeActivity(SearchPrescriptionActivity.class);
                    finishActivity();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
        }
    }

    /**
     * 更新药材煎药方式
     *
     * @param boilWay
     * @param medicine
     * @param boil_tv
     */
    private void upDataUseMethod(String boilWay, MedicineDetailMsgBean medicine, View boil_tv) {
        if (!TextUtils.isEmpty(boilWay) && !"无".equals(boilWay)) {
            medicine.setUseMethod(boilWay);//更新已添加的药材中的数据
            //更新布局显示
            boil_tv.setVisibility(View.VISIBLE);
            ((TextView) boil_tv).setText(boilWay);//更新布局显示
        } else {
            medicine.setUseMethod("");//更新已添加的药材中的数据
            //更新布局显示
            boil_tv.setVisibility(View.GONE);
        }
    }

    /**
     * 关闭页面
     */
    private void finishActivity() {
        hideKeyBoard(mInputEt);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                AddCommonPrescriptionActivity.this.finish();
            }
        }, 300);
    }

    /**
     * @param cotentData
     * @param containers 显示 向上的view数据
     */
    private void showUpViewData(List<MedicineOrTempleBean> cotentData, FlowLayout containers) {
        if (cotentData != null && cotentData.size() > 0) {
            containers.removeAllViews();
            for (int i = 0; i < cotentData.size(); i++) {
                final String type = mMedicineOrTemples.get(i).getType();
                final String ids = mMedicineOrTemples.get(i).getId();
                TextView tv = getMedicineTextView(cotentData.get(i).getName(), type);


                tv.setOnClickListener(new View.OnClickListener() {//点击名称请求详情
                    @Override
                    public void onClick(View v) {
                        requestMedicineOrTemplateMsg(ids, type, "isClick");
                    }
                });
                final int finalI = i;
                tv.setOnLongClickListener(new View.OnLongClickListener() {
                    @Override
                    public boolean onLongClick(View v) {
                        String type = mMedicineOrTemples.get(finalI).getType();
                        if (("2").equals(type) || ("3").equals(type)) {
                            templateName = mMedicineOrTemples.get(finalI).getName();
                            NettyUtils.getCommTemplateDetailMsg(mMedicineOrTemples.get(finalI).getId(), type, isLongClick/*, PublicParams.DOSAGEFORM_SLICES_Y, PublicParams.DRUG_PROVIDERID_10*/);
                        }
                        return true;
                    }
                });
                containers.addView(tv);
            }
        } else {
            return;
        }
    }

    /**
     * @param text
     * @return 动态添加textView 此方法用于点击向上箭头显示药材
     */
    @NonNull
    private TextView getMedicineTextView(String text, String type) {
        TextView tv = new TextView(this);
        FlowLayout.LayoutParams params = new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT, FlowLayout.LayoutParams.WRAP_CONTENT);
        params.setMargins(DensityUtils.dip2px(mContext, 5), DensityUtils.dip2px(mContext, 5), DensityUtils.dip2px(mContext, 5), DensityUtils.dip2px(mContext, 5));
        tv.setLayoutParams(params);
        tv.setPadding(DensityUtils.dip2px(mContext, 8), DensityUtils.dip2px(mContext, 5), DensityUtils.dip2px(mContext, 7), DensityUtils.dip2px(mContext, 5));
        tv.setTextColor(getResources().getColor(R.color.br_color_theme_text));
        tv.setBackgroundResource(R.drawable.bg_white_radius3);
        tv.setText(text);
        tv.setTextSize(15);
        if (!TextUtils.isEmpty(type)) {
            if ("2".equals(type) || "3".equals(type)) {
                tv.setTextColor(getResources().getColor(R.color.br_color_theme));
            } else if ("1".equals(type)) {
                tv.setTextColor(mContext.getResources().getColor(R.color.br_color_theme_text));
            }
        }

        //            tv.setTextSize(TypedValue.COMPLEX_UNIT_PX,DensityUtils.sp2px(mContext,15));
        return tv;
    }

    /**
     * @param medicines      药材列表
     * @param medicinesTable 药材容器
     *                       添加药材显示view
     */
    /**
     * @param medicines      药材列表
     * @param medicinesTable 药材容器
     *                       添加药材显示view
     */
    private void addMedicineView(final List<MedicineDetailMsgBean> medicines, final TableLayout medicinesTable, String addMedicineType) {
        for (int i = 0; i < medicines.size(); i++) {
            final View view = LayoutInflater.from(this).inflate(R.layout.medicine_gv_item_layout, null);
            ImageView delete_img = ((ImageView) view.findViewById(R.id.delete_img));
            TextView name_tv = ((TextView) view.findViewById(R.id.name_tv));
            TextView unit_tv = ((TextView) view.findViewById(R.id.unit_tv));
            TextView understock_tv = ((TextView) view.findViewById(R.id.understock_tv));//库存暂缺
            TextView boil_medicine_tv = ((TextView) view.findViewById(R.id.boil_medicine_tv));//煎药备注
            final EditText doseEt = ((EditText) view.findViewById(R.id.dose_et));
            medicineId = medicines.get(i).getDrugId();
            minSaleUnit = medicines.get(i).getMinSaleUnit();

            //显示药材信息
            SetMedicineMsg(medicines, addMedicineType, i, name_tv, unit_tv, understock_tv, boil_medicine_tv, doseEt);
            //添加每味药材
            addMedicineViewItem(medicines, medicinesTable, addMedicineType, i, view, boil_medicine_tv, doseEt);

            // 先设置药材名称的长按事件
            name_tv.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    //显示弹窗，编辑方式修改
                    BRGradientView gradientView = view.findViewById(R.id.gradient_view);
                    showEditModePop(medicinesTable.indexOfChild(view), gradientView);
                    return true;  // 返回true确保不会触发点击事件
                }
            });

            // 设置点击事件
            onNameClick(name_tv, boil_medicine_tv, medicines.get(i));

            //删除某味药材
            delete_img.setOnClickListener(new View.OnClickListener() {//删除此药
                @Override
                public void onClick(View v) {
                    delateMedicineAt(view);
                }
            });

            //添加计量输入框输入监听事件，实时更新药材剂量
            addDoseEtTextChangedListener(doseEt);
        }

        if (!ADDMEDICINETYPE_ADD.equals(addMedicineType)) {
            if (numKeyboard != null) {
                if (!numKeyboard.isShown()) {
                    showInputKeyBoard();
                }
            }
        }
        scrollToBottom(mMedicineScrollview, mMedicineContainerLl);
    }

    /**
     * 计量输入框输入监听事件，实时更新药材剂量
     *
     * @param doseEt
     */
    private void addDoseEtTextChangedListener(final EditText doseEt) {
        doseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                upDateMedicineDose(doseEt);
            }
        });
    }

    /**
     * 添加某一味药材
     *
     * @param medicines
     * @param medicinesTable
     * @param addMedicineType
     * @param i
     * @param view
     * @param boil_medicine_tv
     * @param doseEt
     */
    private void addMedicineViewItem(List<MedicineDetailMsgBean> medicines, TableLayout medicinesTable, String addMedicineType, int i, View view, TextView boil_medicine_tv, EditText doseEt) {
        if (mMedicineIdList.contains(medicineId)) {
            if (addMedicineType.equals(ADDMEDICINETYPE_ADD)) {
                ToastUtils.showShortMsg(mContext, medicines.get(i).getDrugName() + "已存在，无需重复添加！");
            }
        } else {
            if (ADDMEDICINETYPE_ADD.equals(addMedicineType)) {
                hideKeyBoard(mInputEt);
                mBottomViews.setVisibility(View.GONE);
                showKeyBoard(doseEt);
                numKeyboard.bindEditText(doseEt, 0);
                doseEt.setTag(medicines.get(i));
            } else {
                doseEt.setTag(medicines.get(i));
                numKeyboard.bindEditText(doseEt, 0, false);
                numKeyboard.hideKeyboard();
            }

            medicines.get(i).setUseMethod(boil_medicine_tv.getText().toString());

            // 根据编辑模式插入药品
            switch (mEditMode) {
                case NORMAL:
                    // 正常模式，添加到末尾
                    mMedicineList.add(medicines.get(i));
                    mMedicineIdList.add(medicineId);
                    medicinesTable.addView(view);
                    break;
                case FORWARD:
                    // 向前插入
                    if (mEditIndex >= 0 && mEditIndex < mMedicineList.size()) {
                        mMedicineList.add(mEditIndex, medicines.get(i));
                        mMedicineIdList.add(mEditIndex, medicineId);
                        medicinesTable.addView(view, mEditIndex);
                    } else {
                        mMedicineList.add(medicines.get(i));
                        mMedicineIdList.add(medicineId);
                        medicinesTable.addView(view);
                    }
                    break;
                case BACKWARD:
                    // 向后插入
                    if (mEditIndex >= 0 && mEditIndex < mMedicineList.size()) {
                        mMedicineList.add(mEditIndex + 1, medicines.get(i));
                        mMedicineIdList.add(mEditIndex + 1, medicineId);
                        medicinesTable.addView(view, mEditIndex + 1);
                    } else {
                        mMedicineList.add(medicines.get(i));
                        mMedicineIdList.add(medicineId);
                        medicinesTable.addView(view);
                    }
                    break;
                case REPLACE:
                    // 替换模式
                    if (mEditIndex >= 0 && mEditIndex < mMedicineList.size()) {
                        mMedicineList.set(mEditIndex, medicines.get(i));
                        mMedicineIdList.set(mEditIndex, medicineId);
                        medicinesTable.removeViewAt(mEditIndex);
                        medicinesTable.addView(view, mEditIndex);
                    } else {
                        mMedicineList.add(medicines.get(i));
                        mMedicineIdList.add(medicineId);
                        medicinesTable.addView(view);
                    }
                    break;
            }

            // 重置编辑模式
            mEditMode = EditMode.NORMAL;
            mEditIndex = -1;
            if (mEditGradientView != null) {
                mEditGradientView.setVisibility(View.GONE);
                mEditGradientView = null;
            }
        }
    }

    /**
     * 设置药材信息
     *
     * @param medicines
     * @param addMedicineType
     * @param i
     * @param name_tv
     * @param unit_tv
     * @param understock_tv
     * @param boil_medicine_tv
     * @param doseEt
     */
    private void SetMedicineMsg(List<MedicineDetailMsgBean> medicines, String addMedicineType, int i, TextView name_tv, TextView unit_tv, TextView understock_tv, TextView boil_medicine_tv, EditText doseEt) {
        name_tv.setText(TextUtils.isEmpty(medicines.get(i).getDrugName()) ? "" : medicines.get(i).getDrugName());//药材名称
        unit_tv.setText(TextUtils.isEmpty(medicines.get(i).getUnit()) ? "g" : medicines.get(i).getUnit());//药材单位
        //库存隐藏
        understock_tv.setVisibility(View.GONE);
        //特殊煎药法的显示判断
        if (!TextUtils.isEmpty(medicines.get(i).getUseMethod())) {
            boil_medicine_tv.setVisibility(View.VISIBLE);
            boil_medicine_tv.setText(medicines.get(i).getUseMethod());
        } else {
            boil_medicine_tv.setVisibility(View.GONE);
            boil_medicine_tv.setText("");
        }
        //剂量显示
        if (addMedicineType.equals(ADDMEDICINETYPE_ADD)) {//药材的时候
            doseEt.setText("0");//剂量默认显示0g
        } else if (addMedicineType.equals(ADDMEDICINETYPE_TEMPLATE)) {
            String formattedDose = formatDose(medicines.get(i).getDose());
            doseEt.setText(formattedDose);
        }
        medicines.get(i).setDose(doseEt.getText().toString());//更新此药材的剂量
    }

    private String formatDose(String dose) {
        try {
            float doseValue = Float.parseFloat(dose);
            if (doseValue == (int) doseValue) {
                return String.format(Locale.US, "%d", (int) doseValue);
            } else {
                return dose;
            }
        } catch (NumberFormatException e) {
            return dose;
        }
    }

    /**
     * @param medicine
     * @return 小数位数
     */
    public int getPointScale(MedicineDetailMsgBean medicine) {
        int scal = 0;
        if (medicine != null && !TextUtils.isEmpty(medicine.getMinSaleUnit())) {
            scal = DecimalUtils.getScale(medicine.getMinSaleUnit());
        }
        return scal;
    }

    /**
     * 显示输入键盘，解决键盘不弹出问题
     */
    private void showInputKeyBoard() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (numKeyboard != null && !numKeyboard.isShown()) {
                    mInputEt.requestFocus();
                    showKeyBoard(mInputEt);
                }
            }
        }, 400);
    }

    /**
     * 更改药材剂量
     */
    private void upDateMedicineDose(EditText view) {
        MedicineDetailMsgBean drug = (MedicineDetailMsgBean) view.getTag();
        for (int j = 0; j < mMedicineList.size(); j++) {
            if (mMedicineList.get(j).getDrugId().equals(drug.getDrugId()))
                mMedicineList.get(j).setDose(view.getText().toString());
        }
    }

    /**
     * @param medicineView
     */
    private void delateMedicineAt(final View medicineView) {
        if (mConfirmDialog == null) {
            mConfirmDialog = ConfirmDialog.getInstance(this);
        }
        mConfirmDialog.setDialogContent("是否删除此药材？").setPositiveText("确认").setNavigationText("取消").show();
        mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
            @Override
            public void onNavigationBtnClicked(View view) {
                mConfirmDialog.dismiss();
            }

            @Override
            public void onPositiveBtnClicked(View view) {
                try {
                    int position = mMedicinesTable.indexOfChild(medicineView);
                    //删除已添加的某味药材
                    mMedicineList.remove(position);
                    mMedicineIdList.remove(position);
                    mMedicinesTable.removeView(medicineView);
                    mInputEt.setText("");
                    //数字键盘隐藏，显示搜药键盘
                    setInputViewVisible(true);
                    mConfirmDialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });
    }

    /**
     * @param isVisible 是否显示，true 时显示 false时不显示
     *                  设置输入框布局显示与隐藏
     */
    private void setInputViewVisible(boolean isVisible) {
        if (isVisible) {
            if (!mBottomViews.isShown()) {
                mBottomViews.setVisibility(View.VISIBLE);
            }
            numKeyboard.hideKeyboard();
            mInputEt.requestFocus();
            showKeyBoard(mInputEt);
        } else {
            if (mBottomViews.isShown()) {
                mBottomViews.setVisibility(View.GONE);
            }
            hideKeyBoard(mInputEt);
        }

    }

    /**
     * 处理点击返回键的逻辑
     */
    private void doBackLogic() {
        if (mMedicineList.size() > 0) {
            if (mConfirmDialog == null) {
                mConfirmDialog = ConfirmDialog.getInstance(this);
            }
            mConfirmDialog.setDialogContent("已添加药材未保存，是否退出？").setPositiveText("退出").setNavigationText("取消").show();
            mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    mConfirmDialog.dismiss();
//                    showInputKeyBoard();
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    mConfirmDialog.dismiss();
                    finishActivity();

                }
            });
        } else {
            finishActivity();
        }
    }

    /**
     * @param oldDose
     * @param minSaleUnit 剂型向下转型,并保留相应小数位
     */
    private String doseFormate(String oldDose, String minSaleUnit) {
        int mul = 0;//倍数
        String dose;//需要的剂量
        if (TextUtils.isEmpty(minSaleUnit)) {
            minSaleUnit = "0.01";
        }
        if (TextUtils.isEmpty(oldDose)) {
            oldDose = "0";
        }
        float oldDoseF = Float.parseFloat(oldDose);
        float minSaleUnitF = Float.parseFloat(minSaleUnit);
        mul = (int) (oldDoseF / minSaleUnitF);
        if (mul < 1) {//倍数<1，不足规格，默认显示规格
            return minSaleUnit;
        } else {
            dose = DecimalUtils.mul(minSaleUnit, String.valueOf(mul));
            if ("0.01".equals(minSaleUnit)) {
                return DecimalUtils.format(dose, /*DecimalUtils.getScale(minSaleUnit)*/0);
            } else {
                return DecimalUtils.format(dose, DecimalUtils.getScale(minSaleUnit));
            }
        }
    }

    /**
     * @sender {@link DataReceiverImpl#sendEventBus(NettyResult)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void medicineSearchResult(NettyResult result) {
        if (result != null && result.getResult() != null) {
            if (NettyResultCode.SUCCESS.equals(result.getResultCode())) {
                if (ResultType.SEARCH_MEDICINE.equals(result.getResultType()) || ResultType.ASSOCIATIVE_MEDICINE.equals(result.getResultType())) {//搜索药材
                    parseSearchedMedicine(result);//解析搜索到的药材、联想药材并处理相关显示逻辑

                } else if (ResultType.COMM_MEDICINE.equals(result.getResultType()) || ResultType.COMM_TEMPLATE_MEDICINE.equals(result.getResultType())) {//药材详情 药方详情
                    parseMedicineOrTemplate(result);//解析药材或药方详情数据

                }
            }
        }


    }

    /**
     * 解析药材或药方数据
     *
     * @param result
     */
    private void parseMedicineOrTemplate(NettyResult result) {
        HashMap<String, Object> map = (HashMap<String, Object>) result.getResult();
        String taskId = (String) map.get("id");
        JSONObject jsonObject = (JSONObject) map.get("result");
        if (jsonObject != null) {
            List<HashMap<String, Object>> dataMap = (List<HashMap<String, Object>>) jsonObject.get("data");
            if (dataMap != null) {
                try {
                    String jsonStr = JSON.toJSONString(dataMap);
                    rlUpSlideMedicines.setVisibility(View.GONE);//向上弹出的布局中点击名称请求了详情后就隐藏该布局，
                    rlHorizontalLayout.setVisibility(View.VISIBLE);
                    List<MedicineDetailMsgBean> medicines = JSON.parseArray(jsonStr, MedicineDetailMsgBean.class);
                    if (result.getResultType().equals(ResultType.COMM_MEDICINE)) {
                        addMedicineType = ADDMEDICINETYPE_ADD;
                    } else if (result.getResultType().equals(ResultType.COMM_TEMPLATE_MEDICINE)) {
                        addMedicineType = ADDMEDICINETYPE_TEMPLATE;
                    }
                    if (isLongClick.equals(taskId)) {
                        showTemplateDetailPop(medicines);//显示药方详情弹框
                    } else {
                        addMedicineView(medicines, mMedicinesTable, addMedicineType);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void showTemplateDetailPop(List<MedicineDetailMsgBean> medicines) {
        if (medicines.size() > 0) {
            if (templateListPop == null) {
                templateListPop = TemplateListPopWindow.getInstance(AddCommonPrescriptionActivity.this);
            }
            templateListPop.setPopTitle(templateName);
            templateListPop.initPopContentData(medicines);
            templateListPop.showPopFromBottom(mLayoutRoot);
            templateListPop.OnSureBtnClickListener(new TemplateListPopWindow.OnSureBtnClickListener() {
                @Override
                public void onSureBtnClick(List<MedicineDetailMsgBean> list) {
                    addMedicineView(list, mMedicinesTable, addMedicineType);
                    templateListPop.dimissPop();

                }
            });
        }
    }

    /**
     * 解析搜索到的药材、联想药材并处理相关显示逻辑
     *
     * @param result
     */
    private void parseSearchedMedicine(NettyResult result) {
        HashMap<String, Object> map1 = (HashMap<String, Object>) result.getResult();
        JSONObject jsonObject = (JSONObject) map1.get("result");
        if (jsonObject != null) {
            List<HashMap<String, Object>> dataMap = (List<HashMap<String, Object>>) jsonObject.get("data");
            if (dataMap != null) {
                try {
                    String jsonStr = JSON.toJSONString(dataMap);
                    List<MedicineOrTempleBean> datas = JSON.parseArray(jsonStr, MedicineOrTempleBean.class);
                    mMedicineOrTemples.clear();
                    mMedicineOrTemples.addAll(datas);
                    mMedicineHLvAdapter.updateData(mMedicineOrTemples);
                    showUpViewData(mMedicineOrTemples, mUpSlideMedicines);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                mMedicineOrTemples.clear();
                mMedicineHLvAdapter.updateData(mMedicineOrTemples);
                mUpSlideMedicines.removeAllViews();
            }

        }
    }

    @Override
    protected void onDestroy() {
        cancelRequest(saveCommonTemplateCallBack, getBoilWayCallBack, addBoilWayCallBack);
        EventBusUtils.unRegister(this);
        hideKeyBoard(mInputEt);
        super.onDestroy();
    }

    @OnClick({R.id.down_arrow, R.id.up_arrow, R.id.up_slide_medicines_rl})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.down_arrow:
                rlUpSlideMedicines.setVisibility(View.GONE);
                rlHorizontalLayout.setVisibility(View.VISIBLE);
                break;
            case R.id.up_arrow:
                rlHorizontalLayout.setVisibility(View.GONE);
                rlUpSlideMedicines.setVisibility(View.VISIBLE);
                break;
        }
    }

    @Override
    public void onViewTouch(View view) {
        mBottomViews.setVisibility(View.GONE);//输入框布局隐藏、
        hideKeyBoard(mInputEt);
    }

    @Override
    public void onKeyDown(int keyCode, EditText view) {
        if (keyCode == NumberKeyboardView.KEYCODE_BACK) {//删除

        } else if (keyCode == NumberKeyboardView.KEYCODE_POINT) {//小数点
        } else if (keyCode == NumberKeyboardView.KEYCODE_SURE) {//确定键
            upDateMedicineDose(view);
            numKeyboard.hideKeyboard();//数字键盘隐藏
            mBottomViews.setVisibility(View.VISIBLE);//输入框布局显示
            mInputEt.requestFocus();//输入框获取焦点弹出拼音键盘、
            showKeyBoard(mInputEt);

            mInputEt.setText("");//添加完一味药材后，输入框值“”
//            NettyUtils.getAssociativeMedicine(medicineId);//请求联想药材
            NettyUtils.getAssociativeMedicine(getAssociativeDrugId(), "", "", "");//请求联想药材
        }
    }

    /**
     * 联想药材请求药材id
     *
     * @return
     */
    private String getAssociativeDrugId() {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < mMedicineIdList.size(); i++) {
            builder.append(mMedicineIdList.get(i) + ",");
        }
//        String drugIds = builder.toString().substring(0, builder.toString().length() - 1);
        return builder.toString().substring(0, builder.toString().length() - 1);
    }

    /**
     * 刷新煎法数据
     */
    private void refreshBoilWayData() {
        // 请求煎法列表数据
        HashMap<String, String> params = new HashMap<>();
        getBoilWayCallBack = addHttpPostRequest(HttpUrlManager.BOIL_WAY, params, BoilWayBean.class, this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        showInputKeyBoard();
    }

    @Override
    public void onBack(Activity activity) {
        doBackLogic();
    }

    @Override
    public void onBackPressed() {
        doBackLogic();
    }
}
