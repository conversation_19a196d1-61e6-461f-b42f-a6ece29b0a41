package com.doctor.br.activity.mine.agent.achievement;

import android.content.Intent;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.LinearLayout;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.PerformanceSummaryRecyclerAdapter;
import com.doctor.br.bean.AgentPerformanaceSummaryBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：业绩汇总
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/10 12:52
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/10 12:52
 */

public class PerformanceSummaryActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //    private final String TYPE_ALL = "All";//全部
//    private final String TYPE_THISMONTH = "thisMonth";//本月
//    private final String TYPE_LASTMONTH = "lastMonth";//上月
    //界面下的控件
    @BindView(R.id.head_linear)
    LinearLayout headLienar;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

//    private PopUtils bottomPop;//选择全部本月上月的popwindow

    private int totalPage = -1;//总页码
    private int currentPage = 1;//当前页码

    private RequestCallBack getListCallBack;//网络请求获取列表回调

    private List<AgentPerformanaceSummaryBean.ListBean> list;//列表
    private PerformanceSummaryRecyclerAdapter performanceSummaryRecyclerAdapter;

//    private String type = TYPE_ALL;//当前显示的类型

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_patient_numbers);
        initView();
//        initPop();
        getPerformanceListRequest("1");
    }

    private void initView() {
        setActionBarTitle("业绩汇总");
        getBaseActionBar().setRightButtonImg(R.drawable.search);
//        getBaseActionBar().setRightButton("全部");
//        getBaseActionBar().setRightButtonColor(ContextCompat.getColor(this, R.color.br_color_theme));
//        getBaseActionBar().setActionBarChangeListener(this);

        headLienar.setVisibility(View.GONE);

        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                getPerformanceListRequest("1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        mLoadingDialog = null;
                        getPerformanceListRequest(currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        bgaRefreshLayout.setRefreshViewHolder(new BGANormalRefreshViewHolder(this, true));

        recyclerView = (RecyclerView) findViewById(R.id.recycler_view);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setHasFixedSize(true);

        list = new ArrayList<>();
        performanceSummaryRecyclerAdapter = new PerformanceSummaryRecyclerAdapter(this, list, this);
        recyclerView.setAdapter(performanceSummaryRecyclerAdapter);

        emptyView.setEmptyBackgroundColor(ContextCompat.getColor(this, R.color.br_color_white));
    }

//    private void initPop() {
//        final String[] strings = {"全部", "本月", "上月"};
//        bottomPop = new PopUtils(this, R.string.select_month, Arrays.asList(strings), new AdapterView.OnItemClickListener() {
//            @Override
//            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
//                if (mLoadingDialog == null) {
//                    mLoadingDialog = LoadingDialog.getInstance(PerformanceSummaryActivity.this);
//                }
//                switch (position) {
//                    case 0:
//                        getPerformanceListRequest(TYPE_ALL, "1");
//                        break;
//                    case 1:
//                        getPerformanceListRequest(TYPE_THISMONTH, "1");
//                        break;
//                    case 2:
//                        getPerformanceListRequest(TYPE_LASTMONTH, "1");
//                        break;
//                    default:
//                        break;
//                }
//                bottomPop.dismiss();
//            }
//        });
//    }

    /**
     * 右上角按钮的点击
     */
    @Override
    public void onRightBtnClick(View view) {
        startActivity(new Intent(this, PerformanceSearchActivity.class));
    }

    //每个item的点击事件
    @Override
    public void itemClick(int position) {
        Intent intent = new Intent(this, PerformanceMonthActivity.class);
        intent.putExtra(PerformanceMonthActivity.DOCTOR_NAME, list.get(position).getDoctorName());
        intent.putExtra(PerformanceMonthActivity.DOCTOR_ID, list.get(position).getDoctorId());
        startActivity(intent);
    }

    /**
     * 网络请求获取业绩汇总列表
     * <p>
     * //     * @param type 类型，All 全部 thisMonth 本月 lastMonth 上月
     *
     * @param page 想要请求的页码
     */
    private void getPerformanceListRequest(/*String type,*/ String page) {
        emptyView.setVisibility(View.GONE);
        bgaRefreshLayout.setVisibility(View.VISIBLE);
        Map<String, String> map = new HashMap<>();
        map.put("type", "All");
        map.put("page", page);
//        map.put("userId", "2928");//测试使用
        map.put("pageSize", "20");
        getListCallBack = addHttpPostRequest(HttpUrlManager.PERFORMANCE_SUMMARY, map, AgentPerformanaceSummaryBean.class, this);
    }

    @Override
    public void onRequestFinished(final String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (bgaRefreshLayout == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.PERFORMANCE_SUMMARY:
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    AgentPerformanaceSummaryBean agentPerformanaceSummaryBean = (AgentPerformanaceSummaryBean) result.getBodyObject();
//                    type = agentPerformanaceSummaryBean.getType();
//                    if (TYPE_THISMONTH.equals(type)) {
//                        getBaseActionBar().setRightButton("本月");
//                    } else if (TYPE_LASTMONTH.equals(type)) {
//                        getBaseActionBar().setRightButton("上月");
//                    } else {
//                        getBaseActionBar().setRightButton("全部");
//                    }
                    totalPage = agentPerformanaceSummaryBean.getTotalPageSize();
                    currentPage = agentPerformanaceSummaryBean.getCurrentPage();
                    if (currentPage == 1) {
                        list.clear();
                    }
                    list.addAll(agentPerformanaceSummaryBean.getList());
                    performanceSummaryRecyclerAdapter.notifyDataSetChanged();
                    if (currentPage == 1 && list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setVisibility(View.VISIBLE);
                        bgaRefreshLayout.setVisibility(View.GONE);
                    }
                } else {
                    if (totalPage == -1) {
                        emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                        emptyView.setVisibility(View.VISIBLE);
                        bgaRefreshLayout.setVisibility(View.GONE);
                        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                            @Override
                            public void onReload() {
                                getPerformanceListRequest("1");
                            }
                        });
                    }
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (getListCallBack != null) {
            getListCallBack.cancel();
        }
        super.onDestroy();
    }


}
