package com.doctor.br.activity.chatmain;

import android.content.ClipboardManager;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.provider.MediaStore;
import androidx.annotation.NonNull;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.doctor.br.activity.photo.ClipImageActivity;
import com.doctor.br.adapter.chatmain.ChatAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.MsgStatus;
import com.doctor.br.db.entity.Msg;
import com.doctor.br.db.entity.Session;
import com.doctor.br.netty.NettyMsgNotify;
import com.doctor.br.netty.NettyRequestCode;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.NettyResultCode;
import com.doctor.br.netty.NotifyType;
import com.doctor.br.netty.client.NettyUtils;
import com.doctor.br.netty.impl.MsgReceiveFactory;
import com.doctor.br.netty.model.ContentType;
import com.doctor.br.netty.model.Message;
import com.doctor.br.netty.model.MessageType;
import com.doctor.br.netty.model.MsgResourceType;
import com.doctor.br.recordutils.CustomRecoderButton;
import com.doctor.br.utils.DateUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.MediaManager;
import com.doctor.br.utils.SelectImgUtils;
import com.doctor.br.utils.SessionUtils;
import com.doctor.br.view.MessageActionPopWindow;
import com.doctor.br.view.SelectPhotoDialog;
import com.doctor.br.view.refreshlayout.OnRefreshListener;
import com.doctor.br.view.refreshlayout.PullDownLoadMoreLayout;
import com.doctor.br.view.refreshlayout.RefreshLayout;
import com.doctor.greendao.gen.DaoSession;
import com.doctor.greendao.gen.MsgDao;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 客服小然聊天页面
 * @createTime 2017/11/22
 */

public class ChatServiceActivity extends ActionBarActivity implements MessageActionPopWindow.OnPopItemClickedListener, ChatAdapter.OnMsgActionListener {
    @BindView(R2.id.lv_chat_records)
    ListView lvChatRecords;
    @BindView(R2.id.mPullDownLoadMoreLayout)
    PullDownLoadMoreLayout mPullDownLoadMoreLayout;
    @BindView(R2.id.tv_chat_finish)
    TextView tvChatFinish;
    @BindView(R2.id.view_line_bottom)
    View viewLineBottom;
    @BindView(R2.id.iv_chat_keyboard)
    ImageView ivChatKeyboard;
    @BindView(R2.id.iv_chat_voice)
    ImageView ivChatVoice;
    @BindView(R2.id.btn_record)
    CustomRecoderButton btnRecord;
    @BindView(R2.id.et_msg_input)
    EditText etMsgInput;
    @BindView(R2.id.iv_chat_add)
    ImageView ivChatAdd;
    @BindView(R2.id.btn_send)
    Button btnSend;
    @BindView(R2.id.view_line2)
    View viewLine2;
    @BindView(R2.id.ll_action_picture)
    LinearLayout llActionPicture;
    @BindView(R2.id.ll_action_camera)
    LinearLayout llActionCamera;
    @BindView(R2.id.rl_chat_add_container)
    LinearLayout rlChatAddContainer;
    @BindView(R2.id.ll_bottom_layout)
    LinearLayout llBottomLayout;

    public static final int REQUEST_CAPTURE = 100;
    //请求相册
    public static final int REQUEST_PICK = 101;
    //请求截图
    public static final int REQUEST_CROP_PHOTO = 102;
    //请求访问外部存储
    private final int READ_EXTERNAL_STORAGE_REQUEST_CODE = 103;
    //请求写入外部存储
    private final int WRITE_EXTERNAL_STORAGE_REQUEST_CODE = 104;
    private final static int QUICK_REPLY_CODE = 105;
//    private File tempFile;

    private ConfirmDialog mConfirmDialog;
    private MessageActionPopWindow messageActionPopWindow;
    private ChatAdapter chatAdapter;
    private MsgDao msgDao;
    private Msg revokeMsg;
    private String patientUserName = "";
    private String mPhone = "";
    private List<Msg> msgList;
    private String fromUserId;//表示患者的userId
    private String toUserId;//表示医生的userId
    private int page = 0;
    private int pageSize = 20;
    private Handler mHandler = new Handler();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_chat_service);
//        createCameraTempFile(savedInstanceState);
        patientUserName = getIntent().getStringExtra(PublicParams.PATIENT_USRE_NNAME);
        mPhone = getIntent().getStringExtra(PublicParams.PATIENT_USRE_MOBILE);
        if (!TextUtils.isEmpty(patientUserName)) {
            setActionBarTitle(patientUserName);
        }
        if (!TextUtils.isEmpty(mPhone) && !"null".equalsIgnoreCase(mPhone)) {
            setActionBarRightBtnImg(R.drawable.icon_call_phone);
        }
        EventBusUtils.register(this);
        DaoSession daoSession = AppContext.getInstances().getDaoSession();
        msgDao = daoSession.getMsgDao();
        messageActionPopWindow = MessageActionPopWindow.getInstance(mContext);
        messageActionPopWindow.setListener(this);
        fromUserId = SharedPreferenceUtils.getString(mContext, PublicParams.PATIENT_USRE_ID);
        toUserId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        initView();
        getLocalData(false);
        NettyUtils.startOrEndSession(fromUserId, NettyUtils.SESSION_TYPE_START);
    }

    /**
     * 初始化控件
     */
    private void initView() {
        mPullDownLoadMoreLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefreshLayoutRefreshing(RefreshLayout refreshLayout) {

            }

            @Override
            public void onRefreshLayoutLoadMore(RefreshLayout refreshLayout) {
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        page++;
                        getLocalData(true);
                    }
                }, 1000);
            }
        });
        msgList = new ArrayList<>();
        chatAdapter = new ChatAdapter(mContext, msgList, R.layout.item_chat_record);
        chatAdapter.setOnMsgActionListener(this);
        lvChatRecords.setAdapter(chatAdapter);
        llActionPicture.setOnClickListener(this);
        llActionCamera.setOnClickListener(this);


        ivChatKeyboard.setOnClickListener(this);
        ivChatVoice.setVisibility(View.GONE);
        ivChatAdd.setOnClickListener(this);
        tvChatFinish.setOnClickListener(this);
        btnSend.setOnClickListener(this);
        etMsgInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String text = s.toString().trim();
                if (TextUtils.isEmpty(text)) {
                    btnSend.setVisibility(View.GONE);
                    ivChatAdd.setVisibility(View.VISIBLE);
                } else {
                    btnSend.setVisibility(View.VISIBLE);
                    ivChatAdd.setVisibility(View.GONE);
                }
            }
        });
        etMsgInput.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    if (rlChatAddContainer.getVisibility() == View.VISIBLE) {
                        rlChatAddContainer.setVisibility(View.GONE);
                    }
                    if (messageActionPopWindow != null) {
                        messageActionPopWindow.dismiss();
                    }
                }
            }
        });


        lvChatRecords.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View arg0, MotionEvent arg1) {
                if (arg1.getAction() == MotionEvent.ACTION_DOWN) {
                    if (rlChatAddContainer.getVisibility() == View.VISIBLE) {
                        rlChatAddContainer.setVisibility(View.GONE);
                    }
                    hideKeyBoard(etMsgInput);
                    messageActionPopWindow.dismiss();
                }
                return false;
            }
        });

        etMsgInput.setOnClickListener(this);

        mConfirmDialog = ConfirmDialog.getInstance(this);
        mConfirmDialog.setPositiveText("立即拨号")
                .setNavigationText("取消")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        mConfirmDialog.dismiss();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + mPhone));
                        startActivity(intent);
                        mConfirmDialog.dismiss();
                    }
                });
    }

    /**
     * 更新消息为已读
     *
     * @receiver {@link com.doctor.br.fragment.main.NewsFragment#onSessionNotifyEvent(NettyMsgNotify)}
     */
    private void updateMsgToRead() {
        List<Msg> list = msgDao.queryBuilder().where(MsgDao.Properties.From.eq(fromUserId))
                .where(MsgDao.Properties.To.eq(toUserId))
                .where(MsgDao.Properties.IsRead.eq(MsgStatus.NOT_READ))
                .list();
        for (Msg msg : list) {
            msg.setIsRead(MsgStatus.IS_READ);
            msgDao.update(msg);
        }
        EventBusUtils.post(new NettyMsgNotify(NotifyType.SESSION_LIST, new Session()));
    }


    /**
     * 获取本地数据
     */
    private void getLocalData(boolean isLoadMore) {
        int index = msgList.size();
        final List<Msg> list = msgDao.queryBuilder()
                .where(MsgDao.Properties.From.eq(fromUserId))
                .where(MsgDao.Properties.To.eq(toUserId))
                .orderDesc(MsgDao.Properties.CreatedTime)
                .orderDesc(MsgDao.Properties.Id)
                .offset(page * pageSize)
                .limit(pageSize)
                .build().list();
        for (int i = 0; i < list.size(); i++) {
            msgList.add(0, list.get(i));
        }
        if (isLoadMore && (list == null || list.size() == 0 || list.size() < pageSize)) {
            mPullDownLoadMoreLayout.setPullDownRefreshEnable(false);
        } else {
            mPullDownLoadMoreLayout.setPullDownRefreshEnable(true);
        }
        mPullDownLoadMoreLayout.endRefreshing();
        mPullDownLoadMoreLayout.endLoadingMore();
        chatAdapter.notifyData(msgList);
        lvChatRecords.smoothScrollToPosition(list.size());
    }

    @Override
    public void onClick(View v) {
        if (messageActionPopWindow != null) {
            messageActionPopWindow.dismiss();
        }
        switch (v.getId()) {
            case R.id.iv_chat_keyboard:
                etMsgInput.setVisibility(View.VISIBLE);
                btnRecord.setVisibility(View.GONE);
                etMsgInput.setFocusable(true);
                ivChatKeyboard.setVisibility(View.GONE);
                ivChatVoice.setVisibility(View.VISIBLE);
                break;
            case R.id.iv_chat_voice:
                etMsgInput.setVisibility(View.GONE);
                btnRecord.setVisibility(View.VISIBLE);
                ivChatKeyboard.setVisibility(View.VISIBLE);
                ivChatVoice.setVisibility(View.GONE);
                hideKeyBoard(etMsgInput);
                break;
            case R.id.iv_chat_add:
                hideKeyBoard(etMsgInput);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (rlChatAddContainer.getVisibility() == View.VISIBLE) {
                            rlChatAddContainer.setVisibility(View.GONE);
                        } else {
                            rlChatAddContainer.setVisibility(View.VISIBLE);
                        }
                        etMsgInput.setVisibility(View.VISIBLE);
                    }
                }, 100);
                break;
            case R.id.et_msg_input:
                rlChatAddContainer.setVisibility(View.GONE);
                break;
            case R.id.btn_send: {
                Msg msg = sendTextMsg(etMsgInput.getText().toString());
                addMsgToList(msg);
                etMsgInput.setText("");
            }
            break;
            case R.id.btn_record:

                break;
            case R.id.tv_chat_finish: {

            }
            break;

            case R.id.ll_action_picture: {//拍照
                hideKeyBoard(etMsgInput);
                SelectImgUtils.gotoPhoto(this);
                //权限判断
//                if (ContextCompat.checkSelfPermission(mContext, Manifest.permission.READ_EXTERNAL_STORAGE)
//                        != PackageManager.PERMISSION_GRANTED) {
//                    //申请READ_EXTERNAL_STORAGE权限
//                    ActivityCompat.requestPermissions((Activity) mContext, new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
//                            READ_EXTERNAL_STORAGE_REQUEST_CODE);
//                } else {
//                    //跳转到调用系统图库
//                    gotoPhoto();
//                }
            }
            break;
            case R.id.ll_action_camera: {//选择照片

                hideKeyBoard(etMsgInput);
                SelectImgUtils.gotoCarema(this);
//                //权限判断
//                if (ContextCompat.checkSelfPermission(mContext, Manifest.permission.CAMERA)
//                        != PackageManager.PERMISSION_GRANTED) {
//                    //申请WRITE_EXTERNAL_STORAGE权限
//                    ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA},
//                            WRITE_EXTERNAL_STORAGE_REQUEST_CODE);
//                } else {
//                    //跳转到调用系统相机
//                    gotoCarema();
//                }
            }
            break;

        }
    }

    private void addMsgToList(Msg msg) {
        msg.setSendStatus(MsgStatus.SENDING);
        NettyUtils.enQueueSendMsg(msg);
        SessionUtils.getInstance().updateSessionList(msg);
        msgList.add(msg);
        adapterNotify();
    }


    private void adapterNotify() {
        chatAdapter.notifyData(msgList);
        lvChatRecords.setSelection(msgList.size());
    }


    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        if (mConfirmDialog != null) {
            //patientUserName +
            mConfirmDialog.setDialogContent("客服热线：" + mPhone);
            mConfirmDialog.show();
        }
    }

    /**
     * 发送文本消息
     */
    private Msg sendTextMsg(String text) {
        Msg msg = new Msg();
        msg.setContentObjContent(text);
        msg.setContentObjType(ContentType.TEXT);
        msg.setMessageType(MessageType.CHAT);
        msg.setMsgSourceType(MsgResourceType.TYPE_TO);
        msg.setCreatedTime(DateUtils.getNowDate());
        msg.setFrom(fromUserId);
        msg.setTo(toUserId);
        msg.setSendStatus(MsgStatus.SENDING);
        msg.setIsRead(MsgStatus.IS_READ);
        msg.setId(msgDao.insertOrReplace(msg));
        return msg;
    }

    /**
     * 发送图片消息
     */
    private Msg sendImageMsg(Uri uri) {
        Msg msg = new Msg();
        msg.setContentObjContent(uri.toString());
        msg.setContentObjExtra3(uri.toString());
        msg.setContentObjType(ContentType.IMAGE);
        msg.setMessageType(MessageType.CHAT);
        msg.setMsgSourceType(MsgResourceType.TYPE_TO);
        msg.setCreatedTime(DateUtils.getNowDate());
        msg.setFrom(fromUserId);
        msg.setTo(toUserId);
        msg.setSendStatus(MsgStatus.SENDING);
        msg.setIsRead(MsgStatus.IS_READ);
        msg.setId(msgDao.insertOrReplace(msg));
        return msg;
    }


    /**
     * 发送图片消息
     */
    private Msg sendAudioMsg(String url, float seconds) {
        Msg msg = new Msg();
        msg.setContentObjContent(url);
        msg.setContentObjType(ContentType.AUDIO);
        msg.setContentObjExtra1(String.valueOf(seconds));
        msg.setMessageType(MessageType.CHAT);
        msg.setMsgSourceType(MsgResourceType.TYPE_TO);
        msg.setCreatedTime(DateUtils.getNowDate());
        msg.setFrom(fromUserId);
        msg.setTo(toUserId);
        msg.setSendStatus(MsgStatus.SENDING);
        msg.setIsRead(MsgStatus.IS_READ);
        msg.setId(msgDao.insertOrReplace(msg));
        return msg;
    }


    /**
     * 开启会话
     *
     * @return
     */
    private Msg startChat() {
        Msg msg = new Msg();
        msg.setContentObjContent("哈哈哈哈哈哈哈哈哈哈哈哈哈哈");
        msg.setContentObjType(ContentType.START_CHAT_DOCTOR);
        msg.setMessageType(MessageType.CHAT);
        msg.setMsgSourceType(MsgResourceType.TYPE_NOTIFY);
        msg.setCreatedTime(DateUtils.getNowDate());
        msg.setFrom(fromUserId);
        msg.setTo(toUserId);
        msg.setSendStatus(MsgStatus.SENDING);
        msg.setIsRead(MsgStatus.IS_READ);
        msg.setId(msgDao.insertOrReplace(msg));
        return msg;
    }

    /**
     * 结束会话
     *
     * @return
     */
    private Msg finishChat() {
        Msg msg = new Msg();
        msg.setContentObjContent("哈哈哈哈哈哈哈哈哈哈哈哈哈哈");
        msg.setContentObjType(ContentType.FINISH_CHAT_DOCTOR);
        msg.setMessageType(MessageType.CHAT);
        msg.setMsgSourceType(MsgResourceType.TYPE_NOTIFY);
        msg.setCreatedTime(DateUtils.getNowDate());
        msg.setFrom(fromUserId);
        msg.setTo(toUserId);
        msg.setSendStatus(MsgStatus.SENDING);
        msg.setIsRead(MsgStatus.IS_READ);
        msg.setId(msgDao.insertOrReplace(msg));
        return msg;
    }

    /**
     * 系统代发的消息
     *
     * @return
     */
    private Msg systemMsgChat() {
        Msg msg = new Msg();
        msg.setContentObjContent("【系统消息】药方订单（订单编号：759698）正在药房配药，稍后会由顺丰配送。运单号：9352452625543459843 点击查看物流状态");
        msg.setContentObjType(ContentType.LOGISTICS);
        msg.setMessageType(MessageType.CHAT);
        msg.setMsgSourceType(MsgResourceType.TYPE_SYSTEM);
        msg.setCreatedTime(DateUtils.getNowDate());
        msg.setFrom(fromUserId);
        msg.setTo(toUserId);
        msg.setProxyId("0");
        msg.setSendStatus(MsgStatus.SENDING);
        msg.setIsRead(MsgStatus.IS_READ);
        msg.setId(msgDao.insertOrReplace(msg));
        return msg;
    }


    /*******************拍照相关********开始**************/
    /**
     * 跳转到相册
     */
    private void gotoPhoto() {
        Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(Intent.createChooser(intent, "请选择图片"), REQUEST_PICK);
    }


    /**
     * 跳转到照相机
     */
    private void gotoCarema() {
//        if (!AppUtils.cameraIsCanUse()) {
//            ToastUtils.showShortMsg(mContext, "请前往设置打开相机权限后重试！");
//        } else {
//            tempFile = new File(checkDirPath(Environment.getExternalStorageDirectory().getPath() + PublicParams.CACHE_DIR_CACHE+ "/image/"),
//                    System.currentTimeMillis() + ".jpg");
//            Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
//            if (Build.VERSION.SDK_INT > 23) {
//                Uri photoURI = FileUtils.getUriForFile(mContext, tempFile);
//                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI);
//            } else {
//                intent.putExtra(MediaStore.EXTRA_OUTPUT, Uri.fromFile(tempFile));
//            }
//
//            startActivityForResult(intent, REQUEST_CAPTURE);
//        }
    }


    /**
     * 创建调用系统照相机待存储的临时文件
     *
     * @param savedInstanceState
     */
    private void createCameraTempFile(Bundle savedInstanceState) {
//        if (savedInstanceState != null && savedInstanceState.containsKey("tempFile")) {
//            tempFile = (File) savedInstanceState.getSerializable("tempFile");
//        } else {
//            tempFile = new File(checkDirPath(Environment.getExternalStorageDirectory().getPath() + PublicParams.CACHE_DIR_CACHE+ "/image/"),
//                    System.currentTimeMillis() + ".jpg");
//        }
    }

    /**
     * 检查文件是否存在
     */
    private static String checkDirPath(String dirPath) {
        if (TextUtils.isEmpty(dirPath)) {
            return "";
        }
        File dir = new File(dirPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dirPath;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent intent) {
//        super.onActivityResult(requestCode, resultCode, intent);
        hideKeyBoard(etMsgInput);
        switch (requestCode) {
            case SelectPhotoDialog.REQUEST_CAPTURE: //调用系统相机返回
                if (resultCode == RESULT_OK) {
//                    gotoClipActivity(Uri.fromFile(tempFile));
                    String cropImagePath = getRealFilePathFromUri(mContext, Uri.fromFile(SelectImgUtils.tempFile));
                    Msg msg = sendImageMsg(Uri.fromFile(SelectImgUtils.tempFile));
                    addMsgToList(msg);
                }
                break;
            case SelectPhotoDialog.REQUEST_PICK:  //调用系统相册返回
                if (resultCode == RESULT_OK) {
                    Uri uri = intent.getData();
//                    gotoClipActivity(uri);
                    String cropImagePath = getRealFilePathFromUri(mContext, uri);
                    Msg msg = sendImageMsg(uri);
                    addMsgToList(msg);
                }
                break;
            case SelectPhotoDialog.REQUEST_CROP_PHOTO:  //剪切图片返回
                if (resultCode == RESULT_OK) {
                    final Uri uri = intent.getData();
                    if (uri == null) {
                        return;
                    }
                    String cropImagePath = getRealFilePathFromUri(mContext, uri);
                    Msg msg = sendImageMsg(uri);
                    addMsgToList(msg);
                }
                break;
            case QUICK_REPLY_CODE://常见问题返回
                if (resultCode == RESULT_OK) {
                    String replyText = intent.getStringExtra("replyText");
                    if (!TextUtils.isEmpty(replyText)) {
                        Msg msg = sendTextMsg(replyText);
                        addMsgToList(msg);
                    }
                }
                break;

        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        SelectImgUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults);
    }

    /**
     * 打开截图界面
     *
     * @param uri
     */
    public void gotoClipActivity(Uri uri) {
        if (uri == null) {
            return;
        }
        Intent intent = new Intent();
        intent.setClass(mContext, ClipImageActivity.class);
        intent.setData(uri);
        startActivityForResult(intent, REQUEST_CROP_PHOTO);
    }


    /**
     * 根据Uri返回文件绝对路径
     * 兼容了file:///开头的 和 content://开头的情况
     *
     * @param context
     * @param uri
     * @return the file path or null
     */
    public static String getRealFilePathFromUri(final Context context, final Uri uri) {
        if (null == uri) return null;
        final String scheme = uri.getScheme();
        String data = null;
        if (scheme == null)
            data = uri.getPath();
        else if (ContentResolver.SCHEME_FILE.equals(scheme)) {
            data = uri.getPath();
        } else if (ContentResolver.SCHEME_CONTENT.equals(scheme)) {
            Cursor cursor = context.getContentResolver().query(uri, new String[]{MediaStore.Images.ImageColumns.DATA}, null, null, null);
            if (null != cursor) {
                if (cursor.moveToFirst()) {
                    int index = cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATA);
                    if (index > -1) {
                        data = cursor.getString(index);
                    }
                }
                cursor.close();
            }
        }
        return data;
    }

    /*******************拍照相关*****结束*****************/


    @Override
    public void onMsgAction(View view, Msg msg, MessageActionPopWindow.PopItemActionType... actionTypes) {

        messageActionPopWindow.show(view, msg, actionTypes);
    }

    /**
     * 消息长按弹出操作框的点击事件
     *
     * @param view
     * @param msg
     * @param actionType
     */
    @Override
    public void onPopItemClicked(View view, Msg msg, MessageActionPopWindow.PopItemActionType actionType) {
        switch (actionType) {
            case MSG_COPY:
// 从API11开始android推荐使用android.content.ClipboardManager
                // 为了兼容低版本我们这里使用旧版的android.text.ClipboardManager，虽然提示deprecated，但不影响使用。
                ClipboardManager cm = (ClipboardManager) mContext.getSystemService(Context.CLIPBOARD_SERVICE);
                // 将文本内容放到系统剪贴板里。
                cm.setText(msg.getContentObjContent());
                break;
            case MSG_REVOKE://撤回
                if (msg != null && !TextUtils.isEmpty(msg.getCreatedTime())) {
                    long diff = DateUtils.getDiffSeconds(msg.getCreatedTime());
                    if (diff <= 120) {
                        if (msg.getSendStatus() == MsgStatus.SEND_SUCCESS) {
                            revokeMsg = msg;
                            NettyUtils.revokeMsg(msg.getFrom(), msg.getFromId(), msg.getMessageId());
                        } else {
                            msg.setMsgSourceType(MsgResourceType.TYPE_NOTIFY);
                            msg.setContentObjType(ContentType.MSG_REVOKE);
                            msg.setContentObjContent("你撤回了一条消息");
                            msgDao.update(msg);
                            NettyMsgNotify msgNotify = new NettyMsgNotify();
                            msgNotify.setNotifyType(NotifyType.MSG_POSITION_UPDATE);
                            msgNotify.setNotifyMsg(msg);
                            onMsgNotifyEvent(msgNotify);
                        }
                    } else {
                        ToastUtils.showShortMsg(mContext, "只能撤回2分钟之内的消息！");
                    }
                } else {
                    ToastUtils.showShortMsg(mContext, "只能撤回2分钟之内的消息！");
                }
                break;
            case MSG_REPEAT://转发
                Intent intent = new Intent(mContext, MsgRepeatActivity.class);
                intent.putExtra(PublicParams.REPEAT_MSG, msg);
                startActivity(intent);
                break;
        }
        messageActionPopWindow.dismiss();
    }

    /**
     * 消息更新到页面
     *
     * @param result 返回结果
     * @sender {@link com.doctor.br.netty.impl.DataReceiverImpl#sendEventBus(NettyResult)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN) //在ui线程执行
    public void onMsgRevokeEvent(NettyResult result) {
        if (result == null || revokeMsg == null) {
            return;
        }
        if (NettyRequestCode.MSG_REVOKE.equalsIgnoreCase(result.getCode())) {
            if (NettyResultCode.SUCCESS.equalsIgnoreCase(result.getResultCode())) {
                revokeMsg.setMsgSourceType(MsgResourceType.TYPE_NOTIFY);
                revokeMsg.setContentObjType(ContentType.MSG_REVOKE);
                revokeMsg.setContentObjContent("你撤回了一条消息");
                msgDao.update(revokeMsg);
                NettyMsgNotify msgNotify = new NettyMsgNotify();
                msgNotify.setNotifyType(NotifyType.MSG_POSITION_UPDATE);
                msgNotify.setNotifyMsg(revokeMsg);
                onMsgNotifyEvent(msgNotify);
            } else {
                ToastUtils.showShortMsg(mContext, "消息撤回失败");
            }
        }

    }


    /**
     * 消息更新到页面
     *
     * @param msgNotify 返回结果
     * @sender {@link MsgReceiveFactory#receivedMessage(Message)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN) //在ui线程执行
    public void onMsgNotifyEvent(NettyMsgNotify msgNotify) {
        if (msgNotify.getNotifyMsg() == null || !fromUserId.equalsIgnoreCase(msgNotify.getNotifyMsg().getFrom())) {
            return;
        }

        if (NotifyType.MSG_LIST == msgNotify.getNotifyType()) {
            Msg msg = msgNotify.getNotifyMsg();
            msg.setIsRead(MsgStatus.IS_READ);
            msgDao.update(msg);
            msgList.add(msg);
            adapterNotify();
        } else if (NotifyType.MSG_POSITION_UPDATE == msgNotify.getNotifyType()) {
            Msg msg = msgNotify.getNotifyMsg();
            for (int i = msgList.size() - 1; i >= 0; i--) {
                Msg m = msgList.get(i);
                if (m.getId().longValue() == msg.getId().longValue()) {
                    msgList.remove(i);
                    msgList.add(i, msg);
                    adapterNotify();
                    break;
                }
            }
        }
    }

    @Override
    public void onStart() {
//        NettyUtils.startOrEndSession(fromUserId, NettyUtils.SESSION_TYPE_START);
        super.onStart();
    }

    @Override
    public void onResume() {
        updateMsgToRead();
        super.onResume();
    }

    @Override
    public void onStop() {
//        NettyUtils.startOrEndSession(fromUserId, NettyUtils.SESSION_TYPE_END);
        super.onStop();
    }

    @Override
    public void onDestroy() {
        MediaManager.getInstance().release();
        EventBusUtils.unRegister(this);
        NettyUtils.startOrEndSession(fromUserId, NettyUtils.SESSION_TYPE_END);
        super.onDestroy();
    }
}
