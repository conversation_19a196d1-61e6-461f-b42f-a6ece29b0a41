package com.doctor.br.activity.mine;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.alibaba.fastjson.JSON;
import com.doctor.br.activity.LoadDataActivity;
import com.doctor.br.activity.MainActivity;
import com.doctor.br.adapter.mine.PersonalDataListAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.app.Common;
import com.doctor.br.bean.DoctorInfoBean;
import com.doctor.br.bean.UpDateHeadBean;
import com.doctor.br.bean.UserExtralProperty;
import com.doctor.br.bean.event.RefreshDoctorDataEvent;
import com.doctor.br.db.entity.ServerConfig;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.CitiesLoadUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.PopUtils;
import com.doctor.br.utils.SelectImgUtils;
import com.doctor.greendao.gen.ServerConfigDao;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ParseData;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.HttpRequestTask;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.AutoHeightListView;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.io.File;
import java.io.FileDescriptor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

import static com.doctor.br.view.SelectPhotoDialog.REQUEST_CROP_PHOTO;

/**
 * 类描述：个人资料
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/17 10:50
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/17 10:50
 */

public class PersonalDataActivity extends ActionBarActivity implements AdapterView.OnItemClickListener {
    //上个界面传递过来的数据
    public static final String DOCTOR_INFO_BEAN = "doctorInfoBean";
    private DoctorInfoBean prePageDoctorInfoBean;//医生详情javabean
    //常量
    public static final String PAPER_NO = "身份证号";
    public static final String NAME = "姓名";
    //界面下的控件
    @BindView(R.id.update_head_lienar)
    RelativeLayout updateHeadLienar;
    @BindView(R.id.head_img)
    ImageView headImg;
    @BindView(R.id.list_view)
    AutoHeightListView listView;

    private CitiesLoadUtils citiesLoadUtils;//三级联动工具类

    private DoctorInfoBean adapterBean;//adapter里面使用的javabean

    //各种popwindow,上传头像，性别，职称，
    private PopUtils updateHeadPop, sexPop, titlePop;

    private RequestCallBack updateHeadCallBack;//网络请求上传头像回调
    private RequestCallBack saveInfoCallBack;//网络请求保存医生信息回调
    private RequestCallBack getIsShowServiceFeeCallBack;//获取用户扩展属性

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_personal_data);
        getIntentData(savedInstanceState);
        initView();
        initPopList();
        setData();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putParcelable(DOCTOR_INFO_BEAN, prePageDoctorInfoBean);
    }

    private void getIntentData(Bundle savedInstanceState) {
        //获取从个人中心传递过来的数据，如果是登录界面跳转过来调用则为空
        if (savedInstanceState != null) {
            prePageDoctorInfoBean = savedInstanceState.getParcelable(DOCTOR_INFO_BEAN);
        } else {
            prePageDoctorInfoBean = getIntent().getParcelableExtra(DOCTOR_INFO_BEAN);
        }
    }

    private void initView() {
        setActionBarTitle("个人资料");
        getBaseActionBar().setRightButton("保存");
        getBaseActionBar().setRightButtonColor(ContextCompat.getColor(this, R.color.br_color_theme));
        getBaseActionBar().setActionBarChangeListener(this);

        updateHeadLienar.setOnClickListener(this);
        listView.setDividerHeight(0);
        adapterBean = new DoctorInfoBean();
        listView.setAdapter(new PersonalDataListAdapter(this, adapterBean));
        listView.setOnItemClickListener(this);//如果用到popwindow的话，设置onitemclicklistener需要设置tag便于区分
        listView.setTag(listView.getId());

        citiesLoadUtils = new CitiesLoadUtils(this, new CitiesLoadUtils.PickerSelectListener() {
            @Override
            public void pickerSelect(String address, String areaCode) {
                adapterBean.setAddress(address);
                adapterBean.setAreaCode(areaCode);
                setAdapter();
            }
        });
    }

    private void setAdapter() {
        if (prePageDoctorInfoBean == null) {
            SharedPreferenceUtils.putString(PersonalDataActivity.this, PublicParams.PERSONAL_DATA, ParseData.parseObject(adapterBean));
        }
        listView.setAdapter(new PersonalDataListAdapter(this, adapterBean));
    }

    //初始化popwindow的数据
    private void initPopList() {
        String[] updateStrs = {"拍照", "从相册选取"};
        updateHeadPop = new PopUtils(this, R.string.personal_select, Arrays.asList(updateStrs), this);
        String[] sexStrs = {"男", "女"};
        sexPop = new PopUtils(this, R.string.personal_select_sex, Arrays.asList(sexStrs), this);
        String[] titleStrs = {"主治医师", "副主任医师", "主任医师", "其他"};
        titlePop = new PopUtils(this, R.string.personal_select_title, Arrays.asList(titleStrs), this);
    }


    //如果是从个人中心进来的话填充数据
    //如果本地保存有信息则从本地获取数据
    private void setData() {
        String json = SharedPreferenceUtils.getString(this, PublicParams.PERSONAL_DATA);
        if (prePageDoctorInfoBean == null && !TextUtils.isEmpty(json)) {
            adapterBean = ParseData.parseString(json, DoctorInfoBean.class);
            mGlideUtils.loadImage(adapterBean.getHandleUrl(), this, headImg, R.drawable.center_info_add_head_img);
            setAdapter();
            return;
        }
        if (prePageDoctorInfoBean == null) {
            return;
        }
        adapterBean = prePageDoctorInfoBean;
        mGlideUtils.loadImage(adapterBean.getHandleUrl(), this, headImg, R.drawable.center_info_add_head_img);
        setAdapter();
    }

    @Override
    public void onRightBtnClick(View view) {
        saveDoctorInfo(adapterBean.getHandleUrl(), adapterBean.getName(), adapterBean.getSex(), adapterBean.getPaperNo(), adapterBean.getAreaCode(),
                adapterBean.getAddress(), adapterBean.getHospitalName(), adapterBean.getTitle(), adapterBean.getGoodAt(), adapterBean.getDesc());
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.update_head_lienar:
                updateHeadPop.showPopupWindow();
                break;
            default:
                break;
        }
    }


    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        if (parent.getTag() == null) {
            return;
        }
        if (parent.getTag() instanceof Integer) {
            int tag = (int) parent.getTag();
            Intent editIntent = new Intent(this, PersonalDataEditTextActivity.class);
            if (R.id.list_view == tag) {
                //是否已经完善资料
                boolean isPerfected = prePageDoctorInfoBean != null && "1".equals(prePageDoctorInfoBean.getIsPerfected());
                //个人中心的listview
                switch (position) {
                    case 0:
                        //姓名
                        if (isPerfected &&
                                !TextUtils.isEmpty(prePageDoctorInfoBean.getName())) {
                            ToastUtils.showShortMsg(this, "请联系客服修改");
                            return;
                        }
                        editIntent.putExtra(PersonalDataEditTextActivity.TITLE, NAME);
                        editIntent.putExtra(PersonalDataEditTextActivity.TEXT, adapterBean.getName());
                        editIntent.putExtra(PersonalDataEditTextActivity.HINT, "请输入您的真实姓名");
                        editIntent.putExtra(PersonalDataEditTextActivity.MAX_LENGTH, "30");
                        startActivityForResult(editIntent, position);
                        break;
                    case 1:
                        //性别
                        if (isPerfected &&
                                ("1".equals(prePageDoctorInfoBean.getSex()) || "2".equals(prePageDoctorInfoBean.getSex()))) {
                            ToastUtils.showShortMsg(this, "请联系客服修改");
                            return;
                        }
                        sexPop.showPopupWindow();
                        break;
                    case 2:
                        //身份证号
                        if (isPerfected &&
                                (prePageDoctorInfoBean.getPaperNo().length() == 18 ||
                                        prePageDoctorInfoBean.getPaperNo().length() == 15)) {
                            ToastUtils.showShortMsg(this, "请联系客服修改");
                            return;
                        }
                        editIntent.putExtra(PersonalDataEditTextActivity.TITLE, "身份证号");
                        editIntent.putExtra(PersonalDataEditTextActivity.TEXT, adapterBean.getPaperNo());
                        editIntent.putExtra(PersonalDataEditTextActivity.HINT, "请输入您的身份证号");
                        editIntent.putExtra(PersonalDataEditTextActivity.MAX_LENGTH, "18");
                        editIntent.putExtra(PersonalDataEditTextActivity.INPUT_TYPE, PAPER_NO);
                        startActivityForResult(editIntent, position);
                        break;
                    case 3:
                        //地区，三级联动
                        if (isPerfected && !TextUtils.isEmpty(prePageDoctorInfoBean.getAreaCode())) {
                            ToastUtils.showShortMsg(this, "请联系客服修改");
                            return;
                        }
                        citiesLoadUtils.showPickerView();
                        break;
                    case 4:
                        //就职机构
                        editIntent.putExtra(PersonalDataEditTextActivity.TITLE, "就职机构");
                        editIntent.putExtra(PersonalDataEditTextActivity.TEXT, adapterBean.getHospitalName());
                        editIntent.putExtra(PersonalDataEditTextActivity.HINT, "请输入您所在的就职机构");
                        editIntent.putExtra(PersonalDataEditTextActivity.SET_HEIGHT, true);
                        startActivityForResult(editIntent, position);
                        break;
                    case 5:
                        //职称
                        titlePop.showPopupWindow();
                        break;
                    case 6:
                        //擅长
                        editIntent.putExtra(PersonalDataEditTextActivity.TITLE, "擅长");
                        editIntent.putExtra(PersonalDataEditTextActivity.TEXT, adapterBean.getGoodAt());
                        editIntent.putExtra(PersonalDataEditTextActivity.HINT, "请输入您擅长的领域");
                        editIntent.putExtra(PersonalDataEditTextActivity.MAX_LENGTH, "200");
                        editIntent.putExtra(PersonalDataEditTextActivity.SET_HEIGHT, true);
                        startActivityForResult(editIntent, position);
                        break;
                    case 7:
                        //简介
                        editIntent.putExtra(PersonalDataEditTextActivity.TITLE, "简介");
                        editIntent.putExtra(PersonalDataEditTextActivity.TEXT, adapterBean.getDesc());
                        editIntent.putExtra(PersonalDataEditTextActivity.HINT, "请输入您的简介");
                        editIntent.putExtra(PersonalDataEditTextActivity.MAX_LENGTH, "2000");
                        editIntent.putExtra(PersonalDataEditTextActivity.SET_HEIGHT, true);
                        startActivityForResult(editIntent, position);
                        break;
                    default:
                        break;
                }
                return;
            }
            if (R.string.personal_select == tag) {
                //上传头像
                switch (position) {
                    case 0://拍照
                        SelectImgUtils.gotoCarema(this);
                        break;
                    case 1://从相册选取
                        SelectImgUtils.gotoPhoto(this);
                        break;
                    default:
                        break;
                }
                updateHeadPop.dismiss();
                return;
            }
            if (R.string.personal_select_sex == tag) {
                //选择性别
                switch (position) {
                    case 0://男
                        adapterBean.setSex("1");
                        break;
                    case 1://女
                        adapterBean.setSex("2");
                        break;
                    default:
                        break;
                }
                setAdapter();
                sexPop.dismiss();
                return;
            }
            if (R.string.personal_select_title == tag) {
                //选择职称
                switch (position) {
                    //"主治医师", "副主任医师", "主任医师", "其他"
                    case 0:
                        adapterBean.setTitle("主治医师");
                        break;
                    case 1:
                        adapterBean.setTitle("副主任医师");
                        break;
                    case 2:
                        adapterBean.setTitle("主任医师");
                        break;
                    case 3:
                        adapterBean.setTitle("其他");
                        break;
                    default:
                        break;
                }
                setAdapter();
                titlePop.dismiss();
            }
        }

    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent intent) {
        SelectImgUtils.onActivityResult(this, requestCode, resultCode, intent);
        switch (requestCode) {
            case 0://编辑姓名
                if (resultCode == RESULT_OK) {
                    String name = intent.getStringExtra(PersonalDataEditTextActivity.TEXT);
                    adapterBean.setName(name);
                    setAdapter();
                }
                break;
            case 2://编辑身份证号
                if (resultCode == RESULT_OK) {
                    String paperNo = intent.getStringExtra(PersonalDataEditTextActivity.TEXT);
                    adapterBean.setPaperNo(paperNo);
                    setAdapter();
                }
                break;
            case 4://编辑就职机构
                if (resultCode == RESULT_OK) {
                    String hospital = intent.getStringExtra(PersonalDataEditTextActivity.TEXT);
                    adapterBean.setHospitalName(hospital);
                    setAdapter();
                }
                break;
            case 6://擅长
                if (resultCode == RESULT_OK) {
                    String goodAt = intent.getStringExtra(PersonalDataEditTextActivity.TEXT);
                    adapterBean.setGoodAt(goodAt);
                    setAdapter();
                }
                break;
            case 7://简介
                if (resultCode == RESULT_OK) {
                    String descrip = intent.getStringExtra(PersonalDataEditTextActivity.TEXT);
                    adapterBean.setDesc(descrip);
                    setAdapter();
                }
                break;
            case REQUEST_CROP_PHOTO:  //剪切图片返回
                if (resultCode == RESULT_OK) {
                    Uri uri = intent.getData();
                    if (uri == null) {
                        return;
                    }
//                    Log.e(">>>>>>>>>>>>>剪切返回","uri==>"+uri.toString());
                    String u = SelectImgUtils.getRealFilePathFromUri(PersonalDataActivity.this, uri);
                    updateHeadImgRequest(u);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        SelectImgUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults);
    }

    /**
     * 网络请求上传头像
     *
     * @param filePath 图片的路径
     */
    private void updateHeadImgRequest(String filePath) {
        if (mLoadingDialog != null) {
            mLoadingDialog.show();
        }
        File file = new File(filePath);
        Map<String, File> fileParams = new HashMap<>();
        fileParams.put(file.getAbsolutePath(), file);
        updateHeadCallBack = HttpRequestTask.getInstance(this).addHttpPostFileRequest(HttpUrlManager.UPDATE_HEAD, null, fileParams, UpDateHeadBean.class, this);
    }

    private ConfirmDialog confirmDialog;//保存信息对话框

    /**
     * 网络请求保存医生资料
     *
     * @param handleUrl    头像的url
     * @param name         姓名
     * @param sex          性别 1 男 2 女
     * @param paperNo      身份证号
     * @param areaCode     区域编码
     * @param hospitalName 就职机构
     * @param title        职称
     * @param goodAt       擅长
     */
    private void saveDoctorInfo(final String handleUrl, final String name, final String sex, final String paperNo, final String areaCode,
                                final String address, final String hospitalName, final String title, final String goodAt, final String description) {
//        if (TextUtils.isEmpty(handleUrl)) {
//            ToastUtils.showShortMsg(this, "请上传您的照片");
//            return;
//        }
        if (name == null || TextUtils.isEmpty(name.trim())) {
            ToastUtils.showShortMsg(this, "请输入您的姓名");
            return;
        }
        if (!"1".equals(sex) && !"2".equals(sex)) {
            ToastUtils.showShortMsg(this, "请选择您的性别");
            return;
        }
        //是否为身份证号长度
        if (TextUtils.isEmpty(paperNo) || !(paperNo.length() == 18 || paperNo.length() == 15)) {
            ToastUtils.showShortMsg(this, "请输入正确的身份证号");
            return;
        }
        if (TextUtils.isEmpty(areaCode)) {
            ToastUtils.showShortMsg(this, "请选择您所在的区域");
            return;
        }
        if (hospitalName == null || TextUtils.isEmpty(hospitalName.trim())) {
            ToastUtils.showShortMsg(this, "请输入您所在的就职机构");
            return;
        }
        if (TextUtils.isEmpty(title)) {
            ToastUtils.showShortMsg(this, "请选择您的职称");
            return;
        }
        if (goodAt == null || TextUtils.isEmpty(goodAt.trim())) {
            ToastUtils.showShortMsg(this, "请输入您擅长的领域");
            return;
        }
        if (prePageDoctorInfoBean == null) {
            confirmDialog = ConfirmDialog.getInstance(this)
                    .setDialogContent("保存后，姓名、性别、区域及身份证号需联系客服修改")
                    .setPositiveText("保存")
                    .setNavigationText("取消")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
                            confirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            Map<String, String> map = new HashMap<>();
                            if (!TextUtils.isEmpty(handleUrl)) {
                                map.put("headImgUrl", handleUrl);
                            }
                            map.put("sex", sex);
                            map.put("name", name);
                            map.put("hospitalName", hospitalName);
                            map.put("title", title);
                            map.put("goodAt", goodAt);
                            map.put("authImgList", null);
                            map.put("paperNo", paperNo);
                            map.put("areaCode", areaCode);
                            map.put("address", address);
                            map.put("description", description);
                            saveInfoCallBack = addHttpPostRequest(HttpUrlManager.SAVE_DOCTOR_INFO, map, UserExtralProperty.class, PersonalDataActivity.this);
                            confirmDialog.dismiss();
                        }
                    });
            confirmDialog.show();
            return;
        }
        Map<String, String> map = new HashMap<>();
        if (!TextUtils.isEmpty(handleUrl)) {
            map.put("headImgUrl", handleUrl);
        }
        map.put("sex", sex);
        map.put("name", name);
        map.put("hospitalName", hospitalName);
        map.put("title", title);
        map.put("goodAt", goodAt);
        map.put("authImgList", null);
        map.put("paperNo", paperNo);
        map.put("areaCode", areaCode);
        map.put("address", address);
        map.put("description", description);
        saveInfoCallBack = addHttpPostRequest(HttpUrlManager.SAVE_DOCTOR_INFO, map, UserExtralProperty.class, PersonalDataActivity.this);
    }

    private ConfirmDialog tryDialog;//试用期提示框

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (headImg == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.UPDATE_HEAD://上传头像图片

                if (result.isRequestSuccessed()) {
                    UpDateHeadBean upDateHeadBean = (UpDateHeadBean) result.getBodyObject();
                    GlideUtils.getInstance().loadImage(upDateHeadBean.getNewHeadUrl(), this, headImg, R.drawable.center_info_add_head_img);
                    adapterBean.setHandleUrl(upDateHeadBean.getNewHeadUrl());
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.SAVE_DOCTOR_INFO://保存个人信息
                if (result.isRequestSuccessed()) {
                    UserExtralProperty bean = (UserExtralProperty) result.getBodyObject();
                    //保存是否显示医技服务费的标识
                    if (bean != null && bean.getData() != null) {
                        String isShow = bean.getData().getApp_ShowMedicalServiceFeeRule();
                        if ("0".equalsIgnoreCase(isShow)) {
                            SharedPreferenceUtils.putBoolean(this, PublicParams.SHOW_MEDICAL_SERVICE_FEE, false);
                        } else if ("1".equalsIgnoreCase(isShow)) {
                            SharedPreferenceUtils.putBoolean(this, PublicParams.SHOW_MEDICAL_SERVICE_FEE, true);
                        }

                        refreshMyCenterFragment();
                        ToastUtils.showShortMsg(this, "保存成功");
                        SharedPreferenceUtils.removeString(this, PublicParams.PERSONAL_DATA);
                        if (prePageDoctorInfoBean == null) {
                            //新注册用户弹窗提示，是否认证
                            tryDialog = ConfirmDialog.getInstance(this)
                                    .setDialogContent("恭喜您获得30天试用期资格，为了不影响您的正常使用，请尽快上传资质认证")
                                    .setNavigationText("开始试用")
                                    .setPositiveText("立即认证")
                                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                                        @Override
                                        public void onNavigationBtnClicked(View view) {
                                            tryDialog.dismiss();
                                            Intent intent;
                                            if (isLoadData()) {
                                                intent = new Intent(PersonalDataActivity.this, LoadDataActivity.class);
                                            } else {
                                                intent = new Intent(PersonalDataActivity.this, MainActivity.class);
                                            }
                                            startActivity(intent);
                                            PersonalDataActivity.this.finish();
                                        }

                                        @Override
                                        public void onPositiveBtnClicked(View view) {
                                            Intent intent = new Intent(PersonalDataActivity.this, QualificationActivity.class);
                                            intent.putExtra(QualificationActivity.CLASS, PersonalDataActivity.class.getSimpleName());
                                            intent.putExtra(QualificationActivity.HISTORY_STATE, "0");
                                            intent.putExtra(QualificationActivity.STATE, "3");
                                            intent.putExtra(QualificationSuccessActivity.NAME, adapterBean.getName());
                                            intent.putExtra(QualificationSuccessActivity.HOSPITAL_NAME, adapterBean.getHospitalName());
                                            intent.putExtra(QualificationSuccessActivity.TITLE, adapterBean.getTitle());
                                            startActivity(intent);
                                            PersonalDataActivity.this.finish();
                                        }
                                    });
                            tryDialog.setCanceledOnTouchOutside(false);
                            tryDialog.show();
                        } else {
                            finish();
                        }
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 获取是否显示医技服务费的信息
     */
    private void getIsShowServiceFeeInfo() {
        HashMap map = new HashMap();
        List<String> names = new ArrayList<>();
        names.add("app_ShowMedicalServiceFeeRule");
        map.put("names", JSON.toJSONString(names));
        getIsShowServiceFeeCallBack = addHttpPostRequest(HttpUrlManager.GET_IS_SHOW_SERVICE_FEE, map, UserExtralProperty.class, this);
    }


    /**
     * 判断是否加载数据
     *
     * @return
     */
    private boolean isLoadData() {
        ServerConfigDao serverConfigDao = AppContext.getInstances().getDaoSession().getServerConfigDao();
        List<ServerConfig> list = serverConfigDao.loadAll();
        if (list != null && list.size() > 0) {
            ServerConfig config = list.get(0);
            if (config != null) {
                if (!TextUtils.isEmpty(config.getArea_addressVersion()) && !config.getArea_addressVersion().equalsIgnoreCase(config.getArea_addressVersionLocal())) {
                    return true;
                }

                if (!TextUtils.isEmpty(config.getProduct_drugVersion()) && !config.getProduct_drugVersion().equalsIgnoreCase(config.getProduct_drugVersionLocal())) {
                    return true;
                }
            }
        }
        String updateContactsListTime = SharedPreferenceUtils.getString(this, PublicParams.UPDATE_CONTACTS_LIST_TIME);
        if (TextUtils.isEmpty(updateContactsListTime)) {
            return true;
        }
        return false;
    }

    /**
     * 从个人资料界面更新我的界面个人信息
     *
     * @sender {@link PersonalDataActivity#refreshMyCenterFragment()}
     * @recevier {@link com.doctor.br.fragment.main.MyCenterFragment#refreshDoctorInfo}
     */
    private void refreshMyCenterFragment() {
        EventBusUtils.post(new RefreshDoctorDataEvent(adapterBean.getHandleUrl(), adapterBean.getName(), adapterBean.getTitle()));
    }

    @Override
    public void onBack(Activity activity) {
        onBackPressed();
    }

    @Override
    public void onBackPressed() {
        if (prePageDoctorInfoBean == null) {
            ToastUtils.showShortMsg(this, "请完善信息并保存");
            return;
        }
        super.onBackPressed();
    }


}
