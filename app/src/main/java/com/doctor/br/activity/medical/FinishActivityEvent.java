package com.doctor.br.activity.medical;

import android.text.TextUtils;

/**
 * <AUTHOR>
 * @date 2022/6/19
 */
public class FinishActivityEvent {
    public FinishActivityEvent(String activityName){
        this.activityName = activityName;
    }
    private String activityName;

    public String getActivityName() {
        return activityName;
    }


    public boolean isCurrentActivity(String curActivityName){
        return TextUtils.equals(curActivityName,activityName);
    }
}
