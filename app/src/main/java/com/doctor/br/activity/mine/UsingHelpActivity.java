package com.doctor.br.activity.mine;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.doctor.br.activity.ActionBarWebViewActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.adapter.mine.UsingHelpRecyclerAdapter;
import com.doctor.br.bean.UsingHelpBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：使用帮助界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/14
 */

public class UsingHelpActivity extends ActionBarActivity implements RecyclerItemClickListener {
    //界面下的控件
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.empty_view)
    EmptyView emptyView;

    private List<UsingHelpBean.ResultListBean> list;//常见问题列表
    private UsingHelpRecyclerAdapter usingHelpRecyclerAdapter;//适配器
    private RequestCallBack questionCallBack;//网络请求常见问题列表回调

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_using_help);
        initView();
        getQuestionListRequest();
    }

    private void initView() {
        setActionBarTitle("常见问题");

        recyclerView.setHasFixedSize(true);
        list = new ArrayList<>();
        usingHelpRecyclerAdapter = new UsingHelpRecyclerAdapter(this, list, this);
        recyclerView.setAdapter(usingHelpRecyclerAdapter);
    }

    @Override
    public void itemClick(int position) {
        //跳转到h5页面
        Intent intent = new Intent(this, ActionBarWebViewActivity.class);
        intent.putExtra(PublicParams.WEBVIEW_TITLE, "问题解答");
        intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, list.get(position).getAnw());
        startActivity(intent);
    }

    /**
     * 网络请求获取常见问题列表
     */
    private void getQuestionListRequest() {
        emptyView.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
        map.put("page", "1");
        map.put("pageSize", "20");
        questionCallBack = addHttpPostRequest(HttpUrlManager.USING_HELP, map, UsingHelpBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (emptyView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.USING_HELP://常见问题列表
                if (result.isRequestSuccessed()) {
                    UsingHelpBean usingHelpBean = (UsingHelpBean) result.getBodyObject();
                    list.clear();
                    list.addAll(usingHelpBean.getResultList());
                    usingHelpRecyclerAdapter.notifyDataSetChanged();
                    if (list.size() == 0) {
                        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                        emptyView.setVisibility(View.VISIBLE);
                    }
                } else {
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                        @Override
                        public void onReload() {
                            getQuestionListRequest();
                        }
                    });
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (questionCallBack != null) {
            questionCallBack.cancel();
        }
        super.onDestroy();
    }


}
