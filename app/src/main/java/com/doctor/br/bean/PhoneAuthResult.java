package com.doctor.br.bean;

import org.newapp.ones.base.dataBean.ResponseResult;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 号码认证的返回结果（code=000452）
 * @createTime 2025/06/08
 */
public class PhoneAuthResult extends ResponseResult {
    
    /**
     * 手机号码
     */
    private String mobile;
    
    /**
     * 号码认证是否可用
     * true: 可用，false: 不可用
     */
    private boolean available;

    /**
     * 获取手机号码
     * @return 手机号码
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * 设置手机号码
     * @param mobile 手机号码
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * 获取号码认证可用状态
     * @return true: 可用，false: 不可用
     */
    public boolean isAvailable() {
        return available;
    }

    /**
     * 设置号码认证可用状态
     * @param available 可用状态
     */
    public void setAvailable(boolean available) {
        this.available = available;
    }

    @Override
    public String toString() {
        return "PhoneAuthResult{" +
                "mobile='" + mobile + '\'' +
                ", available=" + available +
                ", code='" + getCode() + '\'' +
                ", errorMsg='" + getErrorMsg() + '\'' +
                '}';
    }
}