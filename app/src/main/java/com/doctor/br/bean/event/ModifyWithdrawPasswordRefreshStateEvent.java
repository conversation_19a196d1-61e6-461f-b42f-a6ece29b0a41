package com.doctor.br.bean.event;

/**
 * 类描述：修改提现密码界面刷新状态事件
 * 创建人：Assistant
 * 创建时间：2024/12/15
 */

public class ModifyWithdrawPasswordRefreshStateEvent {
    private boolean passwordModifyRefresh;
    private boolean smsModifyRefresh;

    public ModifyWithdrawPasswordRefreshStateEvent(boolean passwordModifyRefresh, boolean smsModifyRefresh) {
        this.passwordModifyRefresh = passwordModifyRefresh;
        this.smsModifyRefresh = smsModifyRefresh;
    }

    public boolean isPasswordModifyRefresh() {
        return passwordModifyRefresh;
    }

    public void setPasswordModifyRefresh(boolean passwordModifyRefresh) {
        this.passwordModifyRefresh = passwordModifyRefresh;
    }

    public boolean isSmsModifyRefresh() {
        return smsModifyRefresh;
    }

    public void setSmsModifyRefresh(boolean smsModifyRefresh) {
        this.smsModifyRefresh = smsModifyRefresh;
    }
}