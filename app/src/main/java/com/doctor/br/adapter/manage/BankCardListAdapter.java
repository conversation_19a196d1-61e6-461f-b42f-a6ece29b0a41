package com.doctor.br.adapter.manage;

import androidx.recyclerview.widget.RecyclerView;
import android.widget.ImageView;

import com.doctor.br.bean.BankCardBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;

import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;
import cn.bingoogolapple.androidcommon.adapter.BGAViewHolderHelper;

/**
 * Created by hanruifeng on 2017/12/11.
 */

public class BankCardListAdapter extends BGARecyclerViewAdapter<BankCardBean.BankCardItemBean> {

    public BankCardListAdapter(RecyclerView recyclerView) {
        super(recyclerView, R.layout.item_bank_card);
    }

    @Override
    protected void fillData(BGAViewHolderHelper helper, int position, BankCardBean.BankCardItemBean bankCardItemBean) {
        if(bankCardItemBean==null) return;
        helper.setText(R.id.tv_bank_card,bankCardItemBean.getBankName());
        ImageView ivBankCard = helper.getImageView(R.id.iv_bank_card);
        GlideUtils.getInstance().loadImage(bankCardItemBean.getBankImgUrl(),mContext,ivBankCard);
    }
}
