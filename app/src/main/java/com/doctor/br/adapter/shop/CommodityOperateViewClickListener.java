package com.doctor.br.adapter.shop;

/**
 * Author:sunxiaxia
 * createdDate:2018/8/22
 * description:商品操作事件监听
 * eg: 删除、下架、上架、分享等操作
 */


public interface CommodityOperateViewClickListener {
    /**
     * recyclerview item的点击监听
     *
     * @param position 点击的item
     */
    void itemClick(int position);

    /**
     * recyclerview item中左边view的点击监听
     *
     * @param position 点击的item
     */
    void onLeftViewClick(int position);

    /**
     * recyclerview item中右边view的点击监听
     *
     * @param position 点击的item
     */
    void onRightViewClick(int position);
}
