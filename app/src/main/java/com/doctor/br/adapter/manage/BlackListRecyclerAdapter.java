package com.doctor.br.adapter.manage;

import android.annotation.SuppressLint;
import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.bean.BlackListBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：黑名单recyclerview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/6
 */

public class BlackListRecyclerAdapter extends RecyclerView.Adapter<BlackListRecyclerAdapter.VH> implements View.OnClickListener {


    private Context context;
    private List<BlackListBean.DataBean> list;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public BlackListRecyclerAdapter(Context context, List<BlackListBean.DataBean> list, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_black_list, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(view);
        if (recyclerItemItemClickListener != null) {
            holder.selectCb.setOnClickListener(this);
        }
        return holder;
    }

    @SuppressLint("ResourceAsColor")
    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.selectCb.setTag(R.id.img_url, position);

        BlackListBean.DataBean dataBean = list.get(position);

        if (dataBean.isSelected()) {
            holder.selectCb.setChecked(true);
        } else {
            holder.selectCb.setChecked(false);
        }

        if (position == 0) {
            holder.topLine.setVisibility(View.GONE);
        } else {
            holder.topLine.setVisibility(View.VISIBLE);
        }

        GlideUtils.getInstance().loadImage(dataBean.getHead_image_url(), context, holder.headImgIv, R.drawable.default_head_img);

        String nickName = dataBean.getPatientName();
        String remark = dataBean.getRemark();
        //  患者是否有备注，展示样式
        if (!TextUtils.isEmpty(nickName)) {
            if (TextUtils.isEmpty(remark)) {
                //没有备注
                holder.nickNameTv.setText((nickName.length() > 6 ? nickName.substring(0, 6) + "…" : nickName) + "");
            } else {
                //有备注
                holder.nickNameTv.setText(remark + "(" + (nickName.length() > 3 ? nickName.substring(0, 3) + "…" : nickName) + ")");
            }
        } else {
            holder.nickNameTv.setText("");
        }

        if (!TextUtils.isEmpty(dataBean.getSex())) {
            if ("1".equals(dataBean.getSex())) {//1为男，其他为女
                holder.sexImgIv.setImageResource(R.drawable.male_icon_white);
                holder.sexAgeLl.setBackgroundResource(R.drawable.sex_age_blue_bg);
            } else {
                holder.sexImgIv.setImageResource(R.drawable.female_icon_white);
                holder.sexAgeLl.setBackgroundResource(R.drawable.sex_age_red_bg);
            }
        }
        if (!TextUtils.isEmpty(dataBean.getAge()) && !"0".equals(dataBean.getAge())) {
            holder.ageTv.setVisibility(View.VISIBLE);
            holder.ageTv.setText(dataBean.getAge() + "");
        } else {
            holder.ageTv.setVisibility(View.GONE);
        }
        if (TextUtils.isEmpty(dataBean.getSex()) && TextUtils.isEmpty(dataBean.getAge())) {
            holder.sexAgeLl.setBackgroundResource(R.color.br_color_white);
        }

        if (dataBean.getTags() != null && dataBean.getTags().size() > 0) {
            holder.tvTag1.setText(dataBean.getTags().get(0));
            holder.tvTag1.setVisibility(View.VISIBLE);
            if (dataBean.getTags().size() > 1) {
                holder.tvTag2.setText(dataBean.getTags().get(1));
                holder.tvTag2.setVisibility(View.VISIBLE);
            } else {
                holder.tvTag2.setVisibility(View.GONE);
            }
        } else {
            holder.tvTag1.setVisibility(View.GONE);
            holder.tvTag2.setVisibility(View.GONE);
        }


    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.select_cb)
        CheckBox selectCb;
        @BindView(R.id.head_img_iv)
        ShapeImageView headImgIv;
        @BindView(R.id.nick_name_tv)
        TextView nickNameTv;
        @BindView(R.id.sex_img_iv)
        ImageView sexImgIv;
        @BindView(R.id.age_tv)
        TextView ageTv;
        @BindView(R.id.sex_age_ll)
        LinearLayout sexAgeLl;
        @BindView(R.id.tv_tag1)
        TextView tvTag1;
        @BindView(R.id.tv_tag2)
        TextView tvTag2;
        @BindView(R.id.top_line)
        View topLine;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
