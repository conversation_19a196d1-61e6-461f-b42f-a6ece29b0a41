package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.bean.GetAuthenticationBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.ImageUtils;
import org.newapp.ones.base.utils.glide.GlideUtils;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：资质认证，为了方便编写
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/30
 */

public class QualificationAdapter extends RecyclerView.Adapter implements View.OnClickListener {
    private final int HEADER = 111;//头部
    private final int ITEM = 222;//条目
    private final int FOOTER = 333;//尾部

    private Context context;
    private GetAuthenticationBean getAuthenticationBean;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;
    private String isAuthentication;//提交审核是否可以点击

    private ForegroundColorSpan blueColorSpan;
    private AbsoluteSizeSpan sizeSpan;

    public QualificationAdapter(Context context, GetAuthenticationBean getAuthenticationBean, String isAuthentication, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.getAuthenticationBean = getAuthenticationBean;
        this.isAuthentication = isAuthentication;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;

        blueColorSpan = new ForegroundColorSpan(ContextCompat.getColor(context, R.color.br_color_theme));
        sizeSpan = new AbsoluteSizeSpan(14, true);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case HEADER:
                View view1 = LayoutInflater.from(context).inflate(R.layout.item_qualification_header, parent, false);//这种方法不会使布局自适应
                return new HeaderVH(view1);
            case ITEM:
                View view2 = LayoutInflater.from(context).inflate(R.layout.item_qualification_item, parent, false);//这种方法不会使布局自适应
                ItemVH itemVH = new ItemVH(view2);
                if (recyclerItemItemClickListener != null) {
                    itemVH.leftAddImg.setOnClickListener(this);
                    itemVH.centerAddImg.setOnClickListener(this);
                    itemVH.rightAddImg.setOnClickListener(this);
                }
                return itemVH;
            case FOOTER:
                View view3 = LayoutInflater.from(context).inflate(R.layout.item_qualification_footer, parent, false);//这种方法不会使布局自适应
                FooterVH footerVH = new FooterVH(view3);
                if (recyclerItemItemClickListener != null && !"2".equals(isAuthentication)) {
                    footerVH.commitBtn.setOnClickListener(this);
                }
                return footerVH;
            default:
                break;
        }
        return null;
    }


    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        switch (holder.getItemViewType()) {
            case HEADER:
                break;
            case ITEM:
                ItemVH itemVH = (ItemVH) holder;
                itemVH.leftAddImg.setTag(R.id.img_url, position);
                itemVH.rightAddImg.setTag(R.id.img_url, position);
                itemVH.centerAddImg.setTag(R.id.img_url, position);
                //默认只显示一个图片位置
                itemVH.rightAddImg.setVisibility(View.GONE);
                itemVH.centerAddImg.setVisibility(View.GONE);
                //图片出错按钮默认隐藏
                itemVH.leftDeleteImg.setVisibility(View.GONE);
                itemVH.centerDeleteImg.setVisibility(View.GONE);
                itemVH.rightDeleteImg.setVisibility(View.GONE);
                switch (position) {
                    case 1:
                        String str1 = "请上传 医师资格证书或其他相关证书（原件）";
                        SpannableString spannableString1 = new SpannableString(str1);
                        spannableString1.setSpan(blueColorSpan, str1.indexOf("医师资格证书"), str1.indexOf("或"), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                        spannableString1.setSpan(sizeSpan, str1.indexOf("（"), str1.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                        itemVH.nameTv.setText(spannableString1);
                        if (getAuthenticationBean == null) {
                            break;
                        }
                        if (getAuthenticationBean.getWorkCardUrl().size() >= 1) {
                            GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(getAuthenticationBean.getWorkCardUrl().get(0).getImgUrl()), context, itemVH.leftAddImg, R.drawable.qualification_add_img);
                            itemVH.centerAddImg.setVisibility(View.VISIBLE);
                            if ("4".equals(getAuthenticationBean.getWorkCardUrl().get(0).getResult())) {
                                itemVH.leftDeleteImg.setVisibility(View.VISIBLE);
                            }
                        }
                        if (getAuthenticationBean.getWorkCardUrl().size() >= 2) {
                            GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(getAuthenticationBean.getWorkCardUrl().get(1).getImgUrl()), context, itemVH.centerAddImg, R.drawable.qualification_add_img);
                            itemVH.rightAddImg.setVisibility(View.VISIBLE);
                            if ("4".equals(getAuthenticationBean.getWorkCardUrl().get(1).getResult())) {
                                itemVH.centerDeleteImg.setVisibility(View.VISIBLE);
                            }
                        }
                        if (getAuthenticationBean.getWorkCardUrl().size() >= 3) {
                            GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(getAuthenticationBean.getWorkCardUrl().get(2).getImgUrl()), context, itemVH.rightAddImg, R.drawable.qualification_add_img);
                            if ("4".equals(getAuthenticationBean.getWorkCardUrl().get(2).getResult())) {
                                itemVH.rightDeleteImg.setVisibility(View.VISIBLE);
                            }
                        }
                        break;
                    case 2:
                        String str2 = "请上传 医师执业证书（原件、复印件、网站截图）";
                        SpannableString spannableString2 = new SpannableString(str2);
                        spannableString2.setSpan(blueColorSpan, str2.indexOf("医师"), str2.indexOf("（"), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                        spannableString2.setSpan(sizeSpan, str2.indexOf("（"), str2.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                        itemVH.nameTv.setText(spannableString2);
                        if (getAuthenticationBean == null) {
                            break;
                        }
                        if (getAuthenticationBean.getQualificationsCardUrl().size() >= 1) {
                            GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(getAuthenticationBean.getQualificationsCardUrl().get(0).getImgUrl()), context, itemVH.leftAddImg, R.drawable.qualification_add_img);
                            itemVH.centerAddImg.setVisibility(View.VISIBLE);
                            if ("4".equals(getAuthenticationBean.getQualificationsCardUrl().get(0).getResult())) {
                                itemVH.leftDeleteImg.setVisibility(View.VISIBLE);
                            }
                        }
                        if (getAuthenticationBean.getQualificationsCardUrl().size() >= 2) {
                            GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(getAuthenticationBean.getQualificationsCardUrl().get(1).getImgUrl()), context, itemVH.centerAddImg, R.drawable.qualification_add_img);
                            itemVH.rightAddImg.setVisibility(View.VISIBLE);
                            if ("4".equals(getAuthenticationBean.getQualificationsCardUrl().get(1).getResult())) {
                                itemVH.centerDeleteImg.setVisibility(View.VISIBLE);
                            }
                        }
                        if (getAuthenticationBean.getQualificationsCardUrl().size() >= 3) {
                            GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(getAuthenticationBean.getQualificationsCardUrl().get(2).getImgUrl()), context, itemVH.rightAddImg, R.drawable.qualification_add_img);
                            if ("4".equals(getAuthenticationBean.getQualificationsCardUrl().get(2).getResult())) {
                                itemVH.rightDeleteImg.setVisibility(View.VISIBLE);
                            }
                        }
                        break;
                    case 3:
                        String str3 = "请上传 职称证书（主治及以上职称请务必上传原件，缺省系统将默认中医师）";
                        SpannableString spannableString3 = new SpannableString(str3);
                        spannableString3.setSpan(blueColorSpan, str3.indexOf("职称证书"), str3.indexOf("（"), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                        spannableString3.setSpan(sizeSpan, str3.indexOf("（"), str3.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                        itemVH.nameTv.setText(spannableString3);
                        if (getAuthenticationBean == null) {
                            break;
                        }
                        if (getAuthenticationBean.getPracticeCardUrl().size() >= 1) {
                            GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(getAuthenticationBean.getPracticeCardUrl().get(0).getImgUrl()), context, itemVH.leftAddImg, R.drawable.qualification_add_img);
                            itemVH.centerAddImg.setVisibility(View.VISIBLE);
                            if ("4".equals(getAuthenticationBean.getPracticeCardUrl().get(0).getResult())) {
                                itemVH.leftDeleteImg.setVisibility(View.VISIBLE);
                            }
                        }
                        if (getAuthenticationBean.getPracticeCardUrl().size() >= 2) {
                            GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(getAuthenticationBean.getPracticeCardUrl().get(1).getImgUrl()), context, itemVH.centerAddImg, R.drawable.qualification_add_img);
                            itemVH.rightAddImg.setVisibility(View.VISIBLE);
                            if ("4".equals(getAuthenticationBean.getPracticeCardUrl().get(1).getResult())) {
                                itemVH.centerDeleteImg.setVisibility(View.VISIBLE);
                            }
                        }
                        if (getAuthenticationBean.getPracticeCardUrl().size() >= 3) {
                            GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(getAuthenticationBean.getPracticeCardUrl().get(2).getImgUrl()), context, itemVH.rightAddImg, R.drawable.qualification_add_img);
                            if ("4".equals(getAuthenticationBean.getPracticeCardUrl().get(2).getResult())) {
                                itemVH.rightDeleteImg.setVisibility(View.VISIBLE);
                            }
                        }
                        break;
                    default:
                        break;
                }
                break;
            case FOOTER:
                FooterVH footerVH = (FooterVH) holder;
                footerVH.commitBtn.setTag(R.id.img_url, position);
                if ("2".equals(isAuthentication)) {
                    footerVH.commitBtn.setBackgroundResource(R.drawable.btn_circle_gray_line);
                    footerVH.commitBtn.setTextColor(ContextCompat.getColor(context, R.color.br_color_black_999));
                    footerVH.commitBtn.setText("审核中");
                }
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemCount() {
        return 5;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return HEADER;
        }
        if (position == getItemCount() - 1) {
            return FOOTER;
        }
        return ITEM;
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class HeaderVH extends RecyclerView.ViewHolder {

        HeaderVH(View itemView) {
            super(itemView);
        }
    }

    static class ItemVH extends RecyclerView.ViewHolder {
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.left_add_img)
        ImageView leftAddImg;
        @BindView(R.id.center_add_img)
        ImageView centerAddImg;
        @BindView(R.id.right_add_img)
        ImageView rightAddImg;
        @BindView(R.id.left_delete_img)
        ImageView leftDeleteImg;
        @BindView(R.id.center_delete_img)
        ImageView centerDeleteImg;
        @BindView(R.id.right_delete_img)
        ImageView rightDeleteImg;

        ItemVH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    static class FooterVH extends RecyclerView.ViewHolder {
        @BindView(R.id.commit_btn)
        com.doctor.br.view.NoDoubleClickBtn commitBtn;

        FooterVH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
