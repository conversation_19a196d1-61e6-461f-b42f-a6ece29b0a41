package com.doctor.br.adapter.manage;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.doctor.br.bean.medical.ClassicMedicationBean;
import com.doctor.br.view.stickyListView.StickyListHeadersAdapter;
import com.doctor.yy.R;

import java.util.List;
/**
 * <AUTHOR>
 * @version 4.0.3
 * @project BrZhongYiAndroid
 * @description  经典方的adapter
 * @createTime 2018/3/14
 */

public class ClassicMedicineListAdapter extends BaseAdapter implements StickyListHeadersAdapter {

    private Context context;
    private List<ClassicMedicationBean.DataBean> list;

    public ClassicMedicineListAdapter(Context context, List<ClassicMedicationBean.DataBean> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public View getHeaderView(int position, View convertView, ViewGroup parent) {
        HeaderViewHolder headerViewHolder;
        if (convertView == null) {
            headerViewHolder = new HeaderViewHolder();
            convertView = LayoutInflater.from(context).inflate(R.layout.item_medicine_list_header, parent, false);//这种方法不会使布局自适应
            headerViewHolder.textView = (TextView) convertView.findViewById(R.id.header_tv);
            convertView.setTag(headerViewHolder);
        } else {
            headerViewHolder = (HeaderViewHolder) convertView.getTag();
        }
        if(list!=null && list.get(position)!=null && list.get(position).getFirstSpell()!=null){//设置字符
            headerViewHolder.textView.setText(list.get(position).getFirstSpell().toUpperCase());
        }else{
            headerViewHolder.textView.setText("#");
        }


        return convertView;
    }





    @Override
    public long getHeaderId(int position) {
        if(TextUtils.isEmpty(list.get(position).getFirstSpell())){//解决首字母为空的问题
            return "|".charAt(0);
        }
        return list.get(position).getFirstSpell().charAt(0);
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public Object getItem(int position) {
        return list.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (convertView == null) {
            viewHolder = new ViewHolder();
            convertView = LayoutInflater.from(context).inflate(R.layout.item_medicine_list, parent, false);//这种方法不会使布局自适应
            viewHolder.textView = (TextView) convertView.findViewById(R.id.name_tv);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        viewHolder.textView.setText(list.get(position).getName());
        return convertView;
    }

    private static class HeaderViewHolder {
        TextView textView;
    }

    private static class ViewHolder {
        TextView textView;
    }
}
