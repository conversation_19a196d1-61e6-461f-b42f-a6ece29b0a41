package com.doctor.br.adapter.manage;

import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableStringBuilder;
import android.view.View;

import com.doctor.br.bean.CommonPrescriptionBean;
import com.doctor.br.bean.medical.MedicineDetailMsgBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.LogUtils;

import java.text.DecimalFormat;
import java.util.List;

import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;
import cn.bingoogolapple.androidcommon.adapter.BGAViewHolderHelper;

/**
 * Created by lenovo on 2017/11/15.
 * 常用方界面的adapter
 */

public class CommonPrescriptionAdapter extends BGARecyclerViewAdapter<CommonPrescriptionBean.DatalistBean> {
    public static final int TYPE_CHARACTER = 1;//头部1

    public CommonPrescriptionAdapter(RecyclerView recyclerView) {
        super(recyclerView, R.layout.item_common_prescription_bottom);
    }

    @Override
    protected void setItemChildListener(BGAViewHolderHelper helper, int viewType) {
        super.setItemChildListener(helper, viewType);
        if(viewType !=TYPE_CHARACTER){
            helper.setItemChildClickListener(R.id.rl_container);//添加点击事件
            helper.setItemChildLongClickListener(R.id.rl_container);//添加长点击事件
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (position<mData.size() && mData.get(position).getType() == TYPE_CHARACTER) {
            return R.layout.item_common_prescription_top;
        }
        return super.getItemViewType(position);
    }

    @Override
    protected void fillData(BGAViewHolderHelper helper, int position, CommonPrescriptionBean.DatalistBean listBean) {

        //int viewType =getItemViewType(position);注意，此方法在点击之后获取的position,会比正常的小1，所以不用此方法
        int viewType = listBean.getType();
        if(viewType !=TYPE_CHARACTER){  //联系人的内容  tv_title     tv_content   view_line
            helper.setText(R.id.tv_title,listBean.getName());
            helper.setText(R.id.tv_content,getMedicationDetail(listBean.getList()));
            setLineData(helper,position,listBean);

        }else{//字符
            if(listBean.getFirstSpell().equals("|")){
                helper.setText(R.id.tv_character,"#");
            }else{
                helper.setText(R.id.tv_character,listBean.getFirstSpell());
            }

        }
    }

    public static String formatNumber(String input) {
        LogUtils.i(CommonPrescriptionAdapter.class, "格式化数字 == " + input);
        try {
            double number = Double.parseDouble(input);
            DecimalFormat df = new DecimalFormat("#.##");
            df.setMinimumFractionDigits(0);
            df.setMaximumFractionDigits(2);
            return df.format(number);
        } catch (NumberFormatException e) {
            return input; // 如果无法解析为数字，则返回原始输入
        }
    }

    public String getMedicationDetail(List<MedicineDetailMsgBean> drugList){//获取用药内容
        if(drugList!=null && drugList.size()>0){
            SpannableStringBuilder ssb = new SpannableStringBuilder();
            for(int i=0;i<drugList.size();i++){
                MedicineDetailMsgBean medicineDetailMsgBean = drugList.get(i);
                if(medicineDetailMsgBean!=null){
                    ssb.append(medicineDetailMsgBean.getDrugName()+"-");
                    String dose = formatNumber(medicineDetailMsgBean.getDose());
                    ssb.append(dose+"");
                    ssb.append(medicineDetailMsgBean.getUnit());
                    if(drugList.size()>1 && i!=drugList.size()-1){
                        ssb.append("，");
                    }
                }

            }

           return ssb.toString();
        }else{
            return "";
        }
    }

    private void setLineData(BGAViewHolderHelper helper, int position, CommonPrescriptionBean.DatalistBean dossierItem) {
        View line = helper.getView(R.id.view_line);
        if(getItemCount()>0){
            if(position == mData.size()-1){  //如果是最后一条，线不可见
                line.setVisibility(View.INVISIBLE);
            }else if(position<mData.size()-1){//不是最后一条
                CommonPrescriptionBean.DatalistBean nextItemBean = mData.get(position + 1);
                if(nextItemBean.getType() ==0){//如果下一条是药材内容,则线可见,否则不可见
                    line.setVisibility(View.VISIBLE);
                }else{
                    line.setVisibility(View.INVISIBLE);
                }
            }
        }
    }

}
