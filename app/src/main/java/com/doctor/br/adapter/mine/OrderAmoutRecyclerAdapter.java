package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentOrderPriceBean;
import com.doctor.yy.R;

import java.util.List;

/**
 * 类描述：订单金额
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/10 16:10
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/10 16:10
 */

public class OrderAmoutRecyclerAdapter extends RecyclerView.Adapter<OrderAmoutRecyclerAdapter.VH> implements View.OnClickListener {
    private Context context;
    private List<AgentOrderPriceBean.ListBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public OrderAmoutRecyclerAdapter(Context context, List<AgentOrderPriceBean.ListBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_patient_number_recycler, parent, false);//这种方法不会使布局自适应
        if (recyclerItemClickListener != null) {
            view.setOnClickListener(this);
        }
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(position);

        holder.numberTv.setText(position + 1 + "");
        holder.doctorNameTv.setText(list.get(position).getDoctorName());
        holder.orderAmountTv.setText(list.get(position).getOrderPrice());
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        TextView numberTv;
        TextView doctorNameTv;
        TextView orderAmountTv;

        public VH(View itemView) {
            super(itemView);
            numberTv = (TextView) itemView.findViewById(R.id.number_tv);
            doctorNameTv = (TextView) itemView.findViewById(R.id.doctor_name_tv);
            orderAmountTv = (TextView) itemView.findViewById(R.id.patient_numbers_tv);
        }
    }
}
