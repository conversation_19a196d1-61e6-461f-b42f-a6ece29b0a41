package com.doctor.br.adapter.manage;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.doctor.br.db.entity.MedicineItem;
import com.doctor.br.utils.RegularUtils;
import com.doctor.br.view.stickyListView.StickyListHeadersAdapter;
import com.doctor.yy.R;

import java.util.List;

/**
 * 类描述：药典列表悬浮固定适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/16
 */

public class MedicineListAdapter extends BaseAdapter implements StickyListHeadersAdapter {

    private Context context;
    private List<MedicineItem> list;

    public MedicineListAdapter(Context context, List<MedicineItem> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public View getHeaderView(int position, View convertView, ViewGroup parent) {
        HeaderViewHolder headerViewHolder;
        if (convertView == null) {
            headerViewHolder = new HeaderViewHolder();
            convertView = LayoutInflater.from(context).inflate(R.layout.item_medicine_list_header, parent, false);//这种方法不会使布局自适应
            headerViewHolder.textView = (TextView) convertView.findViewById(R.id.header_tv);
            convertView.setTag(headerViewHolder);
        } else {
            headerViewHolder = (HeaderViewHolder) convertView.getTag();
        }
        headerViewHolder.textView.setText(RegularUtils.letterFilter(list.get(position).getFristLeter().toUpperCase()));

        return convertView;
    }


    @Override
    public long getHeaderId(int position) {
        return list.get(position).getFristLeter().charAt(0);
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public Object getItem(int position) {
        return list.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (convertView == null) {
            viewHolder = new ViewHolder();
            convertView = LayoutInflater.from(context).inflate(R.layout.item_medicine_list, parent, false);//这种方法不会使布局自适应
            viewHolder.textView = (TextView) convertView.findViewById(R.id.name_tv);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        viewHolder.textView.setText(list.get(position).getDrugName());
        return convertView;
    }

    private static class HeaderViewHolder {
        TextView textView;
    }

    private static class ViewHolder {
        TextView textView;
    }
}
