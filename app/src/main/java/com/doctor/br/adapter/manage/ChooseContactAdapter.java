package com.doctor.br.adapter.manage;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.CheckBox;
import android.widget.RelativeLayout;

import com.doctor.br.app.AppContext;
import com.doctor.br.bean.ChooseContactBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.DensityUtils;

import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;
import cn.bingoogolapple.androidcommon.adapter.BGAViewHolderHelper;

/**
 * Created by lenovo on 2017/11/15.
 */

public class ChooseContactAdapter extends BGARecyclerViewAdapter<ChooseContactBean.ItemsBean> {

    public ChooseContactAdapter(RecyclerView recyclerView) {
        super(recyclerView, R.layout.item_choose_contact_bottom);
    }

    @Override
    protected void setItemChildListener(BGAViewHolderHelper helper, int viewType) {
        super.setItemChildListener(helper, viewType);
        helper.setItemChildClickListener(R.id.rl_container);
    }

    @Override
    public int getItemViewType(int position) {
        if (mData.get(position).getType() == 1) {
            return R.layout.item_choose_contact_title;
        }
        return super.getItemViewType(position);
    }

    @Override
    protected void fillData(BGAViewHolderHelper helper, int position, ChooseContactBean.ItemsBean dossierItem) {

        //int viewType =getItemViewType(position);注意，此方法在点击之后获取的position,会比正常的小1，所以不用此方法
        int viewType = dossierItem.getType();
        if(viewType == 0){//联系人的内容    iv_photo    tv_name    iv_femile    tv_age     tv_location   view_line
            CheckBox cbContact = helper.getView(R.id.cb_contact);
            cbContact.setChecked(dossierItem.isChecked());
            helper.setText(R.id.tv_name,dossierItem.isChecked()?"hehe":"张晓峰");
            //设置分割线
            initDivideLine(helper,position,dossierItem);

        }else if(viewType == 1){//字符
            helper.setText(R.id.tv_character,"A");
        }
    }
    public void  initDivideLine(BGAViewHolderHelper helper, int position, ChooseContactBean.ItemsBean dossierItem){

        if(getItemCount()>0 && position<getItemCount()-1){  //最后一条线是默认的没有margin
            ChooseContactBean.ItemsBean nextItemsBean = mData.get(position + 1);
            View viewLine = helper.getView(R.id.view_line);
            RelativeLayout.LayoutParams viewLineParams = (RelativeLayout.LayoutParams) viewLine.getLayoutParams();
            if(nextItemsBean.getType() == 0){  //如果下一个是联系人
                int padding = DensityUtils.dip2px(AppContext.getContext(), 15);
                viewLineParams.setMargins(padding,viewLine.getTop(),padding,viewLine.getBottom());
            }else{//如果下一个是title
                viewLineParams.setMargins(0,viewLine.getTop(),0,viewLine.getBottom());
            }
            viewLine.setLayoutParams(viewLineParams);
        }
    }

}
