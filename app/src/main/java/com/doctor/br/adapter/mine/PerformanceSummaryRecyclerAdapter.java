package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentPerformanaceSummaryBean;
import com.doctor.yy.R;

import java.util.List;

/**
 * 类描述：业绩汇总适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/10 12:58
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/10 12:58
 */

public class PerformanceSummaryRecyclerAdapter extends RecyclerView.Adapter<PerformanceSummaryRecyclerAdapter.VH> implements View.OnClickListener {

    private Context context;
    private List<AgentPerformanaceSummaryBean.ListBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public PerformanceSummaryRecyclerAdapter(Context context, List<AgentPerformanaceSummaryBean.ListBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_performance_summary_recycler, parent, false);//这种方法不会使布局自适应
        if (recyclerItemClickListener != null) {
            view.setOnClickListener(this);
        }
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(position);

        holder.doctorNameTv.setText(list.get(position).getDoctorName());

        String str = "配比收入:¥" + list.get(position).getPbincome();
        SpannableString spannableString = new SpannableString(str);
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(context,R.color.br_color_theme)),
                str.indexOf("¥"), spannableString.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        holder.ratioIncomeTv.setText(spannableString);

        holder.orderAmountTv.setText("订单金额:¥" + list.get(position).getOrderTotal());
        holder.averageUnitPriceTv.setText("均客单价:¥" + list.get(position).getJkUnit());
        holder.patientNumbersTv.setText("患者数量:" + list.get(position).getPatientCount());
        holder.prescriptionNumbersTv.setText("药方数量:" + list.get(position).getOrderCount());
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        TextView doctorNameTv, ratioIncomeTv,
                orderAmountTv, averageUnitPriceTv,
                patientNumbersTv, prescriptionNumbersTv;

        public VH(View itemView) {
            super(itemView);
            doctorNameTv = (TextView) itemView.findViewById(R.id.doctor_name_tv);
            ratioIncomeTv = (TextView) itemView.findViewById(R.id.ratio_income_tv);
            orderAmountTv = (TextView) itemView.findViewById(R.id.order_amount_tv);
            averageUnitPriceTv = (TextView) itemView.findViewById(R.id.average_unit_price_tv);
            patientNumbersTv = (TextView) itemView.findViewById(R.id.patient_numbers_tv);
            prescriptionNumbersTv = (TextView) itemView.findViewById(R.id.prescription_numbers_tv);
        }
    }
}
