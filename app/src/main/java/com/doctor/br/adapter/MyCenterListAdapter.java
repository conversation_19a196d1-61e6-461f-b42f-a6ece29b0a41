package com.doctor.br.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.bean.DoctorInfoBean;
import com.doctor.yy.R;

import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.SharedPreferenceUtils;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：我的界面下面的item，方便编写
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/21
 */

public class MyCenterListAdapter extends BaseAdapter {

    private Context context;
    private DoctorInfoBean doctorInfoBean;
    private int[] imgs = {R.drawable.center_quanlification, R.drawable.center_secrity_settings, R.drawable.center_usinghelp,
            R.drawable.center_feedback, R.drawable.center_constomer_service, R.drawable.clear_cache, R.drawable.center_about};
    private String[] texts = {"资质认证", "安全设置", "使用帮助", "问题反馈", "联系客服", "清除缓存", "关于"};

    public MyCenterListAdapter(Context context, DoctorInfoBean doctorInfoBean) {
        this.context = context;
        this.doctorInfoBean = doctorInfoBean;
    }

    @Override
    public int getCount() {
        return imgs.length > texts.length ? texts.length : imgs.length;
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_my_center_list, parent, false);//这种方法不会使布局自适应
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        holder.groupImg.setImageResource(imgs[position]);
        holder.groupTitleTv.setText(texts[position]);
        if (position == 2 || position == 7) {
            holder.bottomLine.setVisibility(View.GONE);
            holder.bottomView.setVisibility(View.VISIBLE);
        } else {
            holder.bottomLine.setVisibility(View.VISIBLE);
            holder.bottomView.setVisibility(View.GONE);
        }

        if (doctorInfoBean != null && position == 0) {
            if (doctorInfoBean.getCurrentAuthenticationState() != null) {
                switch (doctorInfoBean.getCurrentAuthenticationState()) {
                    case "1":
                        holder.groupNumberTv.setText("已认证");
                        break;
                    case "2":
                        holder.groupNumberTv.setText("审核中");
                        break;
                    case "3":
                    default:
                        holder.groupNumberTv.setText("未认证");
                        break;
                    case "4":
                        holder.groupNumberTv.setText("认证失败");
                        break;
                }
                holder.groupNumberTv.setVisibility(View.VISIBLE);
            } else {
                holder.groupNumberTv.setVisibility(View.GONE);
            }

//            holder.mySwitch.setVisibility(View.GONE);
        }
//        else  if (position == 6) {
//            //去掉右侧箭头
//            holder.rightImg.setVisibility(View.INVISIBLE);
//            holder.mySwitch.setVisibility(View.VISIBLE);
//        }
        else {
            holder.groupNumberTv.setVisibility(View.GONE);
//            holder.mySwitch.setVisibility(View.GONE);
        }

        holder.newImg.setVisibility(View.GONE);
        if (position == texts.length - 1) {
            //是否已经点击过
            boolean isClicked = SharedPreferenceUtils.getBoolean(context, PublicParams.IS_OPEN_NEW);
            if (!isClicked) {
                holder.newImg.setVisibility(View.VISIBLE);
            }
        }
        return convertView;
    }

    static class ViewHolder {
        @BindView(R.id.group_img)
        ImageView groupImg;
        @BindView(R.id.group_title_tv)
        TextView groupTitleTv;
        @BindView(R.id.new_img)
        ImageView newImg;
        @BindView(R.id.group_number_tv)
        TextView groupNumberTv;
        @BindView(R.id.bottom_line)
        View bottomLine;
        @BindView(R.id.bottom_view)
        View bottomView;
        @BindView(R.id.img)
        View rightImg;
//        @BindView(R.id.mySwitch)
//        View mySwitch;

        ViewHolder(View view) {
            ButterKnife.bind(this, view);
        }
    }
}
