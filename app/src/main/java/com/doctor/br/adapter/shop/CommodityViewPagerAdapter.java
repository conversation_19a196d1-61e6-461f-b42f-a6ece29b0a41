package com.doctor.br.adapter.shop;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.base.request.common.BaseRxFragment;
import com.doctor.br.fragment.medicineshop.DownShelfFragment;
import com.doctor.br.fragment.medicineshop.OnShelfFragment;
import com.doctor.br.fragment.medicineshop.ShelfingFragment;

import java.util.List;


/**
 * Author:sunxiaxia
 * createdDate:2018/8/21
 * description: 商品的viewPager适配器
 */

public class CommodityViewPagerAdapter extends FragmentPagerAdapter {

    private OnShelfFragment mOnShelfFragment;
    private ShelfingFragment mShelfingFragment;
    private DownShelfFragment mDownShelfFragment;
    private List<BaseRxFragment> fragmentList;


    public CommodityViewPagerAdapter(FragmentManager fm, List<BaseRxFragment> fragments) {
        super(fm);
        this.fragmentList = fragments;
    }

    @Override
    public Fragment getItem(int position) {
        return fragmentList.get(position);
    }

    @Override
    public int getCount() {
        return fragmentList.size();
    }
}
