package com.doctor.br.adapter.manage;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.ViewGroup;

import com.doctor.br.bean.PopItem;
import com.doctor.br.utils.BottomPopwindowHelper;

/**
 * 类描述：待发送拍照处方订单列表
 * 创建人：YangYajun
 * 创建时间：2018/7/9
 */

public class DaifasongRecyclerAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements BottomPopwindowHelper.OnClickListener {

    public DaifasongRecyclerAdapter(Context context){

    }
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {

    }

    @Override
    public int getItemCount() {
        return 0;
    }

    @Override
    public void onPopItemClick(int position, PopItem popItem) {

    }

    @Override
    public void onBottomViewClick() {

    }
}
