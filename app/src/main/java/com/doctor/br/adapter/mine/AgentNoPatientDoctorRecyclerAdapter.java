package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentNoPatientDoctorBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：经纪人-未加患者，未开方，已认证，连续5张处方不足100元列表适配器
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/9
 */

public class AgentNoPatientDoctorRecyclerAdapter extends RecyclerView.Adapter<AgentQualificationingDoctorReyclerAdapter.VH> implements View.OnClickListener {


    private Context context;
    private List<AgentNoPatientDoctorBean.DoctorMessagesBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public AgentNoPatientDoctorRecyclerAdapter(Context context, List<AgentNoPatientDoctorBean.DoctorMessagesBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public AgentQualificationingDoctorReyclerAdapter.VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(context).inflate(R.layout.item_agent_no_patient, parent, false);
        AgentQualificationingDoctorReyclerAdapter.VH holder = new AgentQualificationingDoctorReyclerAdapter.VH(itemView);
        if (recyclerItemClickListener != null) {
            holder.itemView.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(AgentQualificationingDoctorReyclerAdapter.VH holder, int position) {
        holder.itemView.setTag(position);

        GlideUtils.getInstance().loadImage(list.get(position).getHeadImage(), context, holder.headImg, R.drawable.default_head_img);
        holder.doctorNameTv.setText(list.get(position).getName());
        if ("1".equals(list.get(position).getSex())) {
            holder.sexImg.setImageResource(R.drawable.male_small);
        } else {
            holder.sexImg.setImageResource(R.drawable.female_small);
        }
        holder.commitTimeTv.setText(list.get(position).getCreatTime());
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.head_img)
        ShapeImageView headImg;
        @BindView(R.id.doctor_name_tv)
        TextView doctorNameTv;
        @BindView(R.id.sex_img)
        ImageView sexImg;
        @BindView(R.id.commit_time_tv)
        TextView commitTimeTv;

        public VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}