package com.doctor.br.adapter.chatmain;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.media.MediaPlayer;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.doctor.br.activity.ActionBarWebViewActivity;
import com.doctor.br.activity.ShowImageActivity;
import com.doctor.br.activity.chatmain.PatientInfoActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.MsgStatus;
import com.doctor.br.db.entity.Msg;
import com.doctor.br.listener.OnMediaChangedListener;
import com.doctor.br.netty.SendMsgQueue;
import com.doctor.br.netty.client.NettyUtils;
import com.doctor.br.netty.model.ContentType;
import com.doctor.br.netty.model.MsgResourceType;
import com.doctor.br.netty.model.OpenType;
import com.doctor.br.utils.DateUtils;
import com.doctor.br.utils.MediaManager;
import com.doctor.br.utils.UIHelper;
import com.doctor.br.view.MessageActionPopWindow;
import com.doctor.greendao.gen.MsgDao;
import com.doctor.yy.R;

import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.ImageUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 交流界面聊天的Adapter
 * @createTime 2017/10/10
 */

public class ChatAdapter extends BaseAdapter {
    public static final String FROM_CHAT_HISTORY_RECORDS = "FROM_CHAT_HISTORY_RECORDS";
    public static final String FROM_CHAT = "FROM_CHAT";
    public static final String FROM_DOSSIER = "FROM_DOSSIER";
    public static final String FROM_PATIENT_INFO = "FROM_PATIENT_INFO";
    private String fromPage;
    private GlideUtils mGlideUtils;
    private int mLayoutResId;
    private Context mContext;
    private List<Msg> mMsgList = new ArrayList<>();
    private String patientHeadImgUrl;
    private String doctorHeadImgUrl;
    private ArrayList<String> imgUrlList;
    private int mMaxItemWith;
    private int mMinItemWith;
    private ImageView viewAnimLeft;
    private ImageView viewAnim;
    private OnMsgActionListener mOnMsgActionListener;
    private MsgDao msgDao;

    private boolean isClick = true;
    private boolean isLongClick = true;

    public boolean isClick() {
        return isClick;
    }

    public void setClick(boolean click) {
        isClick = click;
    }

    public boolean isLongClick() {
        return isLongClick;
    }

    public void setLongClick(boolean longClick) {
        isLongClick = longClick;
    }

    public ChatAdapter(Context context, List<Msg> list, int layoutResId) {
//        super(context, list, layoutResId);
        initData(context, list, layoutResId);
    }

    public ChatAdapter(Context context, List<Msg> list, String fromPage, int layoutResId) {
//        super(context, list, layoutResId);
        initData(context, list, layoutResId);
        this.fromPage = fromPage;

    }

    private void initData(Context context, List<Msg> list, int layoutResId) {
        this.mContext = context;
        this.mLayoutResId = layoutResId;

        // 获取系统宽度
        WindowManager wManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        wManager.getDefaultDisplay().getMetrics(outMetrics);
        mMaxItemWith = (int) (outMetrics.widthPixels * 0.35f);
        mMinItemWith = (int) (outMetrics.widthPixels * 0.15f);
        mGlideUtils = GlideUtils.getInstance();

        msgDao = AppContext.getInstances().getDaoSession().getMsgDao();
        patientHeadImgUrl = SharedPreferenceUtils.getString(mContext, PublicParams.PATIENT_USRE_HEAD_URL);
        doctorHeadImgUrl = SharedPreferenceUtils.getString(mContext, PublicParams.USER_HEAD_URL);
        updateDataImpl(list);
    }

    private void updateDataImpl(List<Msg> list) {
        this.mMsgList.clear();
        if (list != null) {
            mMsgList.addAll(list);
        }
        if (imgUrlList == null) {
            imgUrlList = new ArrayList<>();
        }
        initImgUrlList();
    }

    @Override
    public int getCount() {
        return mMsgList != null ? mMsgList.size() : 0;
    }

    @Override
    public Object getItem(int position) {
        return mMsgList != null ? mMsgList.get(position) : null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }


    @Override
    public int getItemViewType(int position) {
        return mMsgList.get(position).getMsgSourceType();
    }


    @Override
    public int getViewTypeCount() {
        return 4;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder = null;
        View view;
        switch (getItemViewType(position)) {
            case MsgResourceType.TYPE_FROM:
                if (convertView == null) {
                    convertView = LayoutInflater.from(mContext).inflate(R.layout.item_chat_record_from, parent, false);//这种方法不会使布局自适应
                    holder = new FromViewHolder(convertView);
                    convertView.setTag(holder);
                } else {
                    holder = (FromViewHolder) convertView.getTag();
                }
                disposeFromMsg((FromViewHolder) holder, position, mMsgList.get(position));
                break;
            case MsgResourceType.TYPE_TO:
                if (convertView == null) {
                    convertView = LayoutInflater.from(mContext).inflate(R.layout.item_chat_record_to, parent, false);//这种方法不会使布局自适应
                    holder = new ToViewHolder(convertView);
                    convertView.setTag(holder);
                } else {
                    holder = (ToViewHolder) convertView.getTag();
                }
                disposeToMsg((ToViewHolder) holder, position, mMsgList.get(position));
                break;
            case MsgResourceType.TYPE_SYSTEM:
                if (convertView == null) {
                    convertView = LayoutInflater.from(mContext).inflate(R.layout.item_chat_record_system, parent, false);//这种方法不会使布局自适应
                    holder = new SystemViewHolder(convertView);
                    convertView.setTag(holder);
                } else {
                    holder = (SystemViewHolder) convertView.getTag();
                }
                disposeSystemMsg((SystemViewHolder) holder, position, mMsgList.get(position));
                break;
            case MsgResourceType.TYPE_NOTIFY:
                if (convertView == null) {
                    convertView = LayoutInflater.from(mContext).inflate(R.layout.item_chat_record_notify, parent, false);//这种方法不会使布局自适应
                    holder = new NotifyViewHolder(convertView);
                    convertView.setTag(holder);
                } else {
                    holder = (NotifyViewHolder) convertView.getTag();
                }
                disposeNotifyMsg((NotifyViewHolder) holder, position, mMsgList.get(position));
                break;
        }

        return convertView;
    }

    /**
     * 更新全部数据
     */
    public void notifyData(List<Msg> list) {
        updateDataImpl(list);
        notifyDataSetChanged();
    }

    /**
     * 更新某一条数据（插入并更新）
     *
     * @param position
     */
    public void notifyData(int position) {
        List<Msg> newList = new ArrayList<>(mMsgList.size());
        newList.addAll(mMsgList);
        notifyData(newList);
    }

    /**
     * 初始化图片对应的url的List
     */
    private void initImgUrlList() {
        imgUrlList.clear();
        for (Msg msg : mMsgList) {
            if (msg != null && ContentType.IMAGE.equalsIgnoreCase(msg.getContentObjType())) {
                imgUrlList.add(msg.getContentObjContent());
            }
        }
    }


    /**
     * 判断是否显示消息的时间，以及时间格式化处理后显示
     *
     * @param position
     * @param tvTime
     * @param msg
     */
    private void showMsgTime(int position, TextView tvTime, Msg msg) {
        tvTime.setVisibility(View.GONE);
        //处理时间的显示
        if (position > 0) {
            String msgDatePre = DateUtils.dateFormat(mMsgList.get(position - 1).getCreatedTime());
            String currentDate = DateUtils.dateFormat(msg.getCreatedTime());
            if (!TextUtils.isEmpty(msgDatePre) && !msgDatePre.equalsIgnoreCase(currentDate)) {
                tvTime.setText(DateUtils.dateFormat(msg.getCreatedTime()));
                tvTime.setVisibility(View.VISIBLE);
            } else {
                tvTime.setText("");
                tvTime.setVisibility(View.GONE);
            }
        } else {
            tvTime.setText(DateUtils.dateFormat(msg.getCreatedTime()));
            tvTime.setVisibility(View.VISIBLE);
        }
    }


    static class ViewHolder {

    }

    /**
     * 来自患者的消息显示ViewHolder
     */
    static class FromViewHolder extends ViewHolder {
        TextView tvTime;
        ImageView ivChatFromHead;
        TextView tvChatFromText;
        ImageView ivChatFromImg;
        LinearLayout llChatFromVoice;
        FrameLayout flChatFromVoice;
        ImageView viewChatFromRecorderAnim;
        TextView tvChatFromVoiceSeconds;
        TextView tvWzdAnswerUserInfo;
        TextView tvWzdAnswerDisease;
        TextView tvFzdAnswerUserInfo;
        TextView tvFzdAnswerDisease;
        TextView tvSupplementAnswerUserInfo;
        TextView tvSupplementAnswerDisease;
        LinearLayout llChatFromWzdAnswer;
        LinearLayout llChatFromFzdAnswer;
        LinearLayout llChatFromSupplementAnswer;
        ImageView ivChatFromStatus;

        public FromViewHolder(View holder) {
            tvTime = (TextView) holder.findViewById(R.id.tv_time);
            ivChatFromHead = (ImageView) holder.findViewById(R.id.iv_chat_from_head);
            tvChatFromText = (TextView) holder.findViewById(R.id.tv_chat_from_text);
            ivChatFromImg = (ImageView) holder.findViewById(R.id.iv_chat_from_img);
            llChatFromVoice = (LinearLayout) holder.findViewById(R.id.ll_chat_from_voice);
            flChatFromVoice = (FrameLayout) holder.findViewById(R.id.fl_chat_from_voice);
            viewChatFromRecorderAnim = (ImageView) holder.findViewById(R.id.view_chat_from_recorder_anim);
            tvChatFromVoiceSeconds = (TextView) holder.findViewById(R.id.tv_chat_from_voice_seconds);
            tvWzdAnswerUserInfo = (TextView) holder.findViewById(R.id.tv_wzd_answer_user_info);
            tvWzdAnswerDisease = (TextView) holder.findViewById(R.id.tv_wzd_answer_disease);
            tvFzdAnswerUserInfo = (TextView) holder.findViewById(R.id.tv_fzd_answer_user_info);
            tvFzdAnswerDisease = (TextView) holder.findViewById(R.id.tv_fzd_answer_disease);
            tvSupplementAnswerUserInfo = (TextView) holder.findViewById(R.id.tv_supplement_answer_user_info);
            tvSupplementAnswerDisease = (TextView) holder.findViewById(R.id.tv_supplement_answer_disease);
            llChatFromWzdAnswer = (LinearLayout) holder.findViewById(R.id.ll_chat_wzd_answer);
            llChatFromFzdAnswer = (LinearLayout) holder.findViewById(R.id.ll_chat_fzd_answer);
            llChatFromSupplementAnswer = (LinearLayout) holder.findViewById(R.id.ll_chat_supplement_answer);
            ivChatFromStatus = (ImageView) holder.findViewById(R.id.iv_chat_from_status);
        }
    }

    /**
     * 自己发送的消息显示ViewHolder
     */
    static class ToViewHolder extends ViewHolder {
        TextView tvTime;
        ImageView ivChatToHead;
        TextView tvChatToText;
        ImageView ivChatToImg;
        LinearLayout llChatToVoice;
        FrameLayout flChatToVoice;
        ImageView viewChatToRecorderAnim;
        TextView tvChatToVoiceSeconds;
        LinearLayout llChatToWzdQuestion;
        LinearLayout llChatToFzdQuestion;
        LinearLayout llChatToSupplementQuestion;
        ProgressBar pbChatToFail;
        ImageView ivChatToStatus;

        public ToViewHolder(View holder) {
            tvTime = (TextView) holder.findViewById(R.id.tv_time);
            ivChatToHead = (ImageView) holder.findViewById(R.id.iv_chat_to_head);
            tvChatToText = (TextView) holder.findViewById(R.id.tv_chat_to_text);
            ivChatToImg = (ImageView) holder.findViewById(R.id.iv_chat_to_img);
            llChatToVoice = (LinearLayout) holder.findViewById(R.id.ll_chat_to_voice);
            flChatToVoice = (FrameLayout) holder.findViewById(R.id.fl_chat_to_voice);
            viewChatToRecorderAnim = (ImageView) holder.findViewById(R.id.view_chat_to_recorder_anim);
            tvChatToVoiceSeconds = (TextView) holder.findViewById(R.id.tv_chat_to_voice_seconds);
            llChatToWzdQuestion = (LinearLayout) holder.findViewById(R.id.ll_chat_wzd_question);
            llChatToFzdQuestion = (LinearLayout) holder.findViewById(R.id.ll_chat_fzd_question);
            llChatToSupplementQuestion = (LinearLayout) holder.findViewById(R.id.ll_chat_supplement_question);
            pbChatToFail = (ProgressBar) holder.findViewById(R.id.pb_chat_to_fail);
            ivChatToStatus = (ImageView) holder.findViewById(R.id.iv_chat_to_status);
        }
    }

    /**
     * 系统消息显示ViewHolder
     */
    static class SystemViewHolder extends ViewHolder {
        TextView tvTime;
        TextView tvChatSystemText;
        ImageView ivChatSystemImg;
        LinearLayout llChatSystemText;
        LinearLayout llChatSystemVoice;
        FrameLayout flChatSystemVoice;
        ImageView viewChatSystemRecorderAnim;
        TextView tvChatSystemVoiceSeconds;
        TextView tvSystemDetails;

        public SystemViewHolder(View holder) {
//            super(holder);
            tvTime = (TextView) holder.findViewById(R.id.tv_time);
            tvChatSystemText = (TextView) holder.findViewById(R.id.tv_chat_system_text);
            ivChatSystemImg = (ImageView) holder.findViewById(R.id.iv_chat_system_img);
            llChatSystemText = (LinearLayout) holder.findViewById(R.id.ll_chat_system_text);
            llChatSystemVoice = (LinearLayout) holder.findViewById(R.id.ll_chat_system_voice);
            flChatSystemVoice = (FrameLayout) holder.findViewById(R.id.fl_chat_system_voice);
            viewChatSystemRecorderAnim = (ImageView) holder.findViewById(R.id.view_chat_system_recorder_anim);
            tvChatSystemVoiceSeconds = (TextView) holder.findViewById(R.id.tv_chat_system_voice_seconds);
            tvSystemDetails = (TextView) holder.findViewById(R.id.tv_system_details);
        }

    }

    /**
     * 提示消息显示ViewHolder
     */
    static class NotifyViewHolder extends ViewHolder {
        TextView tvTime;
        TextView tvStartChatFromPatient;
        TextView tvStartChatFromDoctor;
        TextView tvEndChat;


        public NotifyViewHolder(View holder) {
//            super(holder);
            tvTime = (TextView) holder.findViewById(R.id.tv_time);
            tvStartChatFromPatient = (TextView) holder.findViewById(R.id.tv_start_chat_from_patient);
            tvStartChatFromDoctor = (TextView) holder.findViewById(R.id.tv_start_chat_from_doctor);
            tvEndChat = (TextView) holder.findViewById(R.id.tv_end_chat);
        }

    }


    /**
     * 处理系统代发的消息
     *
     * @param holder
     * @param msg
     */
    private void disposeSystemMsg(final SystemViewHolder holder, int position, final Msg msg) {
        showMsgTime(position, holder.tvTime, msg);
        //系统代发消息
        holder.tvChatSystemText.setVisibility(View.GONE);
        holder.ivChatSystemImg.setVisibility(View.GONE);
        holder.llChatSystemVoice.setVisibility(View.GONE);
        holder.tvSystemDetails.setVisibility(View.GONE);
        switch (msg.getContentObjType()) {
            case ContentType.SYSTEM:
                holder.tvChatSystemText.setText(msg.getContentObjContent() + "");
                holder.tvChatSystemText.setVisibility(View.VISIBLE);
                if (msg.getContentObjOpenType() != null) {
                    switch (msg.getContentObjOpenType()) {
                        case OpenType.ORDER: {
                            if (!TextUtils.isEmpty(msg.getContentObjExtra1())) {
                                holder.tvSystemDetails.setVisibility(View.VISIBLE);
                                holder.llChatSystemText.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.ORDER_DETAILS));
                            } else {
                                holder.llChatSystemText.setOnClickListener(null);
                                holder.tvSystemDetails.setVisibility(View.GONE);
                            }
                        }
                        break;
                        case OpenType.LOGISTICS: {
                            if (!TextUtils.isEmpty(msg.getContentObjExtra1())) {
                                holder.tvSystemDetails.setVisibility(View.VISIBLE);
                                holder.llChatSystemText.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.LOGITICS_DETAILS));
                            } else {
                                holder.llChatSystemText.setOnClickListener(null);
                                holder.tvSystemDetails.setVisibility(View.GONE);
                            }
                        }
                        break;
                        case OpenType.OTHER: {
                            if (!TextUtils.isEmpty(msg.getContentObjExtra1())) {
                                holder.tvSystemDetails.setVisibility(View.VISIBLE);
                                holder.llChatSystemText.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.SYSTEM_MSG));
                            } else {
                                holder.llChatSystemText.setOnClickListener(null);
                                holder.tvSystemDetails.setVisibility(View.GONE);
                            }
                        }
                        break;
                    }
                } else {
                    holder.llChatSystemText.setOnClickListener(null);
                    holder.tvSystemDetails.setVisibility(View.GONE);
                }
//                llChatSystemText.setOnLongClickListener(new OnItemLongClickedListener(msg, MessageActionPopWindow.PopItemActionType.MSG_COPY, MessageActionPopWindow.PopItemActionType.MSG_REPEAT, MessageActionPopWindow.PopItemActionType.MSG_TRANSFERRED_CASE));
                break;
            case ContentType.LOGISTICS:
                holder.tvChatSystemText.setText(msg.getContentObjContent() + "");
                holder.tvChatSystemText.setVisibility(View.VISIBLE);
                if (TextUtils.isEmpty(msg.getContentObjUrl()) || TextUtils.isEmpty(msg.getContentObjExtra1())) {
                    holder.tvSystemDetails.setVisibility(View.GONE);
                } else {
                    holder.tvSystemDetails.setVisibility(View.VISIBLE);
                    holder.llChatSystemText.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.LOGITICS_DETAILS));
                }
                break;
            default:
                holder.tvChatSystemText.setVisibility(View.GONE);
                holder.ivChatSystemImg.setVisibility(View.GONE);
                holder.llChatSystemVoice.setVisibility(View.GONE);
                holder.tvSystemDetails.setVisibility(View.GONE);
                break;
        }
        holder.ivChatSystemImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShowImageActivity.showImage(mContext, imgUrlList, msg.getContentObjContent());
            }
        });
    }

    /**
     * 处理登录对象发送的消息
     *
     * @param holder
     * @param msg
     */
    private void disposeToMsg(final ToViewHolder holder, int position, final Msg msg) {
        showMsgTime(position, holder.tvTime, msg);
        holder.tvChatToText.setVisibility(View.GONE);
        holder.ivChatToImg.setVisibility(View.GONE);
        holder.llChatToVoice.setVisibility(View.GONE);
        holder.llChatToWzdQuestion.setVisibility(View.GONE);
        holder.llChatToFzdQuestion.setVisibility(View.GONE);
        holder.llChatToSupplementQuestion.setVisibility(View.GONE);
        holder.ivChatToStatus.setVisibility(View.GONE);
        String headUrl = (String) holder.ivChatToHead.getTag(R.id.img_url);
        if (!TextUtils.isEmpty(ImageUtils.getImgUrl(doctorHeadImgUrl)) && !ImageUtils.getImgUrl(doctorHeadImgUrl).equalsIgnoreCase(headUrl)) {
            mGlideUtils.loadCircleImage(ImageUtils.getImgUrl(doctorHeadImgUrl), mContext, holder.ivChatToHead, R.drawable.default_head_img);
            holder.ivChatToHead.setTag(R.id.img_url, ImageUtils.getImgUrl(doctorHeadImgUrl));
        } else if (TextUtils.isEmpty(ImageUtils.getImgUrl(doctorHeadImgUrl))) {
            mGlideUtils.loadCircleImage(R.drawable.default_head_img, mContext, holder.ivChatToHead, R.drawable.default_head_img);
            holder.ivChatToHead.setTag(R.id.img_url, "");
        }
        switch (msg.getContentObjType()) {
            case ContentType.TEXT:
                holder.tvChatToText.setText(msg.getContentObjContent() + "");
                holder.tvChatToText.setVisibility(View.VISIBLE);
                holder.tvChatToText.setOnLongClickListener(new OnItemLongClickedListener(msg, MessageActionPopWindow.PopItemActionType.MSG_COPY, MessageActionPopWindow.PopItemActionType.MSG_REPEAT, MessageActionPopWindow.PopItemActionType.MSG_REVOKE, MessageActionPopWindow.PopItemActionType.MSG_TRANSFERRED_CASE));
                break;
            case ContentType.IMAGE:
                String url = (String) holder.ivChatToImg.getTag(R.id.img_url);
                if (!TextUtils.isEmpty(ImageUtils.getImgUrl(msg.getContentObjContent())) && !ImageUtils.getImgUrl(msg.getContentObjContent()).equalsIgnoreCase(url)) {
                    mGlideUtils.loadImageReSize(msg.getContentObjExtra3(), ImageUtils.getImgUrl(msg.getContentObjContent()), mContext, holder.ivChatToImg, DensityUtils.dip2px(mContext, 130), DensityUtils.dip2px(mContext, 130), R.drawable.icon_img_load_error);
                    holder.ivChatToImg.setTag(R.id.img_url, ImageUtils.getImgUrl(msg.getContentObjContent()));
                }
                holder.ivChatToImg.setVisibility(View.VISIBLE);
                holder.ivChatToImg.setOnLongClickListener(new OnItemLongClickedListener(msg, MessageActionPopWindow.PopItemActionType.MSG_REPEAT, MessageActionPopWindow.PopItemActionType.MSG_REVOKE, MessageActionPopWindow.PopItemActionType.MSG_TRANSFERRED_CASE));
                break;
            case ContentType.AUDIO:
                holder.llChatToVoice.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(msg.getContentObjExtra1())) {
                    float voiceLength = Float.parseFloat(msg.getContentObjExtra1());
                    ViewGroup.LayoutParams lParams = holder.flChatToVoice.getLayoutParams();
//                    lParams.width = (int) (mMinItemWith + (mMaxItemWith - mMinItemWith) / 60f * voiceLength);
                    lParams.width = mMaxItemWith;
                    holder.flChatToVoice.setLayoutParams(lParams);
                    holder.tvChatToVoiceSeconds.setText(Math.round(voiceLength) + "″");
                } else {
                    ViewGroup.LayoutParams lParams = holder.flChatToVoice.getLayoutParams();
                    holder.flChatToVoice.setLayoutParams(lParams);
                    holder.tvChatToVoiceSeconds.setText("");
                }
                holder.flChatToVoice.setOnClickListener(new View.OnClickListener() {//点击语音消息
                    @Override
                    public void onClick(View v) {
                        if (viewAnimLeft != null) {
//                            viewAnimLeft = viewChatSystemRecorderAnim;
                            viewAnimLeft.setImageResource(R.drawable.v_left_anim_default);
                            viewAnimLeft = null;
                        }
                        // 播放动画
                        if (viewAnim != null) {// 让第二个播放的时候第一个停止播放
                            viewAnim.setImageResource(R.drawable.v_anim_default);
                            viewAnim = null;
                        }
                        viewAnim = holder.viewChatToRecorderAnim;
                        viewAnim.setImageResource(R.drawable.play);
                        final AnimationDrawable drawable = (AnimationDrawable) viewAnim.getDrawable();
                        drawable.start();
                        // 播放音频
                        final MediaManager mediaManager = MediaManager.getInstance();
                        mediaManager.playSound(msg.getContentObjContent(), new OnMediaChangedListener() {
                            @Override
                            public void onPaused(MediaPlayer mediaPlayer) {//暂停
                                viewAnim.setImageResource(R.drawable.v_anim_default);
                            }

                            @Override
                            public void onFiled(MediaPlayer mediaPlayer) {//播放失败
                                mediaManager.setIsPause(true);
                                viewAnim.setImageResource(R.drawable.v_anim_default);
                            }

                            @Override
                            public void onCompletion(MediaPlayer mediaPlayer) {//播放完成
                                mediaManager.setIsPause(true);
                                viewAnim.setImageResource(R.drawable.v_anim_default);
                            }
                        });
                    }
                });
                holder.flChatToVoice.setOnLongClickListener(new OnItemLongClickedListener(msg, MessageActionPopWindow.PopItemActionType.MSG_REVOKE));
                break;
            case ContentType.WZD_QUESTION:
                holder.llChatToWzdQuestion.setVisibility(View.VISIBLE);
                holder.llChatToWzdQuestion.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.WZD_QUESTION_DETAILS));
                break;
            case ContentType.FZD_QUESTION:
                holder.llChatToFzdQuestion.setVisibility(View.VISIBLE);
                holder.llChatToFzdQuestion.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.FZD_QUESTION_DETAILS));
                break;
            case ContentType.SUPPLEMENT_QUESTION:
                holder.llChatToSupplementQuestion.setVisibility(View.VISIBLE);
                holder.llChatToSupplementQuestion.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.SUPPLEMENT_QUESTION_DETAILS));
                break;
            default:
                holder.tvChatToText.setVisibility(View.GONE);
                holder.ivChatToImg.setVisibility(View.GONE);
                holder.llChatToVoice.setVisibility(View.GONE);
                holder.llChatToWzdQuestion.setVisibility(View.GONE);
                holder.llChatToFzdQuestion.setVisibility(View.GONE);
                holder.ivChatToStatus.setVisibility(View.GONE);
                break;
        }
        if (msg.getSendStatus() == null || msg.getSendStatus().intValue() == MsgStatus.SENDING) {
            holder.pbChatToFail.setVisibility(View.VISIBLE);
            holder.ivChatToStatus.setVisibility(View.GONE);
        } else if (msg.getSendStatus().intValue() == MsgStatus.SEND_FAIL) {
            holder.pbChatToFail.setVisibility(View.GONE);
            holder.ivChatToStatus.setVisibility(View.VISIBLE);
        } else {
            holder.pbChatToFail.setVisibility(View.GONE);
            holder.ivChatToStatus.setVisibility(View.GONE);
        }
        holder.ivChatToStatus.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.RESEND_MSG));
        holder.ivChatToImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShowImageActivity.showImage(mContext, imgUrlList, msg.getContentObjContent());
            }
        });
    }

    /**
     * 处理通知类的消息
     *
     * @param holder
     * @param msg
     */
    private void disposeNotifyMsg(NotifyViewHolder holder, int position, Msg msg) {
        showMsgTime(position, holder.tvTime, msg);
        holder.tvStartChatFromPatient.setVisibility(View.GONE);
        holder.tvStartChatFromDoctor.setVisibility(View.GONE);
        holder.tvEndChat.setVisibility(View.GONE);
        switch (msg.getContentObjType()) {
            case ContentType.START_CHAT_PATIENT: {
                String text = mContext.getResources().getString(R.string.start_chat_patient);
                if (text.contains("结束会话")) {
                    SpannableString spannableString = new SpannableString(text);
                    spannableString.setSpan(new ForegroundColorSpan(mContext.getResources().getColor(R.color.br_color_theme)), text.indexOf("结束会话"), text.indexOf("结束会话") + "结束会话".length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                    holder.tvStartChatFromPatient.setText(spannableString);
                } else {
                    holder.tvStartChatFromPatient.setText(text);
                }

                holder.tvStartChatFromPatient.setVisibility(View.VISIBLE);
            }
            break;
            case ContentType.START_CHAT_DOCTOR: {
                String text = mContext.getResources().getString(R.string.start_chat_doctor);
                if (text.contains("结束会话")) {
                    SpannableString spannableString = new SpannableString(text);
                    spannableString.setSpan(new ForegroundColorSpan(mContext.getResources().getColor(R.color.br_color_theme)), text.indexOf("结束会话"), text.indexOf("结束会话") + "结束会话".length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                    holder.tvStartChatFromDoctor.setText(spannableString);
                } else {
                    holder.tvStartChatFromDoctor.setText(text);
                }
                holder.tvStartChatFromDoctor.setVisibility(View.VISIBLE);
            }
            break;
            case ContentType.FINISH_CHAT_DOCTOR: {
                String text = mContext.getResources().getString(R.string.finish_chat_doctor);
                if (text.contains("结束会话")) {
                    SpannableString spannableString = new SpannableString(text);
                    spannableString.setSpan(new ForegroundColorSpan(mContext.getResources().getColor(R.color.br_color_theme)), text.indexOf("结束会话"), text.indexOf("结束会话") + "结束会话".length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                    holder.tvEndChat.setText(spannableString);
                } else {
                    holder.tvEndChat.setText(text);
                }
                holder.tvEndChat.setVisibility(View.VISIBLE);
            }
            break;
            case ContentType.MSG_REVOKE: {
                holder.tvEndChat.setText("您撤回了一条消息");
                holder.tvEndChat.setVisibility(View.VISIBLE);
            }
            break;
        }
    }

    /**
     * 处理由患者发送来的消息
     *
     * @param holder
     * @param msg
     */
    private void disposeFromMsg(final FromViewHolder holder, int position, final Msg msg) {
        showMsgTime(position, holder.tvTime, msg);
        holder.tvChatFromText.setVisibility(View.GONE);
        holder.ivChatFromImg.setVisibility(View.GONE);
        holder.llChatFromVoice.setVisibility(View.GONE);
        holder.llChatFromWzdAnswer.setVisibility(View.GONE);
        holder.llChatFromFzdAnswer.setVisibility(View.GONE);
        holder.ivChatFromStatus.setVisibility(View.GONE);
        holder.llChatFromSupplementAnswer.setVisibility(View.GONE);
        if (msg != null && !PublicParams.CHAT_SERVICE_USER_ID.equalsIgnoreCase(msg.getFrom())) {
            String headUrl = (String) holder.ivChatFromHead.getTag(R.id.img_url);
            if (!TextUtils.isEmpty(ImageUtils.getImgUrl(patientHeadImgUrl)) && !ImageUtils.getImgUrl(patientHeadImgUrl).equalsIgnoreCase(headUrl)) {
                mGlideUtils.loadCircleImage(ImageUtils.getImgUrl(patientHeadImgUrl), mContext, holder.ivChatFromHead, R.drawable.default_head_img);
                holder.ivChatFromHead.setTag(R.id.img_url, ImageUtils.getImgUrl(patientHeadImgUrl));
            } else if (TextUtils.isEmpty(ImageUtils.getImgUrl(patientHeadImgUrl))) {
                mGlideUtils.loadCircleImage(R.drawable.default_head_img, mContext, holder.ivChatFromHead, R.drawable.default_head_img);
                holder.ivChatFromHead.setTag(R.id.img_url, "");
            }
        } else {
            mGlideUtils.loadCircleImage(R.mipmap.logo1, mContext, holder.ivChatFromHead, R.mipmap.logo1);
        }
        switch (msg.getContentObjType()) {
            case ContentType.TEXT:
                holder.tvChatFromText.setText(msg.getContentObjContent() + "");
                holder.tvChatFromText.setVisibility(View.VISIBLE);
                holder.tvChatFromText.setOnLongClickListener(new OnItemLongClickedListener(msg, MessageActionPopWindow.PopItemActionType.MSG_COPY, MessageActionPopWindow.PopItemActionType.MSG_REPEAT, MessageActionPopWindow.PopItemActionType.MSG_TRANSFERRED_CASE));
                break;
            case ContentType.IMAGE:
                String url = (String) holder.ivChatFromImg.getTag(R.id.img_url);
                if (!TextUtils.isEmpty(ImageUtils.getImgUrl(msg.getContentObjContent())) && !ImageUtils.getImgUrl(msg.getContentObjContent()).equalsIgnoreCase(url)) {
                    mGlideUtils.loadImageReSize(msg.getContentObjExtra3(), ImageUtils.getImgUrl(msg.getContentObjContent()), mContext, holder.ivChatFromImg, DensityUtils.dip2px(mContext, 130), DensityUtils.dip2px(mContext, 130), R.drawable.icon_img_load_error);
                    holder.ivChatFromImg.setTag(R.id.img_url, ImageUtils.getImgUrl(msg.getContentObjContent()));
                }
                holder.ivChatFromImg.setVisibility(View.VISIBLE);
                holder.ivChatFromImg.setOnLongClickListener(new OnItemLongClickedListener(msg, MessageActionPopWindow.PopItemActionType.MSG_REPEAT, MessageActionPopWindow.PopItemActionType.MSG_TRANSFERRED_CASE));
                break;
            case ContentType.AUDIO:
                holder.llChatFromVoice.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(msg.getContentObjExtra1())) {
                    float voiceLength = Float.parseFloat(msg.getContentObjExtra1());
                    ViewGroup.LayoutParams lParams = holder.flChatFromVoice.getLayoutParams();
//                    lParams.width = (int) (mMinItemWith + (mMaxItemWith - mMinItemWith) / 60f * voiceLength);
                    lParams.width = mMaxItemWith;
                    holder.flChatFromVoice.setLayoutParams(lParams);
                    holder.tvChatFromVoiceSeconds.setText(Math.round(voiceLength) + "″");
                }
                holder.flChatFromVoice.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (viewAnimLeft != null) {
//                            viewAnimLeft = viewChatSystemRecorderAnim;
                            viewAnimLeft.setImageResource(R.drawable.v_left_anim_default);
                            viewAnimLeft = null;
                        }
                        // 播放动画
                        if (viewAnim != null) {// 让第二个播放的时候第一个停止播放
                            viewAnim.setImageResource(R.drawable.v_anim_default);
                            viewAnim = null;
                        }
                        viewAnimLeft = holder.viewChatFromRecorderAnim;
                        viewAnimLeft.setImageResource(R.drawable.play_left);
                        final AnimationDrawable drawable = (AnimationDrawable) viewAnimLeft.getDrawable();
                        drawable.start();
                        // 播放音频
                        final MediaManager mediaManager = MediaManager.getInstance();
                        mediaManager.playSound(msg.getContentObjContent(), new OnMediaChangedListener() {
                            @Override
                            public void onPaused(MediaPlayer mediaPlayer) {//暂停播放
                                viewAnimLeft.setImageResource(R.drawable.v_left_anim_default);
                            }

                            @Override
                            public void onFiled(MediaPlayer mediaPlayer) {//播放失败
                                drawable.stop();
                                viewAnimLeft.setImageResource(R.drawable.v_left_anim_default);
                            }

                            @Override
                            public void onCompletion(MediaPlayer mediaPlayer) {//播放完成
                                mediaManager.setIsPause(true);
                                viewAnimLeft.setImageResource(R.drawable.v_left_anim_default);
                            }
                        });
                    }
                });
                break;
            case ContentType.WZD_ANSWER: {
                holder.llChatFromWzdAnswer.setVisibility(View.VISIBLE);
                String text = msg.getContentObjExtra1();
                if (!TextUtils.isEmpty(text) && text.endsWith("怀孕")) {
                    SpannableString spannableString = new SpannableString(text);
                    spannableString.setSpan(new ForegroundColorSpan(Color.RED), text.lastIndexOf("怀孕"), text.lastIndexOf("怀孕") + "怀孕".length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                    holder.tvWzdAnswerUserInfo.setText(spannableString);
                } else {
                    holder.tvWzdAnswerUserInfo.setText(text + "");
                }
                holder.tvWzdAnswerDisease.setText(msg.getContentObjExtra2());
                holder.llChatFromWzdAnswer.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.WZD_ANSWER_DETAILS));
            }
            break;
            case ContentType.FZD_ANSWER: {
                holder.llChatFromFzdAnswer.setVisibility(View.VISIBLE);
                String text = msg.getContentObjExtra1();
                if (!TextUtils.isEmpty(text) && text.endsWith("怀孕")) {
                    SpannableString spannableString = new SpannableString(text);
                    spannableString.setSpan(new ForegroundColorSpan(Color.RED), text.lastIndexOf("怀孕"), text.lastIndexOf("怀孕") + "怀孕".length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                    holder.tvFzdAnswerUserInfo.setText(spannableString);
                } else {
                    holder.tvFzdAnswerUserInfo.setText(text + "");
                }
                holder.tvFzdAnswerDisease.setText(msg.getContentObjExtra2());
                holder.llChatFromFzdAnswer.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.FZD_ANSWER_DETAILS));
            }
            break;
            case ContentType.SUPPLEMENT_ANSWER:
                holder.llChatFromSupplementAnswer.setVisibility(View.VISIBLE);
                String text = msg.getContentObjExtra1();
                if (!TextUtils.isEmpty(text) && text.endsWith("怀孕")) {
                    SpannableString spannableString = new SpannableString(text);
                    spannableString.setSpan(new ForegroundColorSpan(Color.RED), text.lastIndexOf("怀孕"), text.lastIndexOf("怀孕") + "怀孕".length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                    holder.tvSupplementAnswerUserInfo.setText(spannableString);
                } else {
                    holder.tvSupplementAnswerUserInfo.setText(text + "");
                }
                holder.tvSupplementAnswerDisease.setText(msg.getContentObjExtra2());
                holder.llChatFromSupplementAnswer.setOnClickListener(new OnItemClickedListener(position, msg, ItemClickActionType.SUPPLEMENT_ANSWER_DETAILS));
                break;
        }
        holder.ivChatFromHead.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (msg != null && !PublicParams.CHAT_SERVICE_USER_ID.equalsIgnoreCase(msg.getFrom())) {
                    Intent intent = new Intent(mContext, PatientInfoActivity.class);
                    intent.putExtra(PublicParams.PATIENT_USRE_ID, msg.getFrom());
                    intent.putExtra(PublicParams.IS_FROM_CHAT_MAIN_ACTIVITY, true);
                    intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                    mContext.startActivity(intent);
                }

            }
        });
        holder.ivChatFromImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShowImageActivity.showImage(mContext, imgUrlList, msg.getContentObjContent());
            }
        });
    }


    /**
     * 消息的点击事件,问诊单消息，复诊单消息，物流信息消息
     */
    class OnItemClickedListener implements View.OnClickListener {
        private int position;
        private Msg msg;
        private ItemClickActionType mItemClickActionType;

        public OnItemClickedListener(Msg msg, ItemClickActionType mItemClickActionType) {
            this.msg = msg;
            this.mItemClickActionType = mItemClickActionType;
        }

        public OnItemClickedListener(int position, Msg msg, ItemClickActionType mItemClickActionType) {
            this.position = position;
            this.msg = msg;
            this.mItemClickActionType = mItemClickActionType;
        }

        @Override
        public void onClick(View v) {
            if (!isClick) {
                return;
            }
            switch (mItemClickActionType) {
                case WZD_QUESTION_DETAILS: {
                    if (msg != null) {
                        String version = msg.getContentObjVersion();
                        String content = msg.getContentObjContent();
                        Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                        intent.putExtra(PublicParams.WEBVIEW_TITLE, "问诊单");
                        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                        if (!TextUtils.isEmpty(version)) {//wxWzd/goFirstReportOrFillInFirstReport?flag=0&docId=0000000020148&userId=9000000030113&wzdId=201712212057575021320171221205757&wzdType=1&version=2
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, msg.getContentObjUrl() + "?docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&flag=0&wzdId=" + msg.getContentObjContent() + "&wzdType=" + msg.getContentObjWzdType() + "&version=" + msg.getContentObjVersion());
                        } else if (content != null && (content.contains("卍1卍") || content.contains("$1$"))) {
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.getH5BaseUrl() + "wxSubmit/goWenzdNew?docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&flag=0&wzdParam=" + msg.getContentObjContent());
                        } else {
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.getH5BaseUrl() + "wxSubmit/goWenzd?flag=0&diff=wzd&docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&wzdId=" + msg.getContentObjContent());
                        }
                        mContext.startActivity(intent);
                    }
                }
                break;
                case WZD_ANSWER_DETAILS: {
                    if (msg != null) {
                        String version = msg.getContentObjVersion();
                        String content = msg.getContentObjContent();
                        Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                        intent.putExtra(PublicParams.WEBVIEW_TITLE, "问诊单");
                        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                        if (!TextUtils.isEmpty(version)) {//wxWzd/skimFinishFirstReport?userId=9000000030113&flag=0&wzdId=2017122318501463234&docId=0000000020148
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, msg.getContentObjUrl() + "?docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&flag=0&wzdId=" + msg.getContentObjContent() + "&wzdType=" + msg.getContentObjWzdType() + "&version=" + msg.getContentObjVersion());
                        } else if (content != null && content.contains("tabIndex")) {
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.getH5BaseUrl() + "wxSubmit/skimFinishFirstReport?docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&flag=0&wzdId=" + msg.getContentObjContent());
                        } else {
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.getH5BaseUrl() + "appQuestion/SeePatientAnswer?flag=0&wzdAnswerId=" + msg.getContentObjContent());
                        }
                        mContext.startActivity(intent);
                    }
                }
                break;
                case FZD_QUESTION_DETAILS: {
                    if (msg != null) {
                        String version = msg.getContentObjVersion();
                        String content = msg.getContentObjContent();
                        Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                        intent.putExtra(PublicParams.WEBVIEW_TITLE, "复诊单");
                        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                        if (!TextUtils.isEmpty(version)) {//wxWzd/showAgainVisitFromPage?docId=0000000020150&userId=10488&wzdId=201712261608457049720171226160845&flag=0
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, msg.getContentObjUrl() + "?docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&flag=0&wzdId=" + msg.getContentObjContent() + "&wzdType=" + msg.getContentObjWzdType() + "&version=" + msg.getContentObjVersion() + "&takerId=" + msg.getContentObjExtra3());
                        } else if (content != null && (content.contains("卍1卍") || content.contains("$1$"))) {
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.getH5BaseUrl() + "wxSubmit/sendReturnVisitFromPage?docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&flag=0&wzdParam=" + msg.getContentObjContent());
                        } else {
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.getH5BaseUrl() + "wxSubmit/goWenzd?flag=0&diff=zhsf&docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&wzdId=" + msg.getContentObjContent());
                        }
                        mContext.startActivity(intent);
                    }
                }
                break;
                case FZD_ANSWER_DETAILS: {
                    if (msg != null) {
                        String version = msg.getContentObjVersion();
                        String content = msg.getContentObjContent();
                        Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                        intent.putExtra(PublicParams.WEBVIEW_TITLE, "复诊单");
                        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                        if (!TextUtils.isEmpty(version)) {//wxWzd/lookAgainVisitForm?flag=0&docId=0000000020150&userId=10488&answerId=2017122618071148334
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, msg.getContentObjUrl() + "?docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&flag=0&answerId=" + msg.getContentObjContent() + "&wzdType=" + msg.getContentObjWzdType() + "&version=" + msg.getContentObjVersion());
                        } else if (content != null && content.contains("tabIndex")) {
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.getH5BaseUrl() + "wxSubmit/skimFinishReturnVisitForm?docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&flag=0&answerId=" + msg.getContentObjContent());
                        } else {
                            intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, BaseConfig.getH5BaseUrl() + "appQuestion/ShowZhsfAnswer?flag=0&zhsfId=" + msg.getContentObjContent());
                        }
                        mContext.startActivity(intent);
                    }
                }
                break;
                case SUPPLEMENT_QUESTION_DETAILS: {
                    if (msg != null) {
                        String version = msg.getContentObjVersion();
                        String content = msg.getContentObjContent();
                        Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                        intent.putExtra(PublicParams.WEBVIEW_TITLE, "定制问诊单");
                        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                        //wxWzd/toSupplementaryProblems?docId=0000000020148&userId=9000000030113&flag=0
                        intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, msg.getContentObjUrl() + "?flag=0&docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&groupId=" + msg.getContentObjContent());
                        mContext.startActivity(intent);
                    }
                }
                break;
                case SUPPLEMENT_ANSWER_DETAILS: {
                    if (msg != null) {
                        String version = msg.getContentObjVersion();
                        String content = msg.getContentObjContent();
                        Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                        intent.putExtra(PublicParams.WEBVIEW_TITLE, "定制问诊单");
                        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                        //wxWzd/skimFinishSupplementaryProblems?answerId=2017122118294644450&flag=0&userId=&docId=0000000020148
                        intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, msg.getContentObjUrl() + "?flag=0&docId=" + msg.getTo() + "&userId=" + msg.getFrom() + "&answerId=" + msg.getContentObjContent());
                        mContext.startActivity(intent);
                    }
                }
                break;
                case LOGITICS_DETAILS: {
                    if (msg != null) {
                        Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                        intent.putExtra(PublicParams.WEBVIEW_TITLE, "物流状态");
                        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);

                        // 检查URL是否已经包含参数
                        String url;
                        if (msg.getContentObjUrl() != null && msg.getContentObjUrl().contains("?preId=")) {
                            // URL已经包含参数，直接使用
                            url = msg.getContentObjUrl();
                        } else {
                            // 构建物流查询URL
                            url = msg.getContentObjUrl() + "?flag=0&preId=" + msg.getContentObjExtra1();
                        }

                        intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, url);
                        mContext.startActivity(intent);
                    }
                }
                break;
                case ORDER_DETAILS: {
                    if (msg != null) {
                        UIHelper.openMedicationDetailActivity(mContext, msg.getContentObjExtra1());
                    }
                }
                case SYSTEM_MSG: {
                    if (msg != null && !TextUtils.isEmpty(msg.getContentObjUrl())) {
                        Intent intent = new Intent(mContext, ActionBarWebViewActivity.class);
                        intent.putExtra(PublicParams.WEBVIEW_TITLE, msg.getContentObjTitle());
                        intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, msg.getContentObjUrl());
                        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE, fromPage);
                        mContext.startActivity(intent);
                    }
                }
                break;
                case RESEND_MSG: {
                    showResendMsgDialog();
                }
                break;

            }
        }

        private void showResendMsgDialog() {
            final ConfirmDialog confirmDialog = ConfirmDialog.getInstance(mContext);
            confirmDialog.setDialogContent("是否重发该消息?").setPositiveText("重发").setNavigationText("取消");
            confirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    confirmDialog.dismiss();
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    try {
                        msgDao.delete(msg);
                        SendMsgQueue.getInstance().deQueue(msg);
                        mMsgList.remove(position);
                        msg.setSendStatus(MsgStatus.SENDING);
                        msg.setCreatedTime(DateUtils.getNowDate());
                        msg.setId(msgDao.insertOrReplace(msg));
                        mMsgList.add(msg);
                        NettyUtils.enQueueSendMsg(msg);
                        notifyData(mMsgList.size());

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    confirmDialog.dismiss();
                }
            });
            confirmDialog.show();
        }
    }

    /**
     * 消息点击动作类型
     */
    enum ItemClickActionType {
        /**
         * 物流详情
         */
        LOGITICS_DETAILS,
        /**
         * 问诊单问题详情
         */
        WZD_QUESTION_DETAILS,
        /**
         * 问诊单答案详情
         */
        WZD_ANSWER_DETAILS,
        /**
         * 复诊单问题详情
         */
        FZD_QUESTION_DETAILS,
        /**
         * 复诊单答案详情
         */
        FZD_ANSWER_DETAILS,
        /**
         * 定制问诊单详情
         */
        SUPPLEMENT_QUESTION_DETAILS,
        /**
         * 定制问诊单答案详情
         */
        SUPPLEMENT_ANSWER_DETAILS,
        /**
         * 重新发送消息
         */
        RESEND_MSG,
        /**
         * 订单详情
         */
        ORDER_DETAILS,
        /**
         * 系统消息点击
         */
        SYSTEM_MSG
    }

    public void setOnMsgActionListener(OnMsgActionListener mOnMsgActionListener) {
        this.mOnMsgActionListener = mOnMsgActionListener;
    }

    public interface OnMsgActionListener {
        public void onMsgAction(View view, Msg msg, MessageActionPopWindow.PopItemActionType... actionTypes);
    }


    /**
     * 消息的长按事件,文本消息，图片消息
     */
    class OnItemLongClickedListener implements View.OnLongClickListener {
        private MessageActionPopWindow.PopItemActionType[] actionTypes;
        private Msg mMessage;

        public OnItemLongClickedListener(Msg msg, MessageActionPopWindow.PopItemActionType... actionTypes) {
            this.mMessage = msg;
            this.actionTypes = actionTypes;
        }

        @Override
        public boolean onLongClick(View v) {
            if (!isLongClick) {
                return true;
            }
            if (mOnMsgActionListener != null) {
                mOnMsgActionListener.onMsgAction(v, mMessage, actionTypes);
            }
            return true;
        }
    }
}
