package com.doctor.br.adapter;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.db.entity.Session;
import com.doctor.br.netty.model.ContentType;
import com.doctor.br.netty.model.SessionStatus;
import com.doctor.br.utils.DateUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.adapter.BaseAdapter;
import org.newapp.ones.base.adapter.ViewHolder;
import org.newapp.ones.base.base.PublicParams;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 消息页面的ListView的Adapter，用于显示会话的联系人
 * @createTime 2017/9/28
 */

public class SessionAdapter extends BaseAdapter<Session> {
    private Context mContext;
    private List<Session> mList;

    public SessionAdapter(Context context, List<Session> list, int layoutResId) {
        super(context, list, layoutResId);
        this.mContext = context;
        this.mList = list;
    }

    @Override
    public void setViewData(ViewHolder holder, int position) {
        ImageView ivHead = (ImageView) holder.findViewById(R.id.iv_head);
        TextView tvName = (TextView) holder.findViewById(R.id.tv_name);
        TextView tvTime = (TextView) holder.findViewById(R.id.tv_time);
        TextView tvContent = (TextView) holder.findViewById(R.id.tv_content);
        TextView tvNotReadCount = (TextView) holder.findViewById(R.id.tv_not_read_count);
        ImageView ivSendState = (ImageView) holder.findViewById(R.id.iv_send_state);
        TextView tvStatus = (TextView) holder.findViewById(R.id.tv_status);
        View lineView = holder.findViewById(R.id.line_view);
        Session session = mList.get(position);

        if (PublicParams.CHAT_SERVICE_USER_ID.equalsIgnoreCase(session.getFrom()) || PublicParams.SYSTEM_USER_ID.equalsIgnoreCase(session.getFrom())) {
            Object tag = ivHead.getTag(R.id.img_url);
            if (tag == null || !(tag instanceof Integer) || R.mipmap.logo1 != (int) tag) {
                mGlideUtils.loadCircleImage(R.mipmap.logo1, mContext, ivHead, R.mipmap.logo1);
                ivHead.setTag(R.id.img_url, R.mipmap.logo1);
            }
        } else {
            Object tag = ivHead.getTag(R.id.img_url);
            if (tag == null || !(tag instanceof String) || !((String) tag).equalsIgnoreCase(getImgUrl(session.getHeadImg()))) {
                mGlideUtils.loadCircleImage(getImgUrl(session.getHeadImg()), mContext, ivHead, R.drawable.default_head_img);
                ivHead.setTag(R.id.img_url, getImgUrl(session.getHeadImg()));
            }
        }
//        session.
        String nickName = session.getName();
        //  患者是否有备注，展示样式
        if (!TextUtils.isEmpty(nickName)) {
            if (TextUtils.isEmpty(session.getRemark())) {
                //没有备注
                tvName.setText((nickName.length() > 6 ? nickName.substring(0, 6) + "…" : nickName) + "");
            } else {
                //有备注
                tvName.setText(session.getRemark() + "(" + (nickName.length() > 3 ? nickName.substring(0, 3) + "…" : nickName) + ")");
            }
        } else {
            tvName.setText("");
        }
        tvTime.setText(DateUtils.dateFormat(TextUtils.isEmpty(session.getTime()) ? "" : session.getTime()));
        if (session.getHasCacheMsg()) {
            tvContent.setText(createSpannable("【草稿】", session.getCacheContent(), ""));
        } else {
            tvContent.setText(formatMsgContent(session));
        }
        if (-1 == session.getSendState()) {//发送失败
            ivSendState.setImageResource(R.drawable.msg_send_failed);
            ivSendState.setVisibility(View.VISIBLE);
        } else if (0 == session.getSendState()) {//发送中
            ivSendState.setImageResource(R.drawable.msg_sending);
            ivSendState.setVisibility(View.VISIBLE);
        } else {//发送成功
            ivSendState.setImageResource(R.drawable.msg_sending);
            ivSendState.setVisibility(View.GONE);
        }
        if (session.getNotReadCount() > 0) {
            if (session.getNotReadCount() > 99) {
                tvNotReadCount.setText("99+");
            } else {
                tvNotReadCount.setText(String.valueOf(session.getNotReadCount()));
            }
            tvNotReadCount.setVisibility(View.VISIBLE);
        } else {
            tvNotReadCount.setText("");
            tvNotReadCount.setVisibility(View.GONE);
        }
        if (PublicParams.SYSTEM_USER_ID.equals(session.getFrom()) || PublicParams.CHAT_SERVICE_USER_ID.equals(session.getFrom())) {
            //客服小然和系统消息不显示状态
            tvStatus.setVisibility(View.GONE);
        } else {
            if (session.getStatus() != null) {
                String statusStr = SessionStatus.getStatusStr(session.getStatus().intValue());
                if (statusStr != null) {
                    tvStatus.setText(statusStr);
                    tvStatus.setVisibility(View.VISIBLE);
                } else {
                    tvStatus.setVisibility(View.GONE);
                }
            } else {
                tvStatus.setVisibility(View.GONE);
            }
        }
        if (position == mList.size() - 1) {
            lineView.setVisibility(View.GONE);
        } else {
            lineView.setVisibility(View.VISIBLE);
        }
    }


    private String formatMsgContent(Session session) {
        if (session == null || session.getContentType() == null) {
            return "";
        }
        String str = "";
        try {
            switch (session.getContentType()) {
                case ContentType.TEXT:
                    str = session.getContent();
                    break;
                case ContentType.IMAGE:
                    str = "[图片]";
                    break;
                case ContentType.AUDIO:
                    str = "[语音]";
                    break;
                case ContentType.FILE:
                    str = "[文件]";
                    break;
                case ContentType.WZD_QUESTION:
                    str = "[问诊单已发送]";
                    break;
                case ContentType.WZD_ANSWER:
                    str = "[问诊单已填写]";
                    break;
                case ContentType.FZD_QUESTION:
                    str = "[复诊单已发送]";
                    break;
                case ContentType.FZD_ANSWER:
                    str = "[复诊单已填写]";
                    break;
                case ContentType.SUPPLEMENT_QUESTION:
                    str = "[定制问诊单已发送]";
                    break;
                case ContentType.SUPPLEMENT_ANSWER:
                    str = "[定制问诊单已填写]";
                    break;
                case ContentType.LOGISTICS:
                    str = "[物流信息]";
                    break;
                case ContentType.START_CHAT_DOCTOR:
                    str = "[开启会话]";
                    break;
                case ContentType.START_CHAT_PATIENT:
                    str = "[开启会话]";
                    break;
                case ContentType.FINISH_CHAT_DOCTOR:
                    str = "[结束会话]";
                    break;
                case ContentType.MSG_REVOKE:
                    str = "您撤回了一条消息";
                    break;
                case ContentType.SM003:
                case ContentType.SYSTEM:
                    str = session.getContent();
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return str;
    }

    /**
     * 生成，如 “共 5 味”的样式（5为红色，且字体较大）
     *
     * @param leftContent
     * @param centerContent
     * @param rightContent
     * @return
     */
    public SpannableStringBuilder createSpannable(String leftContent, String centerContent, String rightContent) {
        SpannableStringBuilder ssb = new SpannableStringBuilder(leftContent);
        if (!TextUtils.isEmpty(centerContent)) {
            ssb.append("" + centerContent + "");
        }
        ssb.setSpan(new ForegroundColorSpan(mContext.getResources().getColor(R.color.br_color_red_ef4d3b)), 0, leftContent.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        ssb.setSpan(new AbsoluteSizeSpan((int) mContext.getResources().getDimension(R.dimen.textsize_15)), 0, leftContent.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        if (!TextUtils.isEmpty(rightContent)) {
            ssb.append(rightContent);
        }
        return ssb;
    }

}
