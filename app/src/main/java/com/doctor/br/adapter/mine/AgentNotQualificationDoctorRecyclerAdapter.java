package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.bean.NotQualificationDoctorBean;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：经纪人-大夫状态-已注册未认证大夫列表适配器
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/7
 */

public class AgentNotQualificationDoctorRecyclerAdapter extends RecyclerView.Adapter<AgentNotQualificationDoctorRecyclerAdapter.VH> {
    private Context context;
    private List<NotQualificationDoctorBean.DoctorMessagesBean> list;

    public AgentNotQualificationDoctorRecyclerAdapter(Context context, List<NotQualificationDoctorBean.DoctorMessagesBean> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public AgentNotQualificationDoctorRecyclerAdapter.VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(context).inflate(R.layout.item_not_qualification_doctor, parent, false);//这种方法不会使布局自适应
        return new AgentNotQualificationDoctorRecyclerAdapter.VH(itemView);
    }

    @Override
    public void onBindViewHolder(AgentNotQualificationDoctorRecyclerAdapter.VH holder, int position) {

        holder.nameTv.setText(list.get(position).getName());
        holder.phoneTv.setText(list.get(position).getMobile());

        holder.timeTv.setText(list.get(position).getCreatTime());

        if ("1".equals(list.get(position).getIsPerfected())) {
            holder.isPerfectImg.setImageResource(R.drawable.agent_is_perfect_img);
        } else {
            holder.isPerfectImg.setImageResource(R.drawable.agent_is_perfect_not_img);

        }
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.phone_tv)
        TextView phoneTv;
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.is_perfect_img)
        ImageView isPerfectImg;

        public VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
