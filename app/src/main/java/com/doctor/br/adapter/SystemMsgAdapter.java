package com.doctor.br.adapter;

import android.content.Context;
import android.widget.TextView;

import com.doctor.br.db.entity.SystemMsg;
import com.doctor.br.utils.DateUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.adapter.BaseAdapter;
import org.newapp.ones.base.adapter.ViewHolder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 系统消息适配器
 * @createTime 2017/12/2
 */

public class SystemMsgAdapter extends BaseAdapter<SystemMsg> {
    private Context mContext;
    private List<SystemMsg> list;

    public SystemMsgAdapter(Context context, List<SystemMsg> list, int layoutResId) {
        super(context, list, layoutResId);
        this.mContext = context;
        this.list = list;
    }

    @Override
    public void setViewData(ViewHolder holder, int position) {
        TextView tvTime = (TextView) holder.findViewById(R.id.tv_time);
        TextView tvContent = (TextView) holder.findViewById(R.id.tv_content);

        SystemMsg systemMsg = list.get(position);
        if (systemMsg != null) {
            tvTime.setText(DateUtils.dateFormat(systemMsg.getCreatedTime() + ""));
            tvContent.setText(systemMsg.getContentObjectContent());
        }
    }
}
