package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：证书示例recyclerview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/1
 */

public class CertificatExampleRecyclerAdapter extends RecyclerView.Adapter<CertificatExampleRecyclerAdapter.VH> implements View.OnClickListener {
    private Context context;
    private int[] imgs;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public CertificatExampleRecyclerAdapter(Context context, int[] imgs, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.imgs = imgs;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }


    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_certificat_example_recycler, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(view);
        int neiBianJu = (int) (DensityUtils.getScreenWidth(context) - context.getResources().getDimension(R.dimen.certificat_example_recycler_padding) * 2);
        int imgWidth = (int) (neiBianJu / 2 - context.getResources().getDimension(R.dimen.certificat_example_recycler_item_padding) * 2);
        int imgHeight = imgWidth * 236 / 334;
        holder.exampleImg.getLayoutParams().width = imgWidth;
        holder.exampleImg.getLayoutParams().height = imgHeight;
        if (recyclerItemItemClickListener != null) {
            holder.exampleImg.setOnClickListener(this);
        }
        return holder;
    }


    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.exampleImg.setTag(R.id.img_url, position);

        GlideUtils.getInstance().loadImage(imgs[position], context, holder.exampleImg, imgs[position]);

        holder.titleTv.setVisibility(View.VISIBLE);
        if (position == 0) {
            holder.titleTv.setText("医师资格证书");
        } else if (position == 2) {
            holder.titleTv.setText("医师执业证书");
        } else if (position == 4) {
            holder.titleTv.setText("职称证书");
        } else {
            holder.titleTv.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        return 5;
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.title_tv)
        TextView titleTv;
        @BindView(R.id.example_img)
        ShapeImageView exampleImg;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
