package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentPerformanceEveryMonthBean;
import com.doctor.yy.R;

import java.util.List;

/**
 * 类描述：业绩汇总-按月份查看
 * 创建人：ShiShaoPo
 * 创建时间：2018/5/16
 */

public class PerformanceEveryMonthAdapter extends RecyclerView.Adapter<PerformanceEveryMonthAdapter.VH> implements View.OnClickListener {

    private Context context;
    private List<AgentPerformanceEveryMonthBean.ListBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public PerformanceEveryMonthAdapter(Context context, List<AgentPerformanceEveryMonthBean.ListBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_performance_summary_recycler, parent, false);//这种方法不会使布局自适应
        if (recyclerItemClickListener != null) {
            view.setOnClickListener(this);
        }
        VH vh = new VH(view);
        vh.rightImg.setVisibility(View.GONE);
        vh.ratioIncomeTv.setVisibility(View.GONE);
        LinearLayout.LayoutParams right1 = (LinearLayout.LayoutParams) vh.patientNumbersTv.getLayoutParams();
        right1.weight = 2;

        LinearLayout.LayoutParams right2 = (LinearLayout.LayoutParams) vh.prescriptionNumbersTv.getLayoutParams();
        right2.weight = 2;
        return vh;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(position);

        holder.doctorNameTv.setText(list.get(position).getPay_time());

        String name = list.get(position).getPatinentName().length() > 3 ?
                list.get(position).getPatinentName().substring(0, 3) + "…" :
                list.get(position).getPatinentName();

        holder.orderAmountTv.setText("订单编号:" + list.get(position).getOrderId());
        holder.patientNumbersTv.setText("配比收入:¥" + list.get(position).getPbincome());

        holder.averageUnitPriceTv.setText("患者姓名:" + name);
        holder.prescriptionNumbersTv.setText("基础药费:¥" + list.get(position).getOrderAmount());
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        TextView doctorNameTv, ratioIncomeTv,
                orderAmountTv, averageUnitPriceTv,
                patientNumbersTv, prescriptionNumbersTv;
        ImageView rightImg;

        public VH(View itemView) {
            super(itemView);
            doctorNameTv = (TextView) itemView.findViewById(R.id.doctor_name_tv);
            ratioIncomeTv = (TextView) itemView.findViewById(R.id.ratio_income_tv);
            orderAmountTv = (TextView) itemView.findViewById(R.id.order_amount_tv);
            averageUnitPriceTv = (TextView) itemView.findViewById(R.id.average_unit_price_tv);
            patientNumbersTv = (TextView) itemView.findViewById(R.id.patient_numbers_tv);
            prescriptionNumbersTv = (TextView) itemView.findViewById(R.id.prescription_numbers_tv);
            rightImg = (ImageView) itemView.findViewById(R.id.right_img);
        }
    }
}
