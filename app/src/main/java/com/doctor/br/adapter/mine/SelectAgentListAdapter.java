package com.doctor.br.adapter.mine;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.bean.AgentSelectBean;
import com.doctor.br.view.stickyListView.StickyListHeadersAdapter;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：选择经纪人适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/13
 */

public class SelectAgentListAdapter extends BaseAdapter implements StickyListHeadersAdapter, View.OnClickListener {


    private Context context;
    private List<AgentSelectBean.RowsBean> selectList;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public SelectAgentListAdapter(Context context, List<AgentSelectBean.RowsBean> selectList, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.selectList = selectList;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }


    @Override
    public View getHeaderView(int position, View convertView, ViewGroup parent) {
        HeaderViewHolder headerViewHolder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_select_agent_header_list, parent, false);//这种方法不会使布局自适应
            headerViewHolder = new HeaderViewHolder(convertView);
            convertView.setTag(headerViewHolder);
        } else {
            headerViewHolder = (HeaderViewHolder) convertView.getTag();
        }
        if (TextUtils.isEmpty(selectList.get(position).getTeamId())) {
            headerViewHolder.teamNameTv.setText("省负责人");
            return convertView;
        }
        headerViewHolder.teamNameTv.setText(selectList.get(position).getTeamName());

        return convertView;
    }

    @Override
    public long getHeaderId(int position) {
        return selectList.get(position).getTeamId().hashCode();
    }

    @Override
    public int getCount() {
        return selectList == null ? 0 : selectList.size();
    }

    @Override
    public Object getItem(int position) {
        return selectList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ItemViewHolder itemViewHolder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_select_agent_list, parent, false);//这种方法不会使布局自适应
            itemViewHolder = new ItemViewHolder(convertView);
            if (recyclerItemItemClickListener != null) {
                itemViewHolder.commitBtn.setOnClickListener(this);
            }
            convertView.setTag(itemViewHolder);
        } else {
            itemViewHolder = (ItemViewHolder) convertView.getTag();
        }
        itemViewHolder.commitBtn.setTag(position);

        itemViewHolder.nameTv.setText(selectList.get(position).getAgentName());
        itemViewHolder.phoneTv.setText(selectList.get(position).getAgentMobile());
        itemViewHolder.addressTv.setText(selectList.get(position).getAgentAreaCode());
        itemViewHolder.numberTv.setText(selectList.get(position).getDoctorNum());

        if (position < selectList.size() - 1 && !selectList.get(position).getTeamId().equals(selectList.get(position + 1).getTeamId())) {
            itemViewHolder.bottomLine.setVisibility(View.GONE);
            itemViewHolder.bottomView.setVisibility(View.VISIBLE);
        } else if (position == selectList.size() - 1) {
            itemViewHolder.bottomLine.setVisibility(View.GONE);
            itemViewHolder.bottomView.setVisibility(View.VISIBLE);
        } else {
            itemViewHolder.bottomLine.setVisibility(View.VISIBLE);
            itemViewHolder.bottomView.setVisibility(View.GONE);
        }
        return convertView;
    }

    @Override
    public void onClick(View v) {
        int position = (int) v.getTag();
        recyclerItemItemClickListener.itemViewClick(position, v);
    }

    static class HeaderViewHolder {
        @BindView(R.id.team_name_tv)
        TextView teamNameTv;

        HeaderViewHolder(View view) {
            ButterKnife.bind(this, view);
        }
    }

    static class ItemViewHolder {
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.phone_tv)
        TextView phoneTv;
        @BindView(R.id.address_tv)
        TextView addressTv;
        @BindView(R.id.number_tv)
        TextView numberTv;
        @BindView(R.id.commit_btn)
        com.doctor.br.view.NoDoubleClickBtn commitBtn;
        @BindView(R.id.bottom_line)
        View bottomLine;
        @BindView(R.id.bottom_view)
        View bottomView;

        ItemViewHolder(View view) {
            ButterKnife.bind(this, view);
        }
    }

}

