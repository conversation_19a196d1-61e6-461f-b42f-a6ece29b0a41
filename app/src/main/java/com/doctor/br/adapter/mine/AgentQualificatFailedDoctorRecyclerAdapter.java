package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentQualifitFailedDoctorBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：经纪人-大夫状态-认证失败列表适配器
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/8
 */

public class AgentQualificatFailedDoctorRecyclerAdapter extends RecyclerView.Adapter<AgentQualificatFailedDoctorRecyclerAdapter.VH> implements View.OnClickListener {

    private Context context;
    private List<AgentQualifitFailedDoctorBean.DoctorMessagesBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public AgentQualificatFailedDoctorRecyclerAdapter(Context context, List<AgentQualifitFailedDoctorBean.DoctorMessagesBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(context).inflate(R.layout.item_qualification_failed_doctor, parent, false);
        VH holder = new VH(itemView);
        if (recyclerItemClickListener != null) {
            holder.itemView.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(position);

        GlideUtils.getInstance().loadImage(list.get(position).getHeadImage(), context, holder.headImg, R.drawable.default_head_img);
        holder.doctorNameTv.setText(list.get(position).getName());

        if ("1".equals(list.get(position).getSex())) {
            holder.sexImg.setImageResource(R.drawable.male_small);
        } else {
            holder.sexImg.setImageResource(R.drawable.female_small);
        }

        holder.commitTimeTv.setText(list.get(position).getApplyDatetime());
        holder.auditTimeTv.setText(list.get(position).getAuditTime());
        holder.failedReasonTv.setText("失败原因: " + list.get(position).getAuditReason());
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.head_img)
        ShapeImageView headImg;
        @BindView(R.id.doctor_name_tv)
        TextView doctorNameTv;
        @BindView(R.id.sex_img)
        ImageView sexImg;
        @BindView(R.id.commit_time_tv)
        TextView commitTimeTv;
        @BindView(R.id.audit_time_tv)
        TextView auditTimeTv;
        @BindView(R.id.failed_reason_tv)
        TextView failedReasonTv;

        public VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
