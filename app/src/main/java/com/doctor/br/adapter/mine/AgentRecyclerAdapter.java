package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentNumberBean;
import com.doctor.yy.R;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_ASSISTANT;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_INSPECTOR;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_MINE;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_MINE_TEAM;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PART;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PART_TEAM;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PERSON;
import static com.doctor.br.activity.mine.agent.AgentActivity.ROLE_PROVINCE;

/**
 * 类描述：经纪人界面recyclerview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/5
 */

public class AgentRecyclerAdapter extends RecyclerView.Adapter<AgentRecyclerAdapter.VH> implements View.OnClickListener {

    private Context context;
    private AgentNumberBean agentNumberBean;
    private RecyclerItemClickListener recyclerItemClickListener;


    private int[] showImgs = new int[0];//根据身份不同显示不同的图片
    private String[] showStrs = new String[0];//根据身份不同显示不同的标题
    private int[] imgs = {R.drawable.agent_dispatch_img,
            R.drawable.agent_framework_img,
            R.drawable.agent_pending_img,
            R.drawable.agent_doctor_img, R.drawable.agent_exception_doctor_img, R.drawable.agent_achievement_img, R.drawable.agent_download_img, R.drawable.agent_task_img};
    private String[] strings = {"调度审核",
            "组织架构",
            "待处理",
            "大夫状态", "异常大夫", "业绩查询", "下载", "任务分配"};

    public AgentRecyclerAdapter(Context context, String isDitui, AgentNumberBean agentNumberBean, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.agentNumberBean = agentNumberBean;
        this.recyclerItemClickListener = recyclerItemClickListener;
        if (TextUtils.isEmpty(isDitui)) {
            return;
        }
        //显示多少个图片和标题
        int numbers = 0;
        //复制数组的起始位置
        int srcPos = 0;
        switch (isDitui) {
            case ROLE_INSPECTOR://总监
                numbers = 2;
                srcPos = 0;
                break;
            case ROLE_ASSISTANT://总助
                numbers = 1;
                srcPos = 1;
                break;
            case ROLE_PROVINCE://省负责人
            case ROLE_MINE_TEAM://直营团队负责人
            case ROLE_PART_TEAM://兼职团队负责人
                numbers = 6;
                srcPos = 2;
                break;
            case ROLE_MINE://普通直营经纪人
            case ROLE_PART://普通兼职经纪人
            case ROLE_PERSON://个人兼职
                numbers = 5;
                srcPos = 2;
                break;
            default:
                //后期全部隐藏，测试使用
//                showImgs = imgs;
//                showStrs = strings;
                break;
        }
        copyImgAndName(srcPos, numbers);
    }

    /**
     * 根据地推身份不同显示不同的图片和标题
     *
     * @param srcPos 拷贝数组的起始位置
     * @param length 需要拷贝的长度
     */
    private void copyImgAndName(int srcPos, int length) {
        showImgs = new int[length];
        showStrs = new String[length ];
        System.arraycopy(imgs, srcPos, showImgs, 0, length);
        System.arraycopy(strings, srcPos, showStrs, 0, length);

//        showImgs[length] = videoImg;
//        showStrs[length] = videoStr;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_agent_recycler, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(view);
        if (recyclerItemClickListener != null) {
            holder.itemView.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(showImgs[position]);

        holder.itemImg.setImageResource(showImgs[position]);
        holder.itemTv.setText(showStrs[position]);

        holder.penddingTv.setVisibility(View.INVISIBLE);
        switch (showImgs[position]) {
            case R.drawable.agent_dispatch_img://待审核
                if (agentNumberBean != null && agentNumberBean.getWaitReviewed() > 0) {
                    if (agentNumberBean.getWaitReviewed() > 99) {
                        holder.penddingTv.setText("99+");
                    } else {
                        holder.penddingTv.setText(agentNumberBean.getWaitReviewed() + "");
                    }
                    holder.penddingTv.setVisibility(View.VISIBLE);
                }
                break;
            case R.drawable.agent_pending_img://待处理
                if (agentNumberBean != null && agentNumberBean.getTotal() > 0) {
                    if (agentNumberBean.getTotal() > 99) {
                        holder.penddingTv.setText("99+");
                    } else {
                        holder.penddingTv.setText(agentNumberBean.getTotal() + "");
                    }
                    holder.penddingTv.setVisibility(View.VISIBLE);
                }
                break;
            case R.drawable.agent_task_img://待分配
                if (agentNumberBean != null && agentNumberBean.getWaitAssigend() > 0) {
                    if (agentNumberBean.getWaitAssigend() > 99) {
                        holder.penddingTv.setText("99+");
                    } else {
                        holder.penddingTv.setText(agentNumberBean.getWaitAssigend() + "");
                    }
                    holder.penddingTv.setVisibility(View.VISIBLE);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemCount() {
        return showImgs.length;
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.item_img)
        ImageView itemImg;
        @BindView(R.id.pendding_tv)
        TextView penddingTv;//小红点
        @BindView(R.id.item_tv)
        TextView itemTv;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
