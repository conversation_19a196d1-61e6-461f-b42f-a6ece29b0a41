package com.doctor.br.adapter.medical;


import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.doctor.br.bean.medical.MedicineOrTempleBean;
import com.doctor.yy.R;

import org.newapp.ones.base.adapter.BaseAdapter;
import org.newapp.ones.base.adapter.ViewHolder;

import java.util.List;

/**
 * created by Sunxiaxia on 2017/12/1 19:37
 * description: 添加药材界面横行listView 适配器
 */
public class MedicineHLvAdapter extends BaseAdapter<MedicineOrTempleBean> {
    public static int TYPE_ADD_MEDICINE = 1;
    public static int TYPE_ADD_TEMPLATE_MEDICINE = 2;
    private List<MedicineOrTempleBean> mList;
    private Context mContext;
    private int type;


    public MedicineHLvAdapter(Context context, List<MedicineOrTempleBean> list, int layoutResId, int type) {
        super(context, list, layoutResId);
        mContext = context;
        this.type = type;
        this.mList = list;
    }

    public void updateData(List<MedicineOrTempleBean> list) {
        this.mList = list;
        notifyDataSetChanged();
    }

    @Override
    public void setViewData(ViewHolder holder, int position) {
        TextView name_tv = ((TextView) holder.findViewById(R.id.name_tv));
        TextView desc_tv = ((TextView) holder.findViewById(R.id.desc_tv));
        name_tv.setText(mList.get(position).getName());
        boolean isShowPrice = type == TYPE_ADD_MEDICINE;
        if ("2".equals(mList.get(position).getType()) || "3".equals(mList.get(position).getType())) {//常用方 经典方
            name_tv.setTextColor(mContext.getResources().getColor(R.color.br_color_theme));
            if ("2".equals(mList.get(position).getType())) {
                desc_tv.setText("常用方");
            } else {
                desc_tv.setText("经典方");
            }
            desc_tv.setTextColor(mContext.getResources().getColor(R.color.br_color_black_999));
        } else if ("1".equals(mList.get(position).getType())) {//药材
            if ("0".equals(mList.get(position).getStock())) {//无库存，
                name_tv.setTextColor(mContext.getResources().getColor(R.color.br_color_et_hint));
                desc_tv.setText("缺药");
                desc_tv.setTextColor(mContext.getResources().getColor(R.color.br_color_red_ff8585));
            } else {
                name_tv.setTextColor(mContext.getResources().getColor(R.color.br_color_theme_text));
                desc_tv.setText("￥" + mList.get(position).getPrice() + "/" + mList.get(position).getUnit());
                desc_tv.setTextColor(mContext.getResources().getColor(R.color.br_color_black_999));
            }
        }
        if (isShowPrice) {
            desc_tv.setVisibility(View.VISIBLE);
        } else {
            desc_tv.setVisibility(View.GONE);
        }


    }
}
