//package com.doctor.br.adapter.mine;
//
//import android.content.Context;
//import android.support.v7.widget.RecyclerView;
//import android.text.TextUtils;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//import android.widget.TextView;
//
//import com.doctor.br.adapter.RecyclerItemClickListener;
//import com.doctor.br.bean.AgentDoctorListBean;
//import com.doctor.yy.R;
//
//import org.newapp.ones.base.utils.glide.GlideUtils;
//import org.newapp.ones.base.widgets.ShapeImageView;
//
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.List;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//
///**
// * 类描述：地推我的大夫列表适配器
// * 创建人：ShiShaoPo
// * 创建时间：2017/9/29 17:47
// * 修改人：ShiShaoPo
// * 修改时间：2017/9/29 17:47
// */
//
//public class MyDoctorsListAdapter extends RecyclerView.Adapter implements View.OnClickListener {
//    private final int HEADER_ITEM = 111;//头部
//    private final int ITEM = 222;//正常item
//
//
//    private Context context;
//    private String doctorNumber;
//    private List<AgentDoctorListBean.ListBean> doctorList;
//    private RecyclerItemClickListener recyclerItemClickListener;
//
//    public MyDoctorsListAdapter(Context context, String doctorNumber, List<AgentDoctorListBean.ListBean> doctorList, RecyclerItemClickListener recyclerItemClickListener) {
//        this.context = context;
//        this.doctorNumber = doctorNumber;
//        this.doctorList = doctorList;
//        this.recyclerItemClickListener = recyclerItemClickListener;
//    }
//
//    public void setDoctorNumber(String doctorNumber) {
//        this.doctorNumber = doctorNumber;
//    }
//
//    @Override
//    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
//        switch (viewType) {
//            case HEADER_ITEM:
//                View view1 = LayoutInflater.from(context).inflate(R.layout.item_my_doctor_header, parent, false);//这种方法不会使布局自适应
//                return new HeaderVH(view1);
//            case ITEM:
//                View view2 = LayoutInflater.from(context).inflate(R.layout.item_my_doctor_list, parent, false);//这种方法不会使布局自适应
//                VH holder = new VH(view2);
//                if (recyclerItemClickListener != null) {
//                    holder.itemView.setOnClickListener(this);
//                }
//                return holder;
//            default:
//                break;
//        }
//        return null;
//    }
//
//    @Override
//    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
//        switch (holder.getItemViewType()) {
//            case HEADER_ITEM:
//                HeaderVH headerVH = (HeaderVH) holder;
//                if (TextUtils.isEmpty(doctorNumber)) {
//                    headerVH.doctorNumberTv.setText("0");
//                } else {
//                    headerVH.doctorNumberTv.setText(doctorNumber);
//                }
//                break;
//            case ITEM:
//                holder.itemView.setTag(R.id.img_url, position);
//                VH vh = (VH) holder;
//                GlideUtils.getInstance().loadImage(doctorList.get(position - 1).getHandImgUrl(), context, vh.headImg, R.drawable.default_head_img);
//                vh.doctorNameTv.setText(doctorList.get(position - 1).getName());
//                if ("1".equals(doctorList.get(position - 1).getSex())) {
//                    vh.sexImg.setImageResource(R.drawable.male_small);
//                } else {
//                    vh.sexImg.setImageResource(R.drawable.female_small);
//                }
//                Date date = new Date(doctorList.get(position - 1).getCreateTime());
//                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                String time = simpleDateFormat.format(date);
//                vh.registerTimeTv.setText(time);
//                break;
//            default:
//                break;
//        }
//    }
//
//    @Override
//    public int getItemViewType(int position) {
////        if (position == 0) {
////            return HEADER_ITEM;
////        }
//        return ITEM;
//    }
//
//    @Override
//    public int getItemCount() {
//        return doctorList.size() + 1;
//    }
//
//    @Override
//    public void onClick(View v) {
//        recyclerItemClickListener.itemClick((Integer) v.getTag(R.id.img_url));
//    }
//
//    public static class HeaderVH extends RecyclerView.ViewHolder {
//        @BindView(R.id.doctor_number_tv)
//        TextView doctorNumberTv;
//
//        HeaderVH(View itemView) {
//            super(itemView);
//            ButterKnife.bind(this, itemView);
//        }
//    }
//
//    public static class VH extends RecyclerView.ViewHolder {
//        @BindView(R.id.head_img)
//        ShapeImageView headImg;
//        @BindView(R.id.doctor_name_tv)
//        TextView doctorNameTv;
//        @BindView(R.id.sex_img)
//        ImageView sexImg;
//        @BindView(R.id.register_time_tv)
//        TextView registerTimeTv;
//
//        VH(View itemView) {
//            super(itemView);
//            ButterKnife.bind(this, itemView);
//        }
//    }
//}
