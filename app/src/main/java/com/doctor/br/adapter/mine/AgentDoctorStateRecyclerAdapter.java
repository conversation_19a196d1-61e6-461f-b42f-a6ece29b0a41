package com.doctor.br.adapter.mine;

import android.content.Context;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentDoctorStateBean;
import com.doctor.yy.R;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：大夫状态列表适配器
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/6
 */

public class AgentDoctorStateRecyclerAdapter extends RecyclerView.Adapter<AgentDoctorStateRecyclerAdapter.VH> implements View.OnClickListener {

    private Context context;
    //    private AgentDoctorStateBean doctorStateBean;
    private RecyclerItemClickListener recyclerItemClickListener;

    private int[] names = {R.string.not_register, R.string.not_qualification, R.string.qualificationing, R.string.qualification_failed,
            R.string.not_patient, R.string.not_extract, R.string.not_pay, R.string.qualification_success};
    private List<String> numbers = new ArrayList<>();

    public AgentDoctorStateRecyclerAdapter(Context context, AgentDoctorStateBean doctorStateBean, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
//        this.doctorStateBean = doctorStateBean;
        numbers.add(doctorStateBean.getUnregisterDoctor());
        numbers.add(doctorStateBean.getUnauthorizedDoctor());
        numbers.add(doctorStateBean.getUnauthorizingDoctor());
        numbers.add(doctorStateBean.getUnauthorizFailedDoctor());
        numbers.add(doctorStateBean.getDoctorNumOfFriends());
        numbers.add(doctorStateBean.getNoOrderDoctor());
        numbers.add(doctorStateBean.getNoPayOrderDoctor());
        numbers.add(doctorStateBean.getAuthorizedDoctor());
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(context).inflate(R.layout.item_agent_doctor_state, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(itemView);
        if (recyclerItemClickListener != null) {
            holder.itemView.setOnClickListener(this);
        }
        return new VH(itemView);
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(names[position]);

        holder.nameTv.setText(names[position]);

        switch (names[position]){
            case R.string.not_patient:
                holder.secondNameTv.setText("（患者量低于5）");
                holder.secondNameTv.setVisibility(View.VISIBLE);
                break;
            case R.string.not_pay:
                holder.secondNameTv.setText("（100元以上）");
                holder.secondNameTv.setVisibility(View.VISIBLE);
                break;
            default:
                holder.secondNameTv.setVisibility(View.GONE);
                break;
        }

        holder.numberTv.setText(numbers.get(position));

        if (position % 2 == 0) {
            holder.leftLine.setVisibility(View.INVISIBLE);
        } else {
            holder.leftLine.setVisibility(View.VISIBLE);
        }


    }

    @Override
    public int getItemCount() {
        return 8;
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }


    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.second_name_tv)
        TextView secondNameTv;
        @BindView(R.id.number_tv)
        TextView numberTv;
        @BindView(R.id.left_line)
        View leftLine;

        public VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
