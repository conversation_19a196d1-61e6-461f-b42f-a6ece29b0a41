package com.doctor.br.adapter.medical;

import android.content.Context;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.List;

/**
*Author:sunxiaxia
*createdDate:2017/10/16
*description:药方模板适配器
*/

public class PrescriptionTemplateAdapter extends FragmentPagerAdapter {
    private List<Fragment> mFragmentList;
    private Context mContext;


    public PrescriptionTemplateAdapter(FragmentManager fm, Context mContext, List<Fragment> mFragmentList) {
        super(fm);
        this.mContext = mContext;
        this.mFragmentList = mFragmentList;
    }

    @Override
    public Fragment getItem(int position) {
        return mFragmentList.get(position);
    }

    @Override
    public int getCount() {
        return mFragmentList.size();
    }

    //重写这个方法，将设置每个Tab的标题
    @Override
    public CharSequence getPageTitle(int position) {

        return "历史药方";
    }

}
