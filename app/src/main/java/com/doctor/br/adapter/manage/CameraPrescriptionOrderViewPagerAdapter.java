package com.doctor.br.adapter.manage;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.doctor.br.activity.manage.CameraPrescriptionOrderActitity;
import com.doctor.br.fragment.manage.pictureOrder.DaifasongFragment;
import com.doctor.br.fragment.manage.pictureOrder.DaishenheFragment;
import com.doctor.br.fragment.manage.pictureOrder.DaizhuanhuanFragment;

/**
 * 类描述：拍照订单界面viewpager适配器
 * 创建人：YangYajun
 * 创建时间：2018/7/6
 */

public class CameraPrescriptionOrderViewPagerAdapter extends FragmentPagerAdapter {
    private DaifasongFragment daifasongFragment;
    private DaishenheFragment daishenheFragment;
    private DaizhuanhuanFragment daizhuanhuanFragment;

    private int size;
    private boolean isShow;

    public CameraPrescriptionOrderViewPagerAdapter(FragmentManager fm,int size,boolean isShow) {
        super(fm);
        this.size = size;
        this.isShow = isShow;
    }

    public void setShow(boolean show) {
        isShow = show;
    }

    @Override
    public Fragment getItem(int position) {
        switch (position){
            case 0:
                if (daifasongFragment == null){
                    daifasongFragment = new DaifasongFragment();
                    Bundle bundle = new Bundle();
                    bundle.putBoolean(CameraPrescriptionOrderActitity.IS_SHOW, isShow);
                    daifasongFragment.setArguments(bundle);
                }
                return  daifasongFragment;
            case 1:
                if (daishenheFragment == null){
                    daishenheFragment = new DaishenheFragment();
                    Bundle bundle = new Bundle();
                    bundle.putBoolean(CameraPrescriptionOrderActitity.IS_SHOW, isShow);
                    daishenheFragment.setArguments(bundle);
                }
                return  daishenheFragment;
            case 2:
                if (daizhuanhuanFragment == null){
                    daizhuanhuanFragment = new DaizhuanhuanFragment();
                    Bundle bundle = new Bundle();
                    bundle.putBoolean(CameraPrescriptionOrderActitity.IS_SHOW, isShow);
                    daizhuanhuanFragment.setArguments(bundle);
                }
                return  daizhuanhuanFragment;
            default:
                break;
        }
        return null;
    }

    @Override
    public int getCount() {
        return size;
    }
}
