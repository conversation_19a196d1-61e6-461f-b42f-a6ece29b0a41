package com.doctor.br.adapter.medical;

import org.newapp.ones.base.dataBean.ResponseResult;

import java.util.List;

/**
 * Created by han<PERSON>ifeng on 2018/3/9.
 */

public class SearchClassicBean extends ResponseResult {

    private List<DataBean> data;

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        public static int TYPE_CATEGORY = 1;
        public static int TYPE_PRESCRIPTION = 2;
        /**
         * dataType : 1
         * name : 安神剂
         * typeId : 19
         * id : 19
         */

        private int dataType;
        private String name;
        private String typeId;//分类id   1=分类；2=经典方
        private String id;//经典方Id

        public int getDataType() {
            return dataType;
        }

        public void setDataType(int dataType) {
            this.dataType = dataType;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTypeId() {
            return typeId;
        }

        public void setTypeId(String typeId) {
            this.typeId = typeId;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }
}
