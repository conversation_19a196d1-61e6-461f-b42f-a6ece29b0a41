package com.doctor.br.adapter.manage;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.MedicationOrderRrcordListBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：养生建议 已完成订单列表适配器（无StickyHeader效果）
 * 创建人：YangYajun
 * 创建时间：2018/8/17
 */

public class AlreadyCompleteRecyclerAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements View.OnClickListener {
    private final int WEEK = 111;//周统计
    private final int ORDER = 222;//订单
    private final int LAST_ITEM = 333;//最后一条

    private Context context;
    private List<MedicationOrderRrcordListBean.DatasBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public AlreadyCompleteRecyclerAdapter(Context context, List<MedicationOrderRrcordListBean.DatasBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case WEEK:
                View view1 = LayoutInflater.from(context).inflate(R.layout.item_order_header, parent, false);//这种方法不会使布局自适应
                Vh1 holder1 = new Vh1(view1);
                return holder1;
            case ORDER:
                View view2 = LayoutInflater.from(context).inflate(R.layout.item_order_list, parent, false);//这种方法不会使布局自适应
                Vh2 holder2 = new Vh2(view2);
                if (recyclerItemClickListener != null) {
                    holder2.itemView.setOnClickListener(this);
                }
                return holder2;
            case LAST_ITEM:
                View view4 = LayoutInflater.from(context).inflate(R.layout.item_order_islast, parent, false);//这种方法不会使布局自适应
                return new QuanbuRecyclerAdapter.LastVH(view4);
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {

        switch (holder.getItemViewType()) {
            case WEEK:
                Vh1 vh1 = (Vh1) holder;
                if ("1".equals(list.get(position).getDataType())) {
                    //本周
                    vh1.weekOrderNumber.setText(TextUtils.isEmpty(list.get(position).getOrderCount()) ? "" : list.get(position).getOrderCount());
                    vh1.bountyMoney.setText(TextUtils.isEmpty(list.get(position).getSettleAmount()) ? "" : list.get(position).getSettleAmount());
                    vh1.dateTv.setText((TextUtils.isEmpty(list.get(position).getDateName()) ? "" : list.get(position).getDateName()) + "（单）");
                    vh1.bountyStatus.setText("奖励（" + (TextUtils.isEmpty(list.get(position).getIsSendAccount()) ? "" : list.get(position).getIsSendAccount()) + "）");
                }
                break;
            case ORDER:

                Vh2 vh2 = (Vh2) holder;
                vh2.itemView.setTag(position);
                vh2.orderNum.setText("订单编号：" + (TextUtils.isEmpty(list.get(position).getOrderId()) ? "" : list.get(position).getOrderId()));
                vh2.timeTv.setText(TextUtils.isEmpty(list.get(position).getOrderTime()) ? "" : list.get(position).getOrderTime());
                vh2.medicationName.setText(TextUtils.isEmpty(list.get(position).getYspProductName()) ? "" :
                        (list.get(position).getYspProductName().length() > 7 ? list.get(position).getYspProductName().substring(0, 7) + "…" : list.get(position).getYspProductName()));
                vh2.medicationDesc.setText(TextUtils.isEmpty(list.get(position).getYspEffect()) ? "" : list.get(position).getYspEffect());
                GlideUtils.getInstance().loadImage(list.get(position).getYspProductImg(), context, vh2.headImg, R.drawable.icon_img_load_error);
                vh2.tvPrices.setText(TextUtils.isEmpty(list.get(position).getYspUnitPrice()) ? "" : list.get(position).getYspUnitPrice());
                vh2.tvNumber.setText(TextUtils.isEmpty(list.get(position).getSaleProductCount()) ? "" : list.get(position).getSaleProductCount());
                vh2.totalPrices.setText(TextUtils.isEmpty(list.get(position).getAmount()) ? "" : list.get(position).getAmount());
                vh2.orderTypeImg.setVisibility(View.VISIBLE);
                break;
            case LAST_ITEM:
                QuanbuRecyclerAdapter.LastVH lastVH = (QuanbuRecyclerAdapter.LastVH) holder;
                lastVH.tv.setText("已加载全部数据");
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (list.get(position).isLast()) {
            return LAST_ITEM;
        }
        switch (list.get(position).getDataType()) {
            case "0"://订单记录
                return ORDER;
            case "1"://周总计
                return WEEK;
            default:
                break;
        }
        return ORDER;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    class Vh1 extends RecyclerView.ViewHolder {

        @BindView(R.id.week_order_number)
        TextView weekOrderNumber;
        @BindView(R.id.date_tv)
        TextView dateTv;
        @BindView(R.id.bounty_money)
        TextView bountyMoney;
        @BindView(R.id.bounty_status)
        TextView bountyStatus;

        public Vh1(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    class Vh2 extends RecyclerView.ViewHolder {

        @BindView(R.id.order_num)
        TextView orderNum;
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.head_img)
        ShapeImageView headImg;
        @BindView(R.id.medication_name)
        TextView medicationName;
        @BindView(R.id.medication_desc)
        TextView medicationDesc;
        @BindView(R.id.tv_prices)
        TextView tvPrices;
        @BindView(R.id.tv_number)
        TextView tvNumber;
        @BindView(R.id.total_prices)
        TextView totalPrices;
        @BindView(R.id.order_type_img)
        ImageView orderTypeImg;

        public Vh2(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
