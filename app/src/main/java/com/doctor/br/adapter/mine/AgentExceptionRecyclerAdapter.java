package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentExceptionDoctorBean;
import com.doctor.yy.R;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：经纪人-异常大夫recyclerview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/7
 */

public class AgentExceptionRecyclerAdapter extends RecyclerView.Adapter<AgentExceptionRecyclerAdapter.VH> implements View.OnClickListener {

    private Context context;
    private RecyclerItemClickListener recyclerItemClickListener;

    public AgentExceptionRecyclerAdapter(Context context, AgentExceptionDoctorBean doctorBean, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        numbers.add(doctorBean.getTwoWeekesNoOrderDoctor());
        numbers.add(doctorBean.getProblemsDoctor());
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    private int[] names = {R.string.two_week_no_order, R.string.five_order};
    private List<String> numbers = new ArrayList<>();

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(context).inflate(R.layout.item_exception_doctor, parent, false);
        VH holder = new VH(itemView);
        if (recyclerItemClickListener != null) {
            holder.itemView.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(names[position]);

        if (position % 2 == 0) {
            holder.leftLine.setVisibility(View.INVISIBLE);
        } else {
            holder.leftLine.setVisibility(View.VISIBLE);
        }

        holder.nameTv.setText(names[position]);
        if (position == 1) {
            holder.secondNameTv.setVisibility(View.VISIBLE);
        } else {
            holder.secondNameTv.setVisibility(View.GONE);
        }
        holder.numberTv.setText(numbers.get(position));
    }

    @Override
    public int getItemCount() {
        return 2;
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.number_tv)
        TextView numberTv;
        @BindView(R.id.second_name_tv)
        TextView secondNameTv;
        @BindView(R.id.left_line)
        View leftLine;

        public VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
