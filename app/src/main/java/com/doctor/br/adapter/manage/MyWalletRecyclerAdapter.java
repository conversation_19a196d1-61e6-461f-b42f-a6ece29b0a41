package com.doctor.br.adapter.manage;

import android.content.Context;
import android.graphics.Color;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.bean.MyWalletBean;
import com.doctor.br.view.NoDoubleClickBtn;
import com.doctor.yy.R;

import org.newapp.ones.base.widgets.EmptyView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：我的钱包recyclerview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/7
 */

public class MyWalletRecyclerAdapter extends RecyclerView.Adapter implements View.OnClickListener {
    private final int TOP_VIEW1 = 111;//显示余额的界面
    private final int TOP_VIEW2 = 222;//选择账单明细的界面
    private final int ITEM_ORDER = 3331;//药方订单、患者咨询费、患者打赏的item
    private final int ITEM_CASH = 3332;//提现、提现失败冲正、提现手续费、提现失败手续费冲正、平台奖励、邀请奖励的item
    private final int EMPTY = 444;//列表为空时展示的界面
    private final int LAST = 555;//列表最后一条显示无更多数据


    private int headerSize = 2;//除了正常item，header的个数
    private Context context;
    private boolean isDoctor;//当前用户是否为doctor
    private String amount;//余额
    private String accountType;//当前账单类型 0 全部 1 收入 2 支出
    private List<MyWalletBean.BillsBean> list; //账单明细list
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public MyWalletRecyclerAdapter(Context context, boolean isDoctor, String amount, String accountType, List<MyWalletBean.BillsBean> list, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.isDoctor = isDoctor;
        this.amount = amount;
        this.accountType = accountType;
        this.list = list;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case TOP_VIEW1:
                View view1 = LayoutInflater.from(context).inflate(R.layout.view_my_wallet_balance, parent, false);//这种方法不会使布局自适应
                VH1 holder1 = new VH1(view1);
                if (recyclerItemItemClickListener != null) {
                    holder1.getMoneyBtn.setOnClickListener(this);
                }
                return holder1;
            case TOP_VIEW2:
                View view2 = LayoutInflater.from(context).inflate(R.layout.view_my_wallet_bill, parent, false);//这种方法不会使布局自适应
                VH2 holder2 = new VH2(view2);
                if (recyclerItemItemClickListener != null) {
                    holder2.currentView.setOnClickListener(this);
                }
                return holder2;
            case ITEM_ORDER:
                View orderView = LayoutInflater.from(context).inflate(R.layout.item_my_wallet_order, parent, false);
                VhOrder vhOrder = new VhOrder(orderView);
//                LinearLayout.LayoutParams yijiParams = (LinearLayout.LayoutParams) vhOrder.yijiTv.getLayoutParams();
//                LinearLayout.LayoutParams orderParams = (LinearLayout.LayoutParams) vhOrder.orderIdTv.getLayoutParams();
//                if (isDoctor) {
//                    yijiParams.weight = 1.3f;
//                    orderParams.weight = 1.3f;
//                } else {
//                    yijiParams.weight = 0.8f;
//                    orderParams.weight = 0.8f;
//                }
//                vhOrder.yijiTv.setLayoutParams(yijiParams);
//                vhOrder.orderIdTv.setLayoutParams(orderParams);
                return vhOrder;
            case ITEM_CASH:
                View cashView = LayoutInflater.from(context).inflate(R.layout.item_wallet_cash, parent, false);
                return new VhCash(cashView);
            case EMPTY:
                View view4 = LayoutInflater.from(context).inflate(R.layout.view_emty, parent, false);//这种方法不会使布局自适应
                if (emptyViewHeight != -1) {
                    view4.getLayoutParams().height = emptyViewHeight;
                }
                return new EmptyVH(view4);
            case LAST:
                View view5 = LayoutInflater.from(context).inflate(R.layout.item_wallet_islast, parent, false);
                return new RecyclerView.ViewHolder(view5) {
                };
            default:
                break;
        }
        return null;
    }

    private int emptyViewHeight = -1;//emptyview的高度

    public void setEmptyViewHeight(int emptyViewHeight) {
        this.emptyViewHeight = emptyViewHeight;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        switch (holder.getItemViewType()) {
            case TOP_VIEW1:
                VH1 holder1 = (VH1) holder;
                holder1.getMoneyBtn.setTag(R.id.img_url, position);
                holder1.balanceTv.setText(amount);
                break;
            case TOP_VIEW2:
                VH2 holder2 = (VH2) holder;
                holder2.currentView.setTag(R.id.img_url, position);
                if ("0".equals(accountType)) {
                    holder2.currentTv.setText("全部");
                } else if ("1".equals(accountType)) {
                    holder2.currentTv.setText("收入");
                } else if ("2".equals(accountType)) {
                    holder2.currentTv.setText("支出");
                }
                break;
            case ITEM_ORDER://药方订单、患者咨询费、患者打赏的item、药铺奖励
                VhOrder vhOrder = (VhOrder) holder;
                vhOrder.linear4.setVisibility(View.GONE);
                vhOrder.llLeft.setVisibility(View.GONE);
                vhOrder.zhenfeiTv.setVisibility(View.GONE);
                switch (list.get(position - headerSize).getSource()) {
                    case "药方订单":
                        vhOrder.linear4.setVisibility(View.GONE);
                        vhOrder.llLeft.setVisibility(View.VISIBLE);
                        vhOrder.zhenfeiTv.setVisibility(View.VISIBLE);
                        vhOrder.typeImg.setImageResource(R.drawable.wallet_order_img);
                        break;
                    case "养生订单奖励":
                        vhOrder.orderIdTv.setVisibility(View.VISIBLE);
                        vhOrder.llLeft.setVisibility(View.VISIBLE);
                        vhOrder.zhenfeiTv.setVisibility(View.GONE);
                        vhOrder.typeImg.setImageResource(R.drawable.wallet_pingtai_img);
                        break;
                    case "患者咨询费":
                        vhOrder.typeImg.setImageResource(R.drawable.wallet_zixun_img);
                        break;
                    case "患者赞赏":
                    default:
                        vhOrder.typeImg.setImageResource(R.drawable.wallet_dashang_img);
                        break;
                }
                if ("1".equals(list.get(position - headerSize).getType())) {
                    vhOrder.moneyTv.setTextColor(Color.parseColor("#de0000"));
                } else {
                    vhOrder.moneyTv.setTextColor(Color.parseColor("#00be82"));
                }
                vhOrder.typeTv.setText(list.get(position - headerSize).getSource());
                vhOrder.timeTv.setText(list.get(position - headerSize).getCreateDate());
                vhOrder.moneyTv.setText(list.get(position - headerSize).getCount());
                if ("养生订单奖励".equals(list.get(position - headerSize).getSource())) {
                    vhOrder.orderIdTv.setText(("订单时间: " + list.get(position - headerSize).getOrderDate()));
                } else {
                    vhOrder.orderIdTv.setText("订单编号: " + list.get(position - headerSize).getOrderId());
                }

                String patientName = list.get(position - headerSize).getPatinetName().length() > 3 ?
                        list.get(position - headerSize).getPatinetName().substring(0, 3) + "…" :
                        list.get(position - headerSize).getPatinetName();
                if (isDoctor) {
                    if ("养生订单奖励".equals(list.get(position - headerSize).getSource())) {
                        vhOrder.linear4.setVisibility(View.GONE);
                        vhOrder.patientNameTv.setText("订单数量: " + list.get(position - headerSize).getOrderCount());
                        vhOrder.yijiTv.setText("订单总额: ¥" + list.get(position - headerSize).getOrderAmount());
//                        vhOrder.zhenfeiTv.setText("奖励金额: ¥" + (list.get(position - headerSize).getCount()).replace("+", ""));
                    } else {
                        vhOrder.patientNameTv.setText("患者: " + patientName);
                        vhOrder.yijiTv.setText("医技服务费: ¥" + list.get(position - headerSize).getServiceCharge());
                        vhOrder.zhenfeiTv.setText("诊费: ¥" + list.get(position - headerSize).getFeeCharge());
                    }
                } else {
                    if ("养生订单奖励".equals(list.get(position - headerSize).getSource())) {
                        vhOrder.linear4.setVisibility(View.VISIBLE);
                        vhOrder.patientNameTv.setText("订单数量: " + list.get(position - headerSize).getOrderCount());
                        vhOrder.yijiTv.setText("订单总额: ¥" + list.get(position - headerSize).getOrderAmount());
//                        vhOrder.zhenfeiTv.setText("奖励金额: ¥" + (list.get(position - headerSize).getCount()).replace("+", ""));
                        vhOrder.shopBelongTv.setText("商品所属：" + list.get(position - headerSize).getDoctorName() + "大夫");
                    } else {
                        vhOrder.patientNameTv.setText("基础药费: ¥" + list.get(position - headerSize).getDrugPrice());
                        String doctorName = list.get(position - headerSize).getDoctorName().length() > 3 ?
                                list.get(position - headerSize).getDoctorName().substring(0, 3) + "…" :
                                list.get(position - headerSize).getDoctorName();
                        vhOrder.yijiTv.setText("开方大夫: " + doctorName);
                        vhOrder.zhenfeiTv.setText("患者: " + patientName);
                    }
                }
                break;
            case ITEM_CASH://提现、提现失败冲正、提现手续费、提现失败手续费冲正、平台奖励、邀请奖励的item
                VhCash vhCash = (VhCash) holder;
                vhCash.cardNumberTv.setVisibility(View.GONE);
                vhCash.cardNameTv.setVisibility(View.GONE);
                vhCash.cashCountTv.setVisibility(View.GONE);
                vhCash.failReasonTv.setVisibility(View.GONE);
                vhCash.sourceDesTv.setVisibility(View.GONE);
                switch (list.get(position - headerSize).getSource()) {
                    case "提现":
                        vhCash.cardNumberTv.setVisibility(View.VISIBLE);
                        vhCash.cardNameTv.setVisibility(View.VISIBLE);
                        vhCash.typeImg.setImageResource(R.drawable.wallet_tixian_img);
                        break;
                    case "提现失败冲正":
                        vhCash.cardNumberTv.setVisibility(View.VISIBLE);
                        vhCash.cardNameTv.setVisibility(View.VISIBLE);
                        vhCash.failReasonTv.setVisibility(View.VISIBLE);
                        vhCash.typeImg.setImageResource(R.drawable.wallet_tixian_fail_img);
                        break;
                    case "提现手续费":
                        vhCash.cardNumberTv.setVisibility(View.VISIBLE);
                        vhCash.cardNameTv.setVisibility(View.VISIBLE);
                        vhCash.cashCountTv.setVisibility(View.VISIBLE);
                        vhCash.typeImg.setImageResource(R.drawable.wallet_tixian_img);
                        break;
                    case "提现失败手续费冲正":
                        vhCash.cardNumberTv.setVisibility(View.VISIBLE);
                        vhCash.cardNameTv.setVisibility(View.VISIBLE);
                        vhCash.cashCountTv.setVisibility(View.VISIBLE);
                        vhCash.failReasonTv.setVisibility(View.VISIBLE);
                        vhCash.typeImg.setImageResource(R.drawable.wallet_tixian_fail_img);
                        break;
                    case "账户金额冲正":
                        vhCash.sourceDesTv.setVisibility(View.VISIBLE);
                        vhCash.typeImg.setImageResource(R.drawable.wallet_tixian_fail_img);
                        break;
                    case "平台奖励":
                    case "邀请奖励":
                    case "认证通过奖励":
                        vhCash.sourceDesTv.setVisibility(View.VISIBLE);
                        vhCash.typeImg.setImageResource(R.drawable.wallet_pingtai_img);
                        break;
                    default:
                        break;
                }
                if ("1".equals(list.get(position - headerSize).getType())) {
                    vhCash.moneyTv.setTextColor(Color.parseColor("#de0000"));
                } else {
                    vhCash.moneyTv.setTextColor(Color.parseColor("#00be82"));
                }
                vhCash.typeTv.setText(list.get(position - headerSize).getSource());
                vhCash.timeTv.setText(list.get(position - headerSize).getCreateDate());
                vhCash.moneyTv.setText(list.get(position - headerSize).getCount());
                vhCash.cardNumberTv.setText("卡号: " + list.get(position - headerSize).getAccountNumber());
                vhCash.cardNameTv.setText("持卡人: " + list.get(position - headerSize).getAccountName());
                vhCash.cashCountTv.setText("提现金额: ¥" + list.get(position - headerSize).getCashMoney());
                vhCash.failReasonTv.setText("失败原因: " + list.get(position - headerSize).getReason());
                if ("账户金额冲正".equals(list.get(position - headerSize).getSource())) {
                    vhCash.sourceDesTv.setText("冲正原因: " + list.get(position - headerSize).getSourceDescription());
                } else {
                    vhCash.sourceDesTv.setText("奖励原因: " + list.get(position - headerSize).getSourceDescription());
                }
                break;
            case EMPTY://没有数据时显示
                EmptyVH emptyVH = (EmptyVH) holder;
                if (emptyViewHeight != -1) {
                    emptyVH.itemView.getLayoutParams().height = emptyViewHeight;
                }
                emptyVH.emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                emptyVH.emptyView.setEmptyText("暂无账单记录");
                emptyVH.emptyView.setEmptyImgResource(R.drawable.empty_no_bill);
                emptyVH.emptyView.setEmptyBackgroundColor(ContextCompat.getColor(context, R.color.br_color_white));
                emptyVH.emptyView.setVisibility(View.VISIBLE);
                break;
            case LAST://最后一条item显示
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemCount() {
        if (TextUtils.isEmpty(amount)) {
            return 0;
        }
        if ("0".equals(accountType) && list.size() == 0) {
            return 2;
        }
        if (list.size() == 0) {
            return headerSize + 1;
        }
        return list.size() + headerSize;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return TOP_VIEW1;
        }
        if (position == 1) {
            if ("0".equals(accountType) && list.size() == 0) {
                return EMPTY;
            }
            return TOP_VIEW2;
        }
        if (list.size() == 0) {
            return EMPTY;
        }
        if (list.get(position - headerSize).isLast()) {
            return LAST;
        }
        switch (list.get(position - headerSize).getSource()) {
            case "药方订单":
            case "养生订单奖励":
            case "患者咨询费":
            case "患者赞赏":
                return ITEM_ORDER;
            //提现、提现失败冲正、提现手续费、提现失败手续费冲正、平台奖励、邀请奖励
            default:
                return ITEM_CASH;
        }
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class VH1 extends RecyclerView.ViewHolder {
        @BindView(R.id.balance_tv)
        TextView balanceTv;//余额
        @BindView(R.id.get_money_btn)
        NoDoubleClickBtn getMoneyBtn;//提现

        VH1(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    static class VH2 extends RecyclerView.ViewHolder {
        @BindView(R.id.current_tv)
        TextView currentTv;//当前类型：全部，收入，支出
        @BindView(R.id.current_view)
        LinearLayout currentView;//可以点击的地方

        VH2(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    static class VhOrder extends RecyclerView.ViewHolder {
        //        @BindView(R.id.linear2)
//        LinearLayout linear2;
        @BindView(R.id.linear4)
        LinearLayout linear4;
        @BindView(R.id.type_img)
        ImageView typeImg;
        @BindView(R.id.type_tv)
        TextView typeTv;
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.shop_belong_tv)
        TextView shopBelongTv;
        @BindView(R.id.money_tv)
        TextView moneyTv;
        @BindView(R.id.order_id_tv)
        TextView orderIdTv;
        @BindView(R.id.patient_name_tv)
        TextView patientNameTv;
        @BindView(R.id.yiji_tv)
        TextView yijiTv;
        @BindView(R.id.zhenfei_tv)
        TextView zhenfeiTv;
        @BindView(R.id.ll_left)
        LinearLayout llLeft;
        @BindView(R.id.ll_right)
        LinearLayout llRight;

        VhOrder(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    static class VhCash extends RecyclerView.ViewHolder {
        @BindView(R.id.type_img)
        ImageView typeImg;
        @BindView(R.id.type_tv)
        TextView typeTv;
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.money_tv)
        TextView moneyTv;
        @BindView(R.id.card_number_tv)
        TextView cardNumberTv;
        @BindView(R.id.card_name_tv)
        TextView cardNameTv;
        @BindView(R.id.cash_count_tv)
        TextView cashCountTv;
        @BindView(R.id.fail_reason_tv)
        TextView failReasonTv;
        @BindView(R.id.source_des_tv)
        TextView sourceDesTv;

        VhCash(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    static class EmptyVH extends RecyclerView.ViewHolder {
        @BindView(R.id.empty_view)
        EmptyView emptyView;

        EmptyVH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
