package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.TextAppearanceSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.activity.mine.agent.AgentActivity;
import com.doctor.br.activity.mine.agent.framework.FrameworkActivity;
import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.bean.AgentFrameworkSearchBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：经纪人组织架构搜索recyclerview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/16
 */

public class AgentFrameworkSearchRecyclerAdapter extends RecyclerView.Adapter<AgentFrameworkSearchRecyclerAdapter.VH> implements View.OnClickListener {

    private Context context;
    private List<AgentFrameworkSearchBean.DataBean> list;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public AgentFrameworkSearchRecyclerAdapter(Context context, List<AgentFrameworkSearchBean.DataBean> list, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_framework_search_recycler, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(view);
        if (recyclerItemItemClickListener != null) {
            holder.dispatchBtn.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.dispatchBtn.setTag(R.id.img_url, position);

        if (position == 0) {
            holder.topLine.setVisibility(View.INVISIBLE);
        } else {
            holder.topLine.setVisibility(View.VISIBLE);
        }

        GlideUtils.getInstance().loadImage(list.get(position).getUserHeadImageUrl(), context, holder.headImg, R.drawable.default_head_img);

        String name = subStr(list.get(position).getUserName(), 3);
        String role = getRole(list.get(position).getProvinceName(), list.get(position).getTeamName(), list.get(position).getUserRole());
        SpannableString spannableString = new SpannableString(name + role);
        TextAppearanceSpan textAppearanceSpan = new TextAppearanceSpan(context, R.style.FrameworkActivityTitleStyle);
        spannableString.setSpan(textAppearanceSpan, name.length(), spannableString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        holder.nameTv.setText(spannableString);

        holder.phoneTv.setText(list.get(position).getUserMobile());
        if (FrameworkActivity.isShow) {
            holder.dispatchBtn.setVisibility(View.VISIBLE);
            if (FrameworkActivity.STATUS_DISPATCH.equals(list.get(position).getUserStatus())) {
                holder.dispatchBtn.setImageResource(R.drawable.framework_dispatch_img);
            } else if (FrameworkActivity.STATUS_DISPATCHING.equals(list.get(position).getUserStatus())) {
                holder.dispatchBtn.setImageResource(R.drawable.framework_no_dispatch_img);
            }
        } else {
            holder.dispatchBtn.setVisibility(View.GONE);
        }
    }

    /**
     * 切割字符串超过maxLength位数，显示...
     *
     * @param str       要处理的字符串
     * @param maxLength 最大长度
     * @return 处理完成的字符串
     */
    private String subStr(String str, int maxLength) {
        if (TextUtils.isEmpty(str) || maxLength <= 0) {
            return "";
        }
        String result = str;
        if (str.length() > maxLength) {
            result = result.substring(0, maxLength) + "…";
        }
        return result;
    }

    //拼接字符串
    private String getRole(String province, String teamName, String role) {
        StringBuilder result = new StringBuilder();
        result.append("（").append(province);
        if (!TextUtils.isEmpty(teamName)) {
            result.append(" - ").append(teamName);
        }
        result.append(" - ");
        if (TextUtils.isEmpty(role)) {
            return result.append("）").toString();
        }
        switch (role) {
            case AgentActivity.ROLE_PERSON://个人兼职
            case AgentActivity.ROLE_PART://团队兼职
            case AgentActivity.ROLE_MINE://团队直营
            default:
                result.append("团队职员");
                break;
            case AgentActivity.ROLE_PART_TEAM://兼职团队负责人
            case AgentActivity.ROLE_MINE_TEAM://直营团队负责人
                result.append("团队负责人");
                break;
            case AgentActivity.ROLE_PROVINCE://省负责人
                result.append("省负责人");
                break;
        }
        result.append("）");
        return result.toString();
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.top_line)
        View topLine;
        @BindView(R.id.head_img)
        ShapeImageView headImg;
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.phone_tv)
        TextView phoneTv;
        @BindView(R.id.dispatch_btn)
        ImageView dispatchBtn;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
