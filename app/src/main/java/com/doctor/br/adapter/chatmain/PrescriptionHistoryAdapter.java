package com.doctor.br.adapter.chatmain;

import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;

import com.doctor.br.bean.PrescriptionHistoryBean;
import com.doctor.br.bean.medical.TemplateDrugBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.DensityUtils;

import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;
import cn.bingoogolapple.androidcommon.adapter.BGAViewHolderHelper;

/**
 * <AUTHOR>
 * @version 4.0.3
 * @project BrZhongYiAndroid
 * @description 历史药方的adapter
 * @createTime 2017/11/15.
 */

public class PrescriptionHistoryAdapter extends BGARecyclerViewAdapter<PrescriptionHistoryBean.DataBean> {
    public PrescriptionHistoryAdapter(RecyclerView recyclerView) {
        super(recyclerView, R.layout.item_prescription_history);
    }

    @Override
    protected void fillData(BGAViewHolderHelper helper, int position, PrescriptionHistoryBean.DataBean dataBean) {
        //tv_name    tv_age     tv_sex   tv_time     tv_disease     tv_medicine
        helper.setText(R.id.tv_name, dataBean.getPatientName().length() > 5 ? dataBean.getPatientName().substring(0,5) + "…" : dataBean.getPatientName());
//        helper.setText(R.id.tv_age,dataBean.getPatientAge()+"岁");
//        helper.setText(R.id.tv_sex,dataBean.getPatientSex());
        helper.setText(R.id.tv_time, dataBean.getCreatedTime());
        helper.setText(R.id.tv_disease, dataBean.getDescription());
        if (!TextUtils.isEmpty(dataBean.getPatientSex())) {
            if ("男".equals(dataBean.getPatientSex())) {//1为男，其他为女
                helper.setImageResource(R.id.sex_img_iv, R.drawable.male_icon_white);
                helper.setBackgroundRes(R.id.sex_age_ll, R.drawable.sex_age_blue_bg);
            } else {
                helper.setImageResource(R.id.sex_img_iv, R.drawable.female_icon_white);
                helper.setBackgroundRes(R.id.sex_age_ll, R.drawable.sex_age_red_bg);
            }
        } else {
            helper.setBackgroundRes(R.id.sex_age_ll, R.color.br_color_white);
        }
        if (!TextUtils.isEmpty(dataBean.getPatientAge()) && !"0".equals(dataBean.getPatientAge())) {
            helper.setVisibility(R.id.age_tv, View.VISIBLE);
            helper.setText(R.id.age_tv, dataBean.getPatientAge());
        } else {
            helper.setVisibility(R.id.age_tv, View.GONE);
        }
        //设置用药的内容
        if (dataBean != null && dataBean.getDrugList() != null && dataBean.getDrugList().size() > 0) {
            SpannableStringBuilder ssb = new SpannableStringBuilder();
            for (int i = 0; i < dataBean.getDrugList().size(); i++) {
                TemplateDrugBean drugBean = dataBean.getDrugList().get(i);
                if (drugBean != null) {
                    ssb.append(drugBean.getDrugName() + " ");
                    ssb.append(drugBean.getDose() + "");
                    ssb.append(drugBean.getUnit());
                    //添加特殊单
                    if (!TextUtils.isEmpty(drugBean.getUseMethod())) {//有特殊煎法
                        SpannableStringBuilder ssMethod = new SpannableStringBuilder();
                        ssMethod.append("(");
                        ssMethod.append(drugBean.getUseMethod());
                        ssMethod.append(")");
                        ssMethod.setSpan(new ForegroundColorSpan(mContext.getResources().getColor(R.color.br_color_red_ef4d3b)), 0, ssMethod.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                        ssMethod.setSpan(new AbsoluteSizeSpan(DensityUtils.sp2px(mContext, 12)), 0, ssMethod.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                        ssb.append(ssMethod);

                    }
                    if (dataBean.getDrugList() != null && dataBean.getDrugList().size() > 1 && i != dataBean.getDrugList().size() - 1) {
                        ssb.append("，");
                    }
                }

            }

            helper.setText(R.id.tv_medicine, ssb);
        }

    }
}
