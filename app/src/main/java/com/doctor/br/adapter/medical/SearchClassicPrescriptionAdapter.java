package com.doctor.br.adapter.medical;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.doctor.yy.R;

import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;
import cn.bingoogolapple.androidcommon.adapter.BGAViewHolderHelper;
/**
 * <AUTHOR>
 * @version 4.0.3
 * @project BrZhongYiAndroid
 * @description   搜索经典方的adapter,搜索经典方二级界面的adapter
 * @createTime 2018/3/14
 */

public class SearchClassicPrescriptionAdapter extends BGARecyclerViewAdapter<SearchClassicBean.DataBean> {

    public SearchClassicPrescriptionAdapter(RecyclerView recyclerView) {
        super(recyclerView, R.layout.item_search_classic);
    }

    @Override
    protected void setItemChildListener(BGAViewHolderHelper helper, int viewType) {
        super.setItemChildListener(helper, viewType);


    }

    @Override
    protected void fillData(BGAViewHolderHelper helper, int position, SearchClassicBean.DataBean dataBean) {
        helper.setText(R.id.tv_title,dataBean.getName());
        //设置分割线
        setLineData(helper,position,dataBean);

        //设置更多的图标
        helper.setVisibility(R.id.iv_detail,SearchClassicBean.DataBean.TYPE_CATEGORY==dataBean.getDataType()?View.VISIBLE:View.GONE);
        System.out.println();
    }

    private void setLineData(BGAViewHolderHelper helper, int position, SearchClassicBean.DataBean dataBean) {//设置分割线
        View line = helper.getView(R.id.view_line);

        if(getItemCount()>0){
                if(position == mData.size()-1){  //如果是最后一条，线不可见
                    line.setVisibility(View.INVISIBLE);
                }else{
                    line.setVisibility(View.VISIBLE);
                }
        }
    }
}
