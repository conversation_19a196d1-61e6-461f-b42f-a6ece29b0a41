//package com.doctor.br.adapter.manage;
//
//import android.content.Context;
//import android.support.v4.content.ContextCompat;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.BaseAdapter;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.TextView;
//
//import com.doctor.br.bean.YiwanchengOrderListBean;
//import com.doctor.br.utils.DecimalUtils;
//import com.doctor.br.view.stickyListView.StickyListHeadersAdapter;
//import com.doctor.yy.R;
//
//import java.util.List;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//
///**
// * 类描述：已完成订单列表适配器
// * 创建人：ShiShaoPo
// * 创建时间：2018/5/18
// */
//
//public class YiwanchengListViewAdapter extends BaseAdapter implements StickyListHeadersAdapter {
//    /**
//     * item的类型，必须从0开始递增
//     */
//    private final int ORDER = 0;
//    private final int NEW_MONTH = 1;
//    private final int OLD_MONTH = 2;
//    private final int LAST_ITEM = 3;
//
//    private final int viewTypeCount = 4;//不同view类型的种类数量，有4种
//
//    private Context context;
//    private List<YiwanchengOrderListBean.ListBean> orderList;
//
//    public YiwanchengListViewAdapter(Context context, List<YiwanchengOrderListBean.ListBean> orderList) {
//        this.context = context;
//        this.orderList = orderList;
//    }
//
//    @Override
//    public View getHeaderView(int position, View convertView, ViewGroup parent) {
//        HeaderVh headerVh;
//        if (convertView == null) {
//            convertView = LayoutInflater.from(context).inflate(R.layout.item_yiwancheng_header, parent, false);//这种方法不会使布局自适应
//            headerVh = new HeaderVh(convertView);
//            convertView.setTag(headerVh);
//        } else {
//            headerVh = (HeaderVh) convertView.getTag();
//        }
//
//        headerVh.hintTv.setVisibility(View.GONE);
//        if ("1".equals(orderList.get(position).getDataType())) {
//            //本月
//            headerVh.rectImg.setImageResource(R.drawable.order_blue_react);
//
//            headerVh.monthTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme_text));
//            headerVh.monthTv.setText("本月");
//
//            int compareResult = DecimalUtils.compareTo(orderList.get(position).getOrderBalance(), "0");
//            if (compareResult > 0) {
//                headerVh.hintTv.setVisibility(View.VISIBLE);
//                headerVh.hintTv.setText("(距离平台额外奖励还差¥" + orderList.get(position).getOrderBalance() + ")");
//            }
//        } else {
//            //之前月份
//            headerVh.rectImg.setImageResource(R.drawable.order_gray_react);
//
//            headerVh.monthTv.setTextColor(ContextCompat.getColor(context,R.color.br_color_et_hint));
//            headerVh.monthTv.setText(orderList.get(position).getMonth());
//        }
//
//        return convertView;
//    }
//
//    @Override
//    public long getHeaderId(int position) {
//        return 0;
//    }
//
//    @Override
//    public int getCount() {
//        return 0;
//    }
//
//    @Override
//    public Object getItem(int position) {
//        return null;
//    }
//
//    @Override
//    public long getItemId(int position) {
//        return 0;
//    }
//
//    @Override
//    public View getView(int position, View convertView, ViewGroup parent) {
//
//        return null;
//    }
//
//    @Override
//    public int getItemViewType(int position) {
//        if (orderList.get(position).isLast()) {
//            return LAST_ITEM;
//        }
//        switch (orderList.get(position).getDataType()) {
//            case "0":
//                return ORDER;
//            case "1":
//                return NEW_MONTH;
//            case "2":
//                return OLD_MONTH;
//            default:
//                break;
//        }
//        return ORDER;
//    }
//
//    @Override
//    public int getViewTypeCount() {
//        return viewTypeCount;
//    }
//
//    static class HeaderVh {
//        @BindView(R.id.rect_img)
//        ImageView rectImg;
//        @BindView(R.id.month_tv)
//        TextView monthTv;
//        @BindView(R.id.hint_tv)
//        TextView hintTv;
//
//        HeaderVh(View itemView) {
//            ButterKnife.bind(this, itemView);
//        }
//    }
//
//    static class NewMonthVh {
//        @BindView(R.id.total_money_tv)
//        TextView totalMoneyTv;
//        @BindView(R.id.split_line)
//        View splitLine;
//        @BindView(R.id.right_linear)
//        LinearLayout rightLinear;
//        @BindView(R.id.margin_money_tv)
//        TextView marginMoneyTv;
//        @BindView(R.id.percent_tv)
//        TextView percentTv;
//
//        public NewMonthVh(View itemView) {
//            ButterKnife.bind(this, itemView);
//        }
//    }
//
//    static class OrderVh {
//        @BindView(R.id.time_tv)
//        TextView timeTv;
//        @BindView(R.id.name_tv)
//        TextView nameTv;
//
//        public OrderVh(View itemView) {
//            ButterKnife.bind(this, itemView);
//        }
//    }
//
//    static class OldMonthVh {
//        @BindView(R.id.total_money_tv)
//        TextView totalMoneyTv;
//        @BindView(R.id.month_tv)
//        TextView monthTv;
//
//        public OldMonthVh(View itemView) {
//            ButterKnife.bind(this, itemView);
//        }
//    }
//}
