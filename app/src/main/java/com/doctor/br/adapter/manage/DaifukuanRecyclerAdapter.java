package com.doctor.br.adapter.manage;

import android.content.Context;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.bean.DaifukuanOrderListBean;
import com.doctor.br.view.NoDoubleClickBtn;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：待付款订单列表
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/19 11:51
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/19 11:51
 */

public class DaifukuanRecyclerAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements View.OnClickListener {
    private final int ORDER_ITEM = 111;//订单item
    private final int LAST_ITEM = 222;//最后一条

    private Context context;
    private List<DaifukuanOrderListBean.ListBean> list;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public DaifukuanRecyclerAdapter(Context context, List<DaifukuanOrderListBean.ListBean> list, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case ORDER_ITEM:
                View view = LayoutInflater.from(context).inflate(R.layout.item_daifukuan_recycler, parent, false);//这种方法不会使布局自适应
                VH holder = new VH(view);
                if (recyclerItemItemClickListener != null) {
                    holder.moreView.setOnClickListener(this);
                    holder.remindBtn.setOnClickListener(this);
                }
                return holder;
            case LAST_ITEM:
                View view2 = LayoutInflater.from(context).inflate(R.layout.item_order_islast, parent, false);//这种方法不会使布局自适应
                return new QuanbuRecyclerAdapter.LastVH(view2);
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        switch (holder.getItemViewType()) {
            case ORDER_ITEM:
                VH vh = (VH) holder;
                vh.moreView.setTag(position);
                vh.remindBtn.setTag(position);
                DaifukuanOrderListBean.ListBean order = list.get(position);
                if (!TextUtils.isEmpty(order.getShareUrl())) {
                    vh.remindBtn.setBackgroundResource(R.drawable.btn_circle_round_line_blue);
                    vh.remindBtn.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme));
                    vh.remindBtn.setText("发送给患者");
                    vh.remindBtn.setVisibility(View.VISIBLE);
                } else {
                    vh.remindBtn.setText("提醒支付");
                    switch (list.get(position).getTxStatus()) {
                        case "0":
                            vh.remindBtn.setVisibility(View.GONE);
                            break;
                        case "1":
                            vh.remindBtn.setBackgroundResource(R.drawable.btn_circle_round_line_blue);
                            vh.remindBtn.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme));
                            vh.remindBtn.setVisibility(View.VISIBLE);
                            break;
                        case "2":
                            vh.remindBtn.setBackgroundResource(R.drawable.btn_circle_gray_line);
                            vh.remindBtn.setTextColor(ContextCompat.getColor(context, R.color.br_color_black_999));
                            vh.remindBtn.setVisibility(View.VISIBLE);
                            break;
                        default:
                            break;
                    }
                }

                vh.typeTv.setText(list.get(position).getType());
                vh.timeTv.setText(list.get(position).getDateTime());

                vh.orderIdTv.setText("订单编号：" + list.get(position).getOrderId());
                String patientName = list.get(position).getPatientName();
                if (TextUtils.isEmpty(patientName)) {
                    patientName = list.get(position).getTakerName();
                }
                if (patientName.length() > 4) {
                    patientName = patientName.substring(0, 4) + "…";
                }
                vh.nameTv.setText("患者：" + patientName);
                vh.serviceTv.setText("医技服务费：¥" + list.get(position).getServiceFee());
                vh.feeTv.setText("诊费：¥" + list.get(position).getConsultationFee());

                if (list.get(position).getSmsOrder() && !list.get(position).getBuyUserMobile().isEmpty()) {
                    vh.patientPhone.setVisibility(View.VISIBLE);
                    vh.patientPhone.setText(String.format("患者手机号：%s", list.get(position).getBuyUserMobile()));
                } else {
                    vh.patientPhone.setVisibility(View.GONE);
                }

                break;
            case LAST_ITEM:
                break;
            default:
                break;
        }
    }


    @Override
    public int getItemViewType(int position) {
        if (list.get(position).isLast()) {
            return LAST_ITEM;
        }
        return ORDER_ITEM;
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(), v);
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.type_tv)
        TextView typeTv;
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.remind_btn)
        NoDoubleClickBtn remindBtn;
        @BindView(R.id.order_id_tv)
        TextView orderIdTv;
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.service_tv)
        TextView serviceTv;
        @BindView(R.id.fee_tv)
        TextView feeTv;
        @BindView(R.id.more_view)
        ConstraintLayout moreView;

        @BindView(R.id.patient_phone)
        TextView patientPhone;

        public VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }


}
