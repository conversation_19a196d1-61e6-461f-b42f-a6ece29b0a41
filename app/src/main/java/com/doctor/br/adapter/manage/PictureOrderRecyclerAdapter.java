package com.doctor.br.adapter.manage;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.activity.manage.CameraPrescriptionOrderActitity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.PictureOrderBean;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：拍照药方订单列表适配器
 * 创建人：ShiShaoPo
 * 创建时间：2018/7/13
 */

public class PictureOrderRecyclerAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements View.OnClickListener {
    private final int ORDER_ITEM = 111;//订单条目
    private final int LAST_ITEM = 222;//最后一条

    private Context context;
    private List<PictureOrderBean.DatasBean> list;
    private String orderState;
    private RecyclerItemClickListener recyclerItemClickListener;

    public PictureOrderRecyclerAdapter(Context context, List<PictureOrderBean.DatasBean> list, String orderState, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.orderState = orderState;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case ORDER_ITEM:
                View view = LayoutInflater.from(context).inflate(R.layout.item_picture_order_list, parent, false);//这种方法不会使布局自适应
                Vh holder = new Vh(view);
                if (recyclerItemClickListener != null) {
                    holder.itemView.setOnClickListener(this);
                }
                if (!CameraPrescriptionOrderActitity.PICTURE_WAIT_SEND.equals(orderState)) {
                    holder.serviceTv.setVisibility(View.GONE);
                    holder.feeTv.setVisibility(View.GONE);
                }
                return holder;
            case LAST_ITEM:
                View view2 = LayoutInflater.from(context).inflate(R.layout.item_order_islast, parent, false);//这种方法不会使布局自适应
                return new QuanbuRecyclerAdapter.LastVH(view2);
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        switch (holder.getItemViewType()) {
            case ORDER_ITEM:
                Vh vh = (Vh) holder;
                vh.itemView.setTag(position);

                vh.timeTv.setText(list.get(position).getCreatedTime());

                vh.orderIdTv.setText("订单编号：" + list.get(position).getOrderId());
                String patientName = list.get(position).getPatientName();
                if (patientName.length() > 4) {
                    patientName = patientName.substring(0, 4) + "…";
                }
                vh.nameTv.setText("患者：" + patientName);
                vh.serviceTv.setText("医技服务费：¥" + list.get(position).getMedicalServiceFee());
                vh.feeTv.setText("诊费：¥" + list.get(position).getConsultationFee());
                break;
            case LAST_ITEM:
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (list.get(position).isLast()) {
            return LAST_ITEM;
        }
        return ORDER_ITEM;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class Vh extends RecyclerView.ViewHolder {
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.order_id_tv)
        TextView orderIdTv;
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.service_tv)
        TextView serviceTv;
        @BindView(R.id.fee_tv)
        TextView feeTv;

        Vh(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
