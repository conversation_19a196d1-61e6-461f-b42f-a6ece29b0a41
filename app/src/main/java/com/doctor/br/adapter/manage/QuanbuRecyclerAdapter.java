package com.doctor.br.adapter.manage;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.QuanbuOrderListBean;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：全部订单列表
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/27
 */

public class QuanbuRecyclerAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements View.OnClickListener {
    private final int ORDER_ITEM = 111;//订单item
    private final int LAST_ITEM = 222;//最后一条
    private Context context;
    private List<QuanbuOrderListBean.ListBean> orderList;
    private RecyclerItemClickListener recyclerItemClickListener;

    public QuanbuRecyclerAdapter(Context context, List<QuanbuOrderListBean.ListBean> orderList, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.orderList = orderList;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case ORDER_ITEM:
                View view1 = LayoutInflater.from(context).inflate(R.layout.item_quanbu_recycler, parent, false);//这种方法不会使布局自适应
                VH holder = new VH(view1);
                if (recyclerItemClickListener != null) {
                    holder.itemView.setOnClickListener(this);
                }
                return holder;
            case LAST_ITEM:
                View view2 = LayoutInflater.from(context).inflate(R.layout.item_order_islast, parent, false);//这种方法不会使布局自适应
                return new LastVH(view2);
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        holder.itemView.setTag(position);
        switch (holder.getItemViewType()) {
            case ORDER_ITEM:
                VH vh = (VH) holder;

                vh.typeTv.setText(orderList.get(position).getType());
                vh.timeTv.setText(orderList.get(position).getDateTime());

                vh.orderIdTv.setText("订单编号：" + orderList.get(position).getOrderId());
                String patientName = orderList.get(position).getPatientName();
                if (patientName.length() > 4) {
                    patientName = patientName.substring(0, 4) + "…";
                }
                vh.nameTv.setText("患者：" + patientName);
                vh.serviceTv.setText("医技服务费：¥" + orderList.get(position).getServiceFee());
                vh.feeTv.setText("诊费：¥" + orderList.get(position).getConsultationFee());

                if (orderList.get(position).isSmsOrder() && !orderList.get(position).getBuyUserMobile().isEmpty()){
                    vh.patientPhone.setText(String.format("患者手机号：%s", orderList.get(position).getBuyUserMobile()));
                    vh.patientPhone.setVisibility(View.VISIBLE);
                }else {
                    vh.patientPhone.setVisibility(View.GONE);
                }

                switch (orderList.get(position).getState()) {
                    case "1":
                        vh.orderTypeImg.setImageResource(R.drawable.order_type_yiwancheng);
                        break;
                    case "2":
                        vh.orderTypeImg.setImageResource(R.drawable.order_type_daifukuan);
                        break;
                    case "7":
                        vh.orderTypeImg.setImageResource(R.drawable.order_type_yiguoqi);
                        break;
                    default:
                        break;
                }
                break;
            case LAST_ITEM:
                break;
            default:
                break;
        }
    }


    @Override
    public int getItemViewType(int position) {
        if (orderList.get(position).isLast()) {
            return LAST_ITEM;
        }
        return ORDER_ITEM;
    }

    @Override
    public int getItemCount() {
        return orderList.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.type_tv)
        TextView typeTv;
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.order_id_tv)
        TextView orderIdTv;
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.service_tv)
        TextView serviceTv;
        @BindView(R.id.fee_tv)
        TextView feeTv;
        @BindView(R.id.order_type_img)
        ImageView orderTypeImg;

        @BindView(R.id.patient_phone)
        TextView patientPhone;

        public VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    public static class LastVH extends RecyclerView.ViewHolder {
        @BindView(R.id.tv)
        public TextView tv;

        public LastVH(View itemView) {
            super(itemView);
            ButterKnife.bind(this,itemView);
        }
    }
}
