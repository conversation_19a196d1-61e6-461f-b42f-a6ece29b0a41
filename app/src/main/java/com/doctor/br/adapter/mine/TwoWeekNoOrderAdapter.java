package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentNoPatientDoctorBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：经纪人-异常大夫-连续两周未开方适配器
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/12
 */

public class TwoWeekNoOrderAdapter extends RecyclerView.Adapter<TwoWeekNoOrderAdapter.VH> implements View.OnClickListener {
    private Context context;
    private List<AgentNoPatientDoctorBean.DoctorMessagesBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public TwoWeekNoOrderAdapter(Context context, List<AgentNoPatientDoctorBean.DoctorMessagesBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public TwoWeekNoOrderAdapter.VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(context).inflate(R.layout.item_agent_no_patient, parent, false);
        VH holder = new VH(itemView);
        holder.typeTv.setText("最后开方时间:");
        if (recyclerItemClickListener != null) {
            holder.itemView.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(position);

        GlideUtils.getInstance().loadImage(list.get(position).getHeadImage(), context, holder.headImg, R.drawable.default_head_img);
        holder.doctorNameTv.setText(list.get(position).getName());
        if ("1".equals(list.get(position).getSex())) {
            holder.sexImg.setImageResource(R.drawable.male_small);
        } else {
            holder.sexImg.setImageResource(R.drawable.female_small);
        }
        holder.noPayOrderNumTv.setText(list.get(position).getLastOrderCreateTime());
    }


    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.head_img)
        ShapeImageView headImg;
        @BindView(R.id.doctor_name_tv)
        TextView doctorNameTv;
        @BindView(R.id.sex_img)
        ImageView sexImg;
        @BindView(R.id.type_tv)
        TextView typeTv;
        @BindView(R.id.commit_time_tv)
        TextView noPayOrderNumTv;

        public VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
