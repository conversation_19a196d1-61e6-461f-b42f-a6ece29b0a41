package com.doctor.br.adapter.mine;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：问题反馈gridview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/12
 */

public class ProblemGridAdapter extends BaseAdapter implements View.OnClickListener {

    private Context context;
    private List<String> imgList;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public ProblemGridAdapter(Context context, List<String> imgList, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.imgList = imgList;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }

    @Override
    public int getCount() {
        if (imgList.size() < 5) {
            //如果图片少于5个说明有添加图片item
            return imgList.size() + 1;
        }
        return 5;
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_problem_grid, parent, false);//这种方法不会使布局自适应
            holder = new ViewHolder(convertView);
            if (recyclerItemItemClickListener != null) {
                holder.itemImg.setOnClickListener(this);
            }
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        if (imgList.size() < 5) {
            if (position == imgList.size()) {
                //添加图片的item
                GlideUtils.getInstance().loadImage(R.drawable.qualification_add_img, context, holder.itemImg, R.drawable.qualification_add_img);
            } else {
                //正常图片的item
                GlideUtils.getInstance().loadImage(imgList.get(position), context, holder.itemImg, R.drawable.image_default);
            }
        } else {
            GlideUtils.getInstance().loadImage(imgList.get(position), context, holder.itemImg, R.drawable.image_default);
        }
        holder.itemImg.setTag(R.id.img_url, position);
        return convertView;
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class ViewHolder {
        @BindView(R.id.item_img)
        ImageView itemImg;

        ViewHolder(View itemView) {
            ButterKnife.bind(this, itemView);
        }
    }
}
