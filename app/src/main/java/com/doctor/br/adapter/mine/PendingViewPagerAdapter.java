package com.doctor.br.adapter.mine;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.doctor.br.fragment.mine.BeReceiveFragment;
import com.doctor.br.fragment.mine.BeScanFragment;

/**
 * 类描述：待处理界面viewpager适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/9
 */

public class PendingViewPagerAdapter extends FragmentPagerAdapter {
    private String isDitui;
    private BeScanFragment beScanFragment;

    public PendingViewPagerAdapter(FragmentManager fm, String isDitui) {
        super(fm);
        this.isDitui = isDitui;
    }

    @Override
    public Fragment getItem(int position) {
        if (position == 0) {
            return new BeReceiveFragment();
        }
        if (beScanFragment == null) {
            beScanFragment = new BeScanFragment();
            Bundle bundle = new Bundle();
            bundle.putString(BeScanFragment.IS_DITUI, isDitui);
            beScanFragment.setArguments(bundle);
        }
        return beScanFragment;
    }

    @Override
    public int getCount() {
        return 2;
    }

}
