package com.doctor.br.adapter.manage;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.doctor.br.fragment.manage.orderRecord.AlreadyCompleteFragment;
import com.doctor.br.fragment.manage.orderRecord.AlreadySendFragment;
import com.doctor.br.fragment.manage.orderRecord.WaitPayFragment;
import com.doctor.br.fragment.manage.orderRecord.WaitSendFragment;


/**
 * 类描述：养生建议 订单记录viewPager适配器
 * 创建人：YangYajun
 * 创建时间：2018/8/15
 */

public class OrderRecordViewPagerAdapter extends FragmentPagerAdapter {
    private WaitSendFragment waitSendFragment;// 待发货
    private WaitPayFragment waitPayFragment;// 待支付
    private AlreadySendFragment alreadySendFragment;// 已发货
    private AlreadyCompleteFragment alreadyCompleteFragment;// 已完成

    private int size;

    public OrderRecordViewPagerAdapter(FragmentManager fm, int size) {
        super(fm);
        this.size = size;
    }

    @Override
    public Fragment getItem(int position) {
        switch (position){
            case 0:
                if (waitPayFragment == null){
                    waitPayFragment = new WaitPayFragment();
                }
                return  waitPayFragment;
            case 1:
                if (waitSendFragment == null){
                    waitSendFragment = new WaitSendFragment();
                }
                return  waitSendFragment;
            case 2:
                if (alreadySendFragment == null){
                    alreadySendFragment = new AlreadySendFragment();
                }
                return  alreadySendFragment;
            case 3:
                if (alreadyCompleteFragment == null){
                    alreadyCompleteFragment = new AlreadyCompleteFragment();
                }
                return alreadyCompleteFragment;
            default:
                return null;
        }
    }

    @Override
    public int getCount() {
        return size;
    }
}
