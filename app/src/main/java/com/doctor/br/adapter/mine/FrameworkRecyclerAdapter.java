package com.doctor.br.adapter.mine;

import android.app.Activity;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.activity.mine.agent.framework.FrameworkDispatchActivity;
import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AgentFrameworkBean;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：组织架构左边省列表适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/9
 */

public class FrameworkRecyclerAdapter extends RecyclerView.Adapter<FrameworkRecyclerAdapter.VH> implements View.OnClickListener {
    private Activity activity;
    private List<AgentFrameworkBean.DataBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public FrameworkRecyclerAdapter(Activity activity, List<AgentFrameworkBean.DataBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.activity = activity;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
        if (activity.getClass().getSimpleName().equals(FrameworkDispatchActivity.class.getSimpleName())) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).isFrameSelected()) {
                    list.get(i).setDispatchSelect(true);
                } else {
                    list.get(i).setDispatchSelect(false);
                }
            }
        }
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(activity).inflate(R.layout.item_framework_left_recycler, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(view);
        if (recyclerItemClickListener != null) {
            holder.itemView.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(R.id.img_url, position);

        String provinceName = list.get(position).getAreaName();
        if (provinceName.length() > 4) {
            provinceName = provinceName.substring(0, 4) + "…";
        }
        holder.provinceTv.setText(provinceName);

        boolean isSelect = list.get(position).isFrameSelected();
        if (activity.getClass().getSimpleName().equals(FrameworkDispatchActivity.class.getSimpleName())) {
            isSelect = list.get(position).isDispatchSelect();
        }
        if (isSelect) {
            holder.contentView.setBackgroundColor(ContextCompat.getColor(activity, R.color.br_color_white));
        } else {
            holder.contentView.setBackgroundColor(ContextCompat.getColor(activity, R.color.br_color_background));
        }
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag(R.id.img_url));
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.province_tv)
        TextView provinceTv;
        @BindView(R.id.content_view)
        LinearLayout contentView;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
