package com.doctor.br.adapter.mine;

import androidx.recyclerview.widget.RecyclerView;

import com.doctor.br.bean.TableListBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;
import cn.bingoogolapple.androidcommon.adapter.BGAViewHolderHelper;

/**
 * 类描述：下载recyclerview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/13
 */

public class DownloadRecyclerAdapter extends BGARecyclerViewAdapter<TableListBean.DataBean> {
    public DownloadRecyclerAdapter(RecyclerView recyclerView) {
        super(recyclerView, R.layout.item_download_recycler);
    }

    @Override
    protected void setItemChildListener(BGAViewHolderHelper helper, int viewType) {
        super.setItemChildListener(helper, viewType);
        helper.setItemChildClickListener(R.id.btn_card);
        helper.setItemChildClickListener(R.id.btn_code);
        helper.setItemChildClickListener(R.id.btn_table);
    }

    @Override
    protected void fillData(BGAViewHolderHelper helper, int position, TableListBean.DataBean dataBean) {
        if (dataBean == null) return;
        ShapeImageView ivHead = helper.getView(R.id.siv_head);
        GlideUtils.getInstance().loadImage(dataBean.getUserHeadImgUrl(), mContext, ivHead, R.drawable.default_head_img);
        helper.setText(R.id.tv_name, dataBean.getUserName());
        String title = "其他".equals(dataBean.getUserTitle()) ? "中医师" : dataBean.getUserTitle();
        helper.setText(R.id.tv_title, title);
        helper.setText(R.id.tv_hospital, dataBean.getUserWorkAt());
    }

   /* private Context context;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public DownloadRecyclerAdapter(Context context, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_download_recycler, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(view);
        if (recyclerItemItemClickListener != null) {
            holder.codeBtn.setOnClickListener(this);
            holder.tableBtn.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.codeBtn.setTag(R.id.img_url, position);
        holder.tableBtn.setTag(R.id.img_url, position);


    }

    @Override
    public int getItemCount() {
        return 50;
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.head_img)
        ShapeImageView headImg;
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.title_tv)
        TextView titleTv;
        @BindView(R.id.hospital_tv)
        TextView hospitalTv;
        @BindView(R.id.code_btn)
        Button codeBtn;
        @BindView(R.id.table_btn)
        Button tableBtn;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }*/
}
