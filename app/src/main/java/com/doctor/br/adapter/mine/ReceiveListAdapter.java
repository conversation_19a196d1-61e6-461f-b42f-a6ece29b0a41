package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.bean.AgentTastListBean;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：待接收列表适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/9
 */

public class ReceiveListAdapter extends RecyclerView.Adapter<ReceiveListAdapter.VH> implements View.OnClickListener {
    private Context context;
    private List<AgentTastListBean.TasksBean> list;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public ReceiveListAdapter(Context context, List<AgentTastListBean.TasksBean> list, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_be_receive_list, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(view);
        if (recyclerItemItemClickListener != null) {
            holder.receiveBtn.setOnClickListener(this);
            holder.refuseBtn.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.refuseBtn.setTag(R.id.img_url, position);
        holder.receiveBtn.setTag(R.id.img_url, position);

        String leftName = list.get(position).getIntroduceINTRODUCER();
        holder.leftNameTv.setText(splitString(leftName));
        String centerName = list.get(position).getIntroduceDoctor();
        holder.centerNameTv.setText(splitString(centerName));
        String rightName = list.get(position).getName();
        holder.rightNameTv.setText(splitString(rightName));
        holder.phoneTv.setText(list.get(position).getMobile());
        holder.titleTv.setText(list.get(position).getProfessor());
        holder.timeTv.setText(list.get(position).getRegistTime());
        holder.addressTv.setText(list.get(position).getCode());
        holder.hospitalTv.setText(list.get(position).getHospital());
    }

    /**
     * 当字符串长度超过3位时，多余的显示...
     *
     * @param name 想要显示的字符串
     * @return 处理完成要显示的内容
     */
    private String splitString(String name) {
        if (name == null) {
            return null;
        }
        String resultStr = name;
        if (resultStr.length() > 3) {
            resultStr = resultStr.substring(0, 3) + "…";
        }
        return resultStr;
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.left_name_tv)
        TextView leftNameTv;
        @BindView(R.id.center_name_tv)
        TextView centerNameTv;
        @BindView(R.id.right_name_tv)
        TextView rightNameTv;
        @BindView(R.id.phone_tv)
        TextView phoneTv;
        @BindView(R.id.title_tv)
        TextView titleTv;
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.address_tv)
        TextView addressTv;
        @BindView(R.id.hospital_tv)
        TextView hospitalTv;
        @BindView(R.id.refuse_btn)
        com.doctor.br.view.NoDoubleClickBtn refuseBtn;
        @BindView(R.id.receive_btn)
        com.doctor.br.view.NoDoubleClickBtn receiveBtn;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
