package com.doctor.br.adapter.manage;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.bean.CommonPrescriptionBean;
import com.doctor.br.bean.medical.MedicineDetailMsgBean;
import com.doctor.br.view.stickyListView.StickyListHeadersAdapter;
import com.doctor.yy.R;

import java.util.List;

/**
 * 类描述：药典列表悬浮固定适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/16
 */

public class CommonPrescription1Adapter extends BaseAdapter implements StickyListHeadersAdapter {

    private Context context;
    private List<CommonPrescriptionBean.DatalistBean> list;

    public CommonPrescription1Adapter(Context context,List<CommonPrescriptionBean.DatalistBean> list) {
        this.context = context;
        this.list = list;
    }
    public void setData(List<CommonPrescriptionBean.DatalistBean> list){
        this.list = list;
        notifyDataSetChanged();
    }

    @Override
    public View getHeaderView(int position, View convertView, ViewGroup parent) {
        HeaderViewHolder headerViewHolder;
        if (convertView == null) {
            headerViewHolder = new HeaderViewHolder();
            convertView = LayoutInflater.from(context).inflate(R.layout.item_medicine_list_header, parent, false);//这种方法不会使布局自适应
            headerViewHolder.tvCharacter = (TextView) convertView.findViewById(R.id.header_tv);
            convertView.setTag(headerViewHolder);
        } else {
            headerViewHolder = (HeaderViewHolder) convertView.getTag();
        }
        String firstSpell = list.get(position).getFirstSpell().toUpperCase();
        /*if(TextUtils.isEmpty(firstSpell)){
            firstSpell = "A";
        }*/
        headerViewHolder.tvCharacter.setText(firstSpell);

        return convertView;
    }





    @Override
    public long getHeaderId(int position) {
        if(TextUtils.isEmpty(list.get(position).getFirstSpell())){//解决首字母为空的问题
            return "A".charAt(0);
        }
        return list.get(position).getFirstSpell().charAt(0);
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public Object getItem(int position) {
        return list.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (convertView == null) {
            viewHolder = new ViewHolder();
            convertView = LayoutInflater.from(context).inflate(R.layout.item_common_prescription_bottom, parent, false);//这种方法不会使布局自适应
            viewHolder.ivDetail = (ImageView) convertView.findViewById(R.id.iv_detail);
            viewHolder.tvTitle = (TextView)convertView.findViewById(R.id.tv_title);
            viewHolder.tvContent = (TextView)convertView.findViewById(R.id.tv_content);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        viewHolder.tvTitle.setText(list.get(position).getName());
        viewHolder.tvContent.setText(getMedicationDetail(list.get(position).getList()));
        return convertView;
    }

    public String getMedicationDetail(List<MedicineDetailMsgBean> drugList){
        if(drugList!=null && drugList.size()>0){
            SpannableStringBuilder ssb = new SpannableStringBuilder();
            for(int i=0;i<drugList.size();i++){
                MedicineDetailMsgBean medicineDetailMsgBean = drugList.get(i);
                if(medicineDetailMsgBean!=null){
                    ssb.append(medicineDetailMsgBean.getDrugName()+" ");
                    ssb.append(medicineDetailMsgBean.getDose()+"");
                    ssb.append(medicineDetailMsgBean.getUnit());
                    if(drugList.size()>1 && i!=drugList.size()-1){
                        ssb.append("，");
                    }
                }

            }

            return ssb.toString();
        }else{
            return "";
        }
    }


    private static class HeaderViewHolder {
        TextView tvCharacter;

    }

    private static class ViewHolder {
        ImageView ivDetail;
        TextView tvTitle;
        TextView tvContent;
    }
}
