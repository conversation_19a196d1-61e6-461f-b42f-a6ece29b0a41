package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.activity.mine.agent.AgentActivity;
import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.bean.AgentDispatchBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：调度审核recyclerview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/9
 */

public class DispatchListAdapter extends RecyclerView.Adapter<DispatchListAdapter.VH> implements View.OnClickListener {
    private Context context;
    private List<AgentDispatchBean.DataBean> list;
    private RecyclerItemItemClickListener recyclerItemItemClickListener;

    public DispatchListAdapter(Context context, List<AgentDispatchBean.DataBean> list, RecyclerItemItemClickListener recyclerItemItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemItemClickListener = recyclerItemItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_dispatch_list, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(view);
        if (recyclerItemItemClickListener != null) {
            holder.receiveBtn.setOnClickListener(this);
            holder.refuseBtn.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.receiveBtn.setTag(R.id.img_url, position);
        holder.refuseBtn.setTag(R.id.img_url, position);

        GlideUtils.getInstance().loadImage(list.get(position).getUserHeadImageUrl(), context, holder.headImg, R.drawable.default_head_img);
        holder.nameTv.setText(list.get(position).getTargetUserName());
        holder.phoneTv.setText(list.get(position).getTargetUserMobile());
        holder.beforeInfoTv.setText("原："+getInfoStr(list.get(position).getPreAreaName(), list.get(position).getPreTeamName(), list.get(position).getPreRole()));
        holder.nowInfoTv.setText("调度至："+getInfoStr(list.get(position).getTargetAreaName(), list.get(position).getTargetTeamName(), list.get(position).getTargetRole()));
    }

    //拼接字符串
    private String getInfoStr(String areaName, String teamName, String role) {
        StringBuilder result = new StringBuilder(areaName);
        result.append(" > ");
        if (!TextUtils.isEmpty(teamName)) {
            result.append(teamName).append(" - ");
        }

        if (TextUtils.isEmpty(role)) {
            result.append("团队职员");
            return result.toString();
        }
        switch (role) {
            case AgentActivity.ROLE_PERSON://个人兼职
            case AgentActivity.ROLE_PART://团队兼职
            case AgentActivity.ROLE_MINE://团队直营
            default:
                result.append("团队职员");
                break;
            case AgentActivity.ROLE_PART_TEAM://兼职团队负责人
            case AgentActivity.ROLE_MINE_TEAM://直营团队负责人
                result.append("团队负责人");
                break;
            case AgentActivity.ROLE_PROVINCE://省负责人
                result.append("省负责人");
                break;
        }
        return result.toString();
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.head_img)
        ShapeImageView headImg;
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.phone_tv)
        TextView phoneTv;
        @BindView(R.id.before_info_tv)
        TextView beforeInfoTv;
        @BindView(R.id.now_info_tv)
        TextView nowInfoTv;
        @BindView(R.id.refuse_btn)
        com.doctor.br.view.NoDoubleClickBtn refuseBtn;
        @BindView(R.id.receive_btn)
        com.doctor.br.view.NoDoubleClickBtn receiveBtn;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
