package com.doctor.br.adapter.chatmain;

import android.content.Context;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 医案，交流，用药三个Fragment的ViewPager的适配器
 * @createTime 2017/9/28
 */

public class ChatMainAdapter extends FragmentPagerAdapter {
    private List<Fragment> mFragmentList;
    private Context mContext;


    public ChatMainAdapter(FragmentManager fm, Context mContext, List<Fragment> mFragmentList) {
        super(fm);
        this.mContext = mContext;
        this.mFragmentList = mFragmentList;
    }

    @Override
    public Fragment getItem(int position) {
        return mFragmentList.get(position);
    }

    @Override
    public int getCount() {
        return mFragmentList.size();
    }

    //重写这个方法，将设置每个Tab的标题
    @Override
    public CharSequence getPageTitle(int position) {
        return "患者档案";
    }

}
