package com.doctor.br.adapter.shop;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.bean.shop.CommodityListBean;
import com.doctor.yy.R;

import org.newapp.ones.base.adapter.BaseRecyclerAdapter;
import org.newapp.ones.base.utils.ImageUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * <AUTHOR>
 * @createdDate 2018/8/22
 * @description 商品列表适配器
 */
public class CommodityRecyclerAdapter extends BaseRecyclerAdapter<CommodityListBean.CommodityInfoBean, BaseRecyclerAdapter.BaseRecyclerViewHolder> implements View.OnClickListener {
    public final static String COMMODITY_TYPE_ONSHELF = "onshelf";//已上架
    public final static String COMMODITY_TYPE_SHELFING = "shelfing";//待上架
    public final static String COMMODITY_TYPE_DOWNSHELF = "downshelf";//已下架
    private final int COMMODITY_ITEM = 111;//商品item
    private final int LAST_ITEM = 222;//最后一条
    private Context context;
    private List<CommodityListBean.CommodityInfoBean> list;
    private CommodityOperateViewClickListener operateViewClickListener;
    private String commodityType;//商品类型：已上架、待上架、已下架

    public CommodityRecyclerAdapter(Context context, List<CommodityListBean.CommodityInfoBean> list, String type,
                                    CommodityOperateViewClickListener operateViewClickListener) {
        super(context, list, R.layout.item_medicine_shop);
        this.context = context;
        this.list = list;
        this.commodityType = type;
        this.operateViewClickListener = operateViewClickListener;
    }

    @Override
    public BaseRecyclerAdapter.BaseRecyclerViewHolder onCreateVH(ViewGroup parent, View view, int viewType) {
        switch (viewType) {
            case COMMODITY_ITEM:
                Vh holder = new Vh(view);
                if (operateViewClickListener != null) {
                    holder.contentRl.setOnClickListener(this);
                    holder.rlLeft.setOnClickListener(this);
                    holder.rlRight.setOnClickListener(this);
                }
                return holder;
            case LAST_ITEM:
                View view2 = LayoutInflater.from(context).inflate(R.layout.item_order_islast, parent, false);//这种方法不会使布局自适应
                return new LastVH(view2);
            default:
                break;
        }
        return null;

    }

    @Override
    public void onBindVH(BaseRecyclerViewHolder holder, CommodityListBean.CommodityInfoBean bean, int position) {
        switch (holder.getItemViewType()) {
            case COMMODITY_ITEM:
                Vh vh = (Vh) holder;
                vh.contentRl.setTag(position);
                vh.rlRight.setTag(position);
                vh.rlLeft.setTag(position);

                vh.iconImg.setImageResource(R.mipmap.logo1);

                if (COMMODITY_TYPE_ONSHELF.equals(commodityType)) {
                    vh.iconLeft.setImageResource(R.drawable.icon_sold_out);
                    vh.tvLeft.setText("下架");
                    vh.iconRight.setImageResource(R.drawable.icon_share);
                    vh.tvRight.setText("分享");
                } else if (COMMODITY_TYPE_SHELFING.equals(commodityType)
                        || COMMODITY_TYPE_DOWNSHELF.equals(commodityType)) {
                    vh.iconLeft.setImageResource(R.drawable.icon_delete_blue);
                    vh.tvLeft.setText("删除");
                    vh.iconRight.setImageResource(R.drawable.icon_to_sale);
                    vh.tvRight.setText("上架");
                }
                vh.medicineName.setText(bean.getName().length() > 6 ? bean.getName().substring(0, 6) + "…" : bean.getName());
                vh.tvNumber.setText("销量:" + (TextUtils.isEmpty(bean.getSaleCount()) ? "" : bean.getSaleCount()));
                vh.tvPrice.setText(TextUtils.isEmpty(bean.getPrice()) ? "" : bean.getPrice());
                vh.medicationDesc.setText(TextUtils.isEmpty(bean.getEffect()) ? "" : bean.getEffect());
                mGlideUtils.loadCircleImage(bean.getImg(), context, vh.iconImg, R.mipmap.logo1);
                if (COMMODITY_TYPE_ONSHELF.equals(commodityType)) {
                    vh.iconLeft.setImageResource(R.drawable.icon_sold_out);
                    vh.tvLeft.setText("下架");
                    vh.iconRight.setImageResource(R.drawable.icon_share);
                    vh.tvRight.setText("分享");
                } else if (COMMODITY_TYPE_SHELFING.equals(commodityType)
                        || COMMODITY_TYPE_DOWNSHELF.equals(commodityType)) {
                    vh.iconLeft.setImageResource(R.drawable.icon_delete_blue);
                    vh.tvLeft.setText("删除");
                    vh.iconRight.setImageResource(R.drawable.icon_to_sale);
                    vh.tvRight.setText("上架");
                }
                vh.medicineName.setText(bean.getName().length() > 6 ? bean.getName().substring(0, 6) + "…" : bean.getName());
                vh.tvNumber.setText("销量:" + (TextUtils.isEmpty(bean.getSaleCount()) ? "" : bean.getSaleCount()));
                vh.tvPrice.setText(TextUtils.isEmpty(bean.getPrice()) ? "" : bean.getPrice());
                vh.medicationDesc.setText(TextUtils.isEmpty(bean.getEffect()) ? "" : bean.getEffect());
                mGlideUtils.loadCircleImage(ImageUtils.getMiddleImageUrl(bean.getImg()), context, vh.iconImg, R.mipmap.logo1);
                break;
            case LAST_ITEM:
                LastVH lastVH = (LastVH) holder;
                lastVH.tv.setText("已加载全部数据");
                break;
        }

    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.content_rl:
                operateViewClickListener.itemClick((Integer) v.getTag());
                break;
            case R.id.rl_left:
                operateViewClickListener.onLeftViewClick((Integer) v.getTag());
                break;
            case R.id.rl_right:
                operateViewClickListener.onRightViewClick((Integer) v.getTag());
                break;

        }
    }

    @Override
    public int getItemViewType(int position) {
        if (list.get(position).isLast()) {
            return LAST_ITEM;
        } else {
            return COMMODITY_ITEM;
        }
    }

    public static class Vh extends BaseRecyclerAdapter.BaseRecyclerViewHolder {
        @BindView(R.id.icon_img)
        ShapeImageView iconImg;
        @BindView(R.id.medicine_name)
        TextView medicineName;
        @BindView(R.id.tv_number)
        TextView tvNumber;
        @BindView(R.id.medication_desc)
        TextView medicationDesc;
        @BindView(R.id.tv_price)
        TextView tvPrice;
        @BindView(R.id.content_rl)
        RelativeLayout contentRl;
        @BindView(R.id.icon_left)
        ImageView iconLeft;
        @BindView(R.id.tv_left)
        TextView tvLeft;
        @BindView(R.id.rl_left)
        RelativeLayout rlLeft;
        @BindView(R.id.icon_right)
        ImageView iconRight;
        @BindView(R.id.tv_right)
        TextView tvRight;
        @BindView(R.id.rl_right)
        RelativeLayout rlRight;

        public Vh(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    static class LastVH extends BaseRecyclerAdapter.BaseRecyclerViewHolder {
        @BindView(R.id.tv)
        TextView tv;

        public LastVH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}