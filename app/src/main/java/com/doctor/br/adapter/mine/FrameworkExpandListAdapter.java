package com.doctor.br.adapter.mine;

import android.content.Context;
import android.content.Intent;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.TextAppearanceSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseExpandableListAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.activity.mine.agent.AgentActivity;
import com.doctor.br.activity.mine.agent.framework.FrameworkActivity;
import com.doctor.br.activity.mine.agent.framework.FrameworkDispatchActivity;
import com.doctor.br.bean.AgentFrameworkBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：组织架构右边可展开列表的适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/9
 */

public class FrameworkExpandListAdapter extends BaseExpandableListAdapter implements View.OnClickListener {
    private Context context;
    private List<AgentFrameworkBean.DataBean> provinceList;//省列表
    private List<AgentFrameworkBean.DataBean.TeamListBean> teamList;//团队列表

    private String areaName;//当前省名称
    private String areaCode;//当前省code

    public FrameworkExpandListAdapter(Context context, List<AgentFrameworkBean.DataBean> provinceList, List<AgentFrameworkBean.DataBean.TeamListBean> teamList) {
        this.context = context;
        this.provinceList = provinceList;
        this.teamList = teamList;

    }

    @Override
    public void onClick(View v) {
        int groupPos = (int) v.getTag();
        int childPos = (int) v.getTag(R.id.img_url);

        if (FrameworkActivity.STATUS_DISPATCHING.equals(teamList.get(groupPos).getUserList().get(childPos).getUserStatus())) {
            return;
        }
        FrameworkDispatchActivity.list = provinceList;
        for (AgentFrameworkBean.DataBean dataBean : provinceList) {
            if (dataBean.isFrameSelected()) {
                areaName = dataBean.getAreaName();
                areaCode = dataBean.getAreaCode();
                break;
            }
        }
        Intent intent = new Intent(context, FrameworkDispatchActivity.class);
        intent.putExtra(FrameworkDispatchActivity.USER_ID, teamList.get(groupPos).getUserList().get(childPos).getUserId());
        intent.putExtra(FrameworkDispatchActivity.AREA_NAME, areaName);
        intent.putExtra(FrameworkDispatchActivity.TEAM_TYPE, teamList.get(groupPos).getTeamType());
        intent.putExtra(FrameworkDispatchActivity.TEAM_ID, teamList.get(groupPos).getUserList().get(childPos).getTeamId());
        intent.putExtra(FrameworkDispatchActivity.ROLE, teamList.get(groupPos).getUserList().get(childPos).getUserRole());
        intent.putExtra(FrameworkDispatchActivity.AREA_CODE, areaCode);
        intent.putExtra(FrameworkDispatchActivity.TEAM_NAME, teamList.get(groupPos).getTeamName());
        context.startActivity(intent);
    }

    @Override
    public int getGroupCount() {
        return teamList.size();
    }

    @Override
    public int getChildrenCount(int groupPosition) {
        return teamList.get(groupPosition).getUserList().size();
    }

    @Override
    public Object getGroup(int groupPosition) {
        return teamList.get(groupPosition);
    }

    @Override
    public Object getChild(int groupPosition, int childPosition) {
        return teamList.get(groupPosition).getUserList().get(childPosition);
    }

    @Override
    public long getGroupId(int groupPosition) {
        return groupPosition;
    }

    @Override
    public long getChildId(int groupPosition, int childPosition) {
        return childPosition;
    }

    @Override
    public boolean hasStableIds() {
        return true;
    }

    @Override
    public View getGroupView(int groupPosition, boolean isExpanded, View convertView, ViewGroup parent) {
        HeaderViewHolder headerViewHolder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_framework_right_list_group, parent, false);//这种方法不会使布局自适应
            headerViewHolder = new HeaderViewHolder(convertView);
            convertView.setTag(headerViewHolder);
        } else {
            headerViewHolder = (HeaderViewHolder) convertView.getTag();
        }
        headerViewHolder.groupTitleTv.setText(teamList.get(groupPosition).getTeamName());
        headerViewHolder.teamTypeTv.setVisibility(View.VISIBLE);
        if (AgentActivity.TEAM_TYPE_MINE.equals(teamList.get(groupPosition).getTeamType())) {
            headerViewHolder.teamTypeTv.setBackgroundResource(R.drawable.corner_76b3ff);
            headerViewHolder.teamTypeTv.setText("直营");
        } else if (AgentActivity.TEAM_TYPE_PART.equals(teamList.get(groupPosition).getTeamType())) {
            headerViewHolder.teamTypeTv.setBackgroundResource(R.drawable.corner_ffb076);
            headerViewHolder.teamTypeTv.setText("兼职");
        }else{
            headerViewHolder.teamTypeTv.setVisibility(View.INVISIBLE);
        }
//        else if (AgentActivity.TEAM_TYPE_PERSON.equals(teamList.get(groupPosition).getTeamType())) {
//            headerViewHolder.teamTypeTv.setBackgroundResource(R.drawable.corner_00be82);
//            headerViewHolder.teamTypeTv.setText("个人");
//        }
        if (isExpanded) {
            headerViewHolder.arrowImg.setRotation(180);
        } else {
            headerViewHolder.arrowImg.setRotation(0);
        }
        return convertView;
    }

    @Override
    public View getChildView(int groupPosition, int childPosition, boolean isLastChild, View convertView, ViewGroup parent) {
        ItemViewHolder itemViewHolder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_framework_right_list_item, parent, false);//这种方法不会使布局自适应
            itemViewHolder = new ItemViewHolder(convertView);
            itemViewHolder.dispatchBtn.setOnClickListener(this);
            convertView.setTag(itemViewHolder);
        } else {
            itemViewHolder = (ItemViewHolder) convertView.getTag();
        }
        itemViewHolder.dispatchBtn.setTag(groupPosition);
        itemViewHolder.dispatchBtn.setTag(R.id.img_url, childPosition);

        String name = teamList.get(groupPosition).getUserList().get(childPosition).getUserName();
        if (name.length() > 3) {
            name = name.substring(0, 3) + "…";
        }
        String role = "";
        switch (teamList.get(groupPosition).getUserList().get(childPosition).getUserRole()) {
            case AgentActivity.ROLE_PART_TEAM://兼职团队负责人
            case AgentActivity.ROLE_MINE_TEAM://直营团队负责人
                role = "（团队负责人）";
                break;
            case AgentActivity.ROLE_PROVINCE://省负责人
                role = "（省负责人）";
                break;
            default:
                break;
        }
        SpannableString spannableString = new SpannableString(name + role);
        TextAppearanceSpan textAppearanceSpan = new TextAppearanceSpan(context, R.style.FrameworkActivityTitleStyle);
        spannableString.setSpan(textAppearanceSpan, name.length(), spannableString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        itemViewHolder.nameTv.setText(spannableString);

        itemViewHolder.phoneTv.setText(teamList.get(groupPosition).getUserList().get(childPosition).getUserMobile());
        GlideUtils.getInstance().loadImage(teamList.get(groupPosition).getUserList().get(childPosition).getUserHeadImageUrl(), context, itemViewHolder.headImg, R.drawable.default_head_img);
        itemViewHolder.addressTv.setText(teamList.get(groupPosition).getUserList().get(childPosition).getUserAreaName());
        if (FrameworkActivity.isShow) {
            if (FrameworkActivity.STATUS_DISPATCH.equals(teamList.get(groupPosition).getUserList().get(childPosition).getUserStatus())) {
                itemViewHolder.dispatchBtn.setBackgroundResource(R.drawable.framework_dispatch_img);
            } else if (FrameworkActivity.STATUS_DISPATCHING.equals(teamList.get(groupPosition).getUserList().get(childPosition).getUserStatus())) {
                itemViewHolder.dispatchBtn.setBackgroundResource(R.drawable.framework_no_dispatch_img);
            }
            itemViewHolder.dispatchBtn.setVisibility(View.VISIBLE);
        } else {
            itemViewHolder.dispatchBtn.setVisibility(View.GONE);
        }
        return convertView;
    }

    @Override
    public boolean isChildSelectable(int groupPosition, int childPosition) {
        return true;
    }

    static class HeaderViewHolder {
        @BindView(R.id.group_title_tv)
        TextView groupTitleTv;
        @BindView(R.id.team_type_tv)
        TextView teamTypeTv;
        @BindView(R.id.arrow_img)
        ImageView arrowImg;

        HeaderViewHolder(View view) {
            ButterKnife.bind(this, view);
        }
    }

    public static class ItemViewHolder {
        @BindView(R.id.container_linear)
        RelativeLayout containerView;
        @BindView(R.id.head_img)
        public ShapeImageView headImg;
        @BindView(R.id.name_tv)
        public TextView nameTv;
        @BindView(R.id.address_tv)
        public TextView addressTv;
        @BindView(R.id.phone_tv)
        public TextView phoneTv;
        @BindView(R.id.dispatch_btn)
        com.doctor.br.view.NoDoubleClickBtn dispatchBtn;

        ItemViewHolder(View view) {
            ButterKnife.bind(this, view);
        }
    }
}
