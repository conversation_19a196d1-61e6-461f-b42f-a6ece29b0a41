package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.activity.mine.agent.AgentActivity;
import com.doctor.br.activity.mine.agent.framework.FrameworkDispatchActivity;
import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.bean.AgentFrameworkBean;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：经纪人组织架构调度界面右侧recyclerview的适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/16
 */

public class AgentFrameworkDispatchRecyclerAdapter extends RecyclerView.Adapter implements View.OnClickListener {
    private final int HEADER = 111;
    private final int ITEM = 222;


    private Context context;
    private List<AgentFrameworkBean.DataBean.TeamListBean> list;
    private RecyclerItemItemClickListener recyclerItemClickListener;

    public AgentFrameworkDispatchRecyclerAdapter(Context context, List<AgentFrameworkBean.DataBean.TeamListBean> list, RecyclerItemItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case HEADER:
                View headerView = LayoutInflater.from(context).inflate(R.layout.item_framework_dispatch_right_recycler_header, parent, false);//这种方法不会使布局自适应
                return new HeaderVH(headerView);
            case ITEM:
                View itemView = LayoutInflater.from(context).inflate(R.layout.item_framework_dispatch_right_recycler, parent, false);//这种方法不会使布局自适应
                VH holder = new VH(itemView);
                if (recyclerItemClickListener != null) {
                    holder.itemView.setOnClickListener(this);
                }
                return holder;
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        switch (holder.getItemViewType()) {
            case HEADER:
                HeaderVH headerVH = (HeaderVH) holder;
                headerVH.headerTv.setText(list.get(position).getTeamName());
                if (FrameworkDispatchActivity.SELECT_ROLE.equals(list.get(position).getTeamName())) {
                    headerVH.topView.setVisibility(View.VISIBLE);
                } else {
                    headerVH.topView.setVisibility(View.GONE);
                }
                break;
            case ITEM:
                VH itemVH = (VH) holder;
                itemVH.itemView.setTag(R.id.img_url, position);

                itemVH.nameTv.setText(list.get(position).getTeamName());
                //设置选中状态
                if (list.get(position).isSelected()) {
                    itemVH.nameTv.setBackgroundResource(R.drawable.btn_circle_theme);
                    itemVH.nameTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_white));
                } else {
                    itemVH.nameTv.setBackgroundResource(R.drawable.btn_circle_gray_line);
                    itemVH.nameTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_title));
                }
                //设置右上角角标
                itemVH.teamTypeTv.setVisibility(View.VISIBLE);
                if (AgentActivity.TEAM_TYPE_MINE.equals(list.get(position).getTeamType())) {
                    itemVH.teamTypeTv.setBackgroundResource(R.drawable.corner_76b3ff);
                    itemVH.teamTypeTv.setText("直");
                } else if (AgentActivity.TEAM_TYPE_PART.equals(list.get(position).getTeamType())) {
                    itemVH.teamTypeTv.setBackgroundResource(R.drawable.corner_ffb076);
                    itemVH.teamTypeTv.setText("兼");
                } else {
                    itemVH.teamTypeTv.setVisibility(View.INVISIBLE);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public int getItemViewType(int position) {
        if (FrameworkDispatchActivity.HEADER_ID.equals(list.get(position).getTeamId())) {
            return HEADER;
        }
        return ITEM;
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemViewClick((Integer) v.getTag(R.id.img_url), v);
    }

    static class HeaderVH extends RecyclerView.ViewHolder {
        @BindView(R.id.top_view)
        View topView;
        @BindView(R.id.header_tv)
        TextView headerTv;

        HeaderVH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.team_type_tv)
        TextView teamTypeTv;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
