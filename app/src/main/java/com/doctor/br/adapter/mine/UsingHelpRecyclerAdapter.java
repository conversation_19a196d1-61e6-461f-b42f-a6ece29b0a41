package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.UsingHelpBean;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：使用帮助常见问题recyclerview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/14
 */

public class UsingHelpRecyclerAdapter extends RecyclerView.Adapter<UsingHelpRecyclerAdapter.VH> implements View.OnClickListener {


    private Context context;
    private List<UsingHelpBean.ResultListBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public UsingHelpRecyclerAdapter(Context context, List<UsingHelpBean.ResultListBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_using_help_recycler, parent, false);//这种方法不会使布局自适应
        VH holder = new VH(view);
        if (recyclerItemClickListener != null) {
            holder.itemView.setOnClickListener(this);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(R.id.img_url, position);

        if (position == 0) {
            holder.lineView.setVisibility(View.GONE);
        } else {
            holder.lineView.setVisibility(View.VISIBLE);
        }
        holder.numberTv.setText(position + 1 + ".");
        holder.questionTv.setText(list.get(position).getQue());
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag(R.id.img_url));
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.number_tv)
        TextView numberTv;
        @BindView(R.id.question_tv)
        TextView questionTv;
        @BindView(R.id.line)
        View lineView;

        VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
