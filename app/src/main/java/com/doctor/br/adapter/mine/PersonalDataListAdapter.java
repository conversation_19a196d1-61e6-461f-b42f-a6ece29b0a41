package com.doctor.br.adapter.mine;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.bean.DoctorInfoBean;
import com.doctor.yy.R;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：个人中心下面的listview适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/18
 */

public class PersonalDataListAdapter extends BaseAdapter {

    private Context context;
    private DoctorInfoBean doctorInfoBean;

    public PersonalDataListAdapter(Context context, DoctorInfoBean doctorInfoBean) {
        this.context = context;
        this.doctorInfoBean = doctorInfoBean;
    }

    @Override
    public int getCount() {
        return 8;
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_personal_date_list, parent, false);//这种方法不会使布局自适应
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        holder.supTitleTv.setVisibility(View.GONE);
        holder.bottomLine.setVisibility(View.VISIBLE);
        holder.bottomView.setVisibility(View.GONE);
        switch (position) {
            case 0:
                holder.titleTv.setText("姓名");
                holder.valueTv.setHint("请输入您的姓名");
                if (doctorInfoBean != null) {
                    holder.valueTv.setText(doctorInfoBean.getName());
                }
                break;
            case 1:
                holder.titleTv.setText("性别");
                holder.valueTv.setHint("请选择您的性别");
                if (doctorInfoBean != null) {
                    if ("1".equals(doctorInfoBean.getSex())) {
                        holder.valueTv.setText("男");
                    } else if ("2".equals(doctorInfoBean.getSex())) {
                        holder.valueTv.setText("女");
                    } else {
                        holder.valueTv.setText("");
                    }
                }
                break;
            case 2:
                holder.titleTv.setText("身份证号");
                holder.valueTv.setHint("请输入您的身份证号");
                if (doctorInfoBean != null) {
                    holder.valueTv.setText(doctorInfoBean.getPaperNo());
                }
                holder.supTitleTv.setVisibility(View.VISIBLE);
                holder.bottomLine.setVisibility(View.GONE);
                holder.bottomView.setVisibility(View.VISIBLE);
                break;
            case 3:
                holder.titleTv.setText("区域");
                holder.valueTv.setHint("请选择您所在的区域");
                if (doctorInfoBean != null) {
                    holder.valueTv.setText(doctorInfoBean.getAddress());
                }
                break;
            case 4:
                holder.titleTv.setText("就职机构");
                holder.valueTv.setHint("请输入您所在的就职机构");
                if (doctorInfoBean != null) {
                    holder.valueTv.setText(doctorInfoBean.getHospitalName());
                }
                break;
            case 5:
                holder.titleTv.setText("职称");
                holder.valueTv.setHint("请选择您的职称");
                if (doctorInfoBean != null) {
                    holder.valueTv.setText(doctorInfoBean.getTitle());
                }
                break;
            case 6:
                holder.titleTv.setText("擅长");
                holder.valueTv.setHint("请输入您擅长的领域");
                if (doctorInfoBean != null) {
                    holder.valueTv.setText(doctorInfoBean.getGoodAt());
                }
                break;
            case 7:
                holder.titleTv.setText("简介");
                holder.valueTv.setHint("请输入您的简介");
                if (doctorInfoBean != null) {
                    holder.valueTv.setText(doctorInfoBean.getDesc());
                }
                holder.bottomLine.setVisibility(View.GONE);
                break;
            default:
                break;
        }

        return convertView;
    }

    static class ViewHolder {
        @BindView(R.id.title_tv)
        TextView titleTv;
        @BindView(R.id.sup_title_tv)
        TextView supTitleTv;
        @BindView(R.id.arrow_img)
        ImageView arrowImg;
        @BindView(R.id.value_tv)
        TextView valueTv;
        @BindView(R.id.bottom_line)
        View bottomLine;
        @BindView(R.id.bottom_view)
        View bottomView;

        ViewHolder(View view) {
            ButterKnife.bind(this, view);
        }
    }
}
