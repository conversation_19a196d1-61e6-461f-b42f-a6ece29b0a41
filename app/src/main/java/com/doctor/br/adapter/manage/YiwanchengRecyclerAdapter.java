package com.doctor.br.adapter.manage;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.YiwanchengOrderListBean;
import com.doctor.br.utils.DecimalUtils;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：已完成订单列表适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/25
 */

public class YiwanchengRecyclerAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements View.OnClickListener {
    private final int MONTH = 111;//月统计
    private final int ORDER = 222;//订单
    private final int LAST_ITEM = 444;//最后一条

    private Context context;
    private List<YiwanchengOrderListBean.ListBean> orderList;
    private RecyclerItemClickListener recyclerItemClickListener;
    private OnLogisticsQueryListener logisticsQueryListener;

    /**
     * 物流查询监听器
     */
    public interface OnLogisticsQueryListener {
        void onLogisticsQuery(String orderId);
    }

    /**
     * 设置物流查询监听器
     * @param listener 物流查询监听器
     */
    public void setOnLogisticsQueryListener(OnLogisticsQueryListener listener) {
        this.logisticsQueryListener = listener;
    }

    public YiwanchengRecyclerAdapter(Context context, List<YiwanchengOrderListBean.ListBean> orderList, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.orderList = orderList;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case MONTH:
                View view1 = LayoutInflater.from(context).inflate(R.layout.item_yiwancheng_recycler1, parent, false);//这种方法不会使布局自适应
                return new VH1(view1);
            case ORDER:
                View view2 = LayoutInflater.from(context).inflate(R.layout.item_yiwancheng_recycler2, parent, false);//这种方法不会使布局自适应
                VH2 holder2 = new VH2(view2);
                if (recyclerItemClickListener != null) {
                    holder2.itemView.setOnClickListener(this);
                }
                return holder2;
            case LAST_ITEM:
                View view4 = LayoutInflater.from(context).inflate(R.layout.item_order_islast, parent, false);//这种方法不会使布局自适应
                return new QuanbuRecyclerAdapter.LastVH(view4);
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        switch (holder.getItemViewType()) {
            case MONTH:
                VH1 vh1 = (VH1) holder;
                vh1.hintTv.setVisibility(View.GONE);
                if ("1".equals(orderList.get(position).getDataType())) {
                    //本月
                    vh1.rectImg.setImageResource(R.drawable.order_blue_react);
                    vh1.monthTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme_text));
                    int compareResult = DecimalUtils.compareTo(orderList.get(position).getOrderBalance(), "0");
                    if (compareResult > 0) {
                        //还没有达到奖励要求，显示提示
                        vh1.hintTv.setVisibility(View.VISIBLE);
                    }
                    vh1.orderCountTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme));
                    vh1.standardMoneyTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme));
                    vh1.consultationFeeTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme));
                    vh1.medicalServiceFeeTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme));
                } else {
                    //之前月份
                    vh1.rectImg.setImageResource(R.drawable.order_gray_react);
                    vh1.monthTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_et_hint));
                    vh1.orderCountTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme_text));
                    vh1.standardMoneyTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme_text));
                    vh1.consultationFeeTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme_text));
                    vh1.medicalServiceFeeTv.setTextColor(ContextCompat.getColor(context, R.color.br_color_theme_text));
                }
                vh1.monthTv.setText(orderList.get(position).getMonth2());
                vh1.hintTv.setText("(距离平台额外奖励还差¥" + orderList.get(position).getOrderBalance() + ")");

                vh1.orderCountTv.setText(String.valueOf(orderList.get(position).getOrderCount()));
                vh1.standardMoneyTv.setText(orderList.get(position).getStandardDrugPrice());
                vh1.consultationFeeTv.setText(orderList.get(position).getConsultationFee());
                vh1.medicalServiceFeeTv.setText(orderList.get(position).getMedicalServiceFee());
                break;
            case ORDER:
                holder.itemView.setTag(position);
                VH2 vh2 = (VH2) holder;

                vh2.typeTv.setText(orderList.get(position).getType());
                vh2.timeTv.setText(orderList.get(position).getDateTime());

                vh2.orderIdTv.setText("订单编号：" + orderList.get(position).getOrderId());
                String patientName = orderList.get(position).getPatientName();
                if (patientName.length() > 4) {
                    patientName = patientName.substring(0, 4) + "…";
                }
                vh2.nameTv.setText("患者：" + patientName);
                vh2.serviceTv.setText("医技服务费：¥" + orderList.get(position).getServiceFee());
                vh2.feeTv.setText("诊费：¥" + orderList.get(position).getConsultationFee());

                if (orderList.get(position).isSmsOrder() && !orderList.get(position).getBuyUserMobile().isEmpty()){
                    vh2.patientPhone.setText(String.format("患者手机号：%s", orderList.get(position).getBuyUserMobile()));
                    vh2.patientPhone.setVisibility(View.VISIBLE);
                }else {
                    vh2.patientPhone.setVisibility(View.GONE);
                }

                // 设置物流查询按钮点击事件
                final int pos = position;
                vh2.logisticsQueryBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (logisticsQueryListener != null) {
                            logisticsQueryListener.onLogisticsQuery(orderList.get(pos).getOrderId());
                        }
                    }
                });

                break;
            case LAST_ITEM:
                //显示出来就行了
                break;
            default:
                break;
        }
    }


    @Override
    public int getItemCount() {
        return orderList.size();
    }

    @Override
    public int getItemViewType(int position) {
        if (orderList.get(position).isLast()) {
            return LAST_ITEM;
        }
        switch (orderList.get(position).getDataType()) {
            case "0":
                return ORDER;
            case "1"://本月
            case "2"://之前月
                return MONTH;
            default:
                break;
        }
        return ORDER;
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }


    static class VH1 extends RecyclerView.ViewHolder {
        @BindView(R.id.rect_img)
        ImageView rectImg;
        @BindView(R.id.month_tv)
        TextView monthTv;
        @BindView(R.id.hint_tv)
        TextView hintTv;
        @BindView(R.id.order_count_tv)
        TextView orderCountTv;
        @BindView(R.id.standard_money_tv)
        TextView standardMoneyTv;
        @BindView(R.id.consultation_fee_tv)
        TextView consultationFeeTv;
        @BindView(R.id.medical_service_fee_tv)
        TextView medicalServiceFeeTv;

        public VH1(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    static class VH2 extends RecyclerView.ViewHolder {
        @BindView(R.id.type_tv)
        TextView typeTv;
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.logistics_query_btn)
        TextView logisticsQueryBtn;
        @BindView(R.id.order_id_tv)
        TextView orderIdTv;
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.service_tv)
        TextView serviceTv;
        @BindView(R.id.fee_tv)
        TextView feeTv;

        @BindView(R.id.patient_phone)
        TextView patientPhone;

        public VH2(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
