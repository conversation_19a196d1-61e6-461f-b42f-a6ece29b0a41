package com.doctor.br.adapter.manage;

import androidx.recyclerview.widget.RecyclerView;

import com.doctor.br.bean.VisitingSettingBean;
import com.doctor.br.view.VisitingTableView;
import com.doctor.yy.R;

import cn.bingoogolapple.androidcommon.adapter.BGARecyclerViewAdapter;
import cn.bingoogolapple.androidcommon.adapter.BGAViewHolderHelper;

/**
 * Created by 韩瑞峰 on 2017/11/15.
 * 出诊设置的adapter
 */

public class VisitingSettingAdapter extends BGARecyclerViewAdapter<VisitingSettingBean.DataBean> {

    public VisitingSettingAdapter(RecyclerView recyclerView) {
        super(recyclerView, R.layout.item_visiting_setting);
    }

    @Override
    protected void setItemChildListener(BGAViewHolderHelper helper, int viewType) {
        super.setItemChildListener(helper, viewType);
        helper.setItemChildClickListener(R.id.rl_edit);
    }

    @Override
    protected void fillData(BGAViewHolderHelper helper, int position, VisitingSettingBean.DataBean dataBean) {
        if(dataBean == null) return;
        helper.setText(R.id.tv_institution_name,dataBean.getHospitalName());
        helper.setText(R.id.tv_institution_position,"地址："+dataBean.getHospitalAreaName()+dataBean.getHospitalAddress());
        VisitingTableView visitingTableView = helper.getView(R.id.vtv);
        visitingTableView.setWorkPlan(dataBean.getWorkPlain());
        visitingTableView.setEditEnable(false);//设置不可标记
    }
}
