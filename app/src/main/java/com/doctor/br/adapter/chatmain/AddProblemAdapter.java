package com.doctor.br.adapter.chatmain;

import android.content.Context;
import android.widget.TextView;

import com.doctor.br.bean.CommonProblem;
import com.doctor.yy.R;

import org.newapp.ones.base.adapter.BaseAdapter;
import org.newapp.ones.base.adapter.ViewHolder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 添加定制问诊单页面中的问题适配器
 * @createTime 2017/12/7
 */

public class AddProblemAdapter extends BaseAdapter<CommonProblem.DataBean> {

    private List<CommonProblem.DataBean> list;
    private Context mContext;

    public AddProblemAdapter(Context context, List<CommonProblem.DataBean> list, int layoutResId) {
        super(context, list, layoutResId);
        this.mContext = context;
        this.list = list;
    }

    @Override
    public void setViewData(ViewHolder holder, int position) {
        TextView indexTv = (TextView) holder.findViewById(R.id.index_tv);
        TextView contentTv = (TextView) holder.findViewById(R.id.content_tv);
        indexTv.setText((position + 1) + "");
        CommonProblem.DataBean dataBean = list.get(position);
        contentTv.setText(dataBean.getContent() + "");
    }
}
