package com.doctor.br.adapter.manage;

import android.content.Context;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.MedicationOrderRrcordListBean;
import com.doctor.yy.R;

import org.newapp.ones.base.utils.ImageUtils;
import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ShapeImageView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：药铺待支付列表适配器
 * 创建人：YangYajun
 * 创建时间：2018/8/16
 */

public class WaitPayRecyclerAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements View.OnClickListener {

    private final int ORDER_ITEM = 111;//订单item
    private final int LAST_ITEM = 222;//最后一条

    private Context context;
    private List<MedicationOrderRrcordListBean.DatasBean> list;
    private RecyclerItemClickListener recyclerItemClickListener;

    public WaitPayRecyclerAdapter(Context context, List<MedicationOrderRrcordListBean.DatasBean> list, RecyclerItemClickListener recyclerItemClickListener) {
        this.context = context;
        this.list = list;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType){
            case ORDER_ITEM:
                View view = LayoutInflater.from(context).inflate(R.layout.item_order_list, parent, false);//这种方法不会使布局自适应
                Vh holder = new Vh(view);
                if (recyclerItemClickListener != null) {
                    holder.itemView.setOnClickListener(this);
                }
                return holder;
            case LAST_ITEM:
                View view2 = LayoutInflater.from(context).inflate(R.layout.item_order_islast, parent, false);//这种方法不会使布局自适应
                return new QuanbuRecyclerAdapter.LastVH(view2);
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        switch (holder.getItemViewType()){
            case ORDER_ITEM:
                Vh vh = (Vh) holder;
                vh.itemView.setTag(position);

                vh.orderNum.setText("订单编号：" + (TextUtils.isEmpty(list.get(position).getOrderId()) ? "" : list.get(position).getOrderId()));
                vh.timeTv.setText(TextUtils.isEmpty(list.get(position).getOrderTime()) ? "" : list.get(position).getOrderTime());
                vh.medicationName.setText(TextUtils.isEmpty(list.get(position).getYspProductName()) ? "" :
                        (list.get(position).getYspProductName().length() > 6 ? list.get(position).getYspProductName().substring(0, 6) + "…" : list.get(position).getYspProductName()));
                vh.medicationDesc.setText(TextUtils.isEmpty(list.get(position).getYspEffect()) ? "" : list.get(position).getYspEffect());
                GlideUtils.getInstance().loadImage(ImageUtils.getMiddleImageUrl(list.get(position).getYspProductImg()), context, vh.headImg, R.drawable.icon_img_load_error);
                vh.tvPrices.setText(TextUtils.isEmpty(list.get(position).getYspUnitPrice()) ? "" : list.get(position).getYspUnitPrice());
                vh.tvNumber.setText(TextUtils.isEmpty(list.get(position).getSaleProductCount()) ? "" : list.get(position).getSaleProductCount());
                vh.totalPrices.setText(TextUtils.isEmpty(list.get(position).getAmount()) ? "" : list.get(position).getAmount());
                break;
            case LAST_ITEM:
                QuanbuRecyclerAdapter.LastVH lastVH = (QuanbuRecyclerAdapter.LastVH) holder;
                lastVH.tv.setText("已加载全部数据");
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (list.get(position).isLast()) {
            return LAST_ITEM;
        }
        return ORDER_ITEM;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class Vh extends RecyclerView.ViewHolder {

        @BindView(R.id.order_num)
        TextView orderNum;
        @BindView(R.id.time_tv)
        TextView timeTv;
        @BindView(R.id.head_img)
        ShapeImageView headImg;
        @BindView(R.id.medication_name)
        TextView medicationName;
        @BindView(R.id.medication_desc)
        TextView medicationDesc;
        @BindView(R.id.tv_prices)
        TextView tvPrices;
        @BindView(R.id.tv_number)
        TextView tvNumber;
        @BindView(R.id.total_prices)
        TextView totalPrices;
        @BindView(R.id.cl_magin)
        ConstraintLayout clMargin;
        public Vh(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
