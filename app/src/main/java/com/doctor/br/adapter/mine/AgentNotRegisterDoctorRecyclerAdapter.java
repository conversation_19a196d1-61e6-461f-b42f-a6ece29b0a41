package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.doctor.br.bean.NotRegisterDoctorBean;
import com.doctor.yy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 类描述：经纪人-已邀请未注册大夫列表适配器
 * 创建人：ShiShaoPo
 * 创建时间：2018/3/7
 */

public class AgentNotRegisterDoctorRecyclerAdapter extends RecyclerView.Adapter<AgentNotRegisterDoctorRecyclerAdapter.VH> {

    private Context context;
    private List<NotRegisterDoctorBean.DoctorMessagesBean> doctorList;

    public AgentNotRegisterDoctorRecyclerAdapter(Context context, List<NotRegisterDoctorBean.DoctorMessagesBean> doctorList) {
        this.context = context;
        this.doctorList = doctorList;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(context).inflate(R.layout.item_not_register_doctor, parent, false);//这种方法不会使布局自适应
        return new VH(itemView);
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {

        holder.nameTv.setText(doctorList.get(position).getName());
        holder.phoneTv.setText(doctorList.get(position).getMobile());

        holder.timeTv.setText(doctorList.get(position).getIntroducerTime());
    }

    @Override
    public int getItemCount() {
        return doctorList.size();
    }

    static class VH extends RecyclerView.ViewHolder {
        @BindView(R.id.name_tv)
        TextView nameTv;
        @BindView(R.id.phone_tv)
        TextView phoneTv;
        @BindView(R.id.time_tv)
        TextView timeTv;

        public VH(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}
