package com.doctor.br.adapter.mine;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.adapter.RecyclerItemClickListener;
import com.doctor.br.bean.AchievementBean;
import com.doctor.yy.R;

/**
 * 类描述：业绩查询适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/9 11:08
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/9 11:08
 */

public class AchievementRecyclerAdapter extends RecyclerView.Adapter<AchievementRecyclerAdapter.VH> implements View.OnClickListener {
    private Context context;
    private AchievementBean achievementBean;
    private RecyclerItemClickListener recyclerItemClickListener;

    private int[] imgs = {R.drawable.achievement_img1, R.drawable.achievement_img2,
            R.drawable.achievement_img3, R.drawable.achievement_img4,
            R.drawable.achievement_img5, R.drawable.achievement_img6};

    private String[] names = {"患者数量", "药方数量", "配比收入",
            "均客单价", "订单金额", "业绩汇总"};

    public AchievementRecyclerAdapter(Context context, AchievementBean achievementBean, RecyclerItemClickListener recyclerItemClickListener) {
        this.achievementBean = achievementBean;
        this.context = context;
        this.recyclerItemClickListener = recyclerItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_performance_enquiry_recycler, parent, false);//这种方法不会使布局自适应
        if (recyclerItemClickListener != null) {
            view.setOnClickListener(this);
        }
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        holder.itemView.setTag(position);

        holder.groupImg.setImageResource(imgs[position]);
        holder.groupTitleTv.setText(names[position]);
        if (achievementBean == null) {
            holder.groupNumberTv.setVisibility(View.INVISIBLE);
            return;
        }
        holder.groupNumberTv.setVisibility(View.VISIBLE);
        switch (position) {
            case 0:
                holder.groupNumberTv.setText(achievementBean.getPatientNum());
                break;
            case 1:
                holder.groupNumberTv.setText(achievementBean.getOrderNum());
                break;
            case 2:
                holder.groupNumberTv.setText(achievementBean.getPbincome());
                break;
            case 3:
                holder.groupNumberTv.setText(achievementBean.getJkunit());
                break;
            case 4:
                holder.groupNumberTv.setText(achievementBean.getOrderPrice());
                break;
            case 5:
                holder.groupNumberTv.setVisibility(View.INVISIBLE);
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemCount() {
        return 6;
    }

    @Override
    public void onClick(View v) {
        recyclerItemClickListener.itemClick((Integer) v.getTag());
    }

    static class VH extends RecyclerView.ViewHolder {
        ImageView groupImg;
        TextView groupTitleTv;
        TextView groupNumberTv;

        public VH(View itemView) {
            super(itemView);
            groupImg = (ImageView) itemView.findViewById(R.id.group_img);
            groupTitleTv = (TextView) itemView.findViewById(R.id.group_title_tv);
            groupNumberTv = (TextView) itemView.findViewById(R.id.group_number_tv);
        }
    }
}
