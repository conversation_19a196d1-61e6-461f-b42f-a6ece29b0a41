package com.doctor.br.fragment.mine;

import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.doctor.br.bean.event.ModifyWithdrawPasswordRefreshStateEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.fragment.BaseFragment;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.MD5Utils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 类描述：短信验证码方式修改提现密码
 * 创建人：Assistant
 * 创建时间：2024/12/15
 */

public class SmsModifyWithdrawPasswordFragment extends BaseFragment {
    @BindView(R.id.phone_tv)
    TextView phoneTv;
    @BindView(R.id.sms_code_et)
    EditText smsCodeEt;
    @BindView(R.id.get_code_btn)
    TextView getCodeBtn;
    @BindView(R.id.new_password_et)
    EditText newPasswordEt;
    @BindView(R.id.confirm_password_et)
    EditText confirmPasswordEt;
    @BindView(R.id.submit_btn)
    TextView submitBtn;

    private CountDownTimer countDownTimer;
    private String phone;
    private RequestCallBack getCodeCallBack;
    private RequestCallBack modifyPasswordCallBack;

    @Override
    protected int getLayoutId() {
        EventBusUtils.register(this);
        return R.layout.fragment_sms_modify_withdraw_password;
    }

    @Override
    protected void init() {
        super.init();
        
        // 获取手机号
        phone = SharedPreferenceUtils.getString(getActivity(), PublicParams.USER_TEL);
        if (!TextUtils.isEmpty(phone) && phone.length() == 11) {
            phoneTv.setText(phone);
        }

        // 初始化按钮状态 - 确认密码按钮始终可点击
        initButtonState();
        
        // 添加文本监听器
        addTextWatchers();
    }

    /**
     * 初始化按钮状态
     */
    private void initButtonState() {
        // 确认密码按钮始终可点击
        submitBtn.setEnabled(true);
        submitBtn.setBackground(ContextCompat.getDrawable(getActivity(), R.drawable.btn_circle_theme));
        
        // 获取验证码按钮初始状态
        switchCodeState(true);
    }

    private void addTextWatchers() {
        // 移除原有的文本监听器，因为按钮始终可点击，不需要动态控制状态
    }

    @OnClick({R.id.get_code_btn, R.id.submit_btn})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.get_code_btn:
                getVerificationCode();
                break;
            case R.id.submit_btn:
                submitModifyPassword();
                break;
        }
    }

    /**
     * 获取验证码
     */
    private void getVerificationCode() {
        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showShortMsg(getActivity(), "手机号码不能为空");
            return;
        }

        Map<String, String> map = new HashMap<>();
        map.put("tel", phone);
        map.put("type", "1"); // 1表示安全设置相关验证码

        getCodeCallBack = addHttpPostRequest(HttpUrlManager.GET_CODE, map, null, this);
    }

    /**
     * 验证字符串是否为纯数字
     */
    private boolean isNumeric(String str) {
        if (TextUtils.isEmpty(str)) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证密码强度（不能是连续数字或相同数字）
     */
    private boolean isValidPassword(String password) {
        if (TextUtils.isEmpty(password) || password.length() != 6) {
            return false;
        }

        // 检查是否为相同数字
        char firstChar = password.charAt(0);
        boolean allSame = true;
        for (int i = 1; i < password.length(); i++) {
            if (password.charAt(i) != firstChar) {
                allSame = false;
                break;
            }
        }
        if (allSame) {
            return false; // 全部相同数字，如111111
        }

        // 检查是否为连续数字（递增或递减）
        boolean isIncreasing = true;
        boolean isDecreasing = true;
        for (int i = 1; i < password.length(); i++) {
            int current = Character.getNumericValue(password.charAt(i));
            int previous = Character.getNumericValue(password.charAt(i - 1));
            
            if (current != previous + 1) {
                isIncreasing = false;
            }
            if (current != previous - 1) {
                isDecreasing = false;
            }
        }
        
        if (isIncreasing || isDecreasing) {
            return false; // 连续数字，如123456或654321
        }

        return true;
    }

    /**
     * 提交修改密码
     */
    private void submitModifyPassword() {
        String smsCode = smsCodeEt.getText().toString().trim();
        String newPassword = newPasswordEt.getText().toString().trim();
        String confirmPassword = confirmPasswordEt.getText().toString().trim();

        // 验证手机号
        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showShortMsg(getActivity(), "手机号码不能为空");
            return;
        }

        // 验证短信验证码
        if (TextUtils.isEmpty(smsCode)) {
            ToastUtils.showShortMsg(getActivity(), "请输入短信验证码");
            return;
        }
        if (smsCode.isEmpty()){
            ToastUtils.showShortMsg(getActivity(), "请输入验证码");
            return;
        }
//        if (smsCode.length() != 6) {
//            ToastUtils.showShortMsg(getActivity(), "验证码必须为6位数字");
//            return;
//        }
        if (!isNumeric(smsCode)) {
            ToastUtils.showShortMsg(getActivity(), "验证码只能输入数字");
            return;
        }

        // 验证新密码
        if (TextUtils.isEmpty(newPassword)) {
            ToastUtils.showShortMsg(getActivity(), "请输入提现密码");
            return;
        }
        if (newPassword.length() != 6) {
            ToastUtils.showShortMsg(getActivity(), "提现密码必须为6位数字");
            return;
        }
        if (!isNumeric(newPassword)) {
            ToastUtils.showShortMsg(getActivity(), "提现密码只能输入数字");
            return;
        }

        // 验证确认密码
        if (TextUtils.isEmpty(confirmPassword)) {
            ToastUtils.showShortMsg(getActivity(), "请输入确认密码");
            return;
        }
        if (confirmPassword.length() != 6) {
            ToastUtils.showShortMsg(getActivity(), "确认密码必须为6位数字");
            return;
        }
        if (!isNumeric(confirmPassword)) {
            ToastUtils.showShortMsg(getActivity(), "确认密码只能输入数字");
            return;
        }

        // 验证两次密码是否一致
        if (!newPassword.equals(confirmPassword)) {
            ToastUtils.showShortMsg(getActivity(), "两次输入的密码不一致");
            return;
        }

        // 验证密码强度（不能是连续数字或相同数字）
        if (!isValidPassword(newPassword)) {
            ToastUtils.showShortMsg(getActivity(), "密码不能为连续数字或相同数字，请重新设置");
            return;
        }

        // 直接修改密码，无需单独校验验证码
        modifyWithdrawPassword();
    }

    /**
     * 修改提现密码
     */
    private void modifyWithdrawPassword() {
        String newPassword = newPasswordEt.getText().toString().trim();
        String smsCode = smsCodeEt.getText().toString().trim();
        String encryptedPassword = MD5Utils.MD5Upper32(newPassword);

        Map<String, String> map = new HashMap<>();
        map.put("userId", SharedPreferenceUtils.getString(getActivity(), PublicParams.USER_ID, ""));
        map.put("password", encryptedPassword);
        map.put("smsCode", smsCode);
        // 验证码方式修改时不需要旧密码，该字段为可选

        modifyPasswordCallBack = addHttpPostRequest(HttpUrlManager.MODIFY_WITHDRAW_PASSWORD, map, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        
        if (HttpUrlManager.GET_CODE.equals(taskId)) {
            // 获取验证码成功
            if (result.isRequestSuccessed()) {
                ToastUtils.showShortMsg(getActivity(), "验证码发送成功");
                startCountDown();
            } else {
                RequestErrorToast.showError(getActivity(), taskId, result.getCode(), result.getErrorMsg());
            }
        } else if (HttpUrlManager.MODIFY_WITHDRAW_PASSWORD.equals(taskId)) {
            // 修改密码
            if (result.isRequestSuccessed()) {
                ToastUtils.showShortMsg(getActivity(), "提现密码修改成功");
                if (getActivity() != null) {
                    getActivity().finish();
                }
            } else {
                RequestErrorToast.showError(getActivity(), taskId, result.getCode(), result.getErrorMsg());
            }
        }
    }

    /**
     * 切换获取验证码按钮的状态
     *
     * @param clickable 是否可以点击
     */
    private void switchCodeState(boolean clickable) {
        if (clickable) {
            getCodeBtn.setBackgroundResource(R.drawable.btn_circle_theme);
            getCodeBtn.setTextColor(ContextCompat.getColor(getActivity(), R.color.br_color_white));
        } else {
            getCodeBtn.setBackgroundResource(R.drawable.btn_circle_gray_line);
            getCodeBtn.setTextColor(ContextCompat.getColor(getActivity(), R.color.br_color_black_999));
        }
    }

    /**
     * 开始倒计时
     */
    private void startCountDown() {
        switchCodeState(false);
        countDownTimer = new CountDownTimer(60000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                getCodeBtn.setText("重新发送(" + (millisUntilFinished / 1000) + "s)");
            }

            @Override
            public void onFinish() {
                switchCodeState(true);
                getCodeBtn.setText("获取验证码");
            }
        };
        countDownTimer.start();
    }

    /**
     * 刷新界面状态
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshState(ModifyWithdrawPasswordRefreshStateEvent event) {
        if (event.isSmsModifyRefresh()) {
            // 清空输入框
            smsCodeEt.setText("");
            newPasswordEt.setText("");
            confirmPasswordEt.setText("");
            
            // 重置验证码按钮状态
            if (countDownTimer != null) {
                countDownTimer.cancel();
            }
            switchCodeState(true);
            getCodeBtn.setText("获取验证码");
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBusUtils.unRegister(this);
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        if (getCodeCallBack != null) {
            getCodeCallBack.cancel();
        }
        if (modifyPasswordCallBack != null) {
            modifyPasswordCallBack.cancel();
        }
    }
}