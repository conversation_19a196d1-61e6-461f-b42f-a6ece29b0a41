package com.doctor.br.utils;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.Log;

import com.mobile.auth.gatewayauth.AuthUIConfig;
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.mobile.auth.gatewayauth.TokenResultListener;

import org.newapp.ones.base.base.BaseConfig;

/**
 * 阿里云号码认证管理器
 * 用于处理一键登录和本机号码校验功能
 */
public class AliyunPhoneAuthManager {
    
    private static final String TAG = "AliyunPhoneAuth";
    
    // 阿里云号码认证SDK配置
    private static final String SDK_SECRET = "mCmiD1cwfqqeuqi016PDe6MRkMUXwWf/YzEPCJHQDLjuMs3uA58GSlVmiLyUXlZF4UwzD8B+U9YLu/YtDFEWD2f7TctTtWph1HtwDmpTAW+jPSZzEnyfWAhphDJpGK257yGAmkw5dg3D4azj0KhFv8G5sh0oP5Fxo3kFPRotcYQbDh7ANELrfkhX9lJzY8uXSz9neNj8Fd9682XnDXWujXnfQRQ35tIXOyHjapCO6W2DJ3MW+ZUHv8McfWr1QpLMxyT1HWcpSvtkbbHL/BnyJTAbovW98fkLgREXEHQxqgU="; // TODO: 需要配置实际的SDK密钥
    
    private PhoneNumberAuthHelper mPhoneNumberAuthHelper;
    private Context mContext;
    private AliyunAuthCallback mCallback;
    private boolean mIsAuthPageShowing = false; // 跟踪授权页状态
    
    public interface AliyunAuthCallback {
        void onAuthSuccess(String accessToken, String phoneNumber);
        void onAuthFailed(String errorMsg, String errorCode);
        void onEnvironmentCheckFailed(String msg);
    }
    
    public AliyunPhoneAuthManager(Context context) {
        // 保存传入的Context，如果是Activity则直接使用，否则使用ApplicationContext
        if (context instanceof android.app.Activity) {
            this.mContext = context;
        } else {
            this.mContext = context.getApplicationContext();
        }
        initSDK();
    }
    
    /**
     * 初始化SDK
     */
    private void initSDK() {
        // 创建token监听器
        TokenResultListener tokenResultListener = new TokenResultListener() {
            @Override
            public void onTokenSuccess(String token) {
                Log.d(TAG, "=== 阿里云SDK Token获取成功回调 ===");
                Log.d(TAG, "Token获取成功: " + token);
                Log.d(TAG, "Token是否为空: " + (token == null ? "null" : (token.isEmpty() ? "empty" : "not empty")));
                if (token != null) {
                    Log.d(TAG, "Token长度: " + token.length());
                    Log.d(TAG, "Token前100个字符: " + (token.length() > 100 ? token.substring(0, 100) + "..." : token));
                    Log.d(TAG, "Token是否为JSON格式: " + token.startsWith("{"));
                }
                Log.d(TAG, "=== 阿里云SDK Token详细信息结束 ===");
                
                // 检查是否是状态响应而不是真正的token
                if (token != null && token.contains("\"code\":\"600001\"") && token.contains("唤起授权页成功")) {
                    Log.w(TAG, "检测到这是授权页唤起成功的状态响应，不是真正的token");
                    Log.w(TAG, "这种情况表示用户还未完成授权，授权页正在显示中");
                    Log.w(TAG, "等待用户在授权页面完成操作...");
                    // 不回调成功，等待用户实际完成授权
                    // 设置标志表示授权页正在显示
                    mIsAuthPageShowing = true;
                    return;
                }
                
                // 如果包含其他错误码，按失败处理
                if (token != null && token.contains("\"code\":") && !token.contains("\"code\":\"600000\"")) {
                    Log.w(TAG, "检测到错误码响应: " + token);
                    // 解析错误信息
                    try {
                        org.json.JSONObject jsonObject = new org.json.JSONObject(token);
                        String code = jsonObject.optString("code", "");
                        String msg = jsonObject.optString("msg", "");
                        Log.w(TAG, "错误码: " + code + ", 错误信息: " + msg);
                        
                        mIsAuthPageShowing = false;
                        if (mCallback != null) {
                            mCallback.onAuthFailed(msg, code);
                        }
                        return;
                    } catch (Exception e) {
                        Log.e(TAG, "解析错误响应失败: " + e.getMessage());
                    }
                }
                
                mIsAuthPageShowing = false; // 成功后授权页会自动关闭
                if (mCallback != null) {
                    // 简化处理，直接回调成功
                    mCallback.onAuthSuccess(token, "");
                }
            }
            
            @Override
            public void onTokenFailed(String msg) {
                Log.e(TAG, "Token获取失败: " + msg);
                mIsAuthPageShowing = false; // 失败后授权页会自动关闭
                if (mCallback != null) {
                    // 解析错误码
                    String errorCode = "TOKEN_FAILED";
                    if (msg != null && msg.contains("600002")) {
                        errorCode = "600002";
                    }
                    mCallback.onAuthFailed(msg, errorCode);
                }
            }
        };
        
        // 获取认证助手实例
        mPhoneNumberAuthHelper = PhoneNumberAuthHelper.getInstance(mContext, tokenResultListener);
        
        // 设置SDK密钥信息
        if (!TextUtils.isEmpty(SDK_SECRET)) {
            mPhoneNumberAuthHelper.setAuthSDKInfo(SDK_SECRET);
        } else {
            Log.w(TAG, "SDK密钥未配置，请在AliyunPhoneAuthManager中配置实际的SDK密钥");
        }
        
        // 设置UI配置
        setUIConfig();
    }
    
    /**
     * 设置授权页UI配置
     * 包括登录按钮文字、页面样式等
     */
    private void setUIConfig() {
        if (mPhoneNumberAuthHelper == null) {
            Log.w(TAG, "PhoneNumberAuthHelper未初始化，无法设置UI配置");
            return;
        }
        
        try {
            Log.d(TAG, "=== 开始设置UI配置 ===");
            Log.d(TAG, "用户协议URL: " + BaseConfig.DOCTOR_AGREEMENT);
            Log.d(TAG, "隐私政策URL: " + BaseConfig.PRIVACY_AGREEMENT);
            
            // 创建UI配置
            AuthUIConfig uiConfig = new AuthUIConfig.Builder()
                // 设置登录按钮文字（必须包含"登录"字样以符合运营商规范）
                .setLogBtnText("本机号码一键登录")
                
                // 设置登录按钮样式
                .setLogBtnTextColor(Color.WHITE)
                .setLogBtnTextSizeDp(18)
                .setLogBtnWidth(280)
                .setLogBtnHeight(45)
                
                // 设置导航栏
                .setNavText("快速登录")
                .setNavTextColor(Color.BLACK)
                .setNavColor(Color.WHITE)
                .setNavHidden(false)
                
                // 设置页面标语
                .setSloganText("为了您的账号安全，请先验证手机号")
                .setSloganTextColor(Color.parseColor("#999999"))
                .setSloganTextSizeDp(14)
                
                // 设置隐私协议 - 使用实际的URL地址确保可点击
                .setAppPrivacyOne("《用户协议》", BaseConfig.DOCTOR_AGREEMENT)
                .setAppPrivacyTwo("《隐私政策》", BaseConfig.PRIVACY_AGREEMENT)
                .setAppPrivacyColor(Color.parseColor("#999999"), Color.parseColor("#0080FF"))
                .setPrivacyState(true) // 默认勾选协议
                .setCheckboxHidden(false)
                
                // 设置页面背景色
                .setAuthPageActIn("in_activity", "out_activity")
                .setAuthPageActOut("in_activity", "out_activity")
                
                .create();
            
            // 应用UI配置
            mPhoneNumberAuthHelper.setAuthUIConfig(uiConfig);
            Log.i(TAG, "UI配置设置成功，登录按钮文字已设置为：本机号码一键登录");
            Log.i(TAG, "隐私协议已配置，协议链接应该可以点击");
            
        } catch (Exception e) {
            Log.e(TAG, "设置UI配置失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 重新初始化SDK - 确保每次调用时状态正确
     */
    private void reinitSDK() {
        Log.d(TAG, "重新初始化SDK...");
        try {
            // 先释放现有资源
            if (mPhoneNumberAuthHelper != null) {
                // 清理现有实例的状态
                mPhoneNumberAuthHelper = null;
            }
            
            // 重新初始化
            initSDK();
            Log.d(TAG, "SDK重新初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "SDK重新初始化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查环境是否支持一键登录
     * @param callback 回调接口
     */
    public void checkEnvironment(AliyunAuthCallback callback) {
        this.mCallback = callback;
        
        if (mPhoneNumberAuthHelper == null) {
            if (callback != null) {
                callback.onEnvironmentCheckFailed("SDK未初始化");
            }
            return;
        }
        
        try {
            // 简化处理：直接调用环境检查，然后尝试启动一键登录
            mPhoneNumberAuthHelper.checkEnvAvailable(PhoneNumberAuthHelper.SERVICE_TYPE_AUTH);
            
            // 环境检查后直接尝试启动一键登录
            Log.d(TAG, "开始尝试一键登录...");
            startOneKeyLogin(callback);
            
        } catch (Exception e) {
            Log.e(TAG, "环境检查异常: " + e.getMessage(), e);
            if (callback != null) {
                callback.onEnvironmentCheckFailed("环境检查异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 启动一键登录页面
     * @param callback 认证回调
     */
    public void startOneKeyLogin(AliyunAuthCallback callback) {
        this.mCallback = callback;
        
        if (mPhoneNumberAuthHelper == null) {
            if (callback != null) {
                callback.onAuthFailed("SDK未初始化", "SDK_NOT_INIT");
            }
            return;
        }
        
        // 检查是否已有授权页存在，先强制退出
        Log.d(TAG, "启动前先确保退出现有授权页...");
        quitLoginPage();
        
        // 等待一小段时间确保授权页完全关闭
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Log.w(TAG, "等待授权页关闭时被中断");
        }
        
        try {
            Log.d(TAG, "正在启动一键登录页面...");
            // 在启动前确保UI配置是最新的
            setUIConfig();
            
            mIsAuthPageShowing = true;
            // 启动一键登录页面
            mPhoneNumberAuthHelper.getLoginToken(mContext, 5000);
        } catch (Exception e) {
            Log.e(TAG, "启动一键登录异常: " + e.getMessage(), e);
            mIsAuthPageShowing = false;
            if (callback != null) {
                callback.onAuthFailed("启动一键登录异常: " + e.getMessage(), "START_LOGIN_ERROR");
            }
        }
    }
    
    /**
     * 进行本机号码校验
     * @param phoneNumber 待校验的手机号
     * @param callback 校验回调
     */
    public void verifyPhoneNumber(String phoneNumber, AliyunAuthCallback callback) {
        this.mCallback = callback;
        
        if (mPhoneNumberAuthHelper == null) {
            if (callback != null) {
                callback.onAuthFailed("SDK未初始化", "SDK_NOT_INIT");
            }
            return;
        }
        
        if (TextUtils.isEmpty(phoneNumber)) {
            if (callback != null) {
                callback.onAuthFailed("手机号不能为空", "PHONE_EMPTY");
            }
            return;
        }
        
        try {
            // 执行本机号码校验 - 使用简化版本
            mPhoneNumberAuthHelper.getVerifyToken(5000);
        } catch (Exception e) {
            Log.e(TAG, "号码校验异常: " + e.getMessage(), e);
            if (callback != null) {
                callback.onAuthFailed("号码校验异常: " + e.getMessage(), "VERIFY_ERROR");
            }
        }
    }
    
    /**
     * 退出一键登录授权页
     * 当用户点击返回按钮或切换到其他登录方式时调用
     */
    public void quitLoginPage() {
        try {
            if (mPhoneNumberAuthHelper != null) {
                Log.d(TAG, "调用quitLoginPage退出授权页");
                mPhoneNumberAuthHelper.quitLoginPage();
            }
        } catch (Exception e) {
            Log.e(TAG, "退出授权页异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 释放资源
     */
    public void release() {
        if (mPhoneNumberAuthHelper != null) {
            // 退出授权页
            quitLoginPage();
            // 简化处理，不调用可能不存在的方法
            mPhoneNumberAuthHelper = null;
        }
        mCallback = null;
    }
    
    /**
     * 设置SDK密钥
     * @param secretKey SDK密钥
     */
    public void setSDKSecret(String secretKey) {
        if (mPhoneNumberAuthHelper != null && !TextUtils.isEmpty(secretKey)) {
            mPhoneNumberAuthHelper.setAuthSDKInfo(secretKey);
        }
    }
}