package com.doctor.br.activity.mine;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;

import androidx.core.content.ContextCompat;

import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.doctor.br.app.AppContext;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.BadgeUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.ActivityManager;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;

/**
 * 类描述：删除账号
 */

public class DeleteAccountActivity extends ActionBarActivity {
    //界面下的控件
    @BindView(R.id.sms_phone_et)
    EditText smsPhoneEt;
    @BindView(R.id.sms_code_et)
    EditText smsCodeEt;
    @BindView(R.id.sms_get_code_btn)
    Button smsGetCodeBtn;
    @BindView(R.id.sms_next_btn)
    Button smsNextBtn;
    private RequestCallBack getCodeCallBack;
    private RequestCallBack sureCodeTrueCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_delete_account);
        initView();
    }

    private void initView() {
        getBaseActionBar().setActionBarTitle("账号注销");
        //从本地取出登录的账号
        String spTel = SharedPreferenceUtils.getString(this, PublicParams.USER_TEL);
        if (!TextUtils.isEmpty(spTel) && spTel.length() == 11) {
            smsPhoneEt.setText(spTel);
            smsPhoneEt.setEnabled(false);
            switchCodeState(smsGetCodeBtn, true);
        } else {
            ToastUtils.showShortMsg(this, "账号登录异常，请重新登录后再试！");
            finish();
            return;
        }
        smsNextBtn.setText("提交注销");
        smsGetCodeBtn.setOnClickListener(this);
        smsNextBtn.setOnClickListener(this);
    }

    /**
     * 切换获取验证码按钮的状态
     *
     * @param clickable 是否可以点击
     */
    private void switchCodeState(TextView button, boolean clickable) {
        if (clickable) {
            button.setBackgroundResource(R.drawable.btn_circle_theme);
            button.setTextColor(ContextCompat.getColor(this, R.color.br_color_white));
        } else {
            button.setBackgroundResource(R.drawable.btn_circle_gray_line);
            button.setTextColor(ContextCompat.getColor(this, R.color.br_color_black_999));
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.sms_get_code_btn:
                //当前界面
                if (smsPhoneEt.getText().length() != 11 || smsGetCodeBtn.getText().toString().contains("重新发送")) {
                    return;
                }
                getCodeRequest(smsPhoneEt.getText().toString(), "1", "1");
                break;
            case R.id.sms_next_btn: {
                //当前界面
                String currentPhone = smsPhoneEt.getText().toString();
                String currentCode = smsCodeEt.getText().toString();
                if (TextUtils.isEmpty(currentPhone)) {
                    ToastUtils.showShortMsg(this, "请输入您的原手机号");
                    smsPhoneEt.requestFocus();
                    return;
                }
                if (TextUtils.isEmpty(currentCode)) {
                    ToastUtils.showShortMsg(this, "请输入验证码");
                    smsCodeEt.requestFocus();
                    return;
                }
                if (!currentPhone.equals(SharedPreferenceUtils.getString(this, PublicParams.USER_TEL))) {
                    ToastUtils.showShortMsg(this, "请输入您正确的原手机号");
                    smsPhoneEt.requestFocus();
                    return;
                }
                mType = "2";
                commitCurrentCodeRequest(currentPhone, currentCode, mType);
            }
            break;
            default:
                break;
        }
    }

    //分别设置倒计时
    private CountDownTimer currentCountDownTimer = new CountDownTimer(60 * 1000, 1000) {
        @Override
        public void onTick(long millisUntilFinished) {
            switchCodeState(smsGetCodeBtn, false);
            smsGetCodeBtn.setText("重新发送(" + millisUntilFinished / 1000 + "s" + ")");
        }

        @Override
        public void onFinish() {
            switchCodeState(smsGetCodeBtn, true);
            smsGetCodeBtn.setText(R.string.get_validation_code);
        }
    };

    /**
     * 网络请求获取验证码
     *
     * @param tel     获取验证码的手机
     * @param type    不知道为啥，反正就是1
     * @param optType 1=包括登陆、忘记密码、安全设置(修改手机号)2=包括注册
     */
    private void getCodeRequest(String tel, String type, String optType) {
        Map<String, String> map = new HashMap<>();
        map.put("tel", tel);
        map.put("type", type);
        map.put("optType", optType);
        getCodeCallBack = addHttpPostRequest(HttpUrlManager.GET_CODE, map, null, this);
    }

    /**
     * 验证手机号与验证码是否匹配，如果是新手机号则更换手机号，如果是旧手机号则只验证是否匹配
     *
     * @param mobile 手机号
     * @param code   验证码
     * @param type   1 验证密码是否匹配 2 验证旧手机号验证码是否匹配 3 提交页面获取验证码 4 提交页面更换手机号
     */
    private void commitCurrentCodeRequest(String mobile, String code, String type) {
        Map<String, String> map = new HashMap<>();
        map.put("mobile", mobile);
        map.put("code", code);
        map.put("type", type);
        map.put("password", null);
        sureCodeTrueCallBack = addHttpPostRequest(HttpUrlManager.MODIFY_PHONE, map, null, this);
    }

    //提交接口类型
    private String mType;

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (smsPhoneEt == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.GET_CODE:
                //获取验证码
                if (result.isRequestSuccessed()) {
                    currentCountDownTimer.start();
                    ToastUtils.showShortMsg(this, "验证码已发送");
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.MODIFY_PHONE:
                if (result.isRequestSuccessed()) {
//                    EmailUtils.getInstance().sendDeleteEmailToAdmin(smsPhoneEt.getText().toString(), new EmailUtils.Callback() {
//                        @Override
//                        public void callback(final boolean successful) {
//                            runOnUiThread(new Runnable() {
//                                @Override
//                                public void run() {
//                                    smsNextBtn.setEnabled(true);
//                                    smsNextBtn.setText("提交注销");
//                                    if (successful) {
//                                        ToastUtils.showLongMsg(DeleteAccountActivity.this, "账号注销申请已提交，我们将在1-2个工作日内进行审核，审核成功后，您的账号将无法登录！");
//                                        Logout();
//                                        finish();
//                                    } else {
//                                        ToastUtils.showShortMsg(DeleteAccountActivity.this, "账号注销申请失败，请稍后再试");
//                                    }
//                                }
//                            });
//                        }
//                    });
                    addHttpPostRequest(HttpUrlManager.ACCOUNT_LOGOUT, null, ResponseResult.class, this);

                } else {
                    smsNextBtn.setEnabled(true);
                    smsNextBtn.setText("提交注销");
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.ACCOUNT_LOGOUT:
                if (result.isRequestSuccessed()) {
                    ToastUtils.showLongMsg(DeleteAccountActivity.this, "账号已注销,您的账号将无法登录！");
                    Logout();
                    finish();
                } else {
                    ToastUtils.showShortMsg(DeleteAccountActivity.this, "账号注销申请失败，请稍后再试");
                }
                break;
            default:
                break;
        }
    }

    /**
     * 退出登录
     */
    private void Logout() {
        BadgeUtils.setBadgeCount(this, 0);
        AppContext.getInstances().getNettyClient().stop();
        AppContext.getInstances().logout();
        SharedPreferenceUtils.clearString(this);
        ActivityManager.getInstance().finishAllActivity();
        Intent i = getBaseContext().getPackageManager()
                .getLaunchIntentForPackage(getBaseContext().getPackageName());
        if (i != null) {
            i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        }
        startActivity(i);
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        if (currentCountDownTimer != null) {
            currentCountDownTimer.cancel();
        }

        if (getCodeCallBack != null) {
            getCodeCallBack.cancel();
        }
        if (sureCodeTrueCallBack != null) {
            sureCodeTrueCallBack.cancel();
        }
        super.onDestroy();
    }

}
