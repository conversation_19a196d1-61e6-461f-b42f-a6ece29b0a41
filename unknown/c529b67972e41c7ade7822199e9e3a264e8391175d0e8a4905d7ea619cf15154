package com.doctor.br.activity.manage;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.activity.ShowImageActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.MedicationDetailBean;
import com.doctor.br.db.entity.Contacts;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.greendao.gen.ContactsDao;
import com.doctor.yy.R;
import com.google.common.collect.Lists;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.glide.GlideUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.EmptyView;

import java.util.HashMap;

import butterknife.BindView;

/**
 * 功能:拍照药方订单状态 1.待审核 2.待转换
 * 创建人:YangYajun
 * 创建时间: 2018/07/10
 */
public class CameraOrderStateActivity extends ActionBarActivity implements View.OnClickListener {
    //  上个界面传回的数据
    public static final String ORDER_ID = "orderId";//订单id
    public static final String ORDER_STATE = "order_state";//拍照药方订单状态 1.待审核 2.待转换
    private String orderId;//订单id

    private int state = 0;// 订单状态
    private String userId;// 医生Id
    private String imgUrl;// 图片Url
    private static String ORDER_CANCELED = "CANCELED";//订单已作废
    private static String ORDER_EXPIRE = "EXPIRE";//订单已过期

    // 界面下的控件
    @BindView(R.id.tv_state)
    TextView tvState;
    @BindView(R.id.tv_describe)
    TextView tvDescribe;
    @BindView(R.id.iv_icon_phone)
    ImageView ivIconPhone;
    @BindView(R.id.tv_service)
    TextView tvService;
    @BindView(R.id.ll_container)
    LinearLayout llContainer;
    @BindView(R.id.tv_complete)
    TextView tvComplete;
    @BindView(R.id.tv_type_supplier)
    TextView tvTypeSupplier;
    @BindView(R.id.iv_camera_order)
    ImageView ivCameraOrder;
    @BindView(R.id.empty_view)
    EmptyView emptyView;
    @BindView(R.id.iv_head)
    ImageView ivHead;
    @BindView(R.id.tv_provide)
    TextView tvProvide;

    private ContactsDao contactsDao;
    private ConfirmDialog callDialog;//打电话对话框
    private RequestCallBack getMedicationDateCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_camera_order_state);
        getIntentData(savedInstanceState);
        initDate();
        initView();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(ORDER_STATE, state);
        outState.putString(ORDER_ID, orderId);
    }

    private void getIntentData(Bundle bundle) {
        if (bundle == null) {
            state = getIntent().getIntExtra(ORDER_STATE, 0);
            orderId = getIntent().getStringExtra(ORDER_ID);
        } else {
            state = bundle.getInt(ORDER_STATE, 0);
            orderId = bundle.getString(ORDER_ID);
        }
    }

    private void initDate() {
        userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        getMedicineDateRequest(orderId, userId);
    }


    protected void initView() {
        tvService.setOnClickListener(this);
        ivCameraOrder.setOnClickListener(this);
        contactsDao = AppContext.getInstances().getDaoSession().getContactsDao();

        tvService.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
                String key = PublicParams.CHAT_SERVICE_USER_ID + "_" + userId;
                final Contacts contacts = contactsDao.load(key);
                if (contacts == null) {
                    return;
                }
                String str1 = "客服热线：" + contacts.getMobile();
                String str2 = str1 + "\n" + contacts.getDescription();
                SpannableString spannableString = new SpannableString(str2);
                spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(CameraOrderStateActivity.this, R.color.br_color_theme)),
                        0, str1.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);

                callDialog = ConfirmDialog.getInstance(CameraOrderStateActivity.this)
//                            .setDialogTitle(str1)
                        .setDialogContentLineSpacing(30f, 1f)
                        .setDialogContent(spannableString)
                        .setPositiveText("立即拨号")
                        .setNavigationText("取消")
                        .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                callDialog.dismiss();
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                Intent dialIntent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + contacts.getMobile()));//跳转到拨号界面，同时传递电话号码
                                startActivity(dialIntent);
                                callDialog.dismiss();
                            }
                        });
                callDialog.show();
            }
        });
    }

    private void initPage(MedicationDetailBean medicationDetailBean) {
        switch (state) {
            case 1:
                //设置是否显示EmptyView
                if (ORDER_CANCELED.equals(medicationDetailBean.getOrderStatus()) || ORDER_EXPIRE.equals(medicationDetailBean.getOrderStatus())) {//已作废或已过期
                    disabled();
                }else {
                    emptyView.setVisibility(View.GONE);
                    setActionBarTitle("待审核");
                    ivHead.setImageResource(R.drawable.manage_state_daishenhe);
                    tvState.setText(R.string.state_top1_line);
                }
                break;
            case 2:
                //设置是否显示EmptyView
                if (ORDER_CANCELED.equals(medicationDetailBean.getOrderStatus()) || ORDER_EXPIRE.equals(medicationDetailBean.getOrderStatus())) {//已作废或已过期
                    disabled();
                }else {
                    emptyView.setVisibility(View.GONE);
                    setActionBarTitle("待转换");
                    ivHead.setImageResource(R.drawable.manage_state_daizhuanhuan);
                    tvState.setText(R.string.state_top2_line);
                }
                break;
            default:
                break;
        }
    }

    private void disabled(){
        emptyView.setVisibility(View.VISIBLE);
        emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
        emptyView.setEmptyText("该订单已失效");
        emptyView.setEmptyImgResource(R.drawable.order_delete);
    }


    /**
     * 获取用药详情
     *
     * @param orderId 订单ID
     * @param userId  用户ID
     */
    private void getMedicineDateRequest(String orderId, String userId) {
        HashMap map = new HashMap();
        map.put("orderId", orderId);
        map.put("userId", userId);
        getMedicationDateCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL, map, MedicationDetailBean.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.MEDICATION_DETAIL:// 获取用药详情
                if (result != null && result.isRequestSuccessed()) {
                    MedicationDetailBean medicationDetailBean = (MedicationDetailBean) result.getBodyObject();
                    if (medicationDetailBean != null) {
                        initMedicationInfo(medicationDetailBean);
                        initPage(medicationDetailBean);
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
            default:
                break;
        }
    }

    /**
     * 初始化信息
     *
     * @param medicationDetailBean
     */
    private void initMedicationInfo(MedicationDetailBean medicationDetailBean) {
        if (medicationDetailBean == null) {
            return;
        }
        imgUrl = medicationDetailBean.getPrescriptionUrl();
        GlideUtils.getInstance().loadImage(imgUrl, CameraOrderStateActivity.this, ivCameraOrder, R.drawable.icon_img_load_error);
        tvProvide.setText(medicationDetailBean.getDrugForm() + "(" + medicationDetailBean.getProductProviteName() + ")");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        cancelRequest(getMedicationDateCallBack);
    }

    @Override
    public void onClick(View view) {
        super.onClick(view);
        switch (view.getId()) {
            case R.id.iv_camera_order://查看大图
                ShowImageActivity.showImage(CameraOrderStateActivity.this, Lists.<String>newArrayList(imgUrl), imgUrl);
                break;
            default:
                break;
        }
    }
}
