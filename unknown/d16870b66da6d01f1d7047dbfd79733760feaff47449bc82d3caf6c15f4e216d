package com.doctor.br.adapter.manage;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.doctor.br.activity.manage.OrderActivity;
import com.doctor.br.fragment.manage.DaifukuanFragment;
import com.doctor.br.fragment.manage.QuanbuFragment;
import com.doctor.br.fragment.manage.YiguoqiFragment;
import com.doctor.br.fragment.manage.YiwanchengFragment;

/**
 * 类描述：订单界面viewpager适配器
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/24
 */

public class OrderViewPagerAdapter extends FragmentPagerAdapter {
    private DaifukuanFragment daifukuanFragment;
    private YiwanchengFragment yiwanchengFragment;
    private YiguoqiFragment yiguoqiFragment;
    private QuanbuFragment quanbuFragment;

    private int size;
    private boolean isShow;

    public OrderViewPagerAdapter(FragmentManager fm, int size, boolean isShow) {
        super(fm);
        this.size = size;
        this.isShow = isShow;
    }

    public void setShow(boolean show) {
        isShow = show;
    }

    @Override
    public Fragment getItem(int position) {
        switch (position) {
            case 0:
                if (daifukuanFragment == null) {
                    daifukuanFragment = new DaifukuanFragment();
                    Bundle bundle = new Bundle();
                    bundle.putBoolean(OrderActivity.IS_SHOW, isShow);
                    daifukuanFragment.setArguments(bundle);
                }
                return daifukuanFragment;
            case 1:
                if (yiwanchengFragment == null) {
                    yiwanchengFragment = new YiwanchengFragment();
                    Bundle bundle = new Bundle();
                    bundle.putBoolean(OrderActivity.IS_SHOW, isShow);
                    yiwanchengFragment.setArguments(bundle);
                }
                return yiwanchengFragment;
            case 2:
                if (yiguoqiFragment == null) {
                    yiguoqiFragment = new YiguoqiFragment();
                    Bundle bundle = new Bundle();
                    bundle.putBoolean(OrderActivity.IS_SHOW, isShow);
                    yiguoqiFragment.setArguments(bundle);
                }
                return yiguoqiFragment;
            case 3:
                if (quanbuFragment == null) {
                    quanbuFragment = new QuanbuFragment();
                    Bundle bundle = new Bundle();
                    bundle.putBoolean(OrderActivity.IS_SHOW, isShow);
                    quanbuFragment.setArguments(bundle);
                }
                return quanbuFragment;
            default:
                return null;
        }
    }

    @Override
    public int getCount() {
        return size;
    }

}
