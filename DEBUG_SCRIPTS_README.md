# Android调试脚本使用指南

本项目提供了三个调试脚本，帮助您快速编译、安装、运行和调试Android应用。

## 📁 脚本文件

### 1. `run_debug.sh` - 基础调试脚本
**功能**: 完整的编译、安装、运行和日志显示流程

**使用方法**:
```bash
./run_debug.sh
```

**特点**:
- 自动检查设备连接
- 清理并编译项目
- 安装APK到真机
- 启动应用
- 显示实时日志

---

### 2. `debug_advanced.sh` - 高级调试脚本
**功能**: 提供更多选项和高级功能的调试脚本

**使用方法**:
```bash
# 查看帮助
./debug_advanced.sh -h

# 基本使用
./debug_advanced.sh

# 清理构建
./debug_advanced.sh -c

# 仅安装现有APK
./debug_advanced.sh -i

# 指定日志级别（仅显示错误）
./debug_advanced.sh -l E

# 过滤日志内容
./debug_advanced.sh -f "MainActivity"

# 组合使用
./debug_advanced.sh -c -l W -f "网络"
```

**参数说明**:
- `-c, --clean`: 清理构建（强制重新编译）
- `-i, --install-only`: 仅安装APK，不重新编译
- `-l, --log-level`: 日志级别 (V|D|I|W|E)
  - V: Verbose（详细）
  - D: Debug（调试）
  - I: Info（信息）
  - W: Warning（警告）
  - E: Error（错误）
- `-f, --filter`: 日志过滤关键词
- `-h, --help`: 显示帮助信息

**特点**:
- 支持命令行参数
- 显示设备信息和应用版本
- 测量应用启动时间
- 支持日志过滤
- 彩色输出

---

### 3. `quick_debug.sh` - 快速调试脚本
**功能**: 最简化的快速调试，适合频繁的代码修改测试

**使用方法**:
```bash
./quick_debug.sh
```

**特点**:
- 最快的执行速度
- 简洁的输出信息
- 适合快速迭代开发
- 使用emoji图标，界面友好

---

## 🚀 使用场景

### 场景1: 首次运行或大改动后
```bash
./debug_advanced.sh -c
```
使用清理构建，确保所有更改都被编译。

### 场景2: 日常开发调试
```bash
./run_debug.sh
```
或
```bash
./quick_debug.sh
```
快速编译、安装和查看日志。

### 场景3: 查看特定日志
```bash
./debug_advanced.sh -l E -f "网络错误"
```
只显示包含"网络错误"的错误级别日志。

### 场景4: 仅重新安装
```bash
./debug_advanced.sh -i
```
不重新编译，直接安装现有APK。

---

## 🔧 环境要求

1. **ADB工具**: 确保adb命令可用
2. **设备连接**: Android设备已连接并开启USB调试
3. **项目环境**: 在Android项目根目录下运行
4. **权限**: 脚本需要执行权限（已自动设置）

---

## 📱 设备准备

1. **开启开发者选项**:
   - 设置 → 关于手机 → 连续点击"版本号"7次

2. **开启USB调试**:
   - 设置 → 开发者选项 → USB调试

3. **授权计算机**:
   - 连接设备时选择"允许USB调试"

4. **验证连接**:
   ```bash
   adb devices
   ```

---

## 🐛 常见问题

### Q: 提示"没有检测到设备"
**A**: 
1. 检查USB连接
2. 确认已开启USB调试
3. 运行 `adb devices` 检查
4. 尝试重新连接设备

### Q: 编译失败
**A**:
1. 检查Java版本兼容性
2. 清理项目: `./gradlew clean`
3. 检查网络连接（下载依赖）

### Q: 应用启动失败
**A**:
1. 检查应用包名和Activity名称
2. 查看logcat错误信息
3. 确认设备有足够存储空间

### Q: 日志显示不全
**A**:
1. 尝试使用 `-l V` 显示所有级别日志
2. 检查应用是否正在运行
3. 使用 `-f` 参数过滤特定内容

---

## 💡 提示

1. **快速开发**: 使用 `quick_debug.sh` 进行快速迭代
2. **问题排查**: 使用 `debug_advanced.sh -l E` 查看错误
3. **性能分析**: 注意启动时间显示
4. **日志过滤**: 使用关键词过滤减少干扰信息
5. **多设备**: 脚本会自动选择第一个连接的设备

---

## 📝 自定义配置

如需修改应用包名或主Activity，请编辑脚本中的以下变量：

```bash
APP_PACKAGE="com.doctor.yy"
MAIN_ACTIVITY="com.doctor.br.activity.LauncherActivity"
```

---

**祝您调试愉快！** 🎉 