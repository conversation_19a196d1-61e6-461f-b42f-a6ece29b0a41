{"permissions": {"allow": ["mcp__search1api__search", "mcp__desktop-commander__search_code", "mcp__desktop-commander__search_files", "mcp__desktop-commander__read_file", "mcp__filesystem__search_files", "mcp__filesystem__read_multiple_files", "<PERSON><PERSON>(./gradlew:*)", "mcp__desktop-commander__read_multiple_files", "Bash(grep:*)", "Bash(find:*)", "mcp__filesystem__directory_tree", "mcp__filesystem__list_directory", "mcp__filesystem__read_file", "mcp__filesystem__write_file", "mcp__desktop-commander__execute_command", "mcp__filesystem__edit_file", "mcp__git__git_log", "mcp__git__git_show", "<PERSON><PERSON>(javac:*)", "<PERSON><PERSON>(chmod:*)", "Bash(java:*)", "Bash(./debug.sh:*)", "Bash(ls:*)", "Bash(/usr/libexec/java_home:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__desktop-commander__list_directory", "mcp__desktop-commander__create_directory", "mcp__desktop-commander__write_file", "mcp__desktop-commander__get_config", "Bash(export:*)", "Bash(git add:*)", "<PERSON><PERSON>(echo:*)", "mcp__filesystem__read_text_file", "mcp__desktop-commander__edit_block"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["filesystem"]}