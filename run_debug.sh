#!/bin/bash

# Android项目调试脚本
# 功能：编译、安装、运行到真机并显示实时日志

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
APP_PACKAGE="com.doctor.yy"
MAIN_ACTIVITY="com.doctor.br.activity.LauncherActivity"
APK_PATH="app/build/outputs/apk/product/debug"

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查ADB连接
check_device() {
    print_info "检查设备连接..."
    
    devices=$(adb devices | grep -v "List of devices attached" | grep "device$" | wc -l)
    
    if [ $devices -eq 0 ]; then
        print_error "没有检测到连接的设备！请确保："
        echo "  1. 设备已连接并开启USB调试"
        echo "  2. 已授权此计算机进行调试"
        exit 1
    elif [ $devices -gt 1 ]; then
        print_warning "检测到多个设备，将使用第一个设备"
        adb devices
    else
        device_name=$(adb devices | grep "device$" | awk '{print $1}')
        print_success "设备已连接: $device_name"
    fi
}

# 清理项目
clean_project() {
    print_info "清理项目..."
    ./gradlew clean
    print_success "项目清理完成"
}

# 编译项目
build_project() {
    print_info "开始编译项目..."
    ./gradlew assembleDebug
    
    if [ $? -eq 0 ]; then
        print_success "项目编译成功"
    else
        print_error "项目编译失败"
        exit 1
    fi
}

# 查找APK文件
find_apk() {
    print_info "查找APK文件..." >&2
    
    apk_file=$(find $APK_PATH -name "*.apk" -type f | head -1)
    
    if [ -z "$apk_file" ]; then
        print_error "未找到APK文件" >&2
        exit 1
    fi
    
    print_success "找到APK文件: $apk_file" >&2
    echo "$apk_file"
}

# 安装APK
install_apk() {
    local apk_file=$1
    print_info "安装APK到设备..."
    
    adb install -r "$apk_file"
    
    if [ $? -eq 0 ]; then
        print_success "APK安装成功"
    else
        print_error "APK安装失败"
        exit 1
    fi
}

# 启动应用
start_app() {
    print_info "启动应用..."
    
    # 先停止应用（如果正在运行）
    adb shell am force-stop $APP_PACKAGE
    
    # 清除日志缓存
    adb logcat -c
    
    # 启动应用
    adb shell am start -n $APP_PACKAGE/$MAIN_ACTIVITY
    
    if [ $? -eq 0 ]; then
        print_success "应用启动成功"
    else
        print_error "应用启动失败"
        exit 1
    fi
}

# 显示实时日志
show_logs() {
    print_info "开始显示实时日志..."
    print_warning "按 Ctrl+C 停止日志显示"
    echo ""
    
    # 等待应用启动
    sleep 2
    
    # 获取应用PID
    app_pid=$(adb shell "ps | grep $APP_PACKAGE" | awk '{print $2}' | head -1)
    
    if [ -n "$app_pid" ]; then
        print_info "应用进程ID: $app_pid"
        # 显示应用相关的日志
        adb logcat --pid=$app_pid -v time
    else
        print_warning "未找到应用进程，显示所有相关日志"
        # 显示包含应用包名的日志
        adb logcat -v time | grep -i "$APP_PACKAGE\|AndroidRuntime\|System.err"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "    Android应用调试脚本"
    echo "========================================"
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "gradlew" ]; then
        print_error "请在Android项目根目录下运行此脚本"
        exit 1
    fi
    
    # 执行调试流程
    check_device
    echo ""
    
    clean_project
    echo ""
    
    build_project
    echo ""
    
    apk_file=$(find_apk)
    echo ""
    
    install_apk "$apk_file"
    echo ""
    
    start_app
    echo ""
    
    show_logs
}

# 信号处理
trap 'print_info "脚本已停止"; exit 0' INT TERM

# 运行主函数
main "$@" 