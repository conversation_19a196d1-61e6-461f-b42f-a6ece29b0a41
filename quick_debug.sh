#!/bin/bash

# 快速调试脚本 - 仅重新安装和显示日志
# 适用于代码修改后的快速测试

APP_PACKAGE="com.doctor.yy"
MAIN_ACTIVITY="com.doctor.br.activity.LauncherActivity"

echo "🚀 快速调试模式"
echo "=================="

# 检查设备
if ! adb devices | grep -q "device$"; then
    echo "❌ 没有检测到设备连接"
    exit 1
fi

echo "📱 设备已连接"

# 编译
echo "🔨 编译项目..."
./gradlew assembleDebug -q

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

# 查找APK
APK_FILE=$(find app/build/outputs/apk/product/debug -name "*.apk" -type f | head -1)

if [ -z "$APK_FILE" ]; then
    echo "❌ 未找到APK文件"
    exit 1
fi

echo "📦 安装APK..."
adb install -r "$APK_FILE" > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ 安装失败"
    exit 1
fi

echo "🎯 启动应用..."
adb shell am force-stop $APP_PACKAGE
adb logcat -c
adb shell am start -n $APP_PACKAGE/$MAIN_ACTIVITY > /dev/null 2>&1

echo "📋 显示日志 (按Ctrl+C停止)..."
echo "================================"

sleep 1

# 获取PID并显示日志
PID=$(adb shell "ps | grep $APP_PACKAGE" | awk '{print $2}' | head -1)

if [ -n "$PID" ]; then
    adb logcat --pid=$PID -v brief
else
    adb logcat -v brief | grep -i "$APP_PACKAGE\|AndroidRuntime"
fi 