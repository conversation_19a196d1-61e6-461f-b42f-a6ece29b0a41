name: Build Huawei Release APK

on:
  push:
    branches:
      - testing

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up JDK 11
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '11'
          cache: gradle

      - name: <PERSON> execute permission for gradlew
        run: chmod +x ./gradlew

      - name: Build with Gradle
        run: ./gradlew assembleHuaweiRelease

      - name: Upload APK
        uses: actions/upload-artifact@v3
        with:
          name: huawei-release-apk
          path: app/build/outputs/apk/huawei/release/brzhongyi-huawei-release-v*.apk
